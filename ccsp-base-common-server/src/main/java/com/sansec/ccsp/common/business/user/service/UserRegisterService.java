package com.sansec.ccsp.common.business.user.service;

import com.sansec.ccsp.common.role.response.UserRoleVO;
import com.sansec.ccsp.common.user.request.AddUserRegisterDTO;
import com.sansec.ccsp.common.user.request.UserRegisterAuditDTO;
import com.sansec.ccsp.common.user.request.UserRegisterDTO;
import com.sansec.ccsp.common.user.request.UserRegisterPageDTO;
import com.sansec.ccsp.common.user.response.UserRegisterVO;
import com.sansec.ccsp.common.user.response.UserTypeVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 用户注册信息表;(USER_REGISTER)表服务接口
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-2-18
 */
public interface UserRegisterService{
    /**
     * 分页查询
     *
     * @param userRegisterPageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<UserRegisterVO>> find(UserRegisterPageDTO userRegisterPageDTO);
    /**
     * 新增数据
     *
     * @param userRegisterDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(AddUserRegisterDTO userRegisterDTO);
    /**
     * 更新数据
     *
     * @param userRegisterDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(UserRegisterDTO userRegisterDTO);
    /**
     * 通过主键删除数据
     *
     * @param userRegisterId
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(Long userRegisterId);

    /**
     * 用户信息审核
     *
     * @param userRegisterAuditDTO 实例对象
     * @return 实例对象
     */
    SecRestResponse<Object> audit(@RequestBody UserRegisterAuditDTO userRegisterAuditDTO);

    /**
     * 获取注册用户类型
     *
     * @return 实例对象
     */
    SecRestResponse<List<UserTypeVO>> getRegisterRoleType();


    /**
     * 获取审核用户注册可选角色
     *
     * @return 实例对象
     */
    SecRestResponse<List<UserRoleVO>> getSelectRoleWithRegisterId(Long userRegisterId);


}
