package com.sansec.ccsp.device.business.devicedic.controller;

import com.sansec.ccsp.device.business.devicedic.request.DicDeviceManagementStatusDTO;
import com.sansec.ccsp.device.business.devicedic.request.DicDeviceManagementStatusPageDTO;
import com.sansec.ccsp.device.business.devicedic.response.DicDeviceManagementStatusVO;
import com.sansec.ccsp.device.business.devicedic.service.DicDeviceManagementStatusService;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

 /**
 * @Description: 密码机使用状态字典表;(DIC_DEVICE_MANAGEMENT_STATUS)表控制层
 * <AUTHOR> xia<PERSON>jiaw<PERSON>
 * @Date: 2023-2-18
 */
@RestController
@RequestMapping("/dicDeviceManagementStatus/v1")
@Validated
public class DicDeviceManagementStatusController{
    @Resource
    private DicDeviceManagementStatusService dicDeviceManagementStatusService;
    
    /** 
     * 分页查询
     *
     * @param dicDeviceManagementStatusPageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    public SecRestResponse<SecPageVO<DicDeviceManagementStatusVO>> find(@RequestBody DicDeviceManagementStatusPageDTO dicDeviceManagementStatusPageDTO){
        return dicDeviceManagementStatusService.find(dicDeviceManagementStatusPageDTO);
    }
    
    /** 
     * 新增数据
     *
     * @param dicDeviceManagementStatusDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    public SecRestResponse<Object> add(@RequestBody DicDeviceManagementStatusDTO dicDeviceManagementStatusDTO){
        return dicDeviceManagementStatusService.add(dicDeviceManagementStatusDTO);
    }
    
    /** 
     * 更新数据
     *
     * @param dicDeviceManagementStatusDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/edit")
    public SecRestResponse<Object> edit(@RequestBody DicDeviceManagementStatusDTO dicDeviceManagementStatusDTO){
        return dicDeviceManagementStatusService.edit(dicDeviceManagementStatusDTO);
    }
    
    /** 
     * 通过主键删除数据
     *
     * @param dicDeviceManagementStatusDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/deleteById")
    public SecRestResponse<Object> deleteById(@RequestBody DicDeviceManagementStatusDTO dicDeviceManagementStatusDTO){
        return dicDeviceManagementStatusService.deleteById(dicDeviceManagementStatusDTO);
    }
}