package com.sansec.ccsp.pt.business.docker.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * docker配置
 * @<PERSON> l<PERSON><PERSON><PERSON>
 * @Date 2024/4/7 17:39
 */
@Data
@Component
@ConfigurationProperties(prefix = "docker.image")
public class DockerImageConfig {
    /**
     * 允许上传镜像的最大容量总和
     */
    private long allowTotalSize = 80;
}
