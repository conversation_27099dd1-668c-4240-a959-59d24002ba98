package com.sansec.ccsp.pt.business.sim.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class SimKeyUserLoginDTO implements Serializable {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 用户状态  1启用 2禁用
     */
    private Integer status;
    /**
     * 证书ID sim_key_cert表中的id，更新后使用新id
     */
    private String certId;
    private Integer simFlag;
    private String commName;
    private String udid;
    private String email;
    private String extend1;
    private String extend2;

    @NotBlank
    private String loginid;

    @NotBlank
    private String password;

}
