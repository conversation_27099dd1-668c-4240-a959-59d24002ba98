package com.sansec.ccsp.pt.business.device.devicedic.service;

import com.sansec.ccsp.device.devicedic.response.DicDeviceMachineTypeApiVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

public interface DicDeviceMachineTypeService {

    /**
     * 获取密码机服务类型字典表中INVALID_FLAG=0的所有数据
     * @return
     */
    SecRestResponse<List<DicDeviceMachineTypeApiVO>> findList();
}
