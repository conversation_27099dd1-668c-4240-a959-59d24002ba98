package com.sansec.ai.common.constant;


/**
 * 公共配置项枚举<br>
 * 系统配置表的统一约束，用于规范查询系统配置项的过程<br>
 * 各模块在从系统配置表查询系统配置项时，必须定义枚举，实现该接口
 * 缓存对象的类型对应的枚举，用于规范缓存中的数据<br>
 * 使用缓存时必须在此声明key，然后通过key获取value
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
public enum CommonConfigEnum implements IConfigCode {
	/**
	 * dify默认应用ID
	 */
	DIFY_DEFAULT_APP_ID("dify_config", "default_app_id"),
	/**
	 * dify默认应用名称
	 */
	DIFY_DEFAULT_APP_NAME("dify_config", "default_app_id"),
	//
	;

	/**
	 * key值前缀
	 */
	private final String typeCode;
	/**
	 * 超时时间
	 */
	private String code;


	CommonConfigEnum(String typeCode) {
		this.typeCode = typeCode;
	}

	CommonConfigEnum(String typeCode, String code) {
		this.typeCode = typeCode;
		this.code = code;
	}

	/**
	 * 获取配置项类型编码
	 *
	 * @return
	 */
	@Override
	public String getTypeCode() {
		return typeCode;
	}

	/**
	 * 获取配置项编码
	 *
	 * @return
	 */
	@Override
	public String getCode() {
		return code;
	}
}
