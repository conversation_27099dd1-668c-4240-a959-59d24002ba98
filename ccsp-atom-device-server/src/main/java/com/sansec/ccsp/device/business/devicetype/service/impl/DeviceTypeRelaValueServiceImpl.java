package com.sansec.ccsp.device.business.devicetype.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.device.business.devicetype.convert.DeviceTypeRelaValueConvert;
import com.sansec.ccsp.device.business.devicetype.entity.DeviceTypeRelaValuePO;
import com.sansec.ccsp.device.business.devicetype.mapper.DeviceTypeRelaValueMapper;
import com.sansec.ccsp.device.business.devicetype.service.DeviceTypeRelaValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class DeviceTypeRelaValueServiceImpl extends ServiceImpl<DeviceTypeRelaValueMapper, DeviceTypeRelaValuePO> implements DeviceTypeRelaValueService {
    @Resource
    private DeviceTypeRelaValueConvert deviceTypeRelaValueConvert;

    @Override
    public void saveBatchPoList(List<DeviceTypeRelaValuePO> poList) {
        this.saveBatch(poList);
    }

    @Override
    public void deleteByDeviceAndValueType(Long deviceTypeId, int valueType) {
        LambdaQueryWrapper<DeviceTypeRelaValuePO> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceTypeRelaValuePO::getDeviceTypeId,deviceTypeId)
                .eq(DeviceTypeRelaValuePO::getValueType,valueType);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public List<Long> getValueByDeviceAndValueType(Long deviceTypeId, int valueType) {
        LambdaQueryWrapper<DeviceTypeRelaValuePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceTypeRelaValuePO::getDeviceTypeId, deviceTypeId)
                .eq(DeviceTypeRelaValuePO::getValueType, valueType);
        List<DeviceTypeRelaValuePO> poList = baseMapper.selectList(queryWrapper);
        List<Long> list = new ArrayList<>();
        poList.forEach(po -> {
            list.add(po.getValue());
        });
        return list;
    }
}
