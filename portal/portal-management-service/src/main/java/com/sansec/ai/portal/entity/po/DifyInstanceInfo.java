package com.sansec.ai.portal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.ai.common.base.entity.BaseEntity;
import lombok.Data;

/**
 * Dify实例信息;
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@TableName("dify_instance_info")
@Data
public class DifyInstanceInfo extends BaseEntity {
	/**
	 * ID
	 */
	@TableId(type = IdType.INPUT)
	private Long id;
	/**
	 * 门户ID
	 */
	private Long portalId;
	/**
	 * 门户编码/URL前缀/实例前缀
	 */
	private String portalCode;
	/**
	 * 实例访问IP
	 */
	private String instanceIp;
	/**
	 * 实例访问端口
	 */
	private Integer instancePort;
	/**
	 * 实例初始化秘钥
	 */
	private String secretKey;
	/**
	 * 实例管理员账号
	 */
	private String userAccount;
	/**
	 * 实例管理员密码
	 */
	private String userPassword;


}