package com.sansec.ccsp.device.devicegroup.request;

import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @CreateTime: 2023-03-14
 * @Author: wangjunjie
 */
@Data
public class DeviceListByTenantDTO extends SecPageDTO {
    /**
     * 租户ID
     */
    private Long tenantId;

    private List<Long> deviceGroupIdList;

    private String deviceName;

    private Long deviceGroupId;

    private Integer isRest;

    // 多借口共用，使用check，不使用注解
    public void check() {
        if (this.tenantId == null) {
            throw new BusinessException("租户Id不可为空");
        }
    }
}
