package com.sansec.ccsp.device.devicemonitor.request;

import lombok.Data;

 /**
 * @description : snmp加密方式字典表(1030);
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2023-9-20
 */
@Data
public class DicSnmpPrivacyDTO{
    /**
     * id
     */
    private Long id;
    /**
     * 认证方式id;与监控组件保持同步
     */
    private Integer snmpPrivacyId;
    /**
     * 认证方式名称;与监控组件同步
     */
    private String snmpPrivacyName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
}