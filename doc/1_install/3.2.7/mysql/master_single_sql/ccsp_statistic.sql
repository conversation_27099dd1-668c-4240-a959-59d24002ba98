/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 100608
 Source Host           : ************:3306
 Source Schema         : ccsp_statistic

 Target Server Type    : MySQL
 Target Server Version : 100608
 File Encoding         : 65001

 Date: 15/05/2023 14:12:58
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for dic_key_status
-- ----------------------------
DROP TABLE IF EXISTS `dic_key_status`;
CREATE TABLE `dic_key_status`  (
                                   `ID` bigint(20) NOT NULL,
                                   `D_NAME` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
                                   `D_VALUE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '值',
                                   `IS_AVAILABLE` int(1) NOT NULL COMMENT '是否启用 1启用 0停用',
                                   PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '密钥状态字典表' ROW_FORMAT = Dynamic;

INSERT INTO `dic_key_status` VALUES (1, '预激活', '1', 1);
INSERT INTO `dic_key_status` VALUES (2, '激活', '2', 1);
INSERT INTO `dic_key_status` VALUES (3, '注销', '3', 1);
INSERT INTO `dic_key_status` VALUES (4, '销毁', '5', 1);

-- ----------------------------
-- Table structure for dic_seal_status
-- ----------------------------
DROP TABLE IF EXISTS `dic_seal_status`;
CREATE TABLE `dic_seal_status`  (
                                    `ID` bigint(20) NOT NULL,
                                    `D_NAME` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
                                    `D_VALUE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '值',
                                    `IS_AVAILABLE` int(1) NOT NULL COMMENT '是否启用 1启用 0停用',
                                    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '签章状态字典表' ROW_FORMAT = Dynamic;

INSERT INTO `dic_seal_status` VALUES (1, '停用', '0', 1);
INSERT INTO `dic_seal_status` VALUES (2, '启用', '1', 1);
INSERT INTO `dic_seal_status` VALUES (3, '删除', '2', 1);

-- ----------------------------
-- Table structure for log_operate
-- ----------------------------
DROP TABLE IF EXISTS `log_operate`;
CREATE TABLE `log_operate`  (
                                `ID` bigint(20) NOT NULL COMMENT '主键',
                                `TENANT_ID` bigint(20) NOT NULL COMMENT '租户ID',
                                `RESULT` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '操作结果，0-成功，1-失败，2-获取操作结果失败',
                                `BUSI_TYPE_ID` bigint(20) NULL DEFAULT NULL COMMENT '业务类型ID',
                                `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '保存时间',
                                `OPER_IP` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求IP',
                                `OPER_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作人',
                                `MODULE_NAME` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块名',
                                `OPER_CONTENT` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作内容',
                                `HMAC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性保护字段',
                                `AUDIT_STATUS` int(10) NULL DEFAULT NULL COMMENT '审计状态;0-审计，1-未审计',
                                `ERROR_MSG` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误信息',
                                `LOG_TYPE` int(10) NULL DEFAULT NULL COMMENT '日志类型，1-平台侧日志，2-服务侧日志',
                                `EXTEND1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备用1',
                                `EXTEND2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备用2',
                                PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for monitor_connection
-- ----------------------------
DROP TABLE IF EXISTS `monitor_connection`;
CREATE TABLE `monitor_connection`(
                                     `ID`                bigint(20) NOT NULL COMMENT 'id',
                                     `SOURCE_TYPE`       int(1) NOT NULL COMMENT '来源 1 服务 2 设备',
                                     `DEVICE_TYPE`       int(1) NOT NULL COMMENT '设备类型',
                                     `CONNECTION_NAME`   varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '连接名称',
                                     `CONNECTION_STATUS` int(1) NOT NULL COMMENT '连接状态 1已连接 2未连接',
                                     `TENANT_CODE`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户标识',
                                     `IP`                varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '设备IP',
                                     `PORT`              int(11) NOT NULL COMMENT '设备端口',
                                     `REMARK`            varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                     `CREATE_BY`         bigint(20) NULL DEFAULT NULL COMMENT '创建人',
                                     `CREATE_TIME`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
                                     `UPDATE_BY`         bigint(20) NULL DEFAULT NULL COMMENT '更新人',
                                     `UPDATE_TIME`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '监控连接信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for statistic_incre_cal
-- ----------------------------
DROP TABLE IF EXISTS `statistic_incre_cal`;
CREATE TABLE `statistic_incre_cal`  (
                                        `ID` bigint(20) UNSIGNED NOT NULL COMMENT 'id',
                                        `TENANT_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户标识',
                                        `APP_CODE` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '应用标识',
                                        `SERVICE_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务简称',
                                        `SERVICE_GROUP_ID` bigint(20) DEFAULT NULL COMMENT '服务组id',
                                        `SERVICE_ID` bigint(20) NULL DEFAULT NULL COMMENT '服务id',
                                        `STATISTIC_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '统计指标名称',
                                        `TOTAL_STATISTIC` bigint(20) NOT NULL COMMENT '总量统计',
                                        `SUCCESS_STATISTIC` bigint(20) NULL DEFAULT NULL COMMENT '成功量统计',
                                        `ERROR_STATISTIC` bigint(20) NULL DEFAULT NULL COMMENT '失败量统计',
                                        `STATISTIC_YEAR` int(10) NOT NULL COMMENT '年',
                                        `STATISTIC_MONTH` int(10) NOT NULL COMMENT '月',
                                        `STATISTIC_DAY` int(10) NOT NULL COMMENT '日',
                                        `STATISTIC_HOUR` int(10) NOT NULL COMMENT '时',
                                        `STATISTIC_MINUTE` int(10) NULL DEFAULT NULL COMMENT '分',
                                        `STATISTIC_SECOND` int(10) NULL DEFAULT NULL COMMENT '秒',
                                        `HMAC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性校验值',
                                        `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
                                        PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '增量类统计采集计算表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for statistic_incre_record
-- ----------------------------
DROP TABLE IF EXISTS `statistic_incre_record`;
CREATE TABLE `statistic_incre_record`  (
                                           `ID` bigint(20) NOT NULL COMMENT 'id',
                                           `TENANT_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户标识',
                                           `APP_CODE` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '应用标识',
                                           `SERVICE_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务简称',
                                           `SERVICE_GROUP_ID` bigint(20) DEFAULT NULL COMMENT '服务组id',
                                           `SERVICE_ID` bigint(20) NULL DEFAULT NULL COMMENT '服务id',
                                           `STATISTIC_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '统计指标名称',
                                           `TOTAL_STATISTIC` bigint(20) NOT NULL COMMENT '总量统计',
                                           `SUCCESS_STATISTIC` bigint(20) NULL DEFAULT NULL COMMENT '成功量统计',
                                           `ERROR_STATISTIC` bigint(20) NULL DEFAULT NULL COMMENT '失败量统计',
                                           `STATISTIC_YEAR` int(10) NOT NULL COMMENT '年',
                                           `STATISTIC_MONTH` int(10) NOT NULL COMMENT '月',
                                           `STATISTIC_DAY` int(10) NOT NULL COMMENT '日',
                                           `STATISTIC_HOUR` int(10) NOT NULL COMMENT '时',
                                           `STATISTIC_MINUTE` int(10) NULL DEFAULT NULL COMMENT '分',
                                           `STATISTIC_SECOND` int(10) NULL DEFAULT NULL COMMENT '秒',
                                           `HMAC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性校验值',
                                           `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
                                           `TIME_STAMP` bigint(20) DEFAULT NULL COMMENT '插入时间戳',
                                           PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '增量类统计采集记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for statistic_incre_total_cal
-- ----------------------------
DROP TABLE IF EXISTS `statistic_incre_total_cal`;
CREATE TABLE `statistic_incre_total_cal`  (
                                              `ID` bigint(20) NOT NULL COMMENT 'id',
                                              `TENANT_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户标识',
                                              `APP_CODE` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '应用标识',
                                              `SERVICE_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务简称',
                                              `SERVICE_GROUP_ID` bigint(20) DEFAULT NULL COMMENT '服务组id',
                                              `STATISTIC_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '统计指标名称',
                                              `TOTAL_STATISTIC` bigint(20) NOT NULL COMMENT '总量统计',
                                              `SUCCESS_STATISTIC` bigint(20) NULL DEFAULT NULL COMMENT '成功量统计',
                                              `ERROR_STATISTIC` bigint(20) NULL DEFAULT NULL COMMENT '失败量统计',
                                              `STATISTIC_YEAR` int(10) NOT NULL COMMENT '年',
                                              `STATISTIC_MONTH` int(10) NOT NULL COMMENT '月',
                                              `STATISTIC_DAY` int(10) NOT NULL COMMENT '日',
                                              `STATISTIC_HOUR` int(10) NOT NULL COMMENT '时',
                                              `STATISTIC_MINUTE` int(10) NULL DEFAULT NULL COMMENT '分',
                                              `STATISTIC_SECOND` int(10) NULL DEFAULT NULL COMMENT '秒',
                                              `HMAC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性校验值',
                                              `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
                                              PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '增量类统计采集总量计算表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for statistic_unincre_cal
-- ----------------------------
DROP TABLE IF EXISTS `statistic_unincre_cal`;
CREATE TABLE `statistic_unincre_cal`  (
                                          `ID` bigint(20) NOT NULL COMMENT 'id',
                                          `TENANT_CODE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户标识',
                                          `APP_CODE` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '应用标识',
                                          `SERVICE_TYPE_ID` bigint(20) NOT NULL COMMENT '服务类型',
                                          `SERVICE_GROUP_ID` bigint(20) DEFAULT NULL COMMENT '服务组id',
                                          `SERVICE_ID` bigint(20) NULL DEFAULT NULL COMMENT '服务id',
                                          `STATISTIC_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '统计指标名称',
                                          `SUB_STATISTIC_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '子统计指标名称',
                                          `SUB_STATISTIC_VALUE` bigint(20) NOT NULL COMMENT '子统计指标值',
                                          `STATISTIC_YEAR` int(10) NULL DEFAULT NULL COMMENT '年',
                                          `STATISTIC_MONTH` int(10) NULL DEFAULT NULL COMMENT '月',
                                          `STATISTIC_DAY` int(10) NULL DEFAULT NULL COMMENT '日',
                                          `STATISTIC_HOUR` int(10) NULL DEFAULT NULL COMMENT '时',
                                          `STATISTIC_MINUTE` int(10) NULL DEFAULT NULL COMMENT '分',
                                          `STATISTIC_SECOND` int(10) NULL DEFAULT NULL COMMENT '秒',
                                          `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
                                          `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
                                          PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '非增量类统计采集计算表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
                            `JOB_ID`          bigint                                                 NOT NULL COMMENT '任务号',
                            `JOB_NAME`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '任务名称',
                            `JOB_GROUP`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '任务组名',
                            `SERVER_ID`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务模块',
                            `METHOD_URL`      varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调用接口',
                            `JSON_PARAM`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'json格式参数',
                            `CRON_EXPRESSION` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'CRON执行表达式',
                            `MISFIRE_POLICY`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '3' COMMENT '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
                            `CONCURRENT`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）',
                            `JOB_STATUS`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
                            `CREATED_BY`      bigint NULL DEFAULT NULL COMMENT '创建人',
                            `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
                            `UPDATED_BY`      bigint NULL DEFAULT NULL COMMENT '更新人',
                            `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
                            `REMARK`          varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`JOB_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '定时任务表' ROW_FORMAT = Dynamic;

INSERT INTO `sys_job` VALUES (1, 'clear_incre_record', 'statistic', 'ccsp-atom-static', NULL, 'statisticHandlerServiceImpl.increRecordClear()', '0 20 0 * * ?', '3', '0', '0', NULL, NULL, NULL, NULL, '每天00:20清理记录表');
INSERT INTO `sys_job` VALUES (2, 'clear_incre_cal', 'statistic', 'ccsp-atom-static', NULL, 'statisticHandlerServiceImpl.increCalClear()', '0 20 1 * * ?', '3', '0', '0', NULL, NULL, NULL, NULL, '每天01:20清理计算表');
INSERT INTO `sys_job` VALUES (3, 'clear_unincre_cal', 'statistic', 'ccsp-atom-static', NULL, 'statisticHandlerServiceImpl.unIncreCalClear()', '0 3 * * * ?', '3', '0', '0', NULL, NULL, NULL, NULL, '每小时第三分钟清理非增量表');
INSERT INTO `sys_job` VALUES (4, 'compute_incre_cal', 'statistic', 'ccsp-atom-static', NULL, 'statisticHandlerServiceImpl.increCalCompute()', '0 5 * * * ?', '3', '0', '0', NULL, NULL, NULL, NULL, '每小时第五分钟统计记录表到计算表');
INSERT INTO `sys_job` VALUES (5, 'compute_incre_total_cal', 'statistic', 'ccsp-atom-static', NULL, 'statisticHandlerServiceImpl.increTotalCompute()', '0 * * * * ?', '3', '0', '0', NULL, NULL, NULL, NULL, '每分钟统计记录表到总量表');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
                                `JOB_LOG_ID`     bigint                                                NOT NULL COMMENT '任务日志ID',
                                `JOB_ID`         bigint                                                NOT NULL COMMENT '任务ID',
                                `JOB_MESSAGE`    varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志信息',
                                `STATUS`         varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '执行状态（0失败 1正常）',
                                `EXCEPTION_INFO` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '异常信息',
                                `CREATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建时间',
                                `TRIGGER_TIME`   bigint NULL DEFAULT NULL COMMENT '触发时间',
                                PRIMARY KEY (`JOB_LOG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '定时任务执行日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS `sys_task`;
CREATE TABLE `sys_task`  (
                             `TASK_ID`     bigint                                                 NOT NULL COMMENT '任务号',
                             `TASK_NAME`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '任务名称',
                             `TASK_GROUP`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '任务组名;执行任务串行',
                             `SERVER_ID`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务模块',
                             `METHOD_URL`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调用接口',
                             `JSON_PARAM`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'json格式参数',
                             `TASK_STATUS` int                                                    NOT NULL DEFAULT 0 COMMENT '状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时)',
                             `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
                             `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
                             `REMARK`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                             `TIMEOUT`     int NULL DEFAULT NULL COMMENT '超时时间;单位秒',
                             `START_TIME`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开始时间',
                             `END_TIME`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结束时间',
                             `POLICY`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   NOT NULL DEFAULT '0' COMMENT '是否允许重复执行;0-不允许，1允许',
                             PRIMARY KEY (`TASK_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '异步任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_task_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_task_log`;
CREATE TABLE `sys_task_log`  (
                                 `TASK_LOG_ID`    bigint                                               NOT NULL COMMENT '任务日志ID',
                                 `TASK_ID`        bigint                                               NOT NULL COMMENT '任务ID',
                                 `TASK_MESSAGE`   varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志信息',
                                 `STATUS`         varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '执行状态（0失败 1正常）',
                                 `EXCEPTION_INFO` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '异常信息',
                                 `CREATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间;单位毫秒',
                                 `TRIGGER_TIME`   bigint NULL DEFAULT NULL COMMENT '触发时间;任务服务上送',
                                 PRIMARY KEY (`TASK_LOG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '异步任务执行日志表' ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `screen_template_rel`;
CREATE TABLE `screen_template_rel`  (
        `ID` bigint(20) NOT NULL COMMENT '主键',
        `TEMPLATE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '大屏模板名称',
        `TEMPLATE_CODE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '大屏模板编码',
        `INDEX_ID` bigint(20) NOT NULL COMMENT '模板ID',
        `INDEX_TYPE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '指标类型;pki、vpn、secstorage、secdb、svs、tsc、sms、tsa、kms、secauth',
        `INVALID_FLAG` int(11) NOT NULL COMMENT '是否作废;默认为0',
        `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
        `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
        `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
        `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
        `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
        PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '大屏模板表' ROW_FORMAT = Dynamic;

INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(1, '默认模板', 'default', 1, 'kms', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(2, '默认模板', 'default', 2, 'secauth', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(3, '默认模板', 'default', 3, 'pki', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(4, '默认模板', 'default', 4, 'secdb', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(5, '默认模板', 'default', 5, 'secstorage', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(6, '默认模板', 'default', 6, 'vpn', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(7, '默认模板', 'default', 7, 'svs', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(8, '默认模板', 'default', 8, 'tsc', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(9, '默认模板', 'default', 9, 'sms', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(10, '默认模板', 'default', 10, 'tsa', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(11, '默认模板', 'default', 11, 'center', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_TEMPLATE_REL (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES(12, '默认模板', 'default', 12, 'center-tenant', 0, NULL, NULL, NULL, NULL, NULL);


DROP TABLE IF EXISTS `screen_index`;
CREATE TABLE `screen_index`  (
     `ID` bigint(20) NOT NULL COMMENT '主键',
     `NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '指标名称',
     `TEMPLATE` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模板',
     `INVALID_FLAG` int(11) NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
     `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
     `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
     `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
     `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
     `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
     PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '大屏模板指标表' ROW_FORMAT = Dynamic;

INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (1, 'kms-密钥管理模板',
        '{"type":"pie1","title":"密钥管理","updateFrequency":10,"elements":[[{"chartOption":{"type":"pie"},"unit":"个","name":"预激活","key":"yujihuo""title":"密钥总数"},{"chartOption":{"type":"pie"},"unit":"个","name":"激活","key":"jihuo""title":"密钥总数"},{"chartOption":{"type":"pie"},"unit":"个","name":"注销","key":"zhuxiao""title":"密钥总数"},{"chartOption":{"type":"pie"},"unit":"个","name":"销毁","key":"xiaohui""title":"密钥总数"}]],"valueList":[[{"key":"yujihuo","value":"${kmsKeyStateStatistic_1}"},{"key":"jihuo","value":"${kmsKeyStateStatistic_2}"},{"key":"zhuxiao","value":"${kmsKeyStateStatistic_3}"},{"key":"xiaohui","value":"${kmsKeyStateStatistic_5}"}]]}',
        0, NULL, NULL, NULL, NULL, 'kmsBusiStatistic_totalError 密钥调用错误次数');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (2, 'secauth-动态令牌模板',
        '{"type":"pie2","title":"动态令牌","updateFrequency":10,"elements":[[{"chartOption":{"type":"pie"},"unit":"次","name":"成功次数","key":"success","title":"总认证次数"},{"chartOption":{"type":"pie"},"unit":"次","name":"失败次数","key":"fail","title":"总认证次数"}]],"valueList":[[{"key":"success","value":"${secauthBusiStatistic_totalNumSuccess}"},{"key":"fail","value":"${secauthBusiStatistic_totalError}"}]]}',
        0, NULL, NULL, NULL, NULL, 'secauth');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (3, 'pki-数据加解密模板',
        '{"type":"iconGroup","title":"数据加解密","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"密钥数量","value":"${encKeyNumStatistic_keyNums}","colorType":"blue","key":"keyNum","unit":"个"}]},{"elementType":"hexagon1","layout":"row","elementConfig":[{"name":"总调用次数","value":"${encBusiStatistic_totals}","icon":"lock","colorType":"blue","key":"busiNum","unit":"次"}]}]}',
        0, NULL, NULL, NULL, NULL, 'pki');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (4, 'secdb-数据库加密模板',
        '{"type":"iconGroup","title":"数据库加密","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"密钥数量","value":"${secDbKeyNumStatistic_keyNums}","colorType":"blue","key":"keyNum","unit":"个"}]},{"elementType":"hexagon1","layout":"row","elementConfig":[{"name":"加密数据库","value":"${dbStatistic_dbNum}","icon":"database","colorType":"blue","key":"dbNum","unit":"个"},{"colorType":"gold","name":"加密表","value":"${dbStatistic_dbTableNum}","icon":"search","key":"dbTableNum","unit":"个"}]}]}',
        0, NULL, NULL, NULL, NULL, 'secdb');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (5, 'secstorage-文件加密模板',
        '{"type":"iconGroup","title":"文件加密","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"NAS服务器","value":"${serverStatistic_nasServerNum}","colorType":"blue","key":"signAppCertNum","unit":"个"}]},{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"文件服务器","value":"${serverStatistic_fileServerNum}","colorType":"purple","key":"signUserCertStatistic","unit":"个"}]}]}',
        0, NULL, NULL, NULL, NULL, 'secstorage');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (6, 'vpn-VPN服务模板',
        '{"type":"iconGroup","title":"VPN服务","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"并发连接数","value":"${vpnConcurrentConnectNumStatistic_vpnConcurrentConnectNumStatistic}","colorType":"blue","key":"ConcurrentConnectNum","unit":"个"},{"name":"新建连接数","value":"${vpnNewConnectNumStatistic_vpnNewConnectNumStatistic}","colorType":"purple","key":"NewConnectNum","unit":"个"}]},{"elementType":"noIcon","layout":"column","elementConfig":[{"name":"吞吐量-收","value":"${vpnThroughputRNumStatistic_vpnThroughputRNumStatistic}","colorType":"blue","key":"RNum","unit":"Kbps"},{"name":"吞吐量-发","value":"${vpnThroughputSNumStatistic_vpnThroughputSNumStatistic}","colorType":"gold","key":"SNum","unit":"Kbps"}]}]}',
        0, NULL, NULL, NULL, NULL, 'vpn');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (7, 'svs-签名验签模板',
        '{"type":"iconGroup","title":"签名验签","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"应用证书数量","value":"${signAppCertStatistic_certNumbers}","colorType":"blue","key":"signAppCertNum","unit":"个"}]},{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"用户证书数量","value":"${signUserCertStatistic_certNumbers}","colorType":"purple","key":"signUserCertStatistic","unit":"个"}]}]}',
        0, NULL, NULL, NULL, NULL, 'svs');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (8, 'tsc-电子签章模板',
        '{"type":"iconGroup","title":"电子签章","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"签章数量","value":"${sealNumStatistic_sealNum}","colorType":"blue","key":"sealNum","unit":"个"}]},{"elementType":"hexagon1","layout":"row","elementConfig":[{"name":"总签署次数","value":"${sealSignBusiStatistic_totals}","icon":"draft","colorType":"blue","key":"sealSignNum","unit":"次"},{"name":"总验签次数","value":"${sealVerifyBusiStatistic_totals}","icon":"search","colorType":"gold","key":"sealVerifyNum","unit":"次"}]}]}',
        0, NULL, NULL, NULL, NULL, 'tsc');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (9, 'sms-协同签名模板',
        '{"type":"iconGroup","title":"协同签名","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"用户数量","value":"${userNumStatistic_userNum}","colorType":"blue","key":"userNum","unit":"个"}]},{"elementType":"hexagon1","layout":"row","elementConfig":[{"name":"总调用次数","value":"${splitBusiStatistic_totals}","icon":"terminal","colorType":"blue","key":"splitNum","unit":"次"}]}]}',
        0, NULL, NULL, NULL, NULL, 'sms');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (10, 'tsa-时间戳模板',
        '{"type":"iconGroup","title":"时间戳","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"总调用次数","value":"${timeBusiStatistic_totals}","colorType":"blue","key":"timeBusiStatistic","unit":"次"}]}]}',
        0, NULL, NULL, NULL, NULL, 'tsa');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (11, 'center-全租户模板',
        '{"type":"centerPanel","title":"密码服务管理平台","updateFrequency":5,"elements":[{"name":"设备概况","value":"${device_sum}","icon":"device","key":"device_sum","unit":"台","children":[{"name":"已使用","value":"${device_used}","icon":"device","unit":"台"},{"name":"空闲","value":"${device_unused}","icon":"device","unit":"台"},{"name":"异常","value":"${device_error}","icon":"device","unit":"台"}]},{"name":"服务概况","value":"${service_sum}","icon":"service","key":"service_sum","unit":"个","children":[{"name":"运行中","value":"${service_run}","icon":"device","unit":"个"},{"name":"初始化中","value":"${service_start}","icon":"device","unit":"个"},{"name":"异常","value":"${service_error}","icon":"device","unit":"个"}]},{"name":"许可证数量","value":"${license_num}","icon":"license","key":"license_num","unit":"个"}],"enableAlarm":true}',
        0, NULL, NULL, NULL, NULL, 'center');
INSERT INTO SCREEN_INDEX (ID, NAME, TEMPLATE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK)
VALUES (12, 'center-tenant-单个租户模板',
        '{"type":"centerPanel","title":"密码服务管理平台","updateFrequency":5,"elements":[{"name":"设备概况","value":"${device_sum}","icon":"device","key":"device_sum","unit":"台","children":[{"name":"已使用","value":"${device_used}","icon":"device","unit":"台"},{"name":"空闲","value":"${device_unused}","icon":"device","unit":"台"},{"name":"异常","value":"${device_error}","icon":"device","unit":"台"}]},{"name":"服务概况","value":"${service_sum}","icon":"service","key":"service_sum","unit":"个","children":[{"name":"运行中","value":"${service_run}","icon":"device","unit":"个"},{"name":"初始化中","value":"${service_start}","icon":"device","unit":"个"},{"name":"异常","value":"${service_error}","icon":"device","unit":"个"}]}],"enableAlarm":true}',
        0, NULL, NULL, NULL, NULL, 'center-tenant');

-- ----------------------------
-- Table structure for screen_statistic
-- ----------------------------
DROP TABLE IF EXISTS `screen_statistic`;
CREATE TABLE `screen_statistic`
(
    `ID`                    bigint(20) NOT NULL COMMENT '主键',
    `INDEX_TYPE`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '指标类型;1增量 2非增量数量计算类 3非增量非计算类',
    `STATISTIC_TYPE`        int(11) NULL DEFAULT NULL COMMENT '统计类型',
         `STATISTIC_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '统计名称',
         `STATISTIC_FIRST` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '一级指标',
         `STATISTIC_SECOND` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '二级指标',
         `INVALID_FLAG` int(11) NULL DEFAULT 0 COMMENT '是否作废;是否作废;默认为0',
         `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
         `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
         `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
         `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
         `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
         PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '大屏统计指标字典表' ROW_FORMAT = Dynamic;

INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(1, 'kms', 1, '密钥调用总次数', 'kmsBusiStatistic', 'totals', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(2, 'kms', 1, '密钥调用成功次数', 'kmsBusiStatistic', 'totalNumSuccess', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(3, 'kms', 1, '密钥调用错误次数', 'kmsBusiStatistic', 'totalError', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(4, 'kms', 2, '密钥总数统计', 'kmsKeyNumStatistic', 'keyNums', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(5, 'kms', 2, '密钥状态分布-预创建', 'kmsKeyStateStatistic', '1', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(6, 'kms', 2, '密钥状态分布-创建', 'kmsKeyStateStatistic', '2', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(7, 'kms', 2, '密钥状态分布-注销', 'kmsKeyStateStatistic', '3', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(8, 'kms', 2, '密钥状态分布-销毁', 'kmsKeyStateStatistic', '5', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(11, 'secauth', 1, '动态令牌总次数', 'secauthBusiStatistic', 'totals', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(12, 'secauth', 1, '动态令牌成功总次数', 'secauthBusiStatistic', 'totalNumSuccess', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(13, 'secauth', 1, '动态令牌失败总次数', 'secauthBusiStatistic', 'totalError', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(21, 'pki', 1, '数据加解密总次数', 'encBusiStatistic', 'totals', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(22, 'pki', 1, '数据加解密成功总次数', 'encBusiStatistic', 'totalNumSuccess', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(23, 'pki', 1, '数据加解密错误总次数', 'encBusiStatistic', 'totalError', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(24, 'pki', 2, '数据加解密密钥总数', 'encKeyNumStatistic', 'keyNums', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(31, 'svs', 1, '签名验签调用总次数', 'signBusiStatistic', 'totals', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(32, 'svs', 1, '签名验签调用成功总次数', 'signBusiStatistic', 'totalNumSuccess', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(33, 'svs', 1, '签名验签调用失败总次数', 'signBusiStatistic', 'totalError', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(34, 'svs', 2, '签名验签应用证书数量', 'signAppCertStatistic', 'certNumbers', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(35, 'svs', 2, '签名验签用户证书数量', 'signUserCertStatistic', 'certNumbers', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(41, 'tsa', 1, '时间戳服务调用总次数', 'timeBusiStatistic', 'totals', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(42, 'tsa', 1, '时间戳服务调用成功总次数', 'timeBusiStatistic', 'totalNumSuccess', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(43, 'tsa', 1, '时间戳服务调用失败总次数', 'timeBusiStatistic', 'totalError', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(51, 'sms', 1, '协同签名总签名总次数', 'splitBusiStatistic', 'totals', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(52, 'sms', 1, '协同签名总签名成功总次数', 'splitBusiStatistic', 'totalNumSuccess', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(53, 'sms', 1, '协同签名总签名失败总次数', 'splitBusiStatistic', 'totalError', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(54, 'sms', 2, '协同签名用户数', 'userNumStatistic', 'userNum', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(61, 'secdb', 2, '数据库加密加密数据库个数', 'dbStatistic', 'dbNum', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(62, 'secdb', 2, '数据库加密加密表个数', 'dbStatistic', 'dbTableNum', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(63, 'secdb', 2, '数据库加密加密字段个数', 'dbStatistic', 'dbColumnNum', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(64, 'secdb', 2, '数据库加密加密钥总数', 'secDbKeyNumStatistic', 'keyNums', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(71, 'secstorage', 2, '文件加密服务器个数', 'serverStatistic', 'fileServerNum', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(72, 'secstorage', 2, 'NAS加密服务器个数', 'serverStatistic', 'nasServerNum', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(81, 'vpn', 3, 'vpn 新建连接数', 'vpnNewConnectNumStatistic', 'vpnNewConnectNumStatistic', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(82, 'vpn', 3, '吞吐量(收)', 'vpnThroughputRNumStatistic', 'vpnThroughputRNumStatistic', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(83, 'vpn', 3, '吞吐量(发)', 'vpnThroughputSNumStatistic', 'vpnThroughputSNumStatistic', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(84, 'vpn', 3, '并发连接数', 'vpnConcurrentConnectNumStatistic', 'vpnConcurrentConnectNumStatistic', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(91, 'tsc', 1, '电子签章签署次数', 'sealSignBusiStatistic', 'totals', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(92, 'tsc', 1, '电子签章签署成功次数', 'sealSignBusiStatistic', 'totalNumSuccess', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(93, 'tsc', 1, '电子签章签署失败次数', 'sealSignBusiStatistic', 'totalError', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(94, 'tsc', 1, '电子签章验签次数', 'sealVerifyBusiStatistic', 'totals', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(95, 'tsc', 1, '电子签章验签次数', 'sealVerifyBusiStatistic', 'totalNumSuccess', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(96, 'tsc', 1, '电子签章验签失败次数', 'sealVerifyBusiStatistic', 'totalError', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(97, 'tsc', 2, '电子签章印章个数', 'sealNumStatistic', 'sealNum', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(98, 'tsc', 2, '电子签章停用状态总数', 'sealStateStatistic', '1', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(99, 'tsc', 2, '电子签章启用状态总数', 'sealStateStatistic', '2', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(100, 'tsc', 2, '电子签章删除状态总数', 'sealStateStatistic', '3', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(101, 'center', 1, '设备运行状态', 'device_used', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(102, 'center', 1, '设备异常状态', 'device_error', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(103, 'center', 1, '设备空闲状态', 'device_unused', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(104, 'center', 1, '服务运行状态', 'service_run', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(105, 'center', 1, '服务异常状态', 'service_error', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(106, 'center', 1, '服务启动状态', 'service_start', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(107, 'center', 1, '服务停止状态', 'service_stop', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(108, 'center', 1, '租户总数', 'tenant_num', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(109, 'center', 1, '许可总数', 'license_num', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(110, 'center', 1, '设备总数量', 'device_sum', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(111, 'center', 1, '服务总数量', 'service_sum', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(121, 'center-tenant', 1, '设备运行状态', 'device_used', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(122, 'center-tenant', 1, '设备异常状态', 'device_error', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(123, 'center-tenant', 1, '设备空闲状态', 'device_unused', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(124, 'center-tenant', 1, '服务运行状态', 'service_run', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(125, 'center-tenant', 1, '服务异常状态', 'service_error', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(126, 'center-tenant', 1, '服务启动状态', 'service_start', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(127, 'center-tenant', 1, '服务停止状态', 'service_stop', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(128, 'center-tenant', 1, '设备总数量', 'device_sum', '', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SCREEN_STATISTIC (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES(129, 'center-tenant', 1, '服务总数量', 'service_sum', '', 0, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for screen_template_url_rel
-- ----------------------------
DROP TABLE IF EXISTS `screen_template_url_rel`;
CREATE TABLE `screen_template_url_rel`  (
                                            `ID` bigint(20) NOT NULL COMMENT '主键',
                                            `TEMPLATE_CODE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模板编码',
                                            `SCREEN_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '大屏名称',
                                            `SCREEN_URL` varchar(600) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '大屏URL地址',
                                            `VALID_FLAG` int(11) NOT NULL COMMENT '是否有效 0：有效，1：无效',
                                            `EXTEND` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '扩展字段',
                                            `REMARK` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                            `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
                                            `CREATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
                                            `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
                                            `UPDATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'SCREEN_TEMPLATE_URL_REL' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of screen_template_url_rel
-- ----------------------------
INSERT INTO `screen_template_url_rel` VALUES (1, 'default', '大屏', '/swmonitor/front/screen?viewName=bigpanel-ccsp001&isShowScreen=1&target=_blank', 0, NULL, NULL, NULL, NULL, NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;
