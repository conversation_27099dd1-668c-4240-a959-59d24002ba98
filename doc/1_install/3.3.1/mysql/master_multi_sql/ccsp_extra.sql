/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 50742
 Source Host           : ************:3306
 Source Schema         : ccsp_ds_extra_331

 Target Server Type    : MySQL
 Target Server Version : 50742
 File Encoding         : 65001

 Date: 25/04/2024 18:59:27
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for dic_work_order
-- ----------------------------
DROP TABLE IF EXISTS `dic_work_order`;
CREATE TABLE `dic_work_order`  (
  `ID` bigint(20) NOT NULL COMMENT '主键',
  `TYPE` int(11) NOT NULL COMMENT '字典类型;1：工单类型',
  `DIC_KEY` int(11) NOT NULL COMMENT '字典键',
  `DIC_VALUE` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典值',
  `IS_AVAILABLE` int(11) NOT NULL COMMENT '是否启用 1启用 0停用',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工单相关字典表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dic_work_order
-- ----------------------------
INSERT INTO `dic_work_order` VALUES (1, 1, 1, '申请密码服务工单', 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_work_order` VALUES (2, 1, 2, '申请密码支持服务工单', 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_work_order` VALUES (3, 1, 3, '申请密码组合服务工单', 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_work_order` VALUES (4, 1, 4, '问题工单', 1, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for encrypt_judge_key
-- ----------------------------
DROP TABLE IF EXISTS `encrypt_judge_key`;
CREATE TABLE `encrypt_judge_key`  (
  `ID` bigint(20) NOT NULL COMMENT '主键',
  `TYPE` int(11) NOT NULL COMMENT '类型;1内部密钥 or 2外部密钥',
  `REGION_ID_SENDER` bigint(20) NULL DEFAULT NULL COMMENT '发送方区域ID;发送方区域ID',
  `TENANT_ID_SENDER` bigint(20) NULL DEFAULT NULL COMMENT '发送方租户ID;发送方租户ID',
  `APP_ID_SENDER` bigint(20) NULL DEFAULT NULL COMMENT '发送方业务账号ID;发送方业务账号ID',
  `REGION_ID_RECEIVER` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接收方区域ID;接收方区域ID',
  `TENANT_ID_RECEIVER` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接收方租户ID;接收方租户ID',
  `APP_ID_RECEIVER` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接收方业务账号ID;接收方业务账号ID',
  `KEY_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密钥名称;密钥名称',
  `KEY_MATERIAL` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密钥材料;KMS返回，明文密钥材料的Base64编码',
  `KEY_LENGTH` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密钥长度;KMS返回，密钥长度',
  `ALG` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密钥算法;KMS返回，密钥算法',
  `INVALID_FLAG` int(1) NOT NULL COMMENT '是否作废;0未作废  1已作废',
  `REMARK` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '加密判定密钥表（520）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of encrypt_judge_key
-- ----------------------------

-- ----------------------------
-- Table structure for license_record
-- ----------------------------
DROP TABLE IF EXISTS `license_record`;
CREATE TABLE `license_record`  (
  `ID` bigint(20) NOT NULL COMMENT '主键',
  `PRODUCT_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '产品名称',
  `CUSTOMER_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '客户名称',
  `LICENSE_TYPE` bigint(20) NOT NULL COMMENT '许可类型;1永久授权2按年授权3按月授权',
  `APPLY_LICENSE_INFO` varchar(3000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '申请许可内容',
  `AUTH_LICENSE_INFO` varchar(3000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '授权许可内容',
  `ISSUE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '许可签发时间',
  `HMAC` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '完整性校验',
  `SERIAL_ID` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '序列号',
  `REMARK` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '许可授权记录表（520）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of license_record
-- ----------------------------

-- ----------------------------
-- Table structure for product_attachment
-- ----------------------------
DROP TABLE IF EXISTS `product_attachment`;
CREATE TABLE `product_attachment`  (
  `ID` bigint(20) NOT NULL COMMENT 'ID',
  `PRODUCT_ID` bigint(20) NULL DEFAULT NULL COMMENT '产品ID',
  `STORAGE_PATH` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '存储路径',
  `FILE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件名称',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '密码产品附件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of product_attachment
-- ----------------------------
INSERT INTO `product_attachment` VALUES (6264179982649133065, 6264186614984607747, '/opt/ccsp/filetarget/Product/b6264179980367431685/6264179980367431685.png', '应用 - 签名验签.png', NULL, 6075546759500924936, '2023-12-01 17:31:51', 6075546759500924936, '2023-12-01 17:35:09');
INSERT INTO `product_attachment` VALUES (6264180458283206665, 6264186614984607747, '/opt/ccsp/filetarget/Product/b6264180457175910402/6264180457175910402.png', '透明简单.png', NULL, 6075546759500924936, '2023-12-01 17:32:05', 6075546759500924936, '2023-12-01 17:35:09');
INSERT INTO `product_attachment` VALUES (6264180572099840008, 6264186614984607747, '/opt/ccsp/filetarget/Product/b6264180571026098178/6264180571026098178.png', '密钥安全.png', NULL, 6075546759500924936, '2023-12-01 17:32:08', 6075546759500924936, '2023-12-01 17:35:09');
INSERT INTO `product_attachment` VALUES (6264180662931687430, 6264186614984607747, '/opt/ccsp/filetarget/Product/b6264180662227044356/6264180662227044356.png', '高可靠.png', NULL, 6075546759500924936, '2023-12-01 17:32:11', 6075546759500924936, '2023-12-01 17:35:09');
INSERT INTO `product_attachment` VALUES (6264186218740320257, 6264186614984607747, '/opt/ccsp/filetarget/Product/b6264186215116441602/6264186215116441602.png', '签名验签.png', NULL, 6075546759500924936, '2023-12-01 17:34:57', 6075546759500924936, '2023-12-01 17:35:09');
INSERT INTO `product_attachment` VALUES (6264204925671704579, 6264218972530018305, '/opt/ccsp/filetarget/Product/b6264204923826210823/6264204923826210823.png', '应用 - 时间戳.png', NULL, 6075546759500924936, '2023-12-01 17:44:14', 6075546759500924936, '2023-12-01 17:51:13');
INSERT INTO `product_attachment` VALUES (6264208335271757833, 6264218972530018305, '/opt/ccsp/filetarget/Product/b6264208333795362823/6264208333795362823.png', '安全密钥存储技术.png', NULL, 6075546759500924936, '2023-12-01 17:45:56', 6075546759500924936, '2023-12-01 17:51:13');
INSERT INTO `product_attachment` VALUES (6264208506198034439, 6264218972530018305, '/opt/ccsp/filetarget/Product/b6264208504822302725/6264208504822302725.png', '高强度的密码技术.png', NULL, 6075546759500924936, '2023-12-01 17:46:01', 6075546759500924936, '2023-12-01 17:51:13');
INSERT INTO `product_attachment` VALUES (6264208602935461891, 6264218972530018305, '/opt/ccsp/filetarget/Product/b6264208602130155528/6264208602130155528.png', '成熟规范的设计架构.png', NULL, 6075546759500924936, '2023-12-01 17:46:04', 6075546759500924936, '2023-12-01 17:51:13');
INSERT INTO `product_attachment` VALUES (6264208731180501000, 6264218972530018305, '/opt/ccsp/filetarget/Product/b6264208730442303490/6264208730442303490.png', '完善的自身保护措施.png', NULL, 6075546759500924936, '2023-12-01 17:46:08', 6075546759500924936, '2023-12-01 17:51:13');
INSERT INTO `product_attachment` VALUES (6264208908582782985, 6264218972530018305, '/opt/ccsp/filetarget/Product/b6264208907609704449/6264208907609704449.png', '强大的并行处理能力.png', NULL, 6075546759500924936, '2023-12-01 17:46:13', 6075546759500924936, '2023-12-01 17:51:13');
INSERT INTO `product_attachment` VALUES (6264209012903512068, 6264218972530018305, '/opt/ccsp/filetarget/Product/b6264209011829770243/6264209011829770243.png', '灵活性和可扩展性.png', NULL, 6075546759500924936, '2023-12-01 17:46:16', 6075546759500924936, '2023-12-01 17:51:13');
INSERT INTO `product_attachment` VALUES (6264213838433486848, 6264218972530018305, '/opt/ccsp/filetarget/Product/b6264213836017567747/6264213836017567747.png', '时间戳服务.png', NULL, 6075546759500924936, '2023-12-01 17:48:40', 6075546759500924936, '2023-12-01 17:51:13');
INSERT INTO `product_attachment` VALUES (6266334411125032966, 6266344853734033410, '/opt/ccsp/filetarget/Product/b6266334231071950851/6266334231071950851.png', '业务监控.png', NULL, 6075546759500924936, '2023-12-02 11:21:58', 6075546759500924936, '2023-12-02 11:27:09');
INSERT INTO `product_attachment` VALUES (6266334533363828736, 6266344853734033410, '/opt/ccsp/filetarget/Product/b6266334532222978056/6266334532222978056.png', '负载均衡.png', NULL, 6075546759500924936, '2023-12-02 11:22:01', 6075546759500924936, '2023-12-02 11:27:09');
INSERT INTO `product_attachment` VALUES (6266334626208942081, 6266344853734033410, '/opt/ccsp/filetarget/Product/b6266334625504299017/6266334625504299017.png', '系统安全.png', NULL, 6075546759500924936, '2023-12-02 11:22:04', 6075546759500924936, '2023-12-02 11:27:09');
INSERT INTO `product_attachment` VALUES (6266334754923743232, 6266344853734033410, '/opt/ccsp/filetarget/Product/b6266334754118436867/6266334754118436867.png', '密钥安全.png', NULL, 6075546759500924936, '2023-12-02 11:22:08', 6075546759500924936, '2023-12-02 11:27:09');
INSERT INTO `product_attachment` VALUES (6266334903301441537, 6266344853734033410, '/opt/ccsp/filetarget/Product/b6266334902596798469/6266334902596798469.png', '标准化.png', NULL, 6075546759500924936, '2023-12-02 11:22:12', 6075546759500924936, '2023-12-02 11:27:09');
INSERT INTO `product_attachment` VALUES (6266336344732731399, 6266344853734033410, '/opt/ccsp/filetarget/Product/b6266336342753019911/6266336342753019911.png', '数据加解密服务.png', NULL, 6075546759500924936, '2023-12-02 11:22:55', 6075546759500924936, '2023-12-02 11:27:09');
INSERT INTO `product_attachment` VALUES (6266344335653603332, 6266344853734033410, '/opt/ccsp/filetarget/Product/b6266344333170575366/6266344333170575366.png', '应用 - 数据加解密.png', NULL, 6075546759500924936, '2023-12-02 11:26:54', 6075546759500924936, '2023-12-02 11:27:09');
INSERT INTO `product_attachment` VALUES (6266369783368386568, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266369613012535296/6266369613012535296.png', '应用 - SSLVPN.png', NULL, 6075546759500924936, '2023-12-02 11:39:32', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266374623662311429, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266374619937769481/6266374619937769481.png', '更安全.png', NULL, 6075546759500924936, '2023-12-02 11:41:56', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266374720970164227, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266374720265521158/6266374720265521158.png', '更高效.png', NULL, 6075546759500924936, '2023-12-02 11:41:59', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266374848577669122, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266374847906580487/6266374847906580487.png', '安全合规.png', NULL, 6075546759500924936, '2023-12-02 11:42:03', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266374962461411337, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266374961387669506/6266374961387669506.png', 'WEB加速.png', NULL, 6075546759500924936, '2023-12-02 11:42:06', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266375060171917318, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266375059567937542/6266375059567937542.png', '负载均衡.png', NULL, 6075546759500924936, '2023-12-02 11:42:09', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266375256733779976, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266375256029136903/6266375256029136903.png', '零客户端.png', NULL, 6075546759500924936, '2023-12-02 11:42:15', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266376198975784962, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266376196425648134/6266376196425648134.png', '高可靠.png', NULL, 6075546759500924936, '2023-12-02 11:42:43', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266376326952388612, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266376326281299970/6266376326281299970.png', '双证书.png', NULL, 6075546759500924936, '2023-12-02 11:42:47', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266408227486435335, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266408225238288385/6266408225238288385.png', '高性能.png', NULL, 6075546759500924936, '2023-12-02 11:58:38', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266408873543469061, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6266408871597312008/6266408871597312008.jpg', 'SSL网关服务.jpg', NULL, 6075546759500924936, '2023-12-02 11:58:57', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `product_attachment` VALUES (6266444254947182593, 6266465492721600514, '/opt/ccsp/filetarget/Product/b6266444252296382471/6266444252296382471.png', '应用 - 协同签名.png', NULL, 6075546759500924936, '2023-12-02 12:16:31', 6075546759500924936, '2023-12-02 12:27:05');
INSERT INTO `product_attachment` VALUES (6266447025972840448, 6266465492721600514, '/opt/ccsp/filetarget/Product/b6266447024160901120/6266447024160901120.png', '签名密钥分割.png', NULL, 6075546759500924936, '2023-12-02 12:17:54', 6075546759500924936, '2023-12-02 12:27:05');
INSERT INTO `product_attachment` VALUES (6266447152036841477, 6266465492721600514, '/opt/ccsp/filetarget/Product/b6266447151332198407/6266447151332198407.png', '无硬件介质依赖.png', NULL, 6075546759500924936, '2023-12-02 12:17:58', 6075546759500924936, '2023-12-02 12:27:05');
INSERT INTO `product_attachment` VALUES (6266447445269022727, 6266465492721600514, '/opt/ccsp/filetarget/Product/b6266447443087984642/6266447443087984642.png', '密钥因子安全存储.png', NULL, 6075546759500924936, '2023-12-02 12:18:06', 6075546759500924936, '2023-12-02 12:27:05');
INSERT INTO `product_attachment` VALUES (6266448750234437633, 6266465492721600514, '/opt/ccsp/filetarget/Product/b6266448745805252613/6266448745805252613.png', '多方参与协作签名.png', NULL, 6075546759500924936, '2023-12-02 12:18:45', 6075546759500924936, '2023-12-02 12:27:05');
INSERT INTO `product_attachment` VALUES (6266448899954313222, 6266465492721600514, '/opt/ccsp/filetarget/Product/b6266448899316779013/6266448899316779013.png', '海量用户支撑.png', NULL, 6075546759500924936, '2023-12-02 12:18:50', 6075546759500924936, '2023-12-02 12:27:05');
INSERT INTO `product_attachment` VALUES (6266448993101416448, 6266465492721600514, '/opt/ccsp/filetarget/Product/b6266448992463882247/6266448992463882247.png', '移动安全加固.png', NULL, 6075546759500924936, '2023-12-02 12:18:53', 6075546759500924936, '2023-12-02 12:27:05');
INSERT INTO `product_attachment` VALUES (6266464753551017990, 6266465492721600514, '/opt/ccsp/filetarget/Product/b6266464750698891264/6266464750698891264.png', '协同签名服务.png', NULL, 6075546759500924936, '2023-12-02 12:26:42', 6075546759500924936, '2023-12-02 12:27:05');
INSERT INTO `product_attachment` VALUES (6266600436668172293, 6266607166747707401, '/opt/ccsp/filetarget/Product/b6266600266043885576/6266600266043885576.png', '应用 - 动态口令.png', NULL, 6075546759500924936, '2023-12-02 13:34:06', 6075546759500924936, '2023-12-02 13:37:27');
INSERT INTO `product_attachment` VALUES (6266602029966821377, 6266607166747707401, '/opt/ccsp/filetarget/Product/b6266602028356208644/6266602028356208644.png', '动态性.png', NULL, 6075546759500924936, '2023-12-02 13:34:53', 6075546759500924936, '2023-12-02 13:37:27');
INSERT INTO `product_attachment` VALUES (6266602161030432773, 6266607166747707401, '/opt/ccsp/filetarget/Product/b6266602157876316163/6266602157876316163.png', '随机性.png', NULL, 6075546759500924936, '2023-12-02 13:34:57', 6075546759500924936, '2023-12-02 13:37:27');
INSERT INTO `product_attachment` VALUES (6266602238876715011, 6266607166747707401, '/opt/ccsp/filetarget/Product/b6266602237131884545/6266602237131884545.png', '一次性.png', NULL, 6075546759500924936, '2023-12-02 13:35:00', 6075546759500924936, '2023-12-02 13:37:27');
INSERT INTO `product_attachment` VALUES (6266602327124871172, 6266607166747707401, '/opt/ccsp/filetarget/Product/b6266602326252455938/6266602326252455938.png', '抗攻击性.png', NULL, 6075546759500924936, '2023-12-02 13:35:02', 6075546759500924936, '2023-12-02 13:37:27');
INSERT INTO `product_attachment` VALUES (6266607093263501321, 6266607166747707401, '/opt/ccsp/filetarget/Product/b6266607090981799942/6266607090981799942.png', '动态令牌服务.png', NULL, 6075546759500924936, '2023-12-02 13:37:24', 6075546759500924936, '2023-12-02 13:37:27');
INSERT INTO `product_attachment` VALUES (6266634619071367171, 6266641228556273670, '/opt/ccsp/filetarget/Product/b6266634616554784769/6266634616554784769.png', '应用 - 文件加密.png', NULL, 6075546759500924936, '2023-12-02 13:51:05', 6075546759500924936, '2023-12-02 13:54:22');
INSERT INTO `product_attachment` VALUES (6266639825679026185, 6266641228556273670, '/opt/ccsp/filetarget/Product/b6266639823665760261/6266639823665760261.png', '文件加密服务.png', NULL, 6075546759500924936, '2023-12-02 13:53:40', 6075546759500924936, '2023-12-02 13:54:22');
INSERT INTO `product_attachment` VALUES (6266640962603845632, 6266641228556273670, '/opt/ccsp/filetarget/Product/b6266640961362331651/6266640961362331651.png', '安全的密钥管理体系.png', NULL, 6075546759500924936, '2023-12-02 13:54:14', 6075546759500924936, '2023-12-02 13:54:22');
INSERT INTO `product_attachment` VALUES (6266641051523090432, 6266641228556273670, '/opt/ccsp/filetarget/Product/b6266641049845368832/6266641049845368832.png', 'KMIP协议支持 1.png', NULL, 6075546759500924936, '2023-12-02 13:54:16', 6075546759500924936, '2023-12-02 13:54:22');
INSERT INTO `product_attachment` VALUES (6266641171446630407, 6266641228556273670, '/opt/ccsp/filetarget/Product/b6266641170607769605/6266641170607769605.png', '支持国产密码算法和通用密码算法.png', NULL, 6075546759500924936, '2023-12-02 13:54:20', 6075546759500924936, '2023-12-02 13:54:22');
INSERT INTO `product_attachment` VALUES (6266648856988616708, 6266660668450670596, '/opt/ccsp/filetarget/Product/b6266648854170044422/6266648854170044422.png', '应用 - 电子签章.png', NULL, 6075546759500924936, '2023-12-02 13:58:09', 6075546759500924936, '2023-12-04 14:24:13');
INSERT INTO `product_attachment` VALUES (6266659329125517315, 6266660668450670596, '/opt/ccsp/filetarget/Product/b6266659327045142530/6266659327045142530.png', '电子签章服务.png', NULL, 6075546759500924936, '2023-12-02 14:03:21', 6075546759500924936, '2023-12-04 14:24:13');
INSERT INTO `product_attachment` VALUES (6266660138894624769, 6266660668450670596, '/opt/ccsp/filetarget/Product/b6266660137518893060/6266660137518893060.png', '电子印章统一管理.png', NULL, 6075546759500924936, '2023-12-02 14:03:45', 6075546759500924936, '2023-12-04 14:24:13');
INSERT INTO `product_attachment` VALUES (6266660233283241985, 6266660668450670596, '/opt/ccsp/filetarget/Product/b6266660232578598917/6266660232578598917.png', '适应性强.png', NULL, 6075546759500924936, '2023-12-02 14:03:48', 6075546759500924936, '2023-12-04 14:24:13');
INSERT INTO `product_attachment` VALUES (6266660461654706178, 6266660668450670596, '/opt/ccsp/filetarget/Product/b6266660460983617538/6266660460983617538.png', '抗抵赖 抗伪造 防篡改.png', NULL, 6075546759500924936, '2023-12-02 14:03:55', 6075546759500924936, '2023-12-04 14:24:13');
INSERT INTO `product_attachment` VALUES (6266660592584099842, 6266660668450670596, '/opt/ccsp/filetarget/Product/b6266660591107704840/6266660591107704840.png', '灵活的部署方式.png', NULL, 6075546759500924936, '2023-12-02 14:03:59', 6075546759500924936, '2023-12-04 14:24:13');
INSERT INTO `product_attachment` VALUES (6266683409933404166, 6266684871933560837, '/opt/ccsp/filetarget/Product/b6266683397585373192/6266683397585373192.png', '应用 - CA.png', NULL, 6075546759500924936, '2023-12-02 14:15:19', 6075546759500924936, '2023-12-02 14:16:02');
INSERT INTO `product_attachment` VALUES (6266683749772691458, 6266684871933560837, '/opt/ccsp/filetarget/Product/b6266683749000939522/6266683749000939522.png', '安全合规.png', NULL, 6075546759500924936, '2023-12-02 14:15:29', 6075546759500924936, '2023-12-02 14:16:02');
INSERT INTO `product_attachment` VALUES (6266683851308402695, 6266684871933560837, '/opt/ccsp/filetarget/Product/b6266683850536650757/6266683850536650757.png', '大容量证书.png', NULL, 6075546759500924936, '2023-12-02 14:15:32', 6075546759500924936, '2023-12-02 14:16:02');
INSERT INTO `product_attachment` VALUES (6266683954756716553, 6266684871933560837, '/opt/ccsp/filetarget/Product/b6266683954085627910/6266683954085627910.png', '支持第三方服务.png', NULL, 6075546759500924936, '2023-12-02 14:15:35', 6075546759500924936, '2023-12-02 14:16:02');
INSERT INTO `product_attachment` VALUES (6266684046863632391, 6266684871933560837, '/opt/ccsp/filetarget/Product/b6266684046192543750/6266684046192543750.png', '灵活定制.png', NULL, 6075546759500924936, '2023-12-02 14:15:38', 6075546759500924936, '2023-12-02 14:16:02');
INSERT INTO `product_attachment` VALUES (6266684133400512519, 6266684871933560837, '/opt/ccsp/filetarget/Product/b6266684132695869444/6266684132695869444.png', '成熟规范的设计架构 1.png', NULL, 6075546759500924936, '2023-12-02 14:15:40', 6075546759500924936, '2023-12-02 14:16:02');
INSERT INTO `product_attachment` VALUES (6266684248861313031, 6266684871933560837, '/opt/ccsp/filetarget/Product/b6266684247720462339/6266684247720462339.png', '强大的并行处理能力 1.png', NULL, 6075546759500924936, '2023-12-02 14:15:44', 6075546759500924936, '2023-12-02 14:16:02');
INSERT INTO `product_attachment` VALUES (6266684666211338249, 6266684871933560837, '/opt/ccsp/filetarget/Product/b6266684663560538116/6266684663560538116.png', '数字证书认证服务.png', NULL, 6075546759500924936, '2023-12-02 14:15:56', 6075546759500924936, '2023-12-02 14:16:02');
INSERT INTO `product_attachment` VALUES (6271970536686487556, 6266438884426123271, '/opt/ccsp/filetarget/Product/b6271970340393060355/6271970340393060355.png', '应用 - 密钥管理.png', NULL, 6075546759500924936, '2023-12-04 10:01:27', 6075546759500924936, '2023-12-04 10:07:02');
INSERT INTO `product_attachment` VALUES (6271972403722192897, 6266438884426123271, '/opt/ccsp/filetarget/Product/b6271971032017012739/6271971032017012739.png', '加密对象统一管理.png', NULL, 6075546759500924936, '2023-12-04 10:02:23', 6075546759500924936, '2023-12-04 10:07:02');
INSERT INTO `product_attachment` VALUES (6271979496726464520, 6266438884426123271, '/opt/ccsp/filetarget/Product/b6271979422269179909/6271979422269179909.png', 'KMIP协议支持.png', NULL, 6075546759500924936, '2023-12-04 10:05:54', 6075546759500924936, '2023-12-04 10:07:02');
INSERT INTO `product_attachment` VALUES (6271979621381179395, 6266438884426123271, '/opt/ccsp/filetarget/Product/b6271979612992571399/6271979612992571399.png', '细粒度密钥策略.png', NULL, 6075546759500924936, '2023-12-04 10:05:58', 6075546759500924936, '2023-12-04 10:07:02');
INSERT INTO `product_attachment` VALUES (6271979736707762185, 6266438884426123271, '/opt/ccsp/filetarget/Product/b6271979722279356421/6271979722279356421.png', '合规性.png', NULL, 6075546759500924936, '2023-12-04 10:06:02', 6075546759500924936, '2023-12-04 10:07:02');
INSERT INTO `product_attachment` VALUES (6271981148443379720, 6266438884426123271, '/opt/ccsp/filetarget/Product/b6271980103122159625/6271980103122159625.png', '密钥管理服务.png', NULL, 6075546759500924936, '2023-12-04 10:06:44', 6075546759500924936, '2023-12-04 10:07:02');
INSERT INTO `product_attachment` VALUES (6271990800174417929, 6266625578366535681, '/opt/ccsp/filetarget/Product/b6271990788698802184/6271990788698802184.png', '应用 - 数据库加密.png', NULL, 6075546759500924936, '2023-12-04 10:11:31', 6075546759500924936, '2023-12-04 10:14:42');
INSERT INTO `product_attachment` VALUES (6271992640635013122, 6266625578366535681, '/opt/ccsp/filetarget/Product/b6271991285572831234/6271991285572831234.png', '应用无改造.png', NULL, 6075546759500924936, '2023-12-04 10:12:26', 6075546759500924936, '2023-12-04 10:14:42');
INSERT INTO `product_attachment` VALUES (6271994779562280961, 6266625578366535681, '/opt/ccsp/filetarget/Product/b6271993420070914048/6271993420070914048.png', '广泛的数据库兼容性.png', NULL, 6075546759500924936, '2023-12-04 10:13:30', 6075546759500924936, '2023-12-04 10:14:42');
INSERT INTO `product_attachment` VALUES (6271994918746064904, 6266625578366535681, '/opt/ccsp/filetarget/Product/b6271994908813953033/6271994908813953033.png', '多种密钥管理方式.png', NULL, 6075546759500924936, '2023-12-04 10:13:34', 6075546759500924936, '2023-12-04 10:14:42');
INSERT INTO `product_attachment` VALUES (6271997052942157828, 6266625578366535681, '/opt/ccsp/filetarget/Product/b6271997013649917959/6271997013649917959.png', '数据库加密服务.png', NULL, 6075546759500924936, '2023-12-04 10:14:38', 6075546759500924936, '2023-12-04 10:14:42');
INSERT INTO `product_attachment` VALUES (6272499445212383242, 6266660668450670596, '/opt/ccsp/filetarget/Product/b6272499445212383241/6272499445212383241.png', '签章过程安全可控.png', NULL, 6075546759500924936, '2023-12-04 14:24:10', 6075546759500924936, '2023-12-04 14:24:13');
INSERT INTO `product_attachment` VALUES (6272863998001416195, 6266660668450670596, '/opt/ccsp/filetarget/Product/b6272863998001416194/6272863998001416194.pdf', '三未信安电子签章接口手册1.3.pdf', NULL, 6075546759500924936, '2023-12-04 17:25:15', 6075546759500924936, '2023-12-04 17:25:23');
INSERT INTO `product_attachment` VALUES (6272868122512197634, 6266438884426123271, '/opt/ccsp/filetarget/Product/b6272868122478643209/6272868122478643209.pdf', '三未信安SecKMS V4.0.0 RESTFUL服务用户指南.pdf', NULL, 6075546759500924936, '2023-12-04 17:27:18', 6075546759500924936, '2023-12-04 17:27:20');
INSERT INTO `product_attachment` VALUES (6272869189845125127, 6266684871933560837, '/opt/ccsp/filetarget/Product/b6272869189811570693/6272869189811570693.pdf', '三未信安数字证书认证服务接口手册v5.0.1.pdf', NULL, 6075546759500924936, '2023-12-04 17:27:49', 6075546759500924936, '2023-12-04 17:27:52');
INSERT INTO `product_attachment` VALUES (6272872748561074183, 6266607166747707401, '/opt/ccsp/filetarget/Product/b6272872748561074182/6272872748561074182.pdf', '三未信安动态令牌平台接口V3.2.1.pdf', NULL, 6075546759500924936, '2023-12-04 17:29:35', 6075546759500924936, '2023-12-04 17:29:37');
INSERT INTO `product_attachment` VALUES (6272873605675485184, 6266465492721600514, '/opt/ccsp/filetarget/Product/b6272873605641930761/6272873605641930761.pdf', '三未信安密码服务平台_协同签名服务接口V4.6.4.pdf', NULL, 6075546759500924936, '2023-12-04 17:30:01', 6075546759500924936, '2023-12-04 17:30:02');
INSERT INTO `product_attachment` VALUES (6272874657338492929, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6272874657304938504/6272874657304938504.pdf', 'SSL VPN服务接口V1.0.pdf', NULL, 6075546759500924936, '2023-12-04 17:30:32', 6075546759500924936, '2023-12-04 17:30:34');
INSERT INTO `product_attachment` VALUES (6272875586527823882, 6266344853734033410, '/opt/ccsp/filetarget/Product/b6272875586527823881/6272875586527823881.pdf', '三未信安密码服务平台_数据加解密服务接口V1.2.pdf', NULL, 6075546759500924936, '2023-12-04 17:31:00', 6075546759500924936, '2023-12-04 17:31:01');
INSERT INTO `product_attachment` VALUES (6272876831967676420, 6264218972530018305, '/opt/ccsp/filetarget/Product/b6272876831967676419/6272876831967676419.pdf', '三未信安时间戳Restful接口V1.0.pdf', NULL, 6075546759500924936, '2023-12-04 17:31:37', 6075546759500924936, '2023-12-04 17:31:39');
INSERT INTO `product_attachment` VALUES (6272877428867467271, 6264186614984607747, '/opt/ccsp/filetarget/Product/b6272877428867467270/6272877428867467270.pdf', '三未信安密码服务平台_签名验签服务接口V1.2.pdf', NULL, 6075546759500924936, '2023-12-04 17:31:55', 6075546759500924936, '2023-12-04 17:31:56');
INSERT INTO `product_attachment` VALUES (6275024353896302594, 6266660668450670596, '/opt/ccsp/filetarget/Product/b6275024353896302593/6275024353896302593.zip', '电子签章服务.zip', NULL, 6075546759500924936, '2023-12-05 11:18:18', 6075546759500924936, '2023-12-05 11:18:26');
INSERT INTO `product_attachment` VALUES (6275026682573883394, 6264186614984607747, '/opt/ccsp/filetarget/Product/b6275026682573883393/6275026682573883393.zip', '签名验签服务.zip', NULL, 6075546759500924936, '2023-12-05 11:19:28', 6075546759500924936, '2023-12-05 11:19:30');
INSERT INTO `product_attachment` VALUES (6275027498718332932, 6264218972530018305, '/opt/ccsp/filetarget/Product/b6275027498684778499/6275027498684778499.zip', '时间戳服务.zip', NULL, 6075546759500924936, '2023-12-05 11:19:52', 6075546759500924936, '2023-12-05 11:19:54');
INSERT INTO `product_attachment` VALUES (6275030634883319809, 6266408976958228482, '/opt/ccsp/filetarget/Product/b6275030634883319808/6275030634883319808.zip', 'SSL VPN加密通道服务.zip', NULL, 6075546759500924936, '2023-12-05 11:21:25', 6075546759500924936, '2023-12-05 11:21:28');
INSERT INTO `product_attachment` VALUES (6275031621618501641, 6266465492721600514, '/opt/ccsp/filetarget/Product/b6275031621618501640/6275031621618501640.zip', '协同签名服务.zip', NULL, 6075546759500924936, '2023-12-05 11:21:55', 6075546759500924936, '2023-12-05 11:21:56');
INSERT INTO `product_attachment` VALUES (6275035653049288714, 6266607166747707401, '/opt/ccsp/filetarget/Product/b6275035653049288713/6275035653049288713.zip', '动态令牌服务.zip', NULL, 6075546759500924936, '2023-12-05 11:23:55', 6075546759500924936, '2023-12-05 11:23:56');
INSERT INTO `product_attachment` VALUES (6275036510365026306, 6266438884426123271, '/opt/ccsp/filetarget/Product/b6275036510365026305/6275036510365026305.zip', '密钥管理服务.zip', NULL, 6075546759500924936, '2023-12-05 11:24:21', 6075546759500924936, '2023-12-05 11:24:22');
INSERT INTO `product_attachment` VALUES (6275037534278518792, 6266641228556273670, '/opt/ccsp/filetarget/Product/b6275037534278518791/6275037534278518791.zip', '文件加密服务.zip', NULL, 6075546759500924936, '2023-12-05 11:24:51', 6075546759500924936, '2023-12-05 11:24:52');
INSERT INTO `product_attachment` VALUES (6315449961585969155, 6315470047973607431, '/opt/ccsp/filetarget/Product/b6315449960747108360/6315449960747108360.png', '政务外网安全接入产品图标.png', NULL, 6075546759500924936, '2023-12-19 09:57:55', 6075546759500924936, '2023-12-22 10:22:00');
INSERT INTO `product_attachment` VALUES (6315459484937750530, 6315470047973607431, '/opt/ccsp/filetarget/Product/b6315459484098889737/6315459484098889737.png', '场景.png', NULL, 6075546759500924936, '2023-12-19 10:02:39', 6075546759500924936, '2023-12-22 10:22:00');
INSERT INTO `product_attachment` VALUES (6315468623554414599, 6315470047973607431, '/opt/ccsp/filetarget/Product/b6315468622614890502/6315468622614890502.png', '随时办公.png', NULL, 6075546759500924936, '2023-12-19 10:07:11', 6075546759500924936, '2023-12-22 10:22:00');
INSERT INTO `product_attachment` VALUES (6315468708413573120, 6315470047973607431, '/opt/ccsp/filetarget/Product/b6315468707910256641/6315468707910256641.png', '简化运维.png', NULL, 6075546759500924936, '2023-12-19 10:07:14', 6075546759500924936, '2023-12-22 10:22:00');
INSERT INTO `product_attachment` VALUES (6315477173458569225, 6315492542361503751, '/opt/ccsp/filetarget/Product/b6315477172653262848/6315477172653262848.png', '统一安全管理平台产品图标.png', NULL, 6075546759500924936, '2023-12-19 10:11:26', 6075546759500924936, '2023-12-22 11:16:49');
INSERT INTO `product_attachment` VALUES (6315490023698728969, 6315492542361503751, '/opt/ccsp/filetarget/Product/b6315490022859868167/6315490022859868167.png', '面向业务的统一安全管理.png', NULL, 6075546759500924936, '2023-12-19 10:17:49', 6075546759500924936, '2023-12-22 11:16:49');
INSERT INTO `product_attachment` VALUES (6315490139226638343, 6315492542361503751, '/opt/ccsp/filetarget/Product/b6315490138723321858/6315490138723321858.png', '多安全场景下的多安全要素信息获取.png', NULL, 6075546759500924936, '2023-12-19 10:17:53', 6075546759500924936, '2023-12-22 11:16:49');
INSERT INTO `product_attachment` VALUES (6315490272941049865, 6315492542361503751, '/opt/ccsp/filetarget/Product/b6315490272035080197/6315490272035080197.png', '支持多种监控对象.png', NULL, 6075546759500924936, '2023-12-19 10:17:57', 6075546759500924936, '2023-12-22 11:16:49');
INSERT INTO `product_attachment` VALUES (6315490376154482697, 6315492542361503751, '/opt/ccsp/filetarget/Product/b6315490375282067462/6315490375282067462.png', '高性能日志采集.png', NULL, 6075546759500924936, '2023-12-19 10:18:00', 6075546759500924936, '2023-12-22 11:16:49');
INSERT INTO `product_attachment` VALUES (6315490487890741252, 6315492542361503751, '/opt/ccsp/filetarget/Product/b6315490487454533640/6315490487454533640.png', '面向实战和场景化的威胁分析.png', NULL, 6075546759500924936, '2023-12-19 10:18:03', 6075546759500924936, '2023-12-22 11:16:49');
INSERT INTO `product_attachment` VALUES (6315491567437481985, 6315492542361503751, '/opt/ccsp/filetarget/Product/b6315491566967719944/6315491566967719944.png', '统一安全管理平台应用场景.png', NULL, 6075546759500924936, '2023-12-19 10:18:35', 6075546759500924936, '2023-12-22 11:16:49');
INSERT INTO `product_attachment` VALUES (6315502458669238273, 6315505555374213120, '/opt/ccsp/filetarget/Product/b6315502457830377478/6315502457830377478.png', '终端安全管理应用场景.png', NULL, 6075546759500924936, '2023-12-19 10:24:00', 6075546759500924936, '2023-12-22 10:31:54');
INSERT INTO `product_attachment` VALUES (6315504723324962825, 6315505555374213120, '/opt/ccsp/filetarget/Product/b6315504722385438727/6315504722385438727.png', '终端安全管理产品图标.png', NULL, 6075546759500924936, '2023-12-19 10:25:07', 6075546759500924936, '2023-12-22 10:31:54');
INSERT INTO `product_attachment` VALUES (6315505137319544832, 6315505555374213120, '/opt/ccsp/filetarget/Product/b6315505136749119490/6315505136749119490.png', '多引擎查杀.png', NULL, 6075546759500924936, '2023-12-19 10:25:20', 6075546759500924936, '2023-12-22 10:31:54');
INSERT INTO `product_attachment` VALUES (6315505230433093639, 6315505555374213120, '/opt/ccsp/filetarget/Product/b6315505229661341702/6315505229661341702.png', '动态访问控制.png', NULL, 6075546759500924936, '2023-12-19 10:25:22', 6075546759500924936, '2023-12-22 10:31:54');
INSERT INTO `product_attachment` VALUES (6315505305796347912, 6315505555374213120, '/opt/ccsp/filetarget/Product/b6315505305259476995/6315505305259476995.png', '运维管理简单.png', NULL, 6075546759500924936, '2023-12-19 10:25:25', 6075546759500924936, '2023-12-22 10:31:54');
INSERT INTO `product_attachment` VALUES (6315511007667423235, 6315517203493292032, '/opt/ccsp/filetarget/Product/b6315511007130552322/6315511007130552322.png', '安全运营产品图标.png', NULL, 6075546759500924936, '2023-12-19 10:28:15', 6075546759500924936, '2023-12-22 10:15:46');
INSERT INTO `product_attachment` VALUES (6315513194577528840, 6315517203493292032, '/opt/ccsp/filetarget/Product/b6315513193839331328/6315513193839331328.png', '安全技术专家团队.png', NULL, 6075546759500924936, '2023-12-19 10:29:20', 6075546759500924936, '2023-12-22 10:15:46');
INSERT INTO `product_attachment` VALUES (6315513454087505927, 6315517203493292032, '/opt/ccsp/filetarget/Product/b6315513453617743881/6315513453617743881.png', '实时响应服务2.png', NULL, 6075546759500924936, '2023-12-19 10:29:28', 6075546759500924936, '2023-12-22 10:15:46');
INSERT INTO `product_attachment` VALUES (6315513532671985672, 6315517203493292032, '/opt/ccsp/filetarget/Product/b6315513532202223619/6315513532202223619.png', '海量威胁情报.png', NULL, 6075546759500924936, '2023-12-19 10:29:30', 6075546759500924936, '2023-12-22 10:15:46');
INSERT INTO `product_attachment` VALUES (6315514686508238853, 6315517203493292032, '/opt/ccsp/filetarget/Product/b6315514685937813505/6315514685937813505.png', '应用场景1.png', NULL, 6075546759500924936, '2023-12-19 10:30:04', 6075546759500924936, '2023-12-22 10:15:46');
INSERT INTO `product_attachment` VALUES (6315518994125555713, 6315524044101126148, '/opt/ccsp/filetarget/Product/b6315518993622239241/6315518993622239241.png', '产品图标.png', NULL, 6075546759500924936, '2023-12-19 10:32:13', 6075546759500924936, '2023-12-22 10:17:23');
INSERT INTO `product_attachment` VALUES (6315521808839084040, 6315524044101126148, '/opt/ccsp/filetarget/Product/b6315521808302213122/6315521808302213122.png', '贴合行业有针对性.png', NULL, 6075546759500924936, '2023-12-19 10:33:36', 6075546759500924936, '2023-12-22 10:17:23');
INSERT INTO `product_attachment` VALUES (6315521890477017091, 6315524044101126148, '/opt/ccsp/filetarget/Product/b6315521889436829700/6315521889436829700.png', '高质量低投入.png', NULL, 6075546759500924936, '2023-12-19 10:33:39', 6075546759500924936, '2023-12-22 10:17:23');
INSERT INTO `product_attachment` VALUES (6315521953861339139, 6315524044101126148, '/opt/ccsp/filetarget/Product/b6315521953123141635/6315521953123141635.png', '丰富的评估团队.png', NULL, 6075546759500924936, '2023-12-19 10:33:41', 6075546759500924936, '2023-12-22 10:17:23');
INSERT INTO `product_attachment` VALUES (6315522861072517121, 6315524044101126148, '/opt/ccsp/filetarget/Product/b6315522860569200642/6315522860569200642.png', '应用场景.png', NULL, 6075546759500924936, '2023-12-19 10:34:08', 6075546759500924936, '2023-12-22 10:17:23');
INSERT INTO `product_attachment` VALUES (6315525541165664256, 6315530435046017028, '/opt/ccsp/filetarget/Product/b6315525540360357897/6315525540360357897.png', '网络安全风险评估产品图标.png', NULL, 6075546759500924936, '2023-12-19 10:35:28', 6075546759500924936, '2023-12-22 14:17:10');
INSERT INTO `product_attachment` VALUES (6315527365083924485, 6315530435046017028, '/opt/ccsp/filetarget/Product/b6315527364513499136/6315527364513499136.png', '贴合行业有针对性.png', NULL, 6075546759500924936, '2023-12-19 10:36:22', 6075546759500924936, '2023-12-22 14:17:10');
INSERT INTO `product_attachment` VALUES (6315528081437493250, 6315530435046017028, '/opt/ccsp/filetarget/Product/b6315528080799959044/6315528080799959044.png', '高质量低投入.png', NULL, 6075546759500924936, '2023-12-19 10:36:43', 6075546759500924936, '2023-12-22 14:17:10');
INSERT INTO `product_attachment` VALUES (6315528501236992009, 6315530435046017028, '/opt/ccsp/filetarget/Product/b6315528498854627328/6315528498854627328.png', '丰富的评估团队.png', NULL, 6075546759500924936, '2023-12-19 10:36:56', 6075546759500924936, '2023-12-22 14:17:10');
INSERT INTO `product_attachment` VALUES (6315532220141799429, 6315535740572141568, '/opt/ccsp/filetarget/Product/b6315532219604928518/6315532219604928518.png', '应急演练产品图标.png', NULL, 6075546759500924936, '2023-12-19 10:38:47', 6075546759500924936, '2023-12-22 10:25:49');
INSERT INTO `product_attachment` VALUES (6315536874275735554, 6315544913951197185, '/opt/ccsp/filetarget/Product/b6315536873839527944/6315536873839527944.png', '产品图标.png', NULL, 6075546759500924936, '2023-12-19 10:41:05', 6075546759500924936, '2023-12-22 10:20:23');
INSERT INTO `product_attachment` VALUES (6315538833720346626, 6315544913951197185, '/opt/ccsp/filetarget/Product/b6315538832982149123/6315538832982149123.png', '攻击检测分析-优势1-高级威胁检测.png', NULL, 6075546759500924936, '2023-12-19 10:42:04', 6075546759500924936, '2023-12-22 10:20:23');
INSERT INTO `product_attachment` VALUES (6315538923411343362, 6315544913951197185, '/opt/ccsp/filetarget/Product/b6315538922874472453/6315538922874472453.png', '攻击检测分析-优势2-快速响应.png', NULL, 6075546759500924936, '2023-12-19 10:42:07', 6075546759500924936, '2023-12-22 10:20:23');
INSERT INTO `product_attachment` VALUES (6315539008203393028, 6315544913951197185, '/opt/ccsp/filetarget/Product/b6315539007767185415/6315539007767185415.png', '攻击检测分析-优势3-灵活编排.png', NULL, 6075546759500924936, '2023-12-19 10:42:09', 6075546759500924936, '2023-12-22 10:20:23');
INSERT INTO `product_attachment` VALUES (6315539093867857926, 6315544913951197185, '/opt/ccsp/filetarget/Product/b6315539093398095876/6315539093398095876.png', '攻击检测分析-优势4-合规.png', NULL, 6075546759500924936, '2023-12-19 10:42:12', 6075546759500924936, '2023-12-22 10:20:23');
INSERT INTO `product_attachment` VALUES (6315539174499158020, 6315544913951197185, '/opt/ccsp/filetarget/Product/b6315539174029395975/6315539174029395975.png', '攻击检测分析-优势5-可视化展示.png', NULL, 6075546759500924936, '2023-12-19 10:42:14', 6075546759500924936, '2023-12-22 10:20:23');
INSERT INTO `product_attachment` VALUES (6315539273484732419, 6315544913951197185, '/opt/ccsp/filetarget/Product/b6315539273082079236/6315539273082079236.png', '攻击威胁分析-优势6-云上部署.png', NULL, 6075546759500924936, '2023-12-19 10:42:17', 6075546759500924936, '2023-12-22 10:20:23');
INSERT INTO `product_attachment` VALUES (6315543692905416713, 6315544913951197185, '/opt/ccsp/filetarget/Product/b6315543692368545794/6315543692368545794.png', '攻击检测分析-应用场景.png', NULL, 6075546759500924936, '2023-12-19 10:44:29', 6075546759500924936, '2023-12-22 10:20:23');
INSERT INTO `product_attachment` VALUES (6315545735565019145, 6315549679519401991, '/opt/ccsp/filetarget/Product/b6315545734726158339/6315545734726158339.png', '数据接口访问控制产品图标.png', NULL, 6075546759500924936, '2023-12-19 10:45:30', 6075546759500924936, '2023-12-22 11:13:38');
INSERT INTO `product_attachment` VALUES (6315548119204759552, 6315549679519401991, '/opt/ccsp/filetarget/Product/b6315548118399453184/6315548118399453184.png', '接口数据访问控制-优势1-完整解决方案.png', NULL, 6075546759500924936, '2023-12-19 10:46:41', 6075546759500924936, '2023-12-22 11:13:38');
INSERT INTO `product_attachment` VALUES (6315548201446672392, 6315549679519401991, '/opt/ccsp/filetarget/Product/b6315548200976910345/6315548200976910345.png', '接口数据访问控制-优势2-可视化.png', NULL, 6075546759500924936, '2023-12-19 10:46:43', 6075546759500924936, '2023-12-22 11:13:38');
INSERT INTO `product_attachment` VALUES (6315548327477118985, 6315549679519401991, '/opt/ccsp/filetarget/Product/b6315548327007356930/6315548327007356930.png', '接口数据访问控制-优势3-数据识别.png', NULL, 6075546759500924936, '2023-12-19 10:46:47', 6075546759500924936, '2023-12-22 11:13:38');
INSERT INTO `product_attachment` VALUES (6315548405625391111, 6315549679519401991, '/opt/ccsp/filetarget/Product/b6315548405189183496/6315548405189183496.png', '接口数据访问控制-优势4-安全预警.png', NULL, 6075546759500924936, '2023-12-19 10:46:49', 6075546759500924936, '2023-12-22 11:13:38');
INSERT INTO `product_attachment` VALUES (6315548945952409607, 6315549679519401991, '/opt/ccsp/filetarget/Product/b6315548945449093122/6315548945449093122.png', '接口数据访问控制-应用场景1.png', NULL, 6075546759500924936, '2023-12-19 10:47:05', 6075546759500924936, '2023-12-22 11:13:38');
INSERT INTO `product_attachment` VALUES (6324149584240445445, 6315535740572141568, '/opt/ccsp/filetarget/Product/b6324149583737128964/6324149583737128964.png', '场景2.png', NULL, 6075546759500924936, '2023-12-22 09:59:04', 6075546759500924936, '2023-12-22 10:25:49');
INSERT INTO `product_attachment` VALUES (6324155361743210501, 6315535740572141568, '/opt/ccsp/filetarget/Product/b6324155360937904131/6324155360937904131.png', 'lQLPJxTeOETlZyM4OLBocIHrCyRotgV1tLyOCZ8A_56_56.png', NULL, 6075546759500924936, '2023-12-22 10:01:56', 6075546759500924936, '2023-12-22 10:25:49');
INSERT INTO `product_attachment` VALUES (6324156689055877129, 6315535740572141568, '/opt/ccsp/filetarget/Product/b6324156688586115075/6324156688586115075.png', '丰富的评估团队.png', NULL, 6075546759500924936, '2023-12-22 10:02:36', 6075546759500924936, '2023-12-22 10:25:49');
INSERT INTO `product_attachment` VALUES (6324669149823240199, 6315530435046017028, '/opt/ccsp/filetarget/Product/b6324669149387032581/6324669149387032581.png', '应用场景.png', NULL, 6075546759500924936, '2023-12-22 14:17:08', 6075546759500924936, '2023-12-22 14:17:10');
INSERT INTO `product_attachment` VALUES (6669656784450882951, 6669663867724369287, '/opt/ccsp/filetarget/Product/b6669656769183616385/6669656769183616385.png', '加解密规则引擎图标.png', NULL, 6649783043579186561, '2024-04-19 14:14:21', 6649783043579186561, '2024-04-19 14:17:52');
INSERT INTO `product_attachment` VALUES (6669658012777975169, 6669663867724369287, '/opt/ccsp/filetarget/Product/b6669658003047189890/6669658003047189890.png', '高效批量加密.png', NULL, 6649783043579186561, '2024-04-19 14:14:58', 6649783043579186561, '2024-04-19 14:17:52');
INSERT INTO `product_attachment` VALUES (6669658837613022599, 6669663867724369287, '/opt/ccsp/filetarget/Product/b6669658829190860162/6669658829190860162.png', '个性化加密规则.png', NULL, 6649783043579186561, '2024-04-19 14:15:22', 6649783043579186561, '2024-04-19 14:17:52');
INSERT INTO `product_attachment` VALUES (6669659731402427777, 6669663867724369287, '/opt/ccsp/filetarget/Product/b6669659721436761481/6669659721436761481.png', '跨云数据传输保障.png', NULL, 6649783043579186561, '2024-04-19 14:15:49', 6649783043579186561, '2024-04-19 14:17:52');
INSERT INTO `product_attachment` VALUES (6669663304815218049, 6669663867724369287, '/opt/ccsp/filetarget/Product/b6669663286796488066/6669663286796488066.png', '加解密规则引擎应用场景.png', NULL, 6649783043579186561, '2024-04-19 14:17:36', 6649783043579186561, '2024-04-19 14:17:52');

-- ----------------------------
-- Table structure for product_scene
-- ----------------------------
DROP TABLE IF EXISTS `product_scene`;
CREATE TABLE `product_scene`  (
  `ID` bigint(20) NOT NULL COMMENT '主键',
  `PRODUCT_ID` bigint(20) NOT NULL COMMENT '产品ID',
  `TITLE` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `SCENE_ICON_ID` bigint(20) NULL DEFAULT NULL COMMENT '场景图片;图片地址',
  `SORD_NUM` int(11) NULL DEFAULT NULL COMMENT '排序',
  `REMARK` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '产品场景' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of product_scene
-- ----------------------------
INSERT INTO `product_scene` VALUES (6264186615051716612, 6264186614984607747, '数据签名', 6264186218740320257, NULL, '签名验签密码服务可用于运维人员相对固定，需使用客户端UKEY进行认证的场景或通讯过程中的数据签名的场景，为业务应用提供安全的签名与验证服务', 6075546759500924936, '2023-12-01 17:35:08', NULL, NULL);
INSERT INTO `product_scene` VALUES (6264218972630681602, 6264218972530018305, '时间戳服务器可应用于众多领域', 6264213838433486848, NULL, '电子政务：电子选举、电子公文审批、电子公证、司法证据和材料保存。医疗领域：电子病历、医疗处置单、检查报告单、电子医学影像。电子商务：电子合同、网上竞拍、网上招投标、电子邮件、电子客票、电子彩票。证券金融：网上银行、电子支付、电子保险、电子票据、证券交易。知识产权：工业产权（专利、商标等）、著作权、商业秘密保护', 6075546759500924936, '2023-12-01 17:51:13', NULL, NULL);
INSERT INTO `product_scene` VALUES (6266344853834696713, 6266344853734033410, '敏感数据加密', 6266336344732731399, NULL, '数据加解密服务一般用于敏感数据加解密或少量文件的加解密，可用于数字证书认证系统、密钥管理系统等安全系统中进行密钥加密存储与保护，为业务提供数据的机密性保护', 6075546759500924936, '2023-12-02 11:27:09', NULL, NULL);
INSERT INTO `product_scene` VALUES (6266408976991782920, 6266408976958228482, 'HTTP安全', 6266408873543469061, NULL, 'SSL网关服务可用于将网站从 HTTP 切换到 HTTPS，从而提供更高的安全性和保护用户隐私的能力', 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_scene` VALUES (6266465492822263816, 6266465492721600514, '安全可靠的身份认证', 6266464753551017990, NULL, '用户在移动端完成敏感信息业务，如合同签署、公文审批等场景，需要可信的数字签名时，传统的智能密码钥匙在手机端应用十分困难，此时，采用协同签名数字签名方式是最为有效便捷的可行方案，用户移动端只需要通过简单的对接集成，就可以在正常的业务过程中获得与智能密码钥匙等价的可靠数字签名', 6075546759500924936, '2023-12-02 12:27:04', NULL, NULL);
INSERT INTO `product_scene` VALUES (6266607166781261828, 6266607166747707401, '等级保护系统建设', 6266607093263501321, NULL, '应用系统中客户端为互联网公众用户的场景下，通过使用动态令牌服务为客户端提供安全可靠的动态令牌，从而实现应用系统的安全登录', 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_scene` VALUES (6266641228623382528, 6266641228556273670, '文件加密保护', 6266639825679026185, NULL, '该服务可提供对SAN存储的磁盘级加密服务和NSA存储的网络文件系统加密服务，该服务模块位于存储系统和上层应用系统之间，对要写入的数据进行加密后存储到后端存储，要读取的数据解密后提供给用户终端或者服务器', 6075546759500924936, '2023-12-02 13:54:22', NULL, NULL);
INSERT INTO `product_scene` VALUES (6266684871967115264, 6266684871933560837, '数字证书颁发服务', 6266684666211338249, NULL, '数字证书认证服务可用于颁发数字证书，用于验证个人或组织的身份信息。这些证书可以用于电子签名、电子邮箱身份验证、VPN身份验证等多种场景，确保通信的可信度和安全性。此外，数字证书认证服务还可用于电子支付和交易安全保护、数字版权保护、软件代码签名等场景，广泛适用于数字通信和信息交换过程中，为各种场景提供身份验证和数据安全的保障', 6075546759500924936, '2023-12-02 14:16:02', NULL, NULL);
INSERT INTO `product_scene` VALUES (6271981772253825030, 6266438884426123271, '业务系统加密和密钥管理', 6271981148443379720, NULL, '密钥管理服务可提供对应用系统的密钥管理，业务系统通过KMIP协议与密钥管理服务进行密钥管理通信，之后对存入的敏感数据调用指定密钥进行加密保护，数据存储到数据库时已经是密文，能够有效保障数据的安全。如需要访问敏感密文数据，可针对业务系统进行访问控制，设置密钥访问控制策略', 6075546759500924936, '2023-12-04 10:07:02', NULL, NULL);
INSERT INTO `product_scene` VALUES (6271997206554347525, 6266625578366535681, '数据库加密保护', 6271997052942157828, NULL, '数据库加密服务可应用于云上平台及容器化部署的数据库，通过将加密插件融入应用容器镜像的方式，保护众多云应用系统的数据库，实现容器中数据库的加密', 6075546759500924936, '2023-12-04 10:14:42', NULL, NULL);
INSERT INTO `product_scene` VALUES (6272538039076390915, 6266660668450670596, '电子文书签章服务', 6266659329125517315, NULL, '电子签章密码服务可应用于政府、金融、工程、保险、医疗、房产、人力资源、物流、电商等领域，为各式文书、合同、文件提供安全可靠的电子签章、印章管理与验证。服务支持签章带时间戳，关键节点采用高可用双服务的负载均衡模式，最大程度的确保电子签章系统本身的健壮性与稳定性，同时保障签章服务性能平滑扩展，为用户提供最佳的使用体验', 6075546759500924936, '2023-12-04 14:43:20', NULL, NULL);
INSERT INTO `product_scene` VALUES (6324183185111779334, 6315517203493292032, '服务场景', 6315514686508238853, NULL, '提升企业风险抵御能力、化解安全压力，对现有企业安全能力进行补充和优化，缩短全产品的各类安全事件处理响应和分析时间，有效保障单位的网络安全及业务系统持续正常运行，并针对服务范围内资产进行持续风险监视，同时提供远程值守团队进行安全保障，提升用户运营效率', 6075546759500924936, '2023-12-22 10:15:46', NULL, NULL);
INSERT INTO `product_scene` VALUES (6324186448582281223, 6315524044101126148, '服务场景', 6315522861072517121, NULL, '针对数据安全现状不清晰，需要梳理数据安全防护现状，同时发现单位数据安全防护短板，提升数据安全管控能力，确保数据安全相关工作依法合规', 6075546759500924936, '2023-12-22 10:17:23', NULL, NULL);
INSERT INTO `product_scene` VALUES (6324188207270725641, 6315530435046017028, '服务场景', 6324669149823240199, NULL, '通过安全评估，全面识别重要信息资产及其面临的威胁以及存在的漏洞弱点，在此基础上进行风险分析，根据风险等级制定整体安全略，安全解决方案，进行安全控制措施的实施，降低安全风险，防范威胁', 6075546759500924936, '2023-12-22 10:18:15', NULL, NULL);
INSERT INTO `product_scene` VALUES (6324192484756162562, 6315544913951197185, '服务场景', 6315543692905416713, NULL, '云探针、云分析平台部署在VPC网络内部，提供完整的网络行为检测及溯源能力。\n云分析平台可以依靠自身独特的分布式搜索架构设计，将所有传送至此的行为信息和告警信息进行快速的存储并建立索引。建立索引后的信息可以在分析平台上快速的搜索到。如此，安全管理人员便可以利用该方案记录过去一段时间内出现的任何一次网络行为，发现任何一次网络行为中的具体细节，为攻击的回溯和发现提供了强大的数据支撑', 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_scene` VALUES (6324195753561819139, 6315470047973607431, '服务场景', 6315459484937750530, NULL, '面向不具备专线接入条件的各级政务部门、移动办公人员、现场执法人员和公众用户，提供安全接入到政务外网的服务', 6075546759500924936, '2023-12-22 10:22:00', NULL, NULL);
INSERT INTO `product_scene` VALUES (6324203422091708422, 6315535740572141568, '服务场景', 6324149584240445445, NULL, '查找和验证应急预案中存在的问题，完善应急预案，提高应急预案的科学性、实用性和可操作性；\n检验应急网络安全所需的应急队伍、准备等方面的情况，发现不足及时调整； \n检验应对网络安全事件的应对水平以及内部协同配合能力； 检验应急响应工作机制，加强网络安全应急响应能力建设，提高应急响应工作水平；\n普及员工应急响应知识，增强网络安全管理的专业化程度，提升内部员工网络安全防范意识', 6075546759500924936, '2023-12-22 10:25:49', NULL, NULL);
INSERT INTO `product_scene` VALUES (6324215667513231369, 6315505555374213120, '服务场景', 6315502458669238273, NULL, '终端安全防护：提供入网终端的安全防护，保护入网终端的基础环境安全。支持对终端配置病毒查杀策略、防护策略、定时扫描、病毒库更新等策略。可对蠕虫病毒、恶意软件、广告软件、勒索软件、引导区病毒的查杀。合规入网：用户终端中安装零信任客户端，为用户提供远程访问的统一入口，零信任应控制台（TAC）作为访问控制中心，根据零信任客户端输入的终端环境安全状态，实现终端设备准入及用户的自适应认证；同时可以根据终端状态及用户行为评估结果，对应用代理（TAP）的加密传输进行放行或阻断。\n终端数据安全：零信任通过安全空间能力，实现应用资源本地数据的安全隔离与保护。ZTNA的可信访问控制台TAC负责统一发布应用资源信息、安全空间策略配置，并授权至用户。实现用户认证后，在终端初始化不同安全空间，并在空间中提供用户授权与空间授权的应用资源访问服务', 6075546759500924936, '2023-12-22 10:31:54', NULL, NULL);
INSERT INTO `product_scene` VALUES (6324299701434058754, 6315549679519401991, '服务场景', 6315548945952409607, NULL, '数据安全建设：API是应用系统与应用系统、应用系统与微服务、微服务与微服务之间传输数据的重要且是唯一通道，API安全防护方案为数据安全数据流转监测提供数据传输通道监测功能，为数据安全态势感知平台建设提供基础数据。\n应用安全防护建设：随着企事业单位信息化普及、数字化转型建设，传统的边界安全防御已不能完全满足应用系统安全防护建设需要，应用系统安全不仅要保证边界安全更要关注应用系统对外提供服务、传输数据的API安全，API安全防护系统为应用系统安全提供安全防护解决方案。', 6075546759500924936, '2023-12-22 11:13:38', NULL, NULL);
INSERT INTO `product_scene` VALUES (6324306100465567752, 6315492542361503751, '服务场景', 6315491567437481985, NULL, '安全能力的统一接入', 6075546759500924936, '2023-12-22 11:16:49', NULL, NULL);
INSERT INTO `product_scene` VALUES (6669663868127022471, 6669663867724369287, '公共数据加密传输', 6669663304815218049, NULL, '加解密规则引擎针可以针对公共数据安全性提供解决方案，实现高效批量加解密数据以及跨云密钥安全传输', 6649783043579186561, '2024-04-19 14:17:52', NULL, NULL);

-- ----------------------------
-- Table structure for product_specs
-- ----------------------------
DROP TABLE IF EXISTS `product_specs`;
CREATE TABLE `product_specs`  (
  `ID` bigint(20) NOT NULL COMMENT '主键',
  `PRODUCT_ID` bigint(20) NOT NULL COMMENT '产品ID',
  `TITLE` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `PRODUCT_UNIT` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '单位',
  `PRODUCT_PRICE` decimal(24, 6) NULL DEFAULT NULL COMMENT '售价',
  `PRODUCT_DISCOUNT` decimal(24, 6) NULL DEFAULT NULL COMMENT '折扣价',
  `SORD_NUM` int(11) NULL DEFAULT NULL COMMENT '排序',
  `REMARK` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '产品规格' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of product_specs
-- ----------------------------

-- ----------------------------
-- Table structure for product_superiority
-- ----------------------------
DROP TABLE IF EXISTS `product_superiority`;
CREATE TABLE `product_superiority`  (
  `ID` bigint(20) NOT NULL,
  `PRODUCT_ID` bigint(20) NOT NULL COMMENT '产品ID',
  `TITLE` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `SUPERIORITY_ICON_ID` bigint(20) NULL DEFAULT NULL COMMENT '优势图标;图标地址',
  `SORD_NUM` int(11) NOT NULL COMMENT '排序',
  `REMARK` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '产品优势' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of product_superiority
-- ----------------------------
INSERT INTO `product_superiority` VALUES (6264186615118825474, 6264186614984607747, '透明简单', 6264180458283206665, 1, '基于透明加密技术，无需对应用程序、业务结构进行更改就能实现加密', 6075546759500924936, '2023-12-01 17:35:09', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6264186615152379908, 6264186614984607747, '密钥安全', 6264180572099840008, 2, '采用“系统保护密钥-用户密钥-会话密钥”三层密钥保护结构，保证关键密钥在任何时候不以明文形式出现在设备外，密钥备份文件受备份密钥加密保护', 6075546759500924936, '2023-12-01 17:35:09', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6264186615185934340, 6264186614984607747, '高可靠性', 6264180662931687430, 3, '具有安全的访问控制机制，支持双活部署、支持冷备和恢复，保障密码运算高可用和灾难恢复', 6075546759500924936, '2023-12-01 17:35:09', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6264218972664236040, 6264218972530018305, '安全密钥存储技术', 6264208335271757833, 1, '使用国家密码管理局审批通过的密码设备存储时间戳密钥对，极大的提高了密钥的安全性', 6075546759500924936, '2023-12-01 17:51:13', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6264218972731344897, 6264218972530018305, '高强度的密码技术', 6264208506198034439, 2, '支持国家密码管理局指定的SM2非对称密钥算法，极大提高了密码保护强度', 6075546759500924936, '2023-12-01 17:51:13', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6264218972764899330, 6264218972530018305, '成熟规范的设计架构', 6264208602935461891, 3, '采用业界主流的内部模块架构方式，与先进的内存检测机制，保证日后的维护扩展与长期稳定运行', 6075546759500924936, '2023-12-01 17:51:13', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6264218972764899331, 6264218972530018305, '完善的自身保护措施', 6264208731180501000, 4, '时间戳系统采取局域网的构建技术，对网络设备进行安全配置；同时，访问时间戳系统必须通过管理员身份认证', 6075546759500924936, '2023-12-01 17:51:13', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6264218972798453766, 6264218972530018305, '强大的并行处理能力', 6264208908582782985, 5, '系统采用多线程模块化设计，拥有强大的并行处理能力', 6075546759500924936, '2023-12-01 17:51:13', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6264218972832008200, 6264218972530018305, '灵活性和可扩展性', 6264209012903512068, 6, '采用先进成熟的技术和设备，满足当前项目的需求，兼顾未来的业务需求，尽量采用先进的技术、设备和材料，能够适应高速的数据传输需要，使整个系统在一段时期内保持技术的先进性，并具有良好的发展潜力，以适应未来业务的发展和技术升级的需要', 6075546759500924936, '2023-12-01 17:51:13', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266344853868251145, 6266344853734033410, '业务监控', 6266334411125032966, 1, '服务组件具备对业务监控的能力，将业务调用情况进行采集，并反馈给所需要的平台、管理端等，进行业务监控和报警', 6075546759500924936, '2023-12-02 11:27:09', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266344853935360000, 6266344853734033410, '负载均衡', 6266334533363828736, 2, '一个服务节点下可配置多台密码设备，可通过直接配置文件或接口动态调用两种方式管理，针对多个密钥源或者用户具备多机房的配置亦提供密钥源负载能力', 6075546759500924936, '2023-12-02 11:27:09', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266344853935360001, 6266344853734033410, '系统安全', 6266334626208942081, 3, '连接口令和白名单等访问控制方式，实现了服务对应用客户端的授权接入, 结合密码安全通道进—步提高了系统的安全性', 6075546759500924936, '2023-12-02 11:27:09', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266344853968914440, 6266344853734033410, '密钥安全', 6266334754923743232, 4, '采用“系统保护密钥-用户密钥-会话密钥”的三层密钥结构,充分保证用户密钥及应用系统的安全性', 6075546759500924936, '2023-12-02 11:27:09', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266344854002468868, 6266344853734033410, '标准化', 6266334903301441537, 5, '接口符合GM/T0018-2012《密码设备应用接口规范》标准,支持PKCS#11、MS-CSP.JCE等国际标准接口', 6075546759500924936, '2023-12-02 11:27:09', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266408977025337348, 6266408976958228482, '更安全', 6266374623662311429, 1, '更严格的会话密钥更新机制，可根据需要设置密钥更新策略', 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266408977092446215, 6266408976958228482, '更高效', 6266374720970164227, 2, '一台设备实现L4→L7层全业务安全防护，L4→L7层连通性；支持TCP、UDP、B/S应用、C/S应用等各类型业务', 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266408977092446216, 6266408976958228482, '安全合规', 6266374848577669122, 3, '遵循国家密码管理及行业内的多项规范标准，服务配置支持多种安全合规的算法协商套件并对低版本的客户端提供了弱算法兼容支持', 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266408977126000646, 6266408976958228482, 'WEB加速', 6266374962461411337, 4, '通过SSL卸载、会话保持机制等方式将复杂的SSL加解密压力转移到网关，从而在不需要升级任何业务服务器硬件的情况下实现明显的WEB加速', 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266408977159555076, 6266408976958228482, '负载均衡', 6266375060171917318, 5, '对于基于七层内容的调度可以根据用户请求的内容来分配资源服务器，保证用户请求分配的多元化和个性化;对于四层的基于IP地址、应用类型和内容等因素实现流量负载', 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266408977193109511, 6266408976958228482, '零客户端', 6266375256733779976, 6, '支持零客户端方式部署，国密算法SSL接入可支持国产国密浏览器无缝接入，无需安装任何客户端软件', 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266408977226663940, 6266408976958228482, '高性能', 6266408227486435335, 7, '采用高端硬件加速引擎技术，配合优化的异步I/O、Cache和Pool机制，使得安全网关具备极佳的性能，满足各种安全接入及应用交付的需求', 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266408977293772807, 6266408976958228482, '高可靠', 6266376198975784962, 8, '通过支持各种可靠性特性，有效保证业务的连续性', 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266408977293772808, 6266408976958228482, '双证书', 6266376326952388612, 9, '支持国密双证书及国际RSA证书信任体系，适用于不同加密需求的场景', 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266465494701312009, 6266465492721600514, '签名密钥分割', 6266447025972840448, 1, '协同签名系统的安全设计依托于密钥分割专利技术，通过将传统的密钥进行分割为客户端密钥因子与服务器端密钥因子两部分，客户端与服务器分别存储各自的密钥因子，以此保证密钥的安全存储。通过注册时交换临时公钥信息，计算出完整的用户公钥，通过CA机构颁发的数字证书对外发布', 6075546759500924936, '2023-12-02 12:27:04', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266465496613914628, 6266465492721600514, '无硬件介质依赖', 6266447152036841477, 2, '协同签名系统中签名密钥采用分割原理，在客户端与服务器端都只出现部分密钥，完整的签名密钥在密钥周期的任何时刻都不会出现，这杜绝了用户签名私钥暴露的风险，因此系统中无需额外的硬件介质进行密钥的存储，降低了成本的同时，极大的扩展了数字签名在移动端的应用场景', 6075546759500924936, '2023-12-02 12:27:04', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266465496681023494, 6266465492721600514, '密钥因子安全存储', 6266447445269022727, 3, '用户手机端的密钥因子采用加密存储，且与设备强相关，用户进行设备变更需要用户提供可信的身份证明；服务器端密钥因子存储采用硬件级加密，密钥因子不会明文出现在硬件之外；在签名业务过程中，密钥因子的调用都要求用户的授权许可，充分保证了密钥因子的安全', 6075546759500924936, '2023-12-02 12:27:04', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266465496714577928, 6266465492721600514, '多方参与和协作签名', 6266448750234437633, 4, '针对业务系统的签名请求，客户端与服务端分别独自计算各自的签名结果，双方各自的签名结果作为中间结果，通过中间结果无法推导任何签名信息，服务器端将中间结果传送给客户端，由客户端最终完成数字签名的合成。签名结果的验证则由服务器端通过用户证书按照传统的验签方式完成', 6075546759500924936, '2023-12-02 12:27:04', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266465496748132359, 6266465492721600514, '海量的用户支撑', 6266448899954313222, 5, '无缝支持云计算环境，系统的承载能力、业务的响应能力都可以进行资源的动态调整，具备有效的支撑海量用户业务系统的并发能力，服务器端采用热备冗余技术，提升系统的抗风险能力', 6075546759500924936, '2023-12-02 12:27:04', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266465496781686785, 6266465492721600514, '移动安全加固', 6266448993101416448, 6, '移动签名组件采用移动安全加固方案进行保护，移动签名组件在集成过程中采用代码签名技术，确保外围调用环境的安全性，在应用过程中采用主流的安全加固方案进行保护，可以有效防止逆向破解', 6075546759500924936, '2023-12-02 12:27:04', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266607166814816258, 6266607166747707401, '动态性', 6266602029966821377, 1, '动态口令令牌产生的口令每分钟变化（针对时间同步动态口令），每个口令都只在其产生的时间范围内有效', 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266607166848370688, 6266607166747707401, '随机性', 6266602161030432773, 2, '动态口令每次都是随机产生，不可预测', 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266607166881925121, 6266607166747707401, '一次性', 6266602238876715011, 3, '每个动态口令使用后，不能再连续重复使用', 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266607166881925122, 6266607166747707401, '抗攻击性', 6266602327124871172, 4, '由于动态性和随机性的特点，用户一分钟内穷举不到，那么下一分钟后需要重新穷举，另外通过系统设置可限制用户失败次数，也可进一步降低风险', 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266641228656936969, 6266641228556273670, '安全的密钥管理体系', 6266640962603845632, 1, '可使用密钥管理系统进行集中密钥管理，提供统一加密策略、访问控制策略和密钥的管理', 6075546759500924936, '2023-12-02 13:54:22', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266641228690491395, 6266641228556273670, 'KMIP协议支持', 6266641051523090432, 2, '采用标准的KMIP协议，对已经实现KMIP协议的客户系统无需任何对接，注册后直接实现系统调用，大幅度降低企业运营成本', 6075546759500924936, '2023-12-02 13:54:22', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266641228690491396, 6266641228556273670, '支持国产密码算法和通用密码算法', 6266641171446630407, 3, '支持国密SM4和国际算法AES对称加密算法', 6075546759500924936, '2023-12-02 13:54:22', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266684872000669696, 6266684871933560837, '安全合规', 6266683749772691458, 1, '采用国家密码管理局指定的商用密码SM1/SM2/SM3/SM4，符合相关技术规范与要求', 6075546759500924936, '2023-12-02 14:16:02', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266684872034224132, 6266684871933560837, '大容量证书', 6266683851308402695, 2, '可支持100万-1000万证书的应用管理', 6075546759500924936, '2023-12-02 14:16:02', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266684872067778564, 6266684871933560837, '支持第三方服务', 6266683954756716553, 3, '包含LDAP、OCSP和密钥管理中心。系统支持标准LDAP协议，并可使用自有或第三方的OCSP服务和密钥管理中心', 6075546759500924936, '2023-12-02 14:16:02', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266684872067778565, 6266684871933560837, '灵活定制', 6266684046863632391, 4, '各项策略配置具备极强的灵活性，并且能够对模块进行裁剪，即可建设大规模运营级系统也可定制化企业级系统', 6075546759500924936, '2023-12-02 14:16:02', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266684872101332995, 6266684871933560837, '成熟规范的技术设计架构', 6266684133400512519, 5, '系统采用业界主流的内部模块架构方式，能够适应未来业务的发展和技术升级的需要；先进的内存检测机制保证业务长期稳定运行', 6075546759500924936, '2023-12-02 14:16:02', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6266684872134887428, 6266684871933560837, '并行处理能力', 6266684248861313031, 6, '多进程、多线程模块化设计使系统拥有强大的并行处理能力', 6075546759500924936, '2023-12-02 14:16:02', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6271981772320933893, 6266438884426123271, '加密对象统一管理', 6271972403722192897, 1, '支持对称密钥、非对称密钥、数字证书和秘密数据等多种加密对象的统一管理，并提供加密对象的生命周期管理、加密属性管理及策略配置', 6075546759500924936, '2023-12-04 10:07:02', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6271981772320933894, 6266438884426123271, 'KMIP协议支持', 6271979496726464520, 2, '支持标准的KMIP协议，对已经实现KMIP协议的客户系统无需任何对接，注册后直接实现系统调用，大幅度降低企业运营成本', 6075546759500924936, '2023-12-04 10:07:02', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6271981772354488322, 6266438884426123271, '细粒度密钥策略', 6271979621381179395, 3, '对每个密钥分配唯一密钥用户，设置加解密、签名验证和密钥获取等控制策略，并配置密钥的访问时间等细粒度控制，通过访问控制策略保证密钥的安全使用和安全管理', 6075546759500924936, '2023-12-04 10:07:02', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6271981772388042756, 6266438884426123271, '合规性', 6271979736707762185, 4, '采用合规的密码算法、密码产品、密码协议和密码技术，硬件密码模块（HSM）采用符合国家批准的密码模块或FIPS 140-2 level 3硬件', 6075546759500924936, '2023-12-04 10:07:02', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6271997206587901955, 6266625578366535681, '应用无改造', 6271992640635013122, 1, '数据库被加密后SQL语法兼容性好，支持模糊查询，基于透明化服务理念，无需对用户应用程序进行二次开发就能实现数据库敏感数据的机密性、完整性保护，降低用户应用密码的过程中加密数据库数据带来的业务改造成本和风险', 6075546759500924936, '2023-12-04 10:14:42', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6271997206621456390, 6266625578366535681, '广泛的数据库兼容性', 6271994779562280961, 2, '支持多种数据库，其中包括主流的国际数据库，如Oracle、MySQL、SQL Server等，也包括主流的国产数据库，如达梦、人大金仓、OpenGuass、瀚高、Postgresql等', 6075546759500924936, '2023-12-04 10:14:42', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6271997206621456391, 6266625578366535681, '多种密钥管理方式', 6271994918746064904, 3, '采用集中密钥管理，支持对接三方密钥管理系统，在密码建设中有效降低密钥管理的复杂度。', 6075546759500924936, '2023-12-04 10:14:42', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6272538039177054216, 6266660668450670596, '电子印章统一管理', 6266660138894624769, 1, '通过电子签章系统可以管理印章的整个生命周期，包括印章的申请、审核、制作、停用、作废等操作', 6075546759500924936, '2023-12-04 14:43:20', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6272538039210608644, 6266660668450670596, '适应性强', 6266660233283241985, 2, '通过电子签章客户端构建用户PC 本地签章支持服务，支持跨平台、多种浏览器的应用，并适配市场多家主流CA 系统，实现数字证书的签发、管理', 6075546759500924936, '2023-12-04 14:43:20', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6272538039277717510, 6266660668450670596, '签章过程安全可控', 6272499445212383242, 3, '支持电子签章过程的统一监管，实现签章流程可控；记录用户的用章行为信息（主要包括： 签章者、签章时间），以便日后安全审计', 6075546759500924936, '2023-12-04 14:43:20', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6272538039277717511, 6266660668450670596, '抗抵赖抗伪造防篡改', 6266660461654706178, 4, '电子签章对应的签名私钥严格控制在签章者手中，其他人无法伪造', 6075546759500924936, '2023-12-04 14:43:20', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6272538039311271940, 6266660668450670596, '灵活的部署方式', 6266660592584099842, 5, '签章系统支持分级、模块化方式部署', 6075546759500924936, '2023-12-04 14:43:20', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324174401366788096, 6315492542361503751, '面向业务的统一安全管理', 6315490023698728969, 1, '云网一体化安全运营与管理平台通过对接网络中现有或未来可能扩容的各类安全防护系统引擎，实现了全面且灵活开放的态势感知系统架构。在这种体系下，任何类型任何厂商的安全设备或系统都可以作为项目网络或业务上各环节的安全监测传感器，这些传感器所产生的安全监测信息都将作为数据源由云网一体化安全运营与管理平台统一获取，这样不但平台可以尽可能全面的采集安全要求信息，同时也可以最大限度的利用项目网络中已有的安全监测防护手段，保护已有投资，实现资源优势整合。\n现有安全资源的引擎化整合是全面获取安全要素信息的基础，在此基础上，平台通过资产感知、攻击感知、运行感知、脆弱性感知、风险感知、威胁感知和态势总揽这些维度来覆盖安全态势各个方面并用来实现全方位的态势感知。\n基于云网一体化安全运营与管理平台进行全方位态势感知的实现流程大致包括3个关键步骤，分别是各类安全要素信息的获取、面向实战和场景化的威胁分析、自动化运营', 6075546759500924936, '2023-12-22 10:11:24', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324174401400342536, 6315492542361503751, '多安全场景下的多安全要素信息获取', 6315490139226638343, 2, '云网端一体化安全运营与管理平台通过对接网络中的各类安全设备、子系统、安全数据源来获取影响网络环境安全态势的各类安全要素信息，这其中包括有系统日志类、攻击类告警、对象弱点类信息、系统运行类信息、网络流量类、资产信息类信息以及外部威胁情报信息。\n态势感知系统为开放型的平台架构，任何安全设备或子系统都可作为安全监视的数据源或引擎，通过平台丰富和高兼容度的信息采集接口实现安全数据的广泛采集，不限安全设备的厂商或型号，最终都将整合到平台的统一安全要素信息分析展现体系中，形成完整全面的一站式态势感知能力。\n系统安全要素采集层的下方是组织网络中各类各厂商的安全设备和系统，以及大量要被防护监控的IT资产。这些设备和资产所能产生的海量安全监控数据和运行日志，包括外部的威胁情报信息都将通过态势感知系统开放的各类信息采集接口进行采集汇总。这实现了态势感知中对可能影响安全态势要素信息获取的重要环节，即合理的整合了环境中已有或将建设的各类安全防护资源形成安全信息源，这也是完整的全方位态势感知系统的实现基础', 6075546759500924936, '2023-12-22 10:11:24', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324174401433896967, 6315492542361503751, '支持多种监控对象', 6315490272941049865, 3, '系统支持对大部分主流IT软硬件资产的监控：\n数据采集模块负责采集获取网内被监护资产对象以及作为监测管控引擎的安全设备所产生的各类安全信息。\n数据采集模块实现了系统数据采集的开放性，其提供了丰富的数据对接接口，除了可采集各类设备或安全子系统上报的异构事件日志外，还可并行实现对结构化以及非结构化海量安全信息的采集。其中结构化数据由一系列标准日志及结构化信息的采集服务接口实现对接，包括但不限于Syslog、SNMP Trap、WMI等，可采集的安全要素信息包括安全事件、运行日志以及性能数据等信息。非结构化数据由一系列文档或文件采集API组成，可采集脆弱性结果、WEB/XML/文本等信息以及各类情报数据', 6075546759500924936, '2023-12-22 10:11:24', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324174401467451398, 6315492542361503751, '高性能日志采集', 6315490376154482697, 4, '系统综合采用多种技术手段，充分适应项目实际网络环境的运行情况，采集项目网络中分散在各个位置的各种厂商、各种类型的海量日志。系统内置了对业界大部分常见厂商和设备类型的日志支持，对于目前暂不支持的管理对象，系统还提供了方便灵活的扩展机制。只要获得管理对象的日志样本以及通讯协议方式，编写一份XML格式日志解析文件，导入系统，即可获得对该管理对象的日志采集能力，无需编码。\n为了最大程度地采集各种厂商、各种类型的日志信息，系统没有强求管理对象必须具备什么日志协议，而是支持通过多种协议方式采集日志。这些协议包括并不仅限于：Syslog、SNMP Trap、FTP、OPSEC LEA、NETBIOS、ODBC、WMI、Shell脚本、VIP、Web Service等等。\n系统的云网端一体化安全运营与管理平台自带日志采集功能，同时也支持在项目网络中分布式部署多个日志采集器，就近采集管理对象的日志信息，并进行日志的范式化、分类、过滤和归并，然后汇聚到管理中心，从而实现对分散管理对象的日志采集，并有效降低网络中日志流的带宽占用', 6075546759500924936, '2023-12-22 10:11:24', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324174401467451399, 6315492542361503751, '面向实战和场景化的威胁分析', 6315490487890741252, 5, '在汇集了海量多方位安全要素信息的基础上，云网端一体化安全运营与管理平台将综合这些数据，面向总体安全态势的认知和监测进行数据的融合、关联分析和发掘分析。这其中包括对资产及业务对象收到攻击威胁和自身风险程度的分析、复杂攻击的攻击过程及攻击目标分析、攻击的危害及影响范围分析、攻击威胁溯源分析、外部威胁情报与内部安全信息比对分析等。这些分析处理工作将为上层态势呈现提供数据和计算任务的支撑', 6075546759500924936, '2023-12-22 10:11:24', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324183185145333764, 6315517203493292032, '安全技术专家团队', 6315513194577528840, 1, '天眼MDR安全托管服务具备多级安全专家团队，云端一线安全分析工程师提供实时检测分析、快速响应、事件管理；二线安全运营经理提供专业技术支持、过程质量把关、快速审核；三线安全专家提供复杂技术研判指导、制定最佳方案、组织事件复盘；本地安全服务工程师不定期上门安全处置', 6075546759500924936, '2023-12-22 10:15:46', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324183185178888195, 6315517203493292032, '全天候实时响应服务', 6315513454087505927, 2, '提供从监测、分析到处置的闭环式托管安全服务，具体地：\n1）通过远程收集网络设备的告警日志等数据，利用大数据存储、高级威胁分析技术，对各类安全数据进行关联分析和深入挖掘。\n2）通过7X*24小时全面、持续的安全监测，及时发现各类网络攻击行为、系统脆弱性事件，并对安全事件进行通报预警及可视化呈现，一旦发生安全事件，安全专家对安全事件进行分析及研判，第一时间通知用户，协助用户对安全事件进行缓解与处置等响应动作。\n3）通过全天候的实时响应服务，帮助用户真正实现安全无忧', 6075546759500924936, '2023-12-22 10:15:46', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324183185212442633, 6315517203493292032, '海量的威胁情报', 6315513532671985672, 3, '拥有业界一流的威胁情报挖掘能力，威胁情报的生产、交换、精准匹配能力都是国内顶尖水平。拥有全球独有的样本库、主防库、互联网域名信息库、拥有最大的存活网址库、最大的中文漏洞库以及其他100多家第三方数据源情报信息。基于海量的互联网及安全大数据、利用机器学习和安全研究双引擎驱动，奇安信可以提供全方位的威胁情报能力，其中包括战术情报、作战情报和战略情报，供安全产品、安全运营/事件响应团队、CSO各层面使用，完善对关键威胁的检测、分析/响应和预警、预防能力。借助于威胁情报中的APT类情报，还可对国际上30多个APT组织的入侵和活动行为进行跟踪，拥有极高的威胁识别准确度，可更早地帮助用户发现各类威胁', 6075546759500924936, '2023-12-22 10:15:46', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324186448582281224, 6315524044101126148, '贴合行业有针对性', 6315521808839084040, 1, '以国家政策为引导，行业标准为靶向，协助企业在短时间内有针对性的掌握数据安全风险详情', 6075546759500924936, '2023-12-22 10:17:23', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324186448615835651, 6315524044101126148, '高质量低投入', 6315521890477017091, 2, '专家以咨询+技术检测为方式，问题切入点准确，不会盲目通过技术工具进行扫描，对业务影响小且实施周期短', 6075546759500924936, '2023-12-22 10:17:23', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324186448649390085, 6315524044101126148, '经验丰富的评估团队', 6315521953861339139, 3, '具备丰富的数据安全理论+实践经验，以专业的基本合规思路，配备技术型工具辅助，提供高质量服务', 6075546759500924936, '2023-12-22 10:17:23', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324188207304280072, 6315530435046017028, '贴合行业有针对性', 6315527365083924485, 1, '以国家政策为引导，行业标准为靶向，协助企业在短时间内有针对性的掌握数据安全风险详情', 6075546759500924936, '2023-12-22 10:18:15', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324188207337834497, 6315530435046017028, '高质量低投入', 6315528081437493250, 2, '专家以咨询+技术检测为方式，问题切入点准确，不会盲目通过技术工具进行扫描，对业务影响小且实施周期短', 6075546759500924936, '2023-12-22 10:18:15', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324188207337834498, 6315530435046017028, '经验丰富的评估团队', 6315528501236992009, 3, '具备丰富的数据安全理论+实践经验，以专业的基本合规思路，配备技术型工具辅助，提供高质量服务', 6075546759500924936, '2023-12-22 10:18:15', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324192484789717001, 6315544913951197185, '高级威胁的全面检测分析', 6315538833720346626, 1, '攻击威胁分析系统内置ATT&CK模型和可视化狩猎分析工具，全面助力已知威胁的快速发现和未知威胁的自主拓线分析', 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324192484823271425, 6315544913951197185, '重大安全事件的快速响应', 6315538923411343362, 2, '基于威胁情报的上下文，攻击威胁分析系统可以帮助安全运营人员发现、研判和处置重大安全事件，如：永恒之蓝、APT事件、NotPetya、Marai Botnet。\n网络攻击的回溯和分析\n攻击威胁分析系统还原和存储网络流量的元数据，可以帮助客户回溯已经发生网络攻击行为，分析攻击路径、受感染面和信息泄露状况', 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324192484823271426, 6315544913951197185, '灵活有效的编排处置', 6315539008203393028, 3, '攻击威胁分析系统通过openc2接口与处置设备联动，通过预置场景和自定义分析场景应对各类告警事件，根据业务需求量身打造处置流程，提升响应处置的速度与准确性', 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324192484856825856, 6315544913951197185, '满足新等保的合规要求', 6315539093867857926, 4, '攻击威胁分析系统满足了新等保2.0对网络攻击检测和分析要求，特别是针对未知的新型网络攻击和APT攻击', 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324192484890380292, 6315544913951197185, '丰富多样的可视化展示', 6315539174499158020, 5, '攻击威胁分析系统内置多款态势大屏，支持将发现的威胁态势在4K的屏幕上投屏展示，满足日常巡检需求', 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324192484890380293, 6315544913951197185, '完备的云上部署方案', 6315539273484732419, 6, '完全标准化的云上部署方案，可支持对用户的纯云上业务的虚拟机之间的横向以及进出虚拟机的纵向流量进行监测', 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324195753595373570, 6315470047973607431, '随时随地安全办公', 6315468623554414599, 1, '通过零信任架构收缩业务暴露面，避免业务直接暴露在互联网，满足出差、外勤、运维、外包、小型分支等利用PC端或移动端开展远程办公。不仅能够最大程度降低对用户体验的损害，确保用户能够方便地接入业务，还能有效阻断来自终端的风险，避免远程办公用户成为风险入口。通过严格的有效的数据防泄露手段，避免业务重要的数据对外泄露', 6075546759500924936, '2023-12-22 10:22:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324195753595373571, 6315470047973607431, '简化运维', 6315468708413573120, 2, '通过零信任架构的落地确保认证更方便，访问速度快，结合有端无端一体化技术，获得与内网接近一致的访问体验，并且性能好，支持大并发场景，技术产品能够全天候无间断工作，确保远程办公的高效、稳定、可靠。同时考虑远程办公涉及大量的用户、终端、组织架构、业务等，能够结合企业自身的流程自动化系统，降低运维管理成本', 6075546759500924936, '2023-12-22 10:22:00', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324203422091708423, 6315535740572141568, '丰富的项目经验', 6324155361743210501, 1, '具有组织参与多次不同形式、不同规模成功应急演练案例', 6075546759500924936, '2023-12-22 10:25:49', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324203422125262850, 6315535740572141568, '强大的的安全服务团队', 6324156689055877129, 2, '安全服务团队拥有大量的经验丰富安全专家，可支撑各种场景的应急演练工作', 6075546759500924936, '2023-12-22 10:25:49', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324211649168672773, 6315549679519401991, '完善的产品解决方案', 6315548119204759552, 1, '基于发现、检测、防护、响应这样一个完整闭环的安全模型进行设计，实现API安全可见、可管、可控', 6075546759500924936, '2023-12-22 10:29:54', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324211649202227207, 6315549679519401991, '丰富多样的可视化能力', 6315548201446672392, 2, '具备资产可视化、风险可视化、行为可视化、数据泄露可视化等多种分析维度的能力，为用户提供多元化的API安全分析功能，全面展现当前网络的API资产情况及API数据安全风险类型', 6075546759500924936, '2023-12-22 10:29:54', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324211649235781639, 6315549679519401991, '强大的数据识别能力', 6315548327477118985, 3, '支持restful、GraphQL、websocket、MQTT、gRPC、JSON-RPC、XML-RPC等API接口类型，且支持80多种文件格式类型的还原及解析，拥有丰富的数据识别指纹库，大大提升了检测的识别率和准确率', 6075546759500924936, '2023-12-22 10:29:54', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324211649235781640, 6315549679519401991, '强大的安全预警能力', 6315548405625391111, 4, '通过API智能识别、数据安全智能识别、漏洞利用检测、未知威胁检测、API访问控制等安全防护技术帮助客户解决API资产管理、敏感数据泄露、API异常行为、攻击威胁等API安全问题， 大大增强了API接口的防护能力，做到提前预警及快速响应，以降低恶意攻击行为对内网造成更多的影响和损失，及时止损', 6075546759500924936, '2023-12-22 10:29:54', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324215667513231370, 6315505555374213120, '多擎查杀', 6315505137319544832, 1, '天擎内置云查杀（QCE）、猫头鹰（QOWL）、海狮人工智能（QDE）及天狗等多款防病毒及主动防御引擎。基于深厚的攻防技术及安全大数据储备，各引擎具备领先的病毒查杀及攻击防御能力', 6075546759500924936, '2023-12-22 10:31:54', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324215667546785796, 6315505555374213120, '基于多维属性的动态访问控制', 6315505230433093639, 2, '通过多维度属性的策略引擎建立了一套闭环且持续的动态访问控制策略。通过用户属性，环境属性，行为属性，终端属性，应用属性，多维度组合，对用户使用行为、设备情况、网络状态、所处位置进行持续的监测与验证，若某一状态发生变化产生风险时，及时收缩权限。动态访问控制以最小细粒度为原则，动态控制用户的访问权限。为客户提供灵活、动态、整合的访问控制策略，保障企业业务和数据的安全访问，以确保业务系统面对风险时，可灵活的调整权限，抵御未知风险', 6075546759500924936, '2023-12-22 10:31:54', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6324215667580340227, 6315505555374213120, '运维管理简单', 6315505305796347912, 3, '产品提供了自助运维的工具，用户可以通过“网络质量实时检查（实时监测客户端到网关之间的网络质量情况）”、“终端服务诊断修复（实时监测客户端自身服务的运行情况）”、“应用资源自助诊断（自助完成应用资源连通性和权限的监测）”、“日志一键收集（客户端出现异常时用户一键收集日志）”的方式自助解决部分问题或者提供日志信息，减轻管理员的运维难度，提升管理效率', 6075546759500924936, '2023-12-22 10:31:54', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6669663868361903492, 6669663867724369287, '高效批量加密', 6669658012777975169, 1, '高效的批量处理能力，快速、高效地处理大量数据的加密需求，轻松应对数据安全挑战', 6649783043579186561, '2024-04-19 14:17:52', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6669663868663893380, 6669663867724369287, '个性化加密规则', 6669658837613022599, 2, '提供灵活的加密规则定制，对数据进行个性化加密。通过加解密规则引擎服务确定加密字段，以及采用何种加密策略，从而最大程度地保障数据的安全', 6649783043579186561, '2024-04-19 14:17:52', NULL, NULL);
INSERT INTO `product_superiority` VALUES (6669663868898774402, 6669663867724369287, '跨云数据传输保障', 6669659731402427777, 3, '加解密规则引擎提供安全可靠的密钥同步方案，无论是在线同步还是离线分量导出，确保数据在传输过程中的安全性', 6649783043579186561, '2024-04-19 14:17:52', NULL, NULL);

-- ----------------------------
-- Table structure for product_use
-- ----------------------------
DROP TABLE IF EXISTS `product_use`;
CREATE TABLE `product_use`  (
  `ID` bigint(20) NOT NULL COMMENT 'ID',
  `PRODUCT_ID` bigint(20) NOT NULL COMMENT '产品ID',
  `TITLE` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `REMARK` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
  `SORD_NUM` int(11) NOT NULL COMMENT '排序',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '产品功能' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of product_use
-- ----------------------------
INSERT INTO `product_use` VALUES (6264186615219488776, 6264186614984607747, '多应用信任域支持', '支持不同应用的证书及对应密钥的生成及存储;支持配置不同的证书信任域，证书验证策略支持配置不验证、根证书、CRL、OCSP等多种验证策略', 1, 6075546759500924936, '2023-12-01 17:35:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6264186615253043208, 6264186614984607747, '数字签名与验证', '提供基于SM2、RSA等算法的PKCS#1签名/验证、PKCS#7 Attached签名/验证、P7 Detached签名/验证功能;签名格式符合PKCS#7、GM/T0010等标准中定义的数据类型', 2, 6075546759500924936, '2023-12-01 17:35:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6264186615286597636, 6264186614984607747, '数字信封加密和解密', '提供基于SM2、RSA等算法的数字信封加密、解密功能，数字信封格式符合PKCS#7、GM/T0010等标准中定义的数据类型', 3, 6075546759500924936, '2023-12-01 17:35:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6264186615286597637, 6264186614984607747, '带签名的数字信封加密和解密', '提供基于SM2、RSA等算法的带签名的数字信封加密、解密功能，数字信封格式符合PKCS#7、GM/T0010等标准中定义的数据类型', 4, 6075546759500924936, '2023-12-01 17:35:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6264186615320152070, 6264186614984607747, '权限控制', '提供管理员、审计员、操作员多级权限，对设备的不同管理操作需不同管理人员登陆，从而具备相应的管理权限', 5, 6075546759500924936, '2023-12-01 17:35:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6264186615353706504, 6264186614984607747, '维护协议', '支持日志审计、状态监控、SNMP标准协议监控、SYSLOG外送日志、NTP时间同步等扩展功能', 6, 6075546759500924936, '2023-12-01 17:35:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6264186615387260937, 6264186614984607747, '网络协议', '支持IPV4/IPV6双栈协议', 7, 6075546759500924936, '2023-12-01 17:35:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6264218972865562633, 6264218972530018305, '签发可信时间戳', '接收用户发起的时间戳请求，签发可信时间戳', 1, 6075546759500924936, '2023-12-01 17:51:13', NULL, NULL);
INSERT INTO `product_use` VALUES (6264218972899117060, 6264218972530018305, '时间戳管理', '可配置外置数据库存储已签发的时间戳，以便日后取证', 2, 6075546759500924936, '2023-12-01 17:51:13', NULL, NULL);
INSERT INTO `product_use` VALUES (6264218972932671492, 6264218972530018305, '时间戳证书管理', '管理时间戳服务器的数字证书，生成申请服务器证书的申请文件、导入证书', 3, 6075546759500924936, '2023-12-01 17:51:13', NULL, NULL);
INSERT INTO `product_use` VALUES (6266344854002468869, 6266344853734033410, '高性能加解密', '支持包括但不限于以下算法的数据加解密： 非对称算法：RSA、SM2、EcDSA、SM9 对称算法：3DES、AES、SM1、SM4 杂凑算法：SHA1 、SHA224 、SHA256 等', 1, 6075546759500924936, '2023-12-02 11:27:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6266344857559238656, 6266344853734033410, '随机数产生', '采用由国家密码管理局审批的双物理随机数芯片生成随机数', 2, 6075546759500924936, '2023-12-02 11:27:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6266344857592793094, 6266344853734033410, '用户权限控制', '具有基于身份认证的完善的分级权限控制机制，对访问用户分级管理，提高密码设备自身的安全性', 3, 6075546759500924936, '2023-12-02 11:27:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6266344857626347528, 6266344853734033410, '支持标准接口', '提供多种国密、国际标准规范接口，可平滑接入各种业务系统，满足各类业务系统的需求', 4, 6075546759500924936, '2023-12-02 11:27:09', NULL, NULL);
INSERT INTO `product_use` VALUES (6266408977327327235, 6266408976958228482, '动态证书认证', '支持处理单双向SSL连接，并且可同时处理多种类型和多个应用的SSL加解密处理;支持OCSP自动查询、LDAP、手工上传等多种动态认证方式;支持多证书来源、多站点证书认证', 1, 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6266408977360881664, 6266408976958228482, '应用服务支持', '支持不同类型服务的SSL代理，支持四层WEB、TCP、UDP协议的服务的调度代理，支持应用服务的URL映射、协议头转发、细粒度的访问控制和基于证书的用户黑白名单配置等', 2, 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6266408977394436105, 6266408976958228482, 'SSL卸载', '通过对服务器的SSL卸载处理，节省应用系统服务器在额外任务上的性能开支，使它们专注于实际业务处理，大幅度缩短用户请求的响应时间从而极大提升了用户的访问体验', 3, 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6266408977427990535, 6266408976958228482, '连接复用', '通过将众多客户端连接请求捆绑后，复用相对较少的服务器TCP连接，不需要改变任何网络构造也不需要增加组织的硬件投资成本的情况下，减少服务器的工作负荷，提高服务器的处理能力', 4, 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6266408977427990536, 6266408976958228482, '会话保持机制', '通过会话保持技术，可以为访问用户选择曾连接上的特定服务器，实现无缝地处理用户请求;另一方面可以减少新建连接的数量，有助于减小负载均衡设备的系统开销', 5, 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6266408977461544968, 6266408976958228482, '负载均衡', '支持四层到七层的WEB/TCP/UDP服务的负载均衡配置，充分利用所有的服务器资源，有效地避免服务器处理任务“不平衡”现象', 6, 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6266408977495099393, 6266408976958228482, '日志审计', '提供全流程的日志审计，可自定义配置日志记录类型和记录等级，管理员可以时刻掌握网关使用情况和运行情况，对于任何可疑的操作都可以进行追溯查询', 7, 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6266408977495099394, 6266408976958228482, '监控警告', '对各项资源情况进行监控和展示，便于系统的维护和问题定位，支持以邮件和Syslog日志方式对系统问题进行告警', 8, 6075546759500924936, '2023-12-02 11:59:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6266465496781686786, 6266465492721600514, '数据签名与验签功能', '在移动端提供符合国标规范的数据签名功能，并利用CA颁发的数字证书完成验签功能，判断数据的真实性，可靠性', 1, 6075546759500924936, '2023-12-02 12:27:04', NULL, NULL);
INSERT INTO `product_use` VALUES (6266465500338456579, 6266465492721600514, 'CA系统业务对接', '支持对接CA系统，完成移动端的SM2证书颁发，同时可以利用CA系统固定的对接流程完成数字证书的申请、颁发、验证、注销等证书业务', 2, 6075546759500924936, '2023-12-02 12:27:05', NULL, NULL);
INSERT INTO `product_use` VALUES (6266465500405565440, 6266465492721600514, '白名单与网络隔离机制', '支持白名单与网络隔离机制，可以充分结合业务系统自身的安全策略，通过建立安全访问规则提高系统的安全性与抗风险能力', 3, 6075546759500924936, '2023-12-02 12:27:05', NULL, NULL);
INSERT INTO `product_use` VALUES (6266465500405565441, 6266465492721600514, '应用业务系统对接接口', '提供多种形式的系统对接，包括常见客户端SDK（iOS，Android、Windows、Linix、欧拉、鸿蒙）；协同签名服务端提供业务查询，证书调用等Web接口', 4, 6075546759500924936, '2023-12-02 12:27:05', NULL, NULL);
INSERT INTO `product_use` VALUES (6266465500439119877, 6266465492721600514, '丰富的管理功能', '支持日志审计功能，支持系统运行状态监控，具备完善的高危操作风险预警机制，提高系统的易用性与安全性', 5, 6075546759500924936, '2023-12-02 12:27:05', NULL, NULL);
INSERT INTO `product_use` VALUES (6266465500472674307, 6266465492721600514, '统一平台管理', '提供统一的管理平台，实现对用户密钥，用户证书的统一管理', 6, 6075546759500924936, '2023-12-02 12:27:05', NULL, NULL);
INSERT INTO `product_use` VALUES (6266607166915479555, 6266607166747707401, '多类型动态口令', '支持TOTP、HOTP、OCRA多种形式动态口令', 1, 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_use` VALUES (6266607166949033989, 6266607166747707401, '多种算法', '支持HmacSHA1、HmacSHA224、HmacSHA256、HmacSHA384、HmacSHA512、SM3、SM4、HmacSM3等多种算法', 2, 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_use` VALUES (6266607166982588418, 6266607166747707401, '完善用户管理机制', '支持管理系统添加、API接口注册和LDAP、AD同步等多种形式用户管理机制', 3, 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_use` VALUES (6266607166982588419, 6266607166747707401, '多种认证介质对接', '提供各种常用平台（IOS、Android）的SDK，可以支持多种认证介质对接', 4, 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_use` VALUES (6266607167016142852, 6266607166747707401, '支持多租户场景', '支持多租户场景，租户之间数据隔离', 5, 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_use` VALUES (6266607167016142853, 6266607166747707401, '日志审计', '管理员所有关键操作均会记录审计日志，方便审计员进行查询与审计', 6, 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_use` VALUES (6266607167049697289, 6266607166747707401, '监控服务', '支持对口令验证、验证服务器连接情况等内容进行监控统计，并提供相关展示页面', 7, 6075546759500924936, '2023-12-02 13:37:27', NULL, NULL);
INSERT INTO `product_use` VALUES (6266641228724045831, 6266641228556273670, '丰富的加密模式', '支持文件系统级数据加密，数据库透明加密与NAS共享目录的加密，实现多台服务器透明访问NAS共享目录的密文', 1, 6075546759500924936, '2023-12-02 13:54:22', NULL, NULL);
INSERT INTO `product_use` VALUES (6266641228757600256, 6266641228556273670, '加解密服务器管理', '可以添加多个文件加密和nas加密服务器，对指定目录进行加解密操作', 2, 6075546759500924936, '2023-12-02 13:54:22', NULL, NULL);
INSERT INTO `product_use` VALUES (6266641228791154696, 6266641228556273670, '服务管理', '内置密钥管理服务，启用SSL通信和认证客户端证书等功能，监控服务运行状态，对于异常的服务可以自行修复', 3, 6075546759500924936, '2023-12-02 13:54:22', NULL, NULL);
INSERT INTO `product_use` VALUES (6266641228791154697, 6266641228556273670, '访问控制', '支持对加密文件的访问控制，实现对用户、用户组和进程的访问控制，可以限制管理员等特权用户访问加密数据', 4, 6075546759500924936, '2023-12-02 13:54:22', NULL, NULL);
INSERT INTO `product_use` VALUES (6266641228824709128, 6266641228556273670, '集中密钥和策略管理', '连接密钥管理系统实现密钥和加密策略管理，用户从密钥管理系统管理平台生成密钥和加密策略，下发到存储加密客户端，进行加密和策略执行', 5, 6075546759500924936, '2023-12-02 13:54:22', NULL, NULL);
INSERT INTO `product_use` VALUES (6266641228858263554, 6266641228556273670, '离线保护', '支持连接密钥管理系统请求密钥，当密钥管理服务离线时，可关闭加密磁盘的访问，实现加密磁盘的增强保护', 6, 6075546759500924936, '2023-12-02 13:54:22', NULL, NULL);
INSERT INTO `product_use` VALUES (6266684872134887429, 6266684871933560837, '数字证书安全管理', '证书的全生命周期进行管理，包括证书的申请、发布、下载、注销、更新、冻结/解冻、延期、密钥恢复等，依据证书的不同用途或者类型，系统还支持设定不同的证书模板', 1, 6075546759500924936, '2023-12-02 14:16:02', NULL, NULL);
INSERT INTO `product_use` VALUES (6266684872168441859, 6266684871933560837, '体系管理', '包括RA的增删改查，支持对互信的交叉CA进行添加、证书链管理，以及查询、注销等操作', 2, 6075546759500924936, '2023-12-02 14:16:02', NULL, NULL);
INSERT INTO `product_use` VALUES (6266684872201996295, 6266684871933560837, 'CRL管理', '设置“CRL生成设置”后可进行CRL签发，管理功能包括CRL策略设置、新的CRL签发和CRL下载', 3, 6075546759500924936, '2023-12-02 14:16:02', NULL, NULL);
INSERT INTO `product_use` VALUES (6271981772388042757, 6266438884426123271, '加密对象全生命周期管理', '提供对称密钥、非对称密钥、数字证书等加密对象的状态管理和属性管理。完成对加密对象的生成、存储、激活、分发、更新、注销、销毁和删除等全生命周期管理操作及加密属性的获取、添加、修改和删除等操作', 1, 6075546759500924936, '2023-12-04 10:07:02', NULL, NULL);
INSERT INTO `product_use` VALUES (6271981772421597187, 6266438884426123271, '多种算法密钥管理', '支持SM1、SM4、AES、3DES、ZUC等对称算法密钥的生成与管理;支持SM2、SM9、RSA、ECDSA等非对称算法密钥的生成与管理;支持HMAC-SM3、HMAC-SHA512等密钥生成与管理', 2, 6075546759500924936, '2023-12-04 10:07:02', NULL, NULL);
INSERT INTO `product_use` VALUES (6271981772455151616, 6266438884426123271, '密钥安全下发', '客户端业务系统密钥获取操作支持SSL数字证书、密钥用户名和口令及wrapping key等认证及加密方式，几种方式可灵活组合配置。避免接口通信信息泄露、中间人攻击、重放攻击等可能性', 3, 6075546759500924936, '2023-12-04 10:07:02', NULL, NULL);
INSERT INTO `product_use` VALUES (6271981774837516295, 6266438884426123271, '丰富的开发接口', '支持KMIP和REST密钥管理接口，同时支持GM/T 0018密码设备应用接口规范、和REST等密码运算标准接口', 4, 6075546759500924936, '2023-12-04 10:07:02', NULL, NULL);
INSERT INTO `product_use` VALUES (6271981774871070720, 6266438884426123271, '服务管理', '内置密钥管理服务，启用SSL通信和认证客户端证书等功能，监控服务运行状态，对于异常的服务可以自行修复', 5, 6075546759500924936, '2023-12-04 10:07:02', NULL, NULL);
INSERT INTO `product_use` VALUES (6271997206655010818, 6266625578366535681, '数据库机密性保护', '支持对数据库敏感数据使用国密算法进行机密性保护，在数据写入数据库前自动加密，在从数据库读取数据时自动解密，加解密过程对业务应用透明，应用无需改动代码即可实现对敏感数据的加密保护', 1, 6075546759500924936, '2023-12-04 10:14:42', NULL, NULL);
INSERT INTO `product_use` VALUES (6271997206688565248, 6266625578366535681, '数据库完整性保护', '支持对数据库敏感数据使用国密算法进行完整性保护，在数据写入数据库前自动生成完整性校验值，在需要时校验数据的完整性，完整性保护过程对业务应用透明，应用无需改动代码即可实现对敏感数据的完整性保护', 2, 6075546759500924936, '2023-12-04 10:14:42', NULL, NULL);
INSERT INTO `product_use` VALUES (6271997206722119682, 6266625578366535681, '支持模糊查询', '支持选择加密后密文仍然可以支持模糊查询功能的加密模式', 3, 6075546759500924936, '2023-12-04 10:14:42', NULL, NULL);
INSERT INTO `product_use` VALUES (6271997206722119683, 6266625578366535681, '数据库动态脱敏', '支持依据指定的脱敏规则，对数据库返回的数据进行专门的遮盖、替换，确保生产环境的敏感数据能够得到保护', 4, 6075546759500924936, '2023-12-04 10:14:42', NULL, NULL);
INSERT INTO `product_use` VALUES (6271997206755674116, 6266625578366535681, '敏感数据识别', '内置敏感信息识别规则库和识别引擎，支持自动识别数据库中的敏感数据，以辅助用户制定数据库加密规则。产品支持用户自定义敏感信息识别规则，用户可根据业务需要和行业特征增加相应识别规则', 5, 6075546759500924936, '2023-12-04 10:14:42', NULL, NULL);
INSERT INTO `product_use` VALUES (6271997206789228552, 6266625578366535681, '安全的密钥管理', '用户可针对于不同的表/列配置不同的密钥，所有密钥通过数据库加密机集中管理。数据库加密机支持使用三方密钥管理系统中的密钥保护数据库数据安全', 6, 6075546759500924936, '2023-12-04 10:14:42', NULL, NULL);
INSERT INTO `product_use` VALUES (6272538039344826372, 6266660668450670596, '电子印章全生命周期管理', '电子签章管理服务实现电子印章从制作到使用授权、挂失、销毁直至使用审计等全生命周期管理', 1, 6075546759500924936, '2023-12-04 14:43:20', NULL, NULL);
INSERT INTO `product_use` VALUES (6272538039411935233, 6266660668450670596, '数字证书管理', '支持将CA签发的 USBKey 数字证书注册到签章管理系统中，同时支持对接 CA 系统，完成 USBKey 数字证书申请及导入，并具备对已注册数字证书的管理', 2, 6075546759500924936, '2023-12-04 14:43:20', NULL, NULL);
INSERT INTO `product_use` VALUES (6272538039445489669, 6266660668450670596, '系统审计管理', '对电子印章服务生成的日志信息进行审计，可提供印章申请、制作过程的审计，可提供对电子签章用章和维护管理操作审计， 并支持按照用户、日期、操作结果等进行分类查询与统计', 3, 6075546759500924936, '2023-12-04 14:43:20', NULL, NULL);
INSERT INTO `product_use` VALUES (6272538039479044103, 6266660668450670596, '印章自助管理', '为用户提供自助印章注册和管理功能，用户可管理印章信息、印章模版等', 4, 6075546759500924936, '2023-12-04 14:43:20', NULL, NULL);
INSERT INTO `product_use` VALUES (6324183185245997057, 6315517203493292032, '服务内容', '安全运营服务是通过远程的方式为用户提供资产、威胁、脆弱性等方面的综合分析服务，包括系统、主机存在的安全问题，进行安全事件研判，并对事件进行深度溯源分析，对用户资产造成的威胁及时通知用户，并协助进行安全加固', 1, 6075546759500924936, '2023-12-22 10:15:46', NULL, NULL);
INSERT INTO `product_use` VALUES (6324186448682944521, 6315524044101126148, '服务内容', '按照国家、行业数据安全要求，结合数据资产重要性，对重要数据涉及的采集、存储、使用、提供等活动可能存在的安全风险进行评估，识别数据安全风险，发现单位数据安全防护短板，提升数据安全管控能力', 1, 6075546759500924936, '2023-12-22 10:17:23', NULL, NULL);
INSERT INTO `product_use` VALUES (6324188207371388934, 6315530435046017028, '服务内容', '按照国家法律法规要求，参考相关国家标准，分析信息资产存在的安全漏洞、面临的安全威胁及威胁发生的可能性，检查现有安全措施的有效性，识别信息资产中存在的安全风险点，对用户信息资产所面临的风险程度做出准确的评价，提供相关整改修复加固建议', 1, 6075546759500924936, '2023-12-22 10:18:15', NULL, NULL);
INSERT INTO `product_use` VALUES (6324192484923934729, 6315544913951197185, '流量还原', '攻击威胁分析传感器通过对网络流量进行解码还原出真实流量，提取网络层、传输层和应用层的头部信息，甚至是重要负载信息，这些信息将通过加密通道传送到攻击威胁分析分析平台进行统一处理。攻击威胁分析传感器中应用的自主知识产权的协议分析模块，可以在IPv4/IPv6网络环境下，支持 HTTP （网页）、SMTP/POP3（邮件）等主流协议的高性能分析', 1, 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_use` VALUES (6324192484957489155, 6315544913951197185, '高级威胁检测', '攻击威胁分析具备高级威胁检测能力。基于全球数百个威胁情报源和多个安全研究团队的APT事件发现、跟踪成果，运用威胁情报、文件虚拟执行、智能规则引擎、机器学习等技术，攻击威胁分析系统可以检测和发现高级网络攻击和新型网络攻击，涵盖：APT攻击、勒索软件、远控木马、僵尸网络、窃密木马、间谍软件、网络蠕虫、邮件钓鱼等高级攻击，并基于可视化技术，清晰的展示网络中的威胁。 攻击威胁分析流量传感器内置的威胁检测引擎，除了高级威胁检测能力之外，还可检测多种网络协议中的攻击行为，提供网页漏洞利用、webshell上传、网络攻击等多种维度的告警展示，可检测如僵木蠕毒、溢出攻击、拒绝服务、间谍软件、端口扫描、网络钓鱼等多种网络攻击行为，也可检测如SQL注入、XSS、Webshell、代码执行、命令执行、文件包含等多种Web攻击行为，内置的Webshell沙箱和Webshell机器学习模块可以精准检测PHP、ASP、JSP(X)等后门并记录相关信息。 攻击威胁分析传感器拥有威胁情报实时匹配能力，能发现恶意软件、APT事件等威胁，产生的多种告警都会加密，并传输给攻击威胁分析分析平台进行统一分析管理', 2, 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_use` VALUES (6324192484957489156, 6315544913951197185, '日志检索', '攻击威胁分析基于搜索引擎技术构建流量行为日志检索与存储，在本地数据的存储和检索方面，使用大数据平台做为平台基础，并配套了大量的检索和分析功能以对数据做到高效分析。 针对不同使用场景和不同技术水平的用户需求，日志检索模块分为快捷模式、高级模式、专家模式的检索功能，提供告警日志、网络日志、终端日志与第三方日志检索的功能。快捷模式快捷模式只需要填充胶囊字段的值，即可进行基于某一类告警数据的搜索；高级搜索兼容lucene语句进行搜索，在输入框内输入查询语句进行基于多种告警数据的搜索；专家模式专家模式为SPL命令语句搜索，用于专家用户对数据进行统计并支持各种可视化视图展示', 3, 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_use` VALUES (6324192484991043592, 6315544913951197185, '响应处置', '威胁处置能力在信息安全建设中具有重要作用，攻击威胁分析系统为完善威胁分析后续的处置闭环，引入了响应处置能力，以模块化形式在攻击威胁分析系统内置了一套自动化编排响应模型，通过标准的API/openc2接口与处置设备联动，连接畅通的情况下支持自动/手动方式的响应指令下发。主要实现的功能是根据告警信息对相应（不同厂商不同功能）的设备构建完整的响应处置工作流进行联动与处置，实现安全设备间的协同防御', 4, 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_use` VALUES (6324192484991043593, 6315544913951197185, '深度分析', '基于SOAR的自动化处置编排能力，攻击威胁分析响应处置模块结合各类告警和日志进行攻防场景的深度分析，提炼高价值告警和威胁溯源分析拓线，并将分析结果回注攻击威胁分析系统生成新的告警。例如，我们基于远控木马的外连CC地址行为的威胁情报告警，通过SOAR的自动化编排能力来发现外连CC地址告警之后是否有持续的与CC地址的TCP通信行为来判断受害IP的受害程度，对于明确有后续通信行为的产生新的告警，这样即实现对告警的深度分析', 5, 6075546759500924936, '2023-12-22 10:20:23', NULL, NULL);
INSERT INTO `product_use` VALUES (6324195753628928008, 6315470047973607431, '终端业务准入', '管理员既可以对终端设置特定的终端环境基线，又可以针对不同业务、不同用户群体设置特定的环境检查标准，通过建设终端安全基线的形式避免被动式的危险操作', 1, 6075546759500924936, '2023-12-22 10:22:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6324195753662482435, 6315470047973607431, '接入安全', '采用L3VPN与L4VPN融合的隧道技术，兼容性更好，并且能够适应复杂网络环境，访问速度更快速。支持全流量SSL传输加密，防止通信数据被劫持解密风险', 2, 6075546759500924936, '2023-12-22 10:22:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6324195753662482436, 6315470047973607431, '访问控制管理', '支持基于用户访问环境属性、行为属性、身份属性、资源属性的综合分析来动态调整用户的访问权限，以确保业务系统面对风险时，可灵活的调整权限，抵御未知风险', 3, 6075546759500924936, '2023-12-22 10:22:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6324195753696036869, 6315470047973607431, '业务隐身', '通过前置安全代理网关，强制所有访问都必须经过认证、授权和加密。通过用户的认证行为，控制中心会给用户下发唯一的数字身份凭据，后续用户访问可信网关的所有流量，都必须带上这份数字身份凭据', 4, 6075546759500924936, '2023-12-22 10:22:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6324195753696036870, 6315470047973607431, '全面审计溯源', '可记录整个平台范围内用户访问行为日志和事件数据，支持管理员对历史访问行为的日常审计和溯源分析，方便管理员快速识别企业员工日常访问中潜在的恶意威胁活动，可帮助用户明显地降低受到来自外界和内部的恶意侵袭的风险', 5, 6075546759500924936, '2023-12-22 10:22:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6324195753729591298, 6315470047973607431, '集群高可用', '控制中心和可信网关均支持集群部署架构，该集群架构中的所有节点都可以承载业务，集群中的某个节点故障后，该故障节点的流量会均衡转移到集群中的其他正常节点继续提供服务，防止服务端故障后导致服务不可用的情况', 6, 6075546759500924936, '2023-12-22 10:22:00', NULL, NULL);
INSERT INTO `product_use` VALUES (6324203422158817281, 6315535740572141568, '服务内容', '按照网络安全法、数据安全法等法律法规要求，参考国家应急演练标准以安全事件攻击、发现与应急响应处置为流程，开展应急演练工作，加强单位网络安全应急响应能力建设，提高应急响应工作水平。通过应急演练，针对事先设置的突发事件情景及其后续的发展情景，通过实际决策、行动和操作，完成真实应急响应的过程，从而检验和提高相关人员的临场组织指挥、队伍调动、应急处置技能和后勤保障等应急能力', 1, 6075546759500924936, '2023-12-22 10:25:49', NULL, NULL);
INSERT INTO `product_use` VALUES (6324215667580340228, 6315505555374213120, '终端安全', '环境感知：包含运行基础环境、安全基线核查、健康状态及物理环境感知等能力，为终端安全提供实时状态信息', 1, 6075546759500924936, '2023-12-22 10:31:54', NULL, NULL);
INSERT INTO `product_use` VALUES (6324215667613894664, 6315505555374213120, '业务隐藏与保护', '网络隐身：通过访问代理对应用资源和服务进行业务隐藏，实现业务暴露面的收缩，同时对业务访问流量进行引流和隧道加密。采用SPA单包授权机制实现端口隐藏，未授权终端无法连接服务端口。 访问代理：包括应用代理、API代理以及运维代理，保障客体安全。 传输加密：在客户端和服务器之间建立安全传输通道，确保主客体之间数据传输安全，缓解中间人攻击风险', 2, 6075546759500924936, '2023-12-22 10:31:54', NULL, NULL);
INSERT INTO `product_use` VALUES (6324215667647449095, 6315505555374213120, '动态访问控制', '自适应认证：将访问主体（人员、设备和应用）进行数字化身份管理，实现主体组合认证和自适应动态认证。先完成设备认证，再进行用户认证，都成功后才能连接应用，实现层层管控。     动态授权：在RBAC的基础上，叠加IP、时间、设备、地理位置和设备安全要求等属性，实现基于ABAC的属性授权；基于历史行为和访问基线等上下文信息，在RBAC岗位角色授权的基础上，进行权限动态过滤。 风险响应：针对访问主体在访问中存在的风险信息、信任度变化和其他异常，触发相应的指令，如二次认证、退出登录、账号锁定、冻结权限等，实现动态的访问控制能力', 3, 6075546759500924936, '2023-12-22 10:31:54', NULL, NULL);
INSERT INTO `product_use` VALUES (6324299701534722053, 6315549679519401991, 'API检测与识别', 'API安全检测系统通过旁路接入方式获取网络镜像流量，根据内置规则检测识别网络流量中的业务API、公共组件API以及第三方API，支持检测识别Restful、SOAP、GraphQL、websocket、MQTT、gRPC、JSON-RPC、XML-RPC等多种类型的API。', 1, 6075546759500924936, '2023-12-22 11:13:38', NULL, NULL);
INSERT INTO `product_use` VALUES (6324299701568276485, 6315549679519401991, 'API漏洞利用攻击检测', '系统内置API漏洞利用攻击检测规则，支持对OWASP API Security TOP 10对象授权失效、用户身份认证失效、过度数据暴露、资源访问及访问速率缺少限制、功能授权失效等API安全风险检测', 2, 6075546759500924936, '2023-12-22 11:13:38', NULL, NULL);
INSERT INTO `product_use` VALUES (6324299701568276486, 6315549679519401991, '异常行为检测', '系统根据内置的高频访问、非工作时间访问、敏感数据过度下载等多个异常行为检测模型检测识别API访问异常行为，针对API异常行为通过API所属业务应用、是否存在敏感数据访问以及访问敏感数据API排列等多维度进行分析', 3, 6075546759500924936, '2023-12-22 11:13:38', NULL, NULL);
INSERT INTO `product_use` VALUES (6324299701601830919, 6315549679519401991, '敏感数据检测', '系统内置个人信息、商业敏感信息、金融信息、企业信息等多种敏感信息识别规则。敏感数据特征库支持在线升级和更新，同时系统支持根据业务需要自定义敏感数据识别规则。 API检测系统以及API防护系统根据内置规则通过关键字识别、正则表达式、文件指纹以及语义分析等多种技术识别敏感数据包括敏感数据所属类型、敏感数据等级、敏感数据数据量等，支持对结构化、半结构化、非结构化数据识别他，系统讲识别敏感数据上传到API分析与管理系统', 4, 6075546759500924936, '2023-12-22 11:13:38', NULL, NULL);
INSERT INTO `product_use` VALUES (6324299701601830920, 6315549679519401991, '敏感数据流向分析', '系统对API访问行为进行审计记录API访问者IP、时间等多维信息，通过讲API访问行为信息和敏感数据信息进行关联多维分析识别敏感数据流量地区以及流量地区数据量，针对异常访问及异常地区访问进行告警', 5, 6075546759500924936, '2023-12-22 11:13:38', NULL, NULL);
INSERT INTO `product_use` VALUES (6324306100566231049, 6315492542361503751, '服务内容', '系统总体架构，包含SaaS层、PaaS层、IaaS层、数据接入层、通用层、政务云。 SaaS层：包含了PC端和移动端SaaS应用，用来给SaaS用户提供可操作、可见的用户安全管理界面。 PaaS层：包含了业务平台、技术平台相关的技术服务，业务平台为整个系统核心业务模块，提供业务核心能力，后端采用Spring Cloud Alibaba微服务的解决方案实现，前端采用vue2.0+element组件库实现；技术平台是整个平台的技术基础支撑，包括提供数据存储、数据处理、消息路由、容器虚拟化等基础技术服务。 IaaS层：云服务器、物理服务器等IaaS层上的应用，为上层提供硬件资源支撑。 数据接入层：对接政务云的数据，目前通过logstash数据管道和定时任务拉取的方式采集数据，将数据采集后转发到消息中间件。 政务云：信息系统可部署在5家云，华为云、阿里云、腾讯云、紫光云和浪潮云。 通用层：针对平台的所有用户采用统一认证和鉴权管理；对所有后台服务的JVM、堆栈内存、IO、请求链路等使用统一运维监控系统进行监控，对后台系统产生的系统日志统一使用日志服务管理，便于后续系统问题的追踪和定位', 1, 6075546759500924936, '2023-12-22 11:16:49', NULL, NULL);
INSERT INTO `product_use` VALUES (6669663869133655432, 6669663867724369287, '高性能加解密', '支持SM2算法，通过精心优化和卓越的设计，无论海量数据还是复杂的加解密场景都可以为数据安全保驾护航', 1, 6649783043579186561, '2024-04-19 14:17:52', NULL, NULL);
INSERT INTO `product_use` VALUES (6669663869469199752, 6669663867724369287, '智能管理', '提供全面的数据监控和密钥管理功能，随时掌握数据安全状况，及时应对潜在风险', 2, 6649783043579186561, '2024-04-19 14:17:52', NULL, NULL);
INSERT INTO `product_use` VALUES (6669663869670526336, 6669663867724369287, '跨云密钥同步', '提供安全可靠的密钥同步方案，确保跨云传输数据时的密钥安全', 3, 6649783043579186561, '2024-04-19 14:17:52', NULL, NULL);
INSERT INTO `product_use` VALUES (6669663869905407362, 6669663867724369287, '灵活的加密规则', '提供个性化的加密规则定制服务，满足不同级别数据的加密需求', 4, 6649783043579186561, '2024-04-19 14:17:52', NULL, NULL);

-- ----------------------------
-- Table structure for service_product
-- ----------------------------
DROP TABLE IF EXISTS `service_product`;
CREATE TABLE `service_product`  (
  `ID` bigint(20) NOT NULL COMMENT '主键',
  `PRODUCT_TYPE` int(11) NOT NULL COMMENT '服务类型;1：密码服务；2：密码支持服务；3：密码组合服务',
  `DATA_TYPE` int(11) NOT NULL COMMENT '数据类型;1：正式数据；2：草稿数据',
  `PRODUCT_NAME` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品名称',
  `PRODUCT_ICON_ID` bigint(20) NULL DEFAULT NULL COMMENT '产品图标;图标地址',
  `SALES_VOLUME` int(11) NULL DEFAULT NULL COMMENT '销量',
  `PRODUCT_UNIT` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单位',
  `ANNEX_ID` bigint(20) NULL DEFAULT NULL COMMENT '附件;文件地址',
  `PDF_ANNEX_ID` bigint(20) NULL DEFAULT NULL COMMENT 'pdf附件',
  `PRODUCT_STATE` int(11) NOT NULL DEFAULT 0 COMMENT '产品状态;0:下架；1：上架',
  `REMARK` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '密码产品' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of service_product
-- ----------------------------
INSERT INTO `service_product` VALUES (6264186614984607747, 1, 1, '签名验签密码服务', 6264179982649133065, NULL, '实例/月', 6275026682573883394, 6272877428867467271, 1, '签名验签服务能够对各类电子信息数据、电子文档等提供基于数字证书的数字签名服务，可验证签名数据的真实性和有效性；支持RSA与SM2算法与PKCS1和PKCS7证书格式；满足用户在网络行为中不可否认性、信息完整性、机密性等需求。', 6075546759500924936, '2023-12-01 17:35:08', 6075546759500924936, '2023-12-01 17:35:08');
INSERT INTO `service_product` VALUES (6264218972530018305, 1, 1, '时间戳密码服务', 6264204925671704579, NULL, '实例/月', 6275027498718332932, 6272876831967676420, 1, '时间戳密码服务通过从国家标准时间发布机构获取标准时间，从第三方可信CA机构获得服务器数字证书，结合公钥加密技术、数字签名、数字证书和数据摘要等密码技术，为用户的信息数据签发权威、可信的时间戳。', 6075546759500924936, '2023-12-01 17:51:13', 6075546759500924936, '2023-12-01 17:51:13');
INSERT INTO `service_product` VALUES (6266344853734033410, 1, 1, '数据加解密服务', 6266344335653603332, NULL, '实例/月', NULL, 6272875586527823882, 1, '数据加解密服务能够提供高速率、高并发的密码运算，满足数据的签名/验证、加密/解密的要求，支持常见的国密、国际算法，提供标准的REST服务接口，适用各类密码安全应用系统，保证传输信息的机密性、完整性和有效性。', 6075546759500924936, '2023-12-02 11:27:09', 6075546759500924936, '2023-12-02 11:27:09');
INSERT INTO `service_product` VALUES (6266408976958228482, 1, 1, 'SSL网关服务', 6266369783368386568, NULL, '实例/月', 6275030634883319809, 6272874657338492929, 1, 'SSL VPN综合安全网关服务实现了完整的远程接入，允许远程设备可控地访问局域网，以及多个远程设备的互相访问。并利用国家密码管理局审批的密码算法对建立的链路进行安全的加密，为用户提供安全上网和安全接入的保障。', 6075546759500924936, '2023-12-02 11:59:00', 6075546759500924936, '2023-12-02 11:59:00');
INSERT INTO `service_product` VALUES (6266438884426123271, 1, 1, '密钥管理服务', 6271970536686487556, NULL, '实例/月', 6275036510365026306, 6272868122512197634, 1, '密钥管理服务支持包括对称密钥、非对称密钥、数字证书和秘密数据等多种加密对象的统一管理，从而简化密钥管理操作，使加密变得更易于配置和管理，减少了密钥管理系统的维护成本，满足企业多应用多业务场景的密钥管理需求。', 6075546759500924936, '2023-12-04 10:07:02', 6075546759500924936, '2023-12-04 10:07:02');
INSERT INTO `service_product` VALUES (6266465492721600514, 1, 1, '协同签名服务', 6266444254947182593, NULL, '实例/月', 6275031621618501641, 6272873605675485184, 1, '协同签名服务依托于密钥分割、协作签名的专利技术自主研发，能够有效解决移动互联网中安全的数字签名的痛点，用户可以在无需任何额外硬件介质的条件下，为移动端提供安全合规的数字签名。', 6075546759500924936, '2023-12-02 12:27:04', 6075546759500924936, '2023-12-02 12:27:04');
INSERT INTO `service_product` VALUES (6266607166747707401, 1, 1, '动态令牌认证服务', 6266600436668172293, NULL, '实例/月', 6275035653049288714, 6272872748561074183, 1, '动态令牌认证服务是在理论创新的基础上，依托标准算法技术和安全设计规范自主研发的密码产品。该服务能够有效解决移动互联网中安全认证的痛点，用户可以在无需任何额外硬件介质的条件下，为移动端提供安全合规的动态口令认证。', 6075546759500924936, '2023-12-02 13:37:27', 6075546759500924936, '2023-12-02 13:37:27');
INSERT INTO `service_product` VALUES (6266625578366535681, 1, 1, '数据库加密服务', 6271990800174417929, NULL, '实例/月', NULL, NULL, 1, '数据库加密服务是针对数据库数据安全存储的需求，基于数据库透明加密原理研发的数据库主动防御产品，具备对数据库敏感数据进行加密解密、完整性保护、基于加密的权限控制、加密密钥管理等功能，有效防止数据库明文存储引发的数据泄密，以及数据的非法窃取。', 6075546759500924936, '2023-12-04 10:14:42', 6075546759500924936, '2023-12-04 10:14:42');
INSERT INTO `service_product` VALUES (6266641228556273670, 1, 1, '文件加密服务', 6266634619071367171, NULL, '实例/月', 6275037534278518792, NULL, 1, '文件加密服务是保护文件数据的安全产品，以透明加密和访问控制技术实现对非结构化数据的安全保护，提供灵活的加密方式和细粒度的访问控制，且不影响用户原来的架构，满足不同应用场景下非结构化数据的安全需要。', 6075546759500924936, '2023-12-02 13:54:22', 6075546759500924936, '2023-12-02 13:54:22');
INSERT INTO `service_product` VALUES (6266660668450670596, 1, 1, '电子签章密码服务', 6266648856988616708, NULL, '实例/月', 6275024353896302594, 6272863998001416195, 1, '电子签章密码服务通过数字签名技术利用电子化的图形章对电子文档进行签章，保护电子文档的完整性和操作的不可否认性。主要功能包括文档签章、签章验证等，通过管理印章的整个生命周期，实现签章流程可控，保障电子文档的抗抵赖、抗伪造、防篡改特性。', 6075546759500924936, '2023-12-04 14:43:20', 6075546759500924936, '2023-12-04 14:43:20');
INSERT INTO `service_product` VALUES (6266684871933560837, 1, 1, '数字证书认证服务', 6266683409933404166, NULL, '实例/月', NULL, 6272869189845125127, 1, '数字证书认证服务以公钥基础设施（PKI）为核心，向上为应用系统提供数字证书的申请、更新、注销、发布等功能，向下封装标准的数字证书服务接口，提供各类商用密码设备连接接口，为网络应用提供身份认证、访问控制、数据保密和完整性等全面的安全服务。', 6075546759500924936, '2023-12-02 14:16:02', 6075546759500924936, '2023-12-02 14:16:02');
INSERT INTO `service_product` VALUES (6315470047973607431, 2, 1, '政务外网安全接入', 6315449961585969155, NULL, '授权/月', NULL, NULL, 1, '安全接入平台提供构建零信任安全体系的基础产品组件和整体解决方案，满足内/外网访问、多分支接入、业务上云、移动办公等多种业务场景的安全接入需求。', 6075546759500924936, '2023-12-19 10:07:54', 6075546759500924936, '2023-12-22 10:22:00');
INSERT INTO `service_product` VALUES (6315492542361503751, 2, 1, '统一安全管理平台', 6315477173458569225, NULL, '授权/月', NULL, NULL, 1, '对接入的安全能力能够统一纳管，并对接入的安全能力能够进行防护配置，能够展示当前安全及密码服务的安全感态势并能生成安全态势报告及下载导出。', 6075546759500924936, '2023-12-19 10:19:04', 6075546759500924936, '2023-12-22 10:11:24');
INSERT INTO `service_product` VALUES (6315505555374213120, 2, 1, '终端安全管理', 6315504723324962825, NULL, '授权/月', NULL, NULL, 1, '为终端提供终端杀毒、终端环境检测、终端数据安全沙箱、统一身份认证、集中应用授权、审计与分析等功能，实现终端合规入网。', 6075546759500924936, '2023-12-19 10:25:32', 6075546759500924936, '2023-12-22 10:31:54');
INSERT INTO `service_product` VALUES (6315517203493292032, 2, 1, '安全运营服务', 6315511007667423235, NULL, '套/月', NULL, NULL, 1, '安全运营服务（Threat Detection and Response，简称：TDR）是基于先进安全理念与完善的服务体系，由安全托管中心主导运营，融合全流量数据检测分析能力、威胁情报能力以及资深攻防实战安全专家经验，通过云端安全专家团队专人专岗7x24持续保障，实现对网络中出现的各类威胁事件实时告警监测发现、深度分析研判、应急响应处置完整闭环运营工作，帮助用户及时准确感知安全威胁事件，及早采取预防措施，快速响应处置，降低可能造成的影响，最大限度避免安全损失。', 6075546759500924936, '2023-12-19 10:31:19', 6075546759500924936, '2023-12-22 10:15:46');
INSERT INTO `service_product` VALUES (6315524044101126148, 2, 1, '数据安全风险评估服务', 6315518994125555713, NULL, '系统/次', NULL, NULL, 1, '依据《数据安全法》、《网络安全审查办法》等法律法规，参考 《信息安全技术—数据安全能力成熟度模型》等标准规范，针对客户单位开展数据安全合规性评估，发现单位数据安全防护短板，提升数据安全管控能力，确保数据安全相关工作依法合规', 6075546759500924936, '2023-12-19 10:34:43', 6075546759500924936, '2023-12-22 10:17:23');
INSERT INTO `service_product` VALUES (6315530435046017028, 2, 1, '网络安全风险评估服务', 6315525541165664256, NULL, '系统/次', NULL, NULL, 1, '通过网络安全风险评估服务，帮助客户全面识别重要信息资产及其面临的威胁以及存在的漏洞弱点，在此基础上进行风险分析，根据风险等级制定整体安全略，安全解决方案，进行安全控制措施的实施，降低安全风险，防范威胁。', 6075546759500924936, '2023-12-19 10:37:54', 6075546759500924936, '2023-12-22 10:18:15');
INSERT INTO `service_product` VALUES (6315535740572141568, 2, 1, '应急演练', 6315532220141799429, NULL, '系统/次', NULL, NULL, 1, '按照网络安全法、数据安全法等法律法规要求和国家应急演练标准，通过应急演练服务检验客户单位应对网络安全事件的水平和协同配合能力；检验应急响应工作机制与应急预案是否完善。进一步加强单位网络安全应急响应能力建设，提高应急响应工作水平。', 6075546759500924936, '2023-12-19 10:40:32', 6075546759500924936, '2023-12-22 10:25:49');
INSERT INTO `service_product` VALUES (6315544913951197185, 2, 1, '攻击威胁分析', 6315536874275735554, NULL, '套/月', NULL, NULL, 1, '以攻防渗透和数据分析为核心竞争力，聚焦威胁检测和响应，为安全服务和分析人员提供监测预警、威胁检测、溯源分析和响应处置能力的高级威胁检测平台。', 6075546759500924936, '2023-12-19 10:45:05', 6075546759500924936, '2023-12-22 10:20:23');
INSERT INTO `service_product` VALUES (6315549679519401991, 2, 1, '接口数据访问控制', 6315545735565019145, NULL, '实例/月', NULL, NULL, 1, '接口数据访问控制可满足客户API资产的梳理及安全监测需求，帮助客户实现API的传输数据风险可视化、数据泄露可视化、资产可视化、行为可视化的能力，极大程度提升API的安全防护能力。', 6075546759500924936, '2023-12-19 10:47:27', 6075546759500924936, '2023-12-22 10:29:54');
INSERT INTO `service_product` VALUES (6669663867724369287, 4, 1, '加解密规则引擎', 6669656784450882951, NULL, '服务/个', NULL, NULL, 1, '提供灵活的加密规则定制，对数据进行个性化加密。通过加解密规则引擎服务确定加密字段，以及采用何种加密策略，从而最大程度地保障数据的安全。', 6649783043579186561, '2024-04-19 14:17:52', 6649783043579186561, '2024-04-19 14:17:52');

-- ----------------------------
-- Table structure for service_product_unit_quota
-- ----------------------------
DROP TABLE IF EXISTS `service_product_unit_quota`;
CREATE TABLE `service_product_unit_quota`  (
  `ID` bigint(20) NOT NULL COMMENT 'ID',
  `PRODUCT_ID` bigint(20) NOT NULL COMMENT '密码产品ID',
  `BUSI_TYPE_ID` bigint(20) NULL DEFAULT NULL COMMENT '业务类型ID=服务类型ID',
  `QUOTA_VALUE` int(11) NOT NULL COMMENT '配额值',
  `QUOTA_UNIT` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '配额单位',
  `QUOTA_TYPE` int(11) NOT NULL COMMENT '配额类型;1：网关控制； 2：密码服务控制；3：不控制',
  `REMARK` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '密码产品单位限额(330)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of service_product_unit_quota
-- ----------------------------
INSERT INTO `service_product_unit_quota`
VALUES (1, 6264186614984607747, 2, 50, 'Mbs', 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_product_unit_quota`
VALUES (2, 6264218972530018305, 5, 100, '连接数', 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_product_unit_quota`
VALUES (3, 6266344853734033410, 1, 50, 'Mbs', 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_product_unit_quota`
VALUES (4, 6266408976958228482, 11, 400, '人', 3, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_product_unit_quota`
VALUES (5, 6266438884426123271, 4, 500, '个', 2, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_product_unit_quota`
VALUES (6, 6266465492721600514, 6, 10000, '授权', 2, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_product_unit_quota`
VALUES (7, 6266607166747707401, 7, 10000, '授权', 2, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_product_unit_quota`
VALUES (8, 6266625578366535681, 8, 1, '个', 2, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_product_unit_quota`
VALUES (9, 6266641228556273670, 9, 1, '个', 2, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_product_unit_quota`
VALUES (10, 6266660668450670596, 10, 1000, '人', 3, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_product_unit_quota` VALUES (11, 6266684871933560837, 12, 5000, '授权', 3, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for tenant_to_product
-- ----------------------------
DROP TABLE IF EXISTS `tenant_to_product`;
CREATE TABLE `tenant_to_product`  (
  `ID` bigint(20) NOT NULL COMMENT '主键',
  `TENANT_ID` bigint(20) NOT NULL COMMENT '租户ID',
  `PRODUCT_ID` bigint(20) NOT NULL COMMENT '产品ID',
  `SERVICE_GROUP_ID` bigint(20) NULL DEFAULT NULL COMMENT '服务组ID',
  `STATE` int(11) NOT NULL COMMENT '状态;0:未开通；1：开通',
  `HAS_NUM` int(11) NULL DEFAULT NULL COMMENT '持有数量',
  `EXPIRATION_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '过期时间',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户的密码产品' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_to_product
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_to_product_record
-- ----------------------------
DROP TABLE IF EXISTS `tenant_to_product_record`;
CREATE TABLE `tenant_to_product_record`  (
  `ID` bigint(20) NOT NULL COMMENT '主键',
  `TENANT_TO_PRODUCT_ID` bigint(20) NOT NULL COMMENT '租户的密码产品ID',
  `EDIT_TYPE` int(11) NULL DEFAULT NULL COMMENT '编辑类型;1：持有数量；2：到期时间；3：备注',
  `EDIT_BEFORE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编辑前',
  `EDIT_AFTER` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '编辑后',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  `HMAC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'hmac值',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户的密码产品修改记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_to_product_record
-- ----------------------------

-- ----------------------------
-- Table structure for work_order
-- ----------------------------
DROP TABLE IF EXISTS `work_order`;
CREATE TABLE `work_order`  (
  `ID` bigint(20) NOT NULL COMMENT '工单id',
  `SERIAL` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '工单编号',
  `ORDER_TYPE_ID` int(11) NOT NULL COMMENT '工单类型ID',
  `TENANT_ID` bigint(20) NOT NULL COMMENT '租户id',
  `DESCRIPTION` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '问题描述',
  `PRODUCT_ID` bigint(20) NULL DEFAULT NULL COMMENT '申请密码产品id',
  `QUANTITY` int(11) NULL DEFAULT NULL COMMENT '申请数量',
  `ORGANIZATION` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申请组织单位',
  `PERSON_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '申请联系人',
  `PHONE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系人手机号',
  `EMAIL` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系人邮箱',
  `SUBMISSION_BY` bigint(20) NULL DEFAULT NULL COMMENT '提交人',
  `SUBMISSION_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提交时间',
  `PROCESSING_BY` bigint(20) NULL DEFAULT NULL COMMENT '处理人',
  `START_PROCESSING_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开始处理时间',
  `END_BY` bigint(20) NULL DEFAULT NULL COMMENT '结束人',
  `END_PROCESSING_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '处理完成时间',
  `STATUS` int(11) NULL DEFAULT NULL COMMENT '工单状态;1待处理  2处理中  3已处理 4已撤销',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of work_order
-- ----------------------------

-- ----------------------------
-- Table structure for work_order_attachment
-- ----------------------------
DROP TABLE IF EXISTS `work_order_attachment`;
CREATE TABLE `work_order_attachment`  (
  `ID` bigint(20) NOT NULL COMMENT '工单附件id',
  `FATHER_ID` bigint(20) NULL DEFAULT NULL COMMENT '工单id or 工单评论id',
  `TYPE` int(11) NULL DEFAULT NULL COMMENT '类型;1工单  2工单评论',
  `STORAGE_PATH` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '存储路径',
  `FILE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文件名称',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工单附件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of work_order_attachment
-- ----------------------------

-- ----------------------------
-- Table structure for work_order_comment
-- ----------------------------
DROP TABLE IF EXISTS `work_order_comment`;
CREATE TABLE `work_order_comment`  (
  `ID` bigint(20) NOT NULL COMMENT '工单评论id',
  `WORK_ORDER_ID` bigint(20) NOT NULL COMMENT '工单id',
  `TENANT_ID` bigint(20) NOT NULL COMMENT '租户id',
  `CONTENT` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '评论内容',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工单评论表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of work_order_comment
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
