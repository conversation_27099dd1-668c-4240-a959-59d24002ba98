package com.sansec.ccsp.common.business.dic.controller;

import com.sansec.ccsp.common.business.dic.service.DicTenantStatusService;
import com.sansec.ccsp.common.dic.request.DicTenantStatusDTO;
import com.sansec.ccsp.common.dic.request.DicTenantStatusPageDTO;
import com.sansec.ccsp.common.dic.response.DicTenantStatusVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

 /**
 * @Description: 租户状态字典表;(DIC_TENANT_STATUS)表控制层
 * <AUTHOR> x<PERSON>ojiawei
 * @Date: 2023-2-18
 */
@RestController
@RequestMapping("/ccsp/dicTenantStatus")
@Validated
public class DicTenantStatusController{
    @Resource
    private DicTenantStatusService dicTenantStatusService;

    /**
     * 分页查询
     *
     * @param dicTenantStatusPageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    public SecRestResponse<SecPageVO<DicTenantStatusVO>> find(@RequestBody DicTenantStatusPageDTO dicTenantStatusPageDTO){
        return dicTenantStatusService.find(dicTenantStatusPageDTO);
    }

    /**
     * 新增数据
     *
     * @param dicTenantStatusDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    public SecRestResponse<Object> add(@RequestBody DicTenantStatusDTO dicTenantStatusDTO){
        return dicTenantStatusService.add(dicTenantStatusDTO);
    }

    /**
     * 更新数据
     *
     * @param dicTenantStatusDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/edit")
    public SecRestResponse<Object> edit(@RequestBody DicTenantStatusDTO dicTenantStatusDTO){
        return dicTenantStatusService.edit(dicTenantStatusDTO);
    }

    /**
     * 通过主键删除数据
     *
     * @param dicTenantStatusDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/deleteById")
    public SecRestResponse<Object> deleteById(@RequestBody DicTenantStatusDTO dicTenantStatusDTO){
        return dicTenantStatusService.deleteById(dicTenantStatusDTO);
    }
}
