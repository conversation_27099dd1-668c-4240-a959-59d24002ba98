package com.sansec.ccsp.device.business.devicedic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.device.business.devicedic.convert.DicDeviceManagementStatusConvert;
import com.sansec.ccsp.device.business.devicedic.entity.DicDeviceManagementStatusPO;
import com.sansec.ccsp.device.business.devicedic.mapper.DicDeviceManagementStatusMapper;
import com.sansec.ccsp.device.business.devicedic.request.DicDeviceManagementStatusDTO;
import com.sansec.ccsp.device.business.devicedic.request.DicDeviceManagementStatusPageDTO;
import com.sansec.ccsp.device.business.devicedic.response.DicDeviceManagementStatusVO;
import com.sansec.ccsp.device.business.devicedic.service.DicDeviceManagementStatusService;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
 /**
 * @Description: 密码机使用状态字典表;(DIC_DEVICE_MANAGEMENT_STATUS)表服务实现类
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Service
@Slf4j
public class DicDeviceManagementStatusServiceImpl extends ServiceImpl<DicDeviceManagementStatusMapper, DicDeviceManagementStatusPO> implements DicDeviceManagementStatusService{
    @Resource
    private DicDeviceManagementStatusConvert dicDeviceManagementStatusConvert;
    
    /** 
     * 分页查询
     *
     * @param dicDeviceManagementStatusPageDTO 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<DicDeviceManagementStatusVO>> find(DicDeviceManagementStatusPageDTO dicDeviceManagementStatusPageDTO){
        QueryWrapper<DicDeviceManagementStatusPO> queryWrapper = Wrappers.query();
        IPage<DicDeviceManagementStatusPO> page = new Page<>(dicDeviceManagementStatusPageDTO.getPageNum(),dicDeviceManagementStatusPageDTO.getPageSize());
        IPage<DicDeviceManagementStatusPO> dicDeviceManagementStatusPOPage = baseMapper.selectPage(page, queryWrapper);

        SecPageVO<DicDeviceManagementStatusVO> dicDeviceManagementStatusPageVO = dicDeviceManagementStatusConvert.pagePOToSecPageVOPage(dicDeviceManagementStatusPOPage);
        return ResultUtil.ok(dicDeviceManagementStatusPageVO);
    }
    
    /** 
     * 新增数据
     *
     * @param dicDeviceManagementStatusDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> add(DicDeviceManagementStatusDTO dicDeviceManagementStatusDTO){
        DicDeviceManagementStatusPO dicDeviceManagementStatusPO = dicDeviceManagementStatusConvert.dtoToPo(dicDeviceManagementStatusDTO);
        dicDeviceManagementStatusPO.setId(IdGenerator.ins().generator());
        dicDeviceManagementStatusPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.insert(dicDeviceManagementStatusPO);
        return ResultUtil.ok();
    }
    
    /** 
     * 更新数据
     *
     * @param dicDeviceManagementStatusDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(DicDeviceManagementStatusDTO dicDeviceManagementStatusDTO){
        DicDeviceManagementStatusPO dicDeviceManagementStatusPO = dicDeviceManagementStatusConvert.dtoToPo(dicDeviceManagementStatusDTO);
        dicDeviceManagementStatusPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.updateById(dicDeviceManagementStatusPO);
        return ResultUtil.ok();
    }
    
    /** 
     * 通过主键删除数据
     *
     * @param dicDeviceManagementStatusDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> deleteById(DicDeviceManagementStatusDTO dicDeviceManagementStatusDTO){
        baseMapper.deleteById(dicDeviceManagementStatusDTO.getId());
        return ResultUtil.ok();
    }
}