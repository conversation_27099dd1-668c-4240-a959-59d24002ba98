package com.sansec.ccsp.pt.business.license.service;

import com.sansec.ccsp.pt.business.license.entity.LicenseUsePO;
import com.sansec.ccsp.pt.business.license.request.*;
import com.sansec.ccsp.pt.business.license.response.LicenseUnuseVO;
import com.sansec.ccsp.pt.business.license.response.LicenseUseVO;
import com.sansec.ccsp.pt.business.license.response.ServiceInfoTimePageVO;
import com.sansec.ccsp.pt.common.enums.LicenseUseStatusEnum;
import com.sansec.ccsp.servicemgt.serviceinfo.request.ServiceInfoPageDTO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * <AUTHOR> wwl
 * @description : 许可证使用表;(license_use)表服务接口
 * @date : 2023-5-8
 */
public interface LicenseUseService {
    /**
     * 分页查询
     *
     * @param licenseUsePageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<LicenseUseVO>> find(LicenseUsePageDTO licenseUsePageDTO);

    /**
     * 新增数据
     *
     * @param licenseUseDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(LicenseUseDTO licenseUseDTO);

    /**
     * 更新数据
     *
     * @param licenseUseEditDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(LicenseUseEditDTO licenseUseEditDTO);

    /**
     * 通过主键删除数据
     *
     * @param id
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(Long id);

    /**
     * 导入license文件
     *
     * @param licenseUseFileDTO
     * @return
     */
    SecRestResponse<Object> importLicense(LicenseUseFileDTO licenseUseFileDTO);

    /**
     * 解析license文件，返回内容信息
     *
     * @param licenseUseFileDTO
     * @return
     */
    SecRestResponse<Object> parseLicense(LicenseUseFileDTO licenseUseFileDTO);

    /**
     * 查询全部可用的服务license
     *
     * @return
     */
    SecRestResponse<List<LicenseUnuseVO>> findAll();

    /**
     * 根据主键更新数据状态，并更新hmac
     *
     * @param licenseUsePO
     * @param oldStatus
     * @return
     */
    SecRestResponse<Integer> updateById(LicenseUsePO licenseUsePO, LicenseUseStatusEnum oldStatus);


    /**
     * 绑定license
     *
     * @param licenseUseRelDTO
     * @return
     */
    SecRestResponse<Object> bindServer(LicenseUseRelDTO licenseUseRelDTO);

    /**
     * 绑定license
     *
     * @param serverId
     * @return
     */
    SecRestResponse<Object> bindServer(Long serverId);
    /**
     * 循环处理绑定许可
     * @param serverId
     * @param periodType
     * @return
     */
    SecRestResponse<Object> loopBindServer(Long serverId,Integer periodType);

    /**
     * 服务实例续约license
     *
     * @param licenseUseRelDTO
     * @return
     */
    SecRestResponse<Object> renewServer(LicenseUseRelDTO licenseUseRelDTO);

    /**
     * 服务实例续约随机选择一个license
     *
     * @param serverId
     * @return
     */
    SecRestResponse<Object> renewServer(Long serverId);
    /**
     * 循环处理续约许可
     * @param serverId
     * @param periodType
     * @return
     */
    SecRestResponse<Object> loopRenewServer(Long serverId,Integer periodType);
    /**
     * 检查是否存在可用的许可证
     *
     * @return
     */
    SecRestResponse<Boolean> isExistAvailableLicense();

    /**
     * 服务随机实例释放license
     *
     * @param serverId
     * @return
     */
    SecRestResponse<Object> unbindServer(Long serverId);

    /**
     * 分页查询服务有效期
     *
     * @param serviceInfoPageDTO
     * @return
     */
    SecRestResponse<SecPageVO<ServiceInfoTimePageVO>> findServerTime(ServiceInfoPageDTO serviceInfoPageDTO);

    /**
     * 统计服务license数量
     * 统计总数量、已使用数量
     *
     * @return
     */
    SecRestResponse<Object> statsLicense();
}