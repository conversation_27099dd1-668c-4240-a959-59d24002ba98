package com.sansec.ccsp.common.business.menu.entity;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 */
@Data
public class TreeSelect
{


    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 子节点 */
    private List<TreeSelect> children;

    public TreeSelect()
    {

    }
    public TreeSelect(SysMenuPO menu)
    {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public List<TreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<TreeSelect> children)
    {
        this.children = children;
    }
}
