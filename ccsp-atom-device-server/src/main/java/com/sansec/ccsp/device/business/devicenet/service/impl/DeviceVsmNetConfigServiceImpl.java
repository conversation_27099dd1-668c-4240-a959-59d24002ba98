package com.sansec.ccsp.device.business.devicenet.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.device.business.deviceinfo.entity.DeviceInfoPO;
import com.sansec.ccsp.device.business.deviceinfo.handler.dic.DeviceInfoDic;
import com.sansec.ccsp.device.business.deviceinfo.handler.response.vsmhost.VsmHostInfoResponse;
import com.sansec.ccsp.device.business.deviceinfo.mapper.DeviceInfoMapper;
import com.sansec.ccsp.device.business.deviceinfo.service.factory.CloudHsmManageService;
import com.sansec.ccsp.device.business.deviceinfo.service.factory.impl.CloudDeviceManageFactory;
import com.sansec.ccsp.device.business.deviceinfo.service.factory.impl.DeviceManageFactory;
import com.sansec.ccsp.device.business.devicenet.convert.DeviceNetDetailConvert;
import com.sansec.ccsp.device.business.devicenet.convert.DeviceVsmNetConfigConvert;
import com.sansec.ccsp.device.business.devicenet.entity.DeviceNetDetailPO;
import com.sansec.ccsp.device.business.devicenet.entity.DeviceVsmNetConfigPO;
import com.sansec.ccsp.device.business.devicenet.mapper.DeviceVsmNetConfigMapper;
import com.sansec.ccsp.device.business.devicenet.service.DeviceNetDetailService;
import com.sansec.ccsp.device.business.devicenet.service.DeviceVsmNetConfigService;
import com.sansec.ccsp.device.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.device.devicenet.request.*;
import com.sansec.ccsp.device.devicenet.response.DeviceNetAddrVO;
import com.sansec.ccsp.device.devicenet.response.DeviceNetDetailVO;
import com.sansec.ccsp.device.devicenet.response.DeviceVsmNetConfigSelectVO;
import com.sansec.ccsp.device.devicenet.response.DeviceVsmNetConfigVO;
import com.sansec.ccsp.security.util.LoginUserUtil;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.sansec.ccsp.device.common.error.SecErrorCodeConstant.*;

/**
 * @Description: 虚拟机网络配置;(DEVICE_VSM_NET_CONFIG)表服务实现类
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Service
@Slf4j
public class DeviceVsmNetConfigServiceImpl extends ServiceImpl<DeviceVsmNetConfigMapper, DeviceVsmNetConfigPO> implements DeviceVsmNetConfigService{
    @Resource
    private DeviceVsmNetConfigConvert deviceVsmNetConfigConvert;

    @Resource
    private DeviceInfoMapper deviceInfoMapper;

    @Resource
    private DeviceNetDetailService deviceNetDetailService;

    @Resource
    private DeviceNetDetailConvert deviceNetDetailConvert;


    @Resource
    private DeviceManageFactory factory;

    @Resource
    CloudDeviceManageFactory cloudDeviceManageFactory;

    public static String HOST_ID_PARAM="HOST_ID";

    /** 
     * 分页查询
     *
     * @param deviceVsmNetConfigPageDTO 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<DeviceVsmNetConfigVO>> find(DeviceVsmNetConfigPageDTO deviceVsmNetConfigPageDTO){
        QueryWrapper<DeviceVsmNetConfigPO> queryWrapper = Wrappers.query();
        if (StringUtils.isNotBlank(deviceVsmNetConfigPageDTO.getNetName())){
            //网络名称模糊查询
            queryWrapper.like("NET_NAME",deviceVsmNetConfigPageDTO.getNetName());
        }
        queryWrapper.eq(HOST_ID_PARAM,deviceVsmNetConfigPageDTO.getHostId());
        IPage<DeviceVsmNetConfigPO> page = new Page<>(deviceVsmNetConfigPageDTO.getPageNum(),deviceVsmNetConfigPageDTO.getPageSize());
        IPage<DeviceVsmNetConfigPO> deviceVsmNetConfigPOPage = baseMapper.selectPage(page, queryWrapper);

        SecPageVO<DeviceVsmNetConfigVO> deviceVsmNetConfigPageVO = deviceVsmNetConfigConvert.pagePOToSecPageVOPage(deviceVsmNetConfigPOPage);
        return ResultUtil.ok(deviceVsmNetConfigPageVO);
    }

    /**
     * 查询地址资源，合并子网掩码
     * @param deviceNetDetailPageDTO 筛选条件
     * @return
     */
    @Override
    public SecRestResponse<SecPageVO<DeviceNetDetailVO>> find(DeviceNetDetailPageDTO deviceNetDetailPageDTO) {
        SecRestResponse<SecPageVO<DeviceNetDetailVO>> netResult = deviceNetDetailService.find(deviceNetDetailPageDTO);

        List<DeviceNetDetailVO> netDetailVOList = netResult.getResult().getList();

        if(netDetailVOList == null || netDetailVOList.isEmpty()){
            return netResult;
        }
        List<DeviceVsmNetConfigPO> netConfigPOList = baseMapper.selectList(new LambdaQueryWrapper<DeviceVsmNetConfigPO>().
                eq(DeviceVsmNetConfigPO::getHostId,deviceNetDetailPageDTO.getDeviceId()));

        Map<String,String> netConfigMap = netConfigPOList.stream().collect(Collectors.toMap(DeviceVsmNetConfigPO::getGateway,DeviceVsmNetConfigPO::getMask));

        for (DeviceNetDetailVO vo : netDetailVOList){
            vo.setMask(netConfigMap.get(vo.getGateway()));
        }

        return netResult;
    }

    /** 
     * 新增数据
     *
     * @param deviceVsmNetConfigDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> add(DeviceVsmNetConfigDTO deviceVsmNetConfigDTO){
        //参数校验
        if(deviceVsmNetConfigDTO.getNetName().equals(deviceVsmNetConfigDTO.getNetCardName())){
           throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_CONFIG_CARD_EQUAL_NAME_ERROR);
        }

        QueryWrapper<DeviceVsmNetConfigPO> qw=Wrappers.query();
        qw.eq("GATEWAY",deviceVsmNetConfigDTO.getGateway());
        qw.eq(HOST_ID_PARAM,deviceVsmNetConfigDTO.getHostId());
        if(baseMapper.exists(qw)){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_CONFIG_GATEWAY_EXIST);
        }

        qw.clear();
        qw.eq("NET_NAME",deviceVsmNetConfigDTO.getNetName());
        qw.eq(HOST_ID_PARAM,deviceVsmNetConfigDTO.getHostId());
        if(baseMapper.exists(qw)){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_CONFIG_NAME_EXIT_ERROR);
        }
        qw.clear();
        qw.eq("NET_CARD_NAME",deviceVsmNetConfigDTO.getNetName());
        qw.eq(HOST_ID_PARAM,deviceVsmNetConfigDTO.getHostId());
        if(baseMapper.exists(qw)){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_CONFIG_CARD_USED_ERROR);
        }
        DeviceVsmNetConfigPO deviceVsmNetConfigPO = deviceVsmNetConfigConvert.dtoToPo(deviceVsmNetConfigDTO);
        deviceVsmNetConfigPO.setId(IdGenerator.ins().generator());
        deviceVsmNetConfigPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        deviceVsmNetConfigPO.setCreateBy(LoginUserUtil.getUserId());
        baseMapper.insert(deviceVsmNetConfigPO);
        return ResultUtil.ok();
    }

    /**
     * 新增虚机网络和地址资源
     * 1030设计，虚机网络和地址资源合并，新增时需要一同增加
     * @param deviceVsmNetAddDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SecRestResponse<Object> addVsmNetAndAddress(DeviceVsmNetAddDTO deviceVsmNetAddDTO) {
        //首先校验设备存在且类型为云机
        DeviceInfoPO deviceInfoPO = deviceInfoMapper.selectById(deviceVsmNetAddDTO.getDeviceId());
        if(deviceInfoPO == null){
            throw new BusinessException(DEVICE_NOT_EXIST);
        }
        if(deviceInfoPO.getFamilyType() != DeviceInfoDic.VSM_HOST_TYPE){
            throw new BusinessException(DEVICE_NOT_SUPPORT);
        }

        //校验云机下是否存在网关配置，不存在则插入。ip地址插入失败事务回滚。
        QueryWrapper<DeviceVsmNetConfigPO> vsmNetQuery = Wrappers.query();

        vsmNetQuery.lambda().eq(DeviceVsmNetConfigPO::getHostId,deviceVsmNetAddDTO.getDeviceId());
        vsmNetQuery.lambda().eq(DeviceVsmNetConfigPO::getGateway,deviceVsmNetAddDTO.getGateway());

        List<DeviceVsmNetConfigPO> vsmNetConfigPOList = baseMapper.selectList(vsmNetQuery);

        if(vsmNetConfigPOList.size() == 0){
            DeviceVsmNetConfigPO deviceVsmNetConfigPO = deviceVsmNetConfigConvert.deviceVsmNetAddToPo(deviceVsmNetAddDTO);
            deviceVsmNetConfigPO.setId(IdGenerator.ins().generator());
            deviceVsmNetConfigPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
            deviceVsmNetConfigPO.setCreateBy(LoginUserUtil.getUserId());
            baseMapper.insert(deviceVsmNetConfigPO);
        }else{
            String oldMask = vsmNetConfigPOList.get(0).getMask();
            String newMask = deviceVsmNetAddDTO.getMask();
            if(!newMask.equals(oldMask)){
                throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_GATEWAY_MASK_AGIAN);
            }

        }
        DeviceNetDetailAddDTO netDetailDTO = deviceNetDetailConvert.deviceVsmNetAddDtoToDto(deviceVsmNetAddDTO);
        return deviceNetDetailService.add(netDetailDTO);
    }

    /** 
     * 更新数据
     *
     * @param deviceVsmNetConfigDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(DeviceVsmNetConfigDTO deviceVsmNetConfigDTO){
        DeviceVsmNetConfigPO deviceVsmNetConfigPO = deviceVsmNetConfigConvert.dtoToPo(deviceVsmNetConfigDTO);
        deviceVsmNetConfigPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.updateById(deviceVsmNetConfigPO);
        return ResultUtil.ok();
    }
    

    /**
     * 通过主键删除数据
     * @param deviceVsmConfigDeleteDTO
     * @return
     */
    @Override
    public SecRestResponse<Object> deleteById(DeviceVsmConfigDeleteDTO deviceVsmConfigDeleteDTO){
        DeviceVsmNetConfigPO   deviceVsmNetConfigPO=baseMapper.selectById(deviceVsmConfigDeleteDTO.getId());
        if(deviceVsmNetConfigPO==null){
            log.error("删除网络配置失败，没有对应配置信息:{}",deviceVsmConfigDeleteDTO.getId());
            throw new BusinessException(DEVICE_NET_CONFIG_NULL_ERROR);
        }
        String gateway=deviceVsmNetConfigPO.getGateway();
        // 如果存在平台创建的虚机不删，不存在则删除该宿主机下的所有虚机
        Long vsmCount = deviceInfoMapper.selectCount(new LambdaQueryWrapper<DeviceInfoPO>()
                .eq(DeviceInfoPO::getHccsDeviceId, deviceVsmNetConfigPO.getHostId())
                .ne(DeviceInfoPO::getManagementStatusId, DeviceInfoDic.MARK_USE)
                .like(DeviceInfoPO::getMgtIp,gateway.substring(0,gateway.lastIndexOf('.'))));
        if (vsmCount > 0) {
            log.error("删除网络配置失败,存在平台创建虚拟机{}台", vsmCount);
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_DELETE_ERROR);
        }
        baseMapper.deleteById(deviceVsmConfigDeleteDTO.getId());
        return ResultUtil.ok();
    }

    /**
     * 根据地址资源id，删除地址资源。并且删除不存在地址资源的虚机网关。
     * 根据1030项目设计，地址资源与虚机网关合并，并且虚机网关不再与网卡绑定。
     * 因此，在删除云密码机某个网关的全部地址资源时，可以删除该网关配置。
     * @param detailId
     * @return
     */
    @Override
    public SecRestResponse<Object> deleteNetAndAddressById(DeviceNetDetailIdDTO detailId) {
        DeviceNetDetailIdsDTO detailIdsDTO = new DeviceNetDetailIdsDTO();
        detailIdsDTO.setIpIdList(Collections.singletonList(detailId.getIpId()));

        List<DeviceNetDetailPO> addressList = deviceNetDetailService.selectByIds(detailIdsDTO.getIpIdList());
        if(addressList.isEmpty()){
            throw new BusinessException(DEVICE_NET_CONFIG_NULL_ERROR);
        }
        deviceNetDetailService.deleteById(detailIdsDTO);
        DeviceNetDetailPO detailPO = addressList.get(0);

        if(!deviceNetDetailService.ipExistsByDeviceAndGateway(detailPO.getDeviceId(),detailPO.getGateway())){
            Map<String,Object> deleteMap = new HashMap<>();
            deleteMap.put(HOST_ID_PARAM,detailPO.getDeviceId());
            deleteMap.put("GATEWAY",detailPO.getGateway());
            baseMapper.deleteByMap(deleteMap);
        }


        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<DeviceVsmNetConfigVO> info(DeviceVsmNetIdDTO deviceVsmNetIdDTO){
        DeviceVsmNetConfigPO deviceVsmNetConfigPO=baseMapper.selectById(deviceVsmNetIdDTO.getId());
        if(deviceVsmNetConfigPO ==null){
            return ResultUtil.error(DEVICE_NET_CONFIG_INFO_ERROR);
        }
        return ResultUtil.ok(deviceVsmNetConfigConvert.convertVo(deviceVsmNetConfigPO));
    }


    @Override
    public List<DeviceNetAddrVO>  getNetCardList(DeviceNetAddrDTO deviceVsmNetAddrDTO){
        QueryWrapper<DeviceVsmNetConfigPO> queryWrapper = Wrappers.query();
        queryWrapper.eq(HOST_ID_PARAM,deviceVsmNetAddrDTO.getHostId());
        List<DeviceVsmNetConfigPO> deviceVsmNetConfigPOList=baseMapper.selectList(queryWrapper);
        HashMap<String, String> netCardMap = new HashMap<>(deviceVsmNetConfigPOList.size());
        for(DeviceVsmNetConfigPO netConfig:deviceVsmNetConfigPOList){
            netCardMap.put(netConfig.getNetCardName(), netConfig.getNetName());
        }
        DeviceInfoPO deviceInfo=deviceInfoMapper.selectById(deviceVsmNetAddrDTO.getHostId());
        if(deviceInfo==null){
            log.error("获取网卡信息失败,宿主机不存在,deviceId:{}",deviceVsmNetAddrDTO.getHostId());
            throw new BusinessException( DEVICE_INFO_VSM_HOST_NOT_EXIST);
        }

        CloudHsmManageService service = cloudDeviceManageFactory.getCloudHsmInstance(deviceInfo.getDeviceTypeId());
        VsmHostInfoResponse vsmHostInfoResponse = service.getCloudHsmNetCartList(deviceInfo,deviceVsmNetAddrDTO.isProxyRouteIs(),
                deviceVsmNetAddrDTO.getGatewayIp(),deviceVsmNetAddrDTO.getGatewayPort());

        List<VsmHostInfoResponse.NetAddr>  netAddrList=new ArrayList<>(3);
        for(VsmHostInfoResponse.NetAddr netAddr:vsmHostInfoResponse.getNetAddrs()){
            if(StringUtils.isBlank(netCardMap.get(netAddr.getName()))){
                netAddrList.add(netAddr);
            }
        }
        if(netAddrList.isEmpty()){
            String status=vsmHostInfoResponse.getNetAddrs().isEmpty() ? DEVICE_NET_CONFIG_NETADDR_NULL_ERROR:DEVICE_NET_CONFIG_UN_AVAILABLE_ERROR;
            throw new BusinessException(status);
        }
        List<DeviceNetAddrVO> deviceNetAddrVOList = new ArrayList<>(netAddrList.size());
        for(VsmHostInfoResponse.NetAddr netAddr:netAddrList){
            DeviceNetAddrVO deviceNetAddrVO = new DeviceNetAddrVO();
            deviceNetAddrVO.setName(netAddr.getName());
            deviceNetAddrVO.setIp(netAddr.getIp());
            deviceNetAddrVO.setMask(netAddr.getMask());
            deviceNetAddrVO.setGateway(netAddr.getGateway());
            deviceNetAddrVOList.add(deviceNetAddrVO);
        }
        return deviceNetAddrVOList;
    }

    public List<DeviceVsmNetConfigSelectVO> findNetConfigList(DeviceNetAddrDTO deviceVsmNetAddrDTO){
        QueryWrapper<DeviceVsmNetConfigPO> queryWrapper = Wrappers.query();
        queryWrapper.eq(HOST_ID_PARAM,deviceVsmNetAddrDTO.getHostId());
        List<DeviceVsmNetConfigPO> deviceVsmNetConfigPOList=baseMapper.selectList(queryWrapper);
        return deviceVsmNetConfigConvert.convertSelectVo(deviceVsmNetConfigPOList);
    }
}