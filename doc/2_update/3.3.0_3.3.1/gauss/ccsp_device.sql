-- 升级脚本规范 每一项SQL 必需增加 谁写的 SQL大意描述


-- @yaokunyuan 3.3.1device_api增加4.1.2云机初始化数据
INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968601, 'getVsmInfo', 41, 'chsm_sansec_standard_0088', 101, '1', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968602, 'getVsmStatus', 41, 'chsm_sansec_standard_0088', 102, '2', '/', '/api/1.0/vsm/status', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968603, 'configVsmNetwork', 41, 'chsm_sansec_standard_0088', 103, '3', '/', '/api/1.0/vsm/network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968604, 'createVsm', 41, 'chsm_sansec_standard_0088', 104, '4', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968605, 'vsmOperate', 41, 'chsm_sansec_standard_0088', 105, '5', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968606, 'getChsmInfo', 41, 'chsm_sansec_standard_0088', 106, '6', '/', '/api/1.0/chsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968607, 'getChsmStatus', 41, 'chsm_sansec_standard_0088', 107, '7', '/', '/api/1.0/chsm/status', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968608, 'configChsmPublicKey', 41, 'chsm_sansec_standard_0088', 108, '8', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968609, 'clearChsmPublicKey', 41, 'chsm_sansec_standard_0088', 109, '9', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968610, 'getChsmPublicKeyFinger', 41, 'chsm_sansec_standard_0088', 110, '10', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968611, 'configVsm', 41, 'chsm_sansec_standard_0088', 111, '30', '/', '/api/1.0/vsm/config', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968612, 'getHsmStatus', 42, 'vsm_sansec_1002', 2102, '102', '/PlatformServlet', '?method=getState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968613, 'configHsmNetwork', 42, 'vsm_sansec_1002', 2103, '103', '/PlatformServlet', '?method=network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968614, 'restartHsm', 42, 'vsm_sansec_1002', 2104, '104', '/PlatformServlet', '?method=restartHsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968615, 'configHsmPublicKey', 42, 'vsm_sansec_1002', 2105, '105', '/AuthServlet', '?method=authPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968616, 'clearHsmPublicKey', 42, 'vsm_sansec_1002', 2106, '106', '/AuthServlet', '?method=cleanPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968617, 'getHsmPublicKeyFinger', 42, 'vsm_sansec_1002', 2107, '107', '/AuthServlet', '?method=getAuthPKFingerprints', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968618, 'createHsmLmk', 42, 'vsm_sansec_1002', 2108, '108', '/PlatformServlet', '?method=generateLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968619, 'generateHsmLMKWithComponent', 42, 'vsm_sansec_1002', 2109, '109', '/PlatformServlet', '?method=importLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968621, 'getHsmDeviceKeyPair', 42, 'vsm_sansec_1002', 2110, '110', '/PlatformServlet', '?method=gennerAsymmTempKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968622, 'exportHsmMasterKey', 42, 'vsm_sansec_1002', 2111, '111', '/PlatformServlet', '?method=exportLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968623, 'importHsmMasterKey', 42, 'vsm_sansec_1002', 2112, '112', '/PlatformServlet', '?method=importLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968624, 'getDeviceInitState', 42, 'vsm_sansec_1002', 2113, '113', '/PlatformServlet', '?method=initStatus', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968625, 'deviceInit', 42, 'vsm_sansec_1002', 2114, '114', '/PlatformServlet', '?method=doHsmInit', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968626, 'importAsyncKeyPair', 42, 'vsm_sansec_1002', 2115, '115', '/PlatformServlet', '?method=importAsyncKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968627, 'importSyncKeyPair', 42, 'vsm_sansec_1002', 2116, '116', '/PlatformServlet', '?method=importSymmKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968628, 'deleteSymmetricKey', 42, 'vsm_sansec_1002', 2117, '117', '/PlatformServlet', '?method=delHsmSymmetricKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968629, 'deleteSM2Key', 42, 'vsm_sansec_1002', 2118, '118', '/PlatformServlet', '?method=delHsmKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968630, 'getHsmServiceStatus', 42, 'vsm_sansec_1002', 2119, '119', '/PlatformServlet', '?method=getServiceState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968631, 'getHsmInfo', 42, 'vsm_sansec_1002', 2101, '101', '/PlatformServlet', '?method=getHsmInfo', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

-- @yaokunyuan 3.3.1 device_busitype增加4.1.2云机初始化数据
INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4201, 42, 1, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);

INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4202, 42, 2, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);

INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4203, 42, 4, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);

INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4204, 42, 5, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);

INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4205, 42, 6, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);

INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4206, 42, 7, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);

INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4207, 42, 8, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);

INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4208, 42, 9, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);

INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4209, 42, 10, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);

INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4210, 42, 11, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);

INSERT INTO device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4211, 42, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);

-- @yaokunyuan 3.3.1 device_monitor_config增加4.1.2云机初始化数据
INSERT INTO device_monitor_config (ID, DEVICE_TYPE_ID, MONITOR_TYPE, URL, SNMP_VERSION, SNMP_PROTO, SNMP_PORT, SAFE_LEVEL, SECURITY_NAME, AUTHENTICATION_PROTOCOL, AUTHENTICATION_AUTH_CODE, PRIVACY_PROTOCOL, PRIVACY_AUTH_CODE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118880293417519114, 42, 2, NULL, 'v3', 'UDP', 161, 1, 'snmpuser', 5, 'DXTNvsZh9NyBEcy74FgtnQ==', 2, 'DXTNvsZh9NyBEcy74FgtnQ==', NULL, NULL, '2023-10-19 09:22:59', NULL, '2023-10-19 09:22:59');

-- @yaokunyuan 3.3.1device_snmp_oid_config增加4.1.2云机初始化数据
INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560901, 42, '.1.3.6.1.4.1.2021.11.9.0', 'sansec.cpu.userCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:07:18', 6585233652468156297, '2024-03-21 14:07:18');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560902, 42, '.1.3.6.1.4.1.2021.11.10.0', 'sansec.cpu.sysCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:07:28', 6585233652468156297, '2024-03-21 14:07:28');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560903, 42, '.1.2.3.4.111.1.42', 'sansec.cpu.id', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '100-${0}-${1}', NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560904, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560903, 1, '.1.3.6.1.4.1.2021.11.9.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560905, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560903, 2, '.1.3.6.1.4.1.2021.11.10.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560906, 42, '.1.3.6.1.2.1.25.2.2.0', 'sansec.mem.total', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:12', 6585233652468156297, '2024-03-21 14:09:12');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560907, 42, '.1.3.6.1.4.1.2021.4.6.0', 'sansec.mem.free', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:22', 6585233652468156297, '2024-03-21 14:09:22');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560908, 42, '.1.2.3.4.111.2.42', 'sansec.mem.percent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '((${0}-${1})/${0})*100', NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560909, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560908, 1, '.1.3.6.1.2.1.25.2.2.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560910, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560908, 2, '.1.3.6.1.4.1.2021.4.6.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560911, 42, '.1.2.3.4.111.3.42', 'sansec.mem.used', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '${0}-${1}', NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560912, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560911, 1, '.1.3.6.1.2.1.25.2.2.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560913, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560911, 2, '.1.3.6.1.4.1.2021.4.6.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560914, 42, '.1.3.6.1.2.1.25.2.3.1.5', 'sansec.file.osFileStores.totalSpace', 'Integer', 'Walk', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:11:15', 6585233652468156297, '2024-03-21 14:11:15');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560915, 42, '.1.2.3.4.111.6.42', 'sansec.file.wholeTotalSpace', 'Integer', 'Walk', 1, NULL, NULL, NULL, 'SUM', '', NULL, 6585233652468156297, '2024-03-21 14:11:46', 6585233652468156297, '2024-03-21 14:11:46');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560916, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560915, 1, '.1.3.6.1.2.1.25.2.3.1.5', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:11:46', 6585233652468156297, '2024-03-21 14:11:46');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560917, 42, '.1.3.6.1.2.1.25.2.3.1.6', 'sansec.file.osFileStores.usedSpace', 'Integer', 'Walk', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:12:10', 6585233652468156297, '2024-03-21 14:12:10');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560918, 42, '.1.2.3.4.111.7.42', 'sansec.file.wholeUsedSpace', 'Integer', 'Walk', 1, NULL, NULL, NULL, 'SUM', '', NULL, 6585233652468156297, '2024-03-21 14:12:31', 6585233652468156297, '2024-03-21 14:12:31');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560919, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560918, 1, '.1.3.6.1.2.1.25.2.3.1.6', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:12:31', 6585233652468156297, '2024-03-21 14:12:31');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560920, 42, '.1.2.3.4.111.8.42', 'sansec.file.wholeFileSystemPercent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '(${1}/${0})*100', NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560921, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560920, 1, '.1.2.3.4.111.6', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560922, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560920, 2, '.1.2.3.4.111.7', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560923, 42, '.1.2.3.4.111.4.42', 'sansec.cpu.cpuUsedPercent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '${0}+${1}', NULL, 6585233652468156297, '2024-03-21 15:31:29', 6585233652468156297, '2024-03-21 15:31:29');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560924, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560923, 1, '.1.3.6.1.4.1.2021.11.9.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 15:31:29', 6585233652468156297, '2024-03-21 15:31:29');

INSERT INTO device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560925, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560923, 2, '.1.3.6.1.4.1.2021.11.10.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 15:31:29', 6585233652468156297, '2024-03-21 15:31:29');

-- @yaokunyuan 3.3.1 device_type增加4.1.2云机初始化数据
INSERT INTO device_type (DEVICE_TYPE_ID, DEVICE_TYPE_NAME, DEFAULT_FLAG, VENDOR_ID, FAMILY_TYPE, MACHINE_TYPE_ID, PARENT_ID, HCCS_IMAGE_ID, INTERACTION_SERIAL_NUMBER, MGT_METHOD, MGT_PORT, BUSI_PORT, CLOUD_VSM_TOTAL, SUPPORT_MAIN_KEY_FLAG, SUPPORT_SEC_MANAGE_FLAG, SUPPORT_GEN_KEY_FLAG, SUPPORT_SNMP_FLAG, TOKEN_CALL_BACK_FLAG, NEED_PASSWORD_FLAG, READ_INFO_FLAG, MGT_PUBLICKEY_FLAG, KEY_TEMPLET_IDS, DEFAULT_KEY_TEMPLET_IDS, SUPPORT_EXTERNAL_SERVICE, SUPPORT_MANAGE_PT, CONNECT_AUTH_CODE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (41, '云密码机_V4.1.2', 1, 5466516874462629769, 1, NULL, 0, NULL, 'chsm_sansec_standard_0088', 1, 8083, NULL, 32, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 0, 0, NULL, 0, 5808835364836477569, '2023-07-10 10:32:34', 6071976419609675523, '2023-09-27 14:46:38', '');

INSERT INTO device_type (DEVICE_TYPE_ID, DEVICE_TYPE_NAME, DEFAULT_FLAG, VENDOR_ID, FAMILY_TYPE, MACHINE_TYPE_ID, PARENT_ID, HCCS_IMAGE_ID, INTERACTION_SERIAL_NUMBER, MGT_METHOD, MGT_PORT, BUSI_PORT, CLOUD_VSM_TOTAL, SUPPORT_MAIN_KEY_FLAG, SUPPORT_SEC_MANAGE_FLAG, SUPPORT_GEN_KEY_FLAG, SUPPORT_SNMP_FLAG, TOKEN_CALL_BACK_FLAG, NEED_PASSWORD_FLAG, READ_INFO_FLAG, MGT_PUBLICKEY_FLAG, KEY_TEMPLET_IDS, DEFAULT_KEY_TEMPLET_IDS, SUPPORT_EXTERNAL_SERVICE, SUPPORT_MANAGE_PT, CONNECT_AUTH_CODE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (42, '云服务器密码机_V5.2.7.1', 1, 5466516874462629769, 3, 2, 41, 2, 'vsm_sansec_1002', 1, 443, 8008, NULL, 1, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 0, 0, 'zM7MYRiXXu7pgCPspRhR70oMk0sJQgTU7wXIhD0e4NNz8GGRvSvd+Qw/X2NQBq2p', 0, 5808835364836477569, '2023-07-10 10:33:37', 6071976419609675523, '2023-09-27 14:49:53', '');


-- @yaokunyuan3.3.1 device_type_rela_value增加4.1.2云机初始化数据
INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439001, 41, '1', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);

INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439002, 41, '2', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);

INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439003, 41, '3', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);

INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439004, 41, '4', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);

INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439005, 41, '5', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);

INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439006, 41, '6', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);

INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439007, 42, '1', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439008, 42, '2', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439009, 42, '3', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439010, 42, '4', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

INSERT INTO device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439011, 42, '5', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);


-- @yaokunyuan 3.3.1版本没有默认组概念，都为普通组
update  ccsp_device.device_group set DEVICE_GROUP_TYPE=2,IS_REST=0 where DEVICE_GROUP_TYPE=1;

-- @ds  修改厂商管理中密码厂商三未的英文缩写问题
update  ccsp_device.device_vendor set VENDOR_SHORT_NAME='sansec' where VENDOR_ID=5466516874462629769;