package com.sansec.ai.common.quartz.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

/**
 * 定时任务表
 * <AUTHOR>
 * @since 2025-2-24
 */
@TableName("SYS_JOB")
@Data
public class SysJobPO extends BasePO {
    /**
     * 任务号
     */
    @TableId(type = IdType.INPUT)
    private Long jobId;
    /**
     * 任务名称
     */
    private String jobName;
    /**
     * 任务组名
     */
    private String jobGroup;
    /**
     * 服务模块
     */
    private String serverId;
    /**
     * 调用接口
     */
    private String methodUrl;
    /**
     * json格式参数
     */
    private String jsonParam;
    /**
     * CRON执行表达式
     */
    private String cronExpression;
    /**
     * 计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）
     */
    private String misfirePolicy;
    /**
     * 是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）
     */
    private String concurrent;
    /**
     * 状态（0正常 1暂停）
     */
    private String jobStatus;
    /**
     * 创建人
     */
    private Long createdBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updatedBy;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 备注
     */
    private String remark;

}