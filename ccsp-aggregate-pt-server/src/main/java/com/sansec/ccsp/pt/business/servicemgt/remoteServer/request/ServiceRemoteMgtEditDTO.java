package com.sansec.ccsp.pt.business.servicemgt.remoteServer.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
* @description : 管控服务管理表（330）;
* <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
* @date : 2024-3-4
*/
@Data
public class ServiceRemoteMgtEditDTO {
   /**
    * 管控服务ID
    */
   @NotNull(message = "管控服务Id不能为空")
   private Long id;
   /**
    * 服务名称
    */
   @NotBlank(message = "管控服务名称不可为空")
   @Size(max = 50, message = "管控服务名称长度限制50")
   @Pattern(regexp = CommonPattern.COMMON_NAME, message = "管控服务名称只能包含中文、英文、数字、特殊字符-_")
   private String name;

   /**
    * 备注
    */
   @Size(max = 100, message = "备注长度限制100")
   @Pattern(regexp = CommonPattern.COMMON_DESC, message = "备注请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
   private String remark;

}