<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ccsp.datastatic.business.statistic.realtime.mapper.StatisticDayDataRealMapper">
    <sql id="Base_Column_List" >
    ID,COLLECT_TIME,CALL_NUM,REGION_CODE,TENANT_CODE,APP_CODE,SERVICE_IP,SERVICE_PORT,SERVICE_TYPE_CODE,SERVICE_URL,AVG_COST_TIME,MAX_COST_TIME,SERVICE_FLOW_NUM,PEAK_TPS,PEAK_MBPS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>


    <select id="analyzeDataByMonth" resultType="com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticGroupDataVO">

        select

        COLLECT_DATE_TIME as COLLECT_TIME

        ,REGION_CODE
        ,TENANT_CODE
        ,APP_CODE
        ,SERVICE_TYPE_CODE

        ,sum(CALL_NUM) as CALL_NUM
        ,avg(AVG_COST_TIME) as AVG_COST_TIME
        ,max(MAX_COST_TIME) as MAX_COST_TIME
        ,sum(SERVICE_FLOW_NUM) as SERVICE_FLOW_NUM

        ,max(PEAK_TPS) as PEAK_TPS
        ,max(PEAK_MBPS) as PEAK_MBPS

        from STATISTIC_DAY_DATA_REAL

        where 1=1

        <if test="collectTimeEq != null and collectTimeEq !=''">
            AND COLLECT_DATE_TIME = #{collectTimeEq}
        </if>

        group by
        COLLECT_DATE_TIME
        ,REGION_CODE
        ,TENANT_CODE
        ,APP_CODE
        ,SERVICE_TYPE_CODE

    </select>

    <select id="queryAnalyzeHourData" resultType="com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticDataVO">



        select

        COLLECT_TIME as COLLECT_TIME
        ,SUM(CALL_NUM) as CALL_NUM
        /*,REGION_CODE
        ,TENANT_CODE
        ,APP_CODE
        ,SERVICE_IP
        ,SERVICE_PORT
        ,SERVICE_TYPE_CODE

        ,SERVICE_URL*/
        ,AVG(AVG_COST_TIME) as AVG_COST_TIME
        ,MAX(MAX_COST_TIME) as MAX_COST_TIME
        ,SUM(SERVICE_FLOW_NUM) as SERVICE_FLOW_NUM

        from STATISTIC_DAY_DATA_REAL

        where 1=1


        <if test="beginTime != null and beginTime !='' and (endTime ==null or endTime=='') ">
            and COLLECT_TIME &gt;= #{beginTime}
        </if>

        <if test="endTime != null and endTime !='' and (beginTime ==null or beginTime=='') ">
            and COLLECT_TIME &lt;= #{endTime}
        </if>

        <if test="beginTime != null and beginTime !='' and endTime !=null and endTime !='' ">
            and COLLECT_TIME  &gt;= #{beginTime} and  COLLECT_TIME  &lt;= #{endTime}
        </if>


        <if test="regionCode != null and regionCode !='' ">
            and REGION_CODE = #{regionCode}
        </if>


        <if test="tenantCode != null and tenantCode !='' ">
            and TENANT_CODE = #{tenantCode}
        </if>


        <if test="appCode != null and appCode !='' ">
            and APP_CODE = #{appCode}
        </if>


        <if test="serviceIp != null and serviceIp !='' ">
            and SERVICE_IP = #{serviceIp}
        </if>


        <if test="servicePort != null ">
            and SERVICE_PORT = #{servicePort}
        </if>


        <if test="serviceTypeCode != null  and serviceTypeCode !='' ">
            and SERVICE_TYPE_CODE = #{serviceTypeCode}
        </if>


        group by COLLECT_TIME

        order by COLLECT_TIME asc

    </select>

    <delete id="clearRealTimeData">

        delete

        from STATISTIC_DAY_DATA_REAL

        where  COLLECT_TIME &lt; #{deleteBeforeTime}


    </delete>

</mapper>