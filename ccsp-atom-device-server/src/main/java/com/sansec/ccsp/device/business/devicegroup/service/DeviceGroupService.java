package com.sansec.ccsp.device.business.devicegroup.service;

import com.sansec.ccsp.device.devicegroup.request.*;
import com.sansec.ccsp.device.devicegroup.response.*;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoInGroupVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>aw<PERSON>
 * @Description: 设备组;(DEVICE_GROUP)表服务接口
 * @Date: 2023-2-18
 */
public interface DeviceGroupService {
    /**
     * @param deviceGroupPageDTO 筛选条件
     * @return 查询结果
     * @Description: 分页查询
     */
    SecRestResponse<SecPageVO<DeviceGroupVO>> find(DeviceGroupPageDTO deviceGroupPageDTO);

    /**
     * @param deviceGroupDTO
     * @return 实例对象
     * @Description: 新增数据
     */
    SecRestResponse<DeviceGroupContextVO> add(DeviceGroupDTO deviceGroupDTO);

    /**
     * @param deviceGroupDTO
     * @return 实例对象
     * @Description: 更新数据
     */
    SecRestResponse<Long> edit(DeviceGroupEditDTO deviceGroupDTO);

    /**
     * @param deviceGroupDTO
     * @return 实例对象
     * @Description: 通过主键删除数据
     */
    SecRestResponse<Object> deleteById(DeviceGroupDeleteDTO deviceGroupDTO);

    /**
     * 根据租户Id批量删除设备组列表
     *
     * @param dto
     * @return
     */
    SecRestResponse<List<Long>> deleteListByTenantId(DeleteByTenantIdDTO dto);

    SecRestResponse<List<DeviceGroupVO>> findDeviceGroupList(DeviceGroupFindListDTO deviceGroupFindListDTO);

    SecRestResponse<DeviceGroupVO> info(DevicesGroupIdDTO devicesGroupIdDTO);

    SecRestResponse<SecPageVO<DeviceListByTenantIdPageVO>> getDeviceListByTenantId(DeviceListByTenantDTO devicePageDTO);

    /**
     * @param devicePageDTO
     * @return
     * @Description: 获取多个设备组下的设备信息
     */
    SecRestResponse<SecPageVO<DeviceListByTenantIdPageVO>> getDevicesByGroupIds(DeviceListByGroupIdsDTO devicePageDTO);

    SecRestResponse<List<DeviceGroupIdNameVO>> getDeviceGroupIdNameList(DeviceGroupIdNameDTO deviceGroupIdNameDTO);

    SecRestResponse<List<DeviceGroupIdNameVO>> getAllDeviceGroupIdNameList(DeviceGroupIdNameDTO deviceGroupIdNameDTO);

    /**
     * @param addDeviceToGroupDTO
     * @return 实例对象
     * @Description: 新增数据
     */
    Long addDeviceToGroup(DeviceToGroupDTO addDeviceToGroupDTO);

    /**
     * 直接将空闲设备添加到租户下的普通设备组中
     *
     * @param deviceToTenantGroupDTO
     * @return
     */
    Long addDeviceToTenantGroup(DeviceToTenantGroupDTO deviceToTenantGroupDTO);

    /**
     * @param deviceListToGroupDTO
     * @return 实例对象
     * @Description: 批量新增数据
     */
    List<DeviceInfoInGroupVO> addDeviceListToGroup(DeviceListToGroupDTO deviceListToGroupDTO);

//    /**
//     * 设备组删除设备
//     *
//     * @param delDeviceToGroupDTO
//     * @return
//     */
//    SecRestResponse<Object> delDeviceToGroup(DeviceToGroupDTO delDeviceToGroupDTO);
//
//    /**
//     * 设置组删除设备直接到pt
//     *
//     * @param delDeviceToGroupDTO
//     * @return
//     */
//    SecRestResponse<Object> delDeviceToPt(DeviceToGroupDTO delDeviceToGroupDTO);

    /**
     * 设备组删除设备
     *
     * @param delDeviceToGroupDTO
     * @return
     */
    SecRestResponse<Object> groupDeleteDevice(DeviceToGroupDTO delDeviceToGroupDTO);


    /**
     * @param devicesByGroupIdDTO 设备组ID
     * @return 查询结果
     * @Description: 查询设备组内的设备信息
     */
    SecRestResponse<List<DeviceInfoInGroupVO>> getDevicesByGroupId(DevicesGroupIdDTO devicesByGroupIdDTO);

    /**
     * @param devicesByGroupIdDTO 设备组ID
     * @return 查询结果
     * @Description: 查询可下发给服务的设备列表
     */
    SecRestResponse<List<DeviceInfoInGroupVO>> getServiceReceiveDeviceByGroupId(DevicesGroupIdDTO devicesByGroupIdDTO);

    /**
     * 获取设备组空闲设备
     * @param dto
     * @return
     */
    SecRestResponse<List<DeviceInfoInGroupVO>> getFreeDeviceByGroup(FreeDeviceByGroupDTO dto);

    /**
     * @param devicesByGroupIdDTO 设备组ID
     * @return 查询结果
     * @Description: 分页查询设备组内的设备信息
     */
    SecRestResponse<SecPageVO<DeviceInfoInGroupVO>> getDevicesByGroupIdPage(DevicesGroupIdDTO devicesByGroupIdDTO);

    /**
     * @param getBusiTypeListDTO 设备组ID和租户ID
     * @return 查询结果
     * @Description: 设备组业务类型列表：查询设备组或租户每种业务类型下的设备ID
     */
    SecRestResponse<List<GetBusiTypeListVO>> getBusiTypeList(GetBusiTypeListDTO getBusiTypeListDTO);

    /**
     * Description: 获取租户下的设备数量（可用设备（资源组下的设备），设备总量）
     *
     * @param deviceCountDTO 租户ID
     * @return 查询结果
     */
    SecRestResponse<DeviceCountVO> getDeviceNumByTenantId(DeviceCountDTO deviceCountDTO);

    /**
     * 根据区域ID查找设备组列表
     *
     * @param deviceGroupByRegionIdDTO
     * @return
     */
    SecRestResponse<List<DeviceGroupVO>> getDeviceGroupListByRegionId(DeviceGroupByRegionIdDTO deviceGroupByRegionIdDTO);

    /**
     * 根据设备组ID获取设备对象
     *
     * @param deviceGroupId
     * @return
     */
    DeviceGroupVO getDeviceGroupVOById(Long deviceGroupId);

    /**
     * 根据租户ID获取设备组列表
     *
     * @param tenantId
     * @return
     */
    List<DeviceGroupVO> getDeviceGroupListByTenantId(Long tenantId);

    /**
     * 根据租户id查询可绑定的异构设备组
     * @param dto
     * @return
     */
    SecRestResponse<List<DeviceGroupVO>> getCanBingExtraDeivceGroup(GetCanBingExtraDeivceGroupDTO dto);
}