package com.sansec.ccsp.staticapi.logoperate.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
public class LogOperatePageDTO extends SecPageDTO {

    @Size(max = 50, message = "操作人名称长度最多50位")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "操作人名称只能包含中文、英文、数字、特殊字符 -_")
    private String operName;

    @Pattern(regexp = CommonPattern.COMMON_IP_LIKE, message = "模糊查询请求IP只能包含数字和.")
    private String operIp;

    private Integer logType;

    @Size(max = 50, message = "功能模块名称长度最多50位")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "功能模块名称请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
    private String moduleName;

    @Pattern(regexp = "^$|^[0-1]$",message = "操作状态类型输入错误0-成功，1-失败")
    private String result;

    @Pattern(regexp = "^$|([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8])))([ ])([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])",
            message = "开始时间格式需为年-月-日 时-分-秒(yyyy-MM-dd HH:mm:ss)的格式")
    private String startTime;

    @Pattern(regexp = "^$|([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8])))([ ])([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])",
            message = "结束时间格式需为年-月-日 时-分-秒(yyyy-MM-dd HH:mm:ss)的格式")
    private String endTime;

    private Long tenantId;

    @Size(max = 100, message = "操作内容长度最多100位")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "操作内容请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
    private String operContent;
}
