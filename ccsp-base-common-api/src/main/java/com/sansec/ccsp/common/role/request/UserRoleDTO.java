package com.sansec.ccsp.common.role.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 用户角色表;
 * @Date: 2023-2-18
 */
@Data
public class UserRoleDTO {
    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不可为空")
    @Size(max = 50, message = "角色名称长度限制50")
    private String roleName;
    /**
     * 排序序号
     */
    @NotNull(message = "显示顺序不可为空")
    private Integer sordNum;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 角色类别;1平台角色 2租户角色
     */
    @NotNull(message = "角色类别不可为空")
    private Integer roleType;
    /**
     * 是否作废;默认为0
     */
    private Integer invalidFlag;
    /**
     * 备注
     */
    @Size(max = 100, message = "备注长度限制100")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "备注请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 菜单列表
     */
    private Long[] menus;
}
