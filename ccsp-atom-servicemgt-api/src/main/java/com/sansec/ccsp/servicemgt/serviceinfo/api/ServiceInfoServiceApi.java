package com.sansec.ccsp.servicemgt.serviceinfo.api;

import com.sansec.ccsp.servicemgt.serviceinfo.api.fallback.ServiceInfoServiceFallBack;
import com.sansec.ccsp.servicemgt.serviceinfo.request.*;
import com.sansec.ccsp.servicemgt.serviceinfo.response.*;
import com.sansec.ccsp.servicemgt.servicetype.request.ServiceTypeDTO;
import com.sansec.ccsp.servicemgt.servicetype.response.ServiceTypeVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 服务管理层
 * @Date: 2023年2月20日
 */
@FeignClient(name = "${spring.protocol.prefix}${spring.application.servicemgt.name}", path = "/ccsp/servicemgt", fallbackFactory = ServiceInfoServiceFallBack.class)
public interface ServiceInfoServiceApi {

    /**
     * @param @param  serviceInfoDTO
     * @param @return 参数
     * @return SecRestResponse<Object> 返回类型
     * @throws
     * @Title: add
     * @Description: 添加
     */
    @PostMapping(value = "/api/serviceinfo/v1/add")
    SecRestResponse<Object> add(@RequestBody @Validated ServiceInfoDTO serviceInfoDTO);

    /**
     * @param @param  serviceInfoDTO
     * @param @return 参数
     * @return SecRestResponse<Object> 返回类型
     * @throws
     * @Title: add
     * @Description: 添加
     */
    @PostMapping(value = "/api/serviceinfo/v2/add")
    SecRestResponse<Object> add(@RequestBody @Validated ServiceAddWrapper serviceAddWrapper);

    /**
     * @param @param  serviceInfoDTO
     * @param @return 参数
     * @return SecRestResponse<Object> 返回类型
     * @throws
     * @Title: edit
     * @Description: 修改
     */
    @PostMapping(value = "/api/serviceinfo/v1/edit")
    SecRestResponse<Object> edit(@RequestBody ServiceInfoEditDTO serviceInfoEditDTO);

    /**
     * @param @param  serviceInfoDeleteDTO
     * @param @return 参数
     * @return SecRestResponse<Object> 返回类型
     * @throws
     * @Title: deleteById
     * @Description: 根据id删除
     */
    @PostMapping(value = "/api/serviceinfo/v1/deleteById")
    SecRestResponse<Object> deleteById(@RequestBody ServiceInfoDeleteDTO serviceInfoDeleteDTO);

    /**
     * @param @param  serviceInfoPageDTO
     * @param @return 参数
     * @return SecRestResponse<SecPageVO < ServiceInfoVO>> 返回类型
     * @throws
     * @Title: find
     * @Description: 分页查询
     */
    @PostMapping(value = "/api/serviceinfo/v1/find")
    SecRestResponse<SecPageVO<ServiceInfoPageVO>> find(@RequestBody ServiceInfoPageDTO serviceInfoPageDTO);

    /**
     * @param @param  serviceInfoPageDTO
     * @param @return 参数
     * @return SecRestResponse<SecPageVO < ServiceInfoVO>> 返回类型
     * @throws
     * @Title: find
     * @Description: 分页查询
     */
    @PostMapping(value = "/api/serviceinfo/v1/findByServiceGroup")
    SecRestResponse<SecPageVO<ServiceInfoPageVO>> findByServiceGroup(@RequestBody ServiceInfoByServiceGroupPageDTO serviceInfoPageDTO);

    /**
     * @param serviceInfoParamDTO
     * @return SecRestResponse<List < ServiceInfoVO>> 返回类型
     * @Description: 根据服务参数获取服务信息
     */
    @PostMapping(value = "/api/serviceinfo/v1/getServiceInfoByServiceParam")
    SecRestResponse<List<ServiceInfoVO>> getServiceInfoByServiceParam(@RequestBody ServiceInfoParamDTO serviceInfoParamDTO);

    /**
     * @param editOperStatusParamDTO
     * @return SecRestResponse<Object> 返回类型
     * @Description: 修改服务操作状态
     */
    @PostMapping(value = "/api/serviceinfo/v1/editServiceOperStatus")
    SecRestResponse<Object> editServiceOperStatus(@RequestBody EditOperStatusParamDTO editOperStatusParamDTO);

    /**
     * @param editRunStatusParamDTO
     * @return SecRestResponse<Object> 返回类型
     * @Description: 修改服务运行状态
     */
    @PostMapping(value = "/api/serviceinfo/v1/editServiceRunStatus")
    SecRestResponse<Object> editServiceRunStatus(@RequestBody EditRunStatusParamDTO editRunStatusParamDTO);

    /**
     * @param findServiceRunStatusParamDTO
     * @return SecRestResponse<Map < Long, Integer>> 返回类型
     * @Description: 查询服务状态
     */
    @PostMapping(value = "/api/serviceinfo/v1/findServiceRunStatus")
    SecRestResponse<Map<Long, Integer>> findServiceRunStatus(@RequestBody FindServiceRunStatusParamDTO findServiceRunStatusParamDTO);

    /**
     * @param findServiceRunStatusParamDTO
     * @return SecRestResponse<Map < Long, Integer>> 返回类型
     * @Description: 查询服务状态
     */
    @PostMapping(value = "/api/serviceinfo/v1/findServiceRunAndOperStatus")
    SecRestResponse<Map<Long, ServiceStatusVO>> findServiceRunAndOperStatus(@RequestBody FindServiceRunStatusParamDTO findServiceRunStatusParamDTO);

    /**
     * @param @param  serviceInfoDTO
     * @param @return 参数
     * @return SecRestResponse<List < ServiceInfoVO>> 返回类型
     * @throws
     * @Title: findList
     * @Description: 根据服务类型、服务名称、租户id查询列表信息-->可继续添加查询条件
     */
    @PostMapping(value = "/api/serviceinfo/v1/findList")
    SecRestResponse<List<ServiceInfoVO>> findList(@RequestBody ServiceInfoDTO serviceInfoDTO);


    @PostMapping(value = "/api/serviceinfo/v1/getServiceByFree")
    SecRestResponse<List<ServiceInfoVO>> getServiceByFree();

    /**
     * @param @param  serviceInfoDTO
     * @param @return 参数
     * @return SecRestResponse<List < ServiceInfoVO>> 返回类型
     * @throws
     * @Title: findById
     * @Description: 根据id查询
     */
    @PostMapping(value = "/api/serviceinfo/v1/findById")
    SecRestResponse<ServiceInfoVO> findById(@RequestBody ServiceInfoDTO serviceInfoDTO);

    @PostMapping(value = "/api/serviceinfo/v1/getServiceByTenantId")
    SecRestResponse<List<ServiceInfoVO>> getServiceByTenantId(@RequestBody ServiceListByTenantIdDTO serviceListByTenantIdDTO);

    @PostMapping(value = "/api/serviceinfo/v1/getServiceByGroupId")
    SecRestResponse<List<ServiceInfoVO>> getServiceByGroupId(@RequestBody ServiceListByGroupIdDTO serviceListByGroupIdDTO);

    @PostMapping(value = "/api/serviceinfo/v1/getServiceByGroupIds")
    SecRestResponse<List<ServiceInfoVO>> getServiceByGroupIds(@RequestBody List<Long> serviceGroupIds);

    @PostMapping(value = "/api/serviceinfo/v1/findIpPortExist")
    SecRestResponse<Boolean> findIpPortExist(@RequestBody ServiceInfoDTO serviceInfoDTO);

    @PostMapping(value = "/api/serviceinfo/v1/findTypeIpExist")
    SecRestResponse<Boolean> findTypeIpExist(@RequestBody ServiceInfoDTO serviceInfoDTO);

    @PostMapping("/api/serviceinfo/v1/numOfSystem")
    SecRestResponse<ServiceStatusChartVO> getServiceNumOfSystemOperations();

    /**
     * @deprecated 2024 331项目废弃 租户绑定共享组，所有服务组租户ID为1
     * @param tenantId
     * @return
     */
    @Deprecated
    @PostMapping("/api/serviceinfo/v1/numOfTenant")
    SecRestResponse<ServiceStatusChartVO> getServiceNumOfTenantOperations(@RequestBody Long tenantId);

    /**
     * @deprecated 2024 331项目废弃 租户绑定共享组，所有服务组租户ID为1
     * @param tenantIdList
     * @return
     */
    @Deprecated
    @PostMapping("/api/serviceinfo/v1/numOfTenantList")
    SecRestResponse<Map<Long,Integer>> getServiceNumByTenantList(@RequestBody List<Long> tenantIdList);

    @PostMapping("/api/serviceinfo/v1/numOfServiceGroup")
    SecRestResponse<ServiceStatusChartVO> getServiceNumOfServiceGroupOperations(@RequestBody Long serviceGroupId);

    @PostMapping("/api/serviceinfo/v1/numOfServiceGroupList")
    SecRestResponse<Map<Long,Integer>> getServiceNumByServiceGroupList(@RequestBody List<Long> serviceGroupIdList);

    @PostMapping("/api/serviceinfo/v1/chartSystem")
    SecRestResponse<ServiceStatusChartVO> getServiceStatusOfSystemOperations();

//    @PostMapping("/api/serviceinfo/v1/chartOrgan")
//    SecRestResponse<ServiceStatusChartVO> getServiceStatusOfOrganOperations(@RequestBody List<Long> tenantIds);

    /**
     * @deprecated 2024 331项目废弃 租户绑定共享组，所有服务组租户ID为1
     * @param tenantId
     * @return
     */
    //2024 331项目废弃
    @Deprecated
    @PostMapping("/api/serviceinfo/v1/chartTenant")
    SecRestResponse<ServiceStatusChartVO> getServiceStatusOfTenantOperations(@RequestBody Long tenantId);

    @PostMapping("/api/serviceinfo/v1/getServiceStatusOfServiceGroupIds")
    SecRestResponse<ServiceStatusChartVO> getServiceStatusOfServiceGroupIds(@RequestBody List<Long> serviceGroupIdList);

    /**
     * @param editByTenantParamDTO
     * @return SecRestResponse<Object> 返回类型
     * @Description: 修改服务信息-修改设备组
     */
    @PostMapping(value = "/api/serviceinfo/v1/editByTenant")
    SecRestResponse<Object> editByTenant(@RequestBody EditByTenantParamDTO editByTenantParamDTO);

    /**
     * 查询当前服务个数
     * @return
     */
    @PostMapping(value = "/api/serviceinfo/v1/getCountService")
    SecRestResponse<Long> getCountService(@RequestBody RegionIdDTO regionIdDTO);

    /**
     * @deprecated 2024 331项目废弃 租户绑定共享组，所有服务组租户ID为1
     * 获取服务组和服务的对应关系，每个服务组下分别有多少服务
     * @param serviceInfoDTO
     * @return
     */
    @Deprecated
    @PostMapping("/api/serviceinfo/v1/getServiceGroupWithInfoVO")
    SecRestResponse<List<ServiceGroupWithInfoVO>> getServiceGroupWithInfoVO(@RequestBody ServiceInfoDTO serviceInfoDTO);


    /**
     * 获取每种服务类型下有几种服务组
     *
     * @param serviceInfoDTO
     * @return
     */
    @PostMapping("/api/serviceinfo/v1/getServiceTypeWithGroupNum")
    SecRestResponse<List<ServiceTypeGroupNumVO>> getServiceTypeWithGroupNum(@RequestBody ServiceInfoDTO serviceInfoDTO);

    /**
     * 根据IP查询监控中的服务
     *
     * @param serviceInfoIpDTO
     * @return
     */
    @PostMapping("/api/serviceinfo/v1/getMonitorServiceListByIp")
    SecRestResponse<List<ServiceInfoVO>> getMonitorServiceListByIp(@RequestBody ServiceInfoIpDTO serviceInfoIpDTO);


    /**
     * 根据IP查询监控中的服务
     *
     * @param serviceInfoIpDTO
     * @return
     */
    @PostMapping("/api/serviceinfo/v1/getServiceListByIp")
    SecRestResponse<List<ServiceInfoVO>> getServiceListByIp(@RequestBody ServiceInfoIpDTO serviceInfoIpDTO);


    /**
     * 根据租户ids和服务类型ids查询服务信息
     * @deprecated 2024 331项目废弃 租户绑定共享组，所有服务组租户ID为1
     * @param serviceInfoDTO
     * @return
     */
    @Deprecated
    @PostMapping("/api/serviceinfo/v1/getListByTenantAndTypeId")
    SecRestResponse<List<ServiceInfoVO>> getListByTenantAndTypeId(@RequestBody ServiceInfoDTO serviceInfoDTO);

    /**
     * 根据区域ID查询服务信息
     *
     * @param serviceInfoByRegionIdDTO
     * @return
     */
    @PostMapping("/api/serviceinfo/v1/getServiceInfoListByRegionId")
    SecRestResponse<List<ServiceInfoVO>> getServiceInfoListByRegionId(@RequestBody ServiceInfoByRegionIdDTO serviceInfoByRegionIdDTO);

    /**
     * 释放出错服务
     */
    @PostMapping("/api/serviceinfo/v1/releaseErrorService")
    SecRestResponse<Object> releaseErrorService(@RequestBody ServiceIdDTO dto);

    @PostMapping("/api/serviceinfo/v1/getServiceIdList")
    SecRestResponse<List<Long>> getServiceIdList();

    @PostMapping("/api/serviceinfo/v1/getServiceTypeIdList")
    SecRestResponse<List<String>> getServiceTypeIdList(@RequestBody @Validated ServiceTenantIdDTO dto);

    @PostMapping("/api/serviceinfo/v1/getBusiMonitorIpList")
    SecRestResponse<List<String>> getBusiMonitorIpList();

    /**
     * 根据服务类型查询服务，同一个服务组只保留一个
     */
    @PostMapping("/api/serviceinfo/v1/selectByServiceTypeIdGroupByServiceGroupId")
    SecRestResponse<List<ServiceInfoVO>>selectByServiceTypeIdGroupByServiceGroupId(@RequestBody Long serviceTypeId);

}
