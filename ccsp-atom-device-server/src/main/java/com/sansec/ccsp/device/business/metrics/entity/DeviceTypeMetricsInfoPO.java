package com.sansec.ccsp.device.business.metrics.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

/**
 * <AUTHOR> wangke
 * @description : 设备指标详情;
 * @date : 2024-6-20
 */
@TableName("DEVICE_TYPE_METRICS_INFO")
@Data
public class DeviceTypeMetricsInfoPO extends BasePO {
    /**
     * ID
     */
    private Long id;
    /**
     * 设备类型ID
     */
    private Long deviceTypeId;
    /**
     * 指标集标识
     */
    private String metricsSetCode;
    /**
     * 指标集类型
     */
    private String metricsSetType;
    /**
     * 指标集分组
     */
    private String metricsSetGroup;
    /**
     * 指标0或者属性1
     */
    private Integer actor;
    /**
     * 指标标识
     */
    private String code;
    /**
     * 指标名称
     */
    private String name;
    /**
     * 单位
     */
    private String unit;
    /**
     * 指标采集OID
     */
    private String oid;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}