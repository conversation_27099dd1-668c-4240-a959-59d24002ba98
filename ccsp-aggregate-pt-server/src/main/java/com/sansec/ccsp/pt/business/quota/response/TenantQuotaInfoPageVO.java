package com.sansec.ccsp.pt.business.quota.response;

import lombok.Data;

@Data
public class TenantQuotaInfoPageVO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 服务标识;服务标识+配额信息key对应唯一配额信息
     */
    private String serviceCode;
    /**
     * 服务类型ID
     */
    private Long serviceTypeId;
    /**
     * 服务类型名称
     */
    private String serviceTypeName;
    /**
     * 配额信息key
     */
    private String quotaName;
    /**
     * 配额显示名称
     */
    private String showName;
    /**
     * 配额值
     */
    private Integer quotaValue;

    /**
     * 配额值单位
     */
    private String valueUnit;

    /**
     * 已使用量
     */
    private Integer usedQuota;
    /**
     * 未使用量
     */
    private Integer residueQuota;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束数据
     */
    private String endTime;
    /**
     * 数据完整性
     */
    private String Hmac;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
}
