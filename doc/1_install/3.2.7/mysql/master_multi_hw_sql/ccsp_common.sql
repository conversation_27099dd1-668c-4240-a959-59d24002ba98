/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 100608 (10.6.8-MariaDB)
 Source Host           : ************:3306
 Source Schema         : ccsp_common

 Target Server Type    : MySQL
 Target Server Version : 100608 (10.6.8-MariaDB)
 File Encoding         : 65001

 Date: 30/03/2023 11:09:04
*/

SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for auth_code_blacklist
-- ----------------------------
DROP TABLE IF EXISTS `auth_code_blacklist`;
CREATE TABLE `auth_code_blacklist`
(
    `ID`               bigint                                                 NOT NULL,
    `AUTH_CODE`        varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '明文',
    `CRYPTO_AUTH_CODE` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '密文',
    `INVALID_FLAG`     int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`           varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`        bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`        bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '口令黑名单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of auth_code_blacklist
-- ----------------------------

-- ----------------------------
-- Table structure for config
-- ----------------------------
DROP TABLE IF EXISTS `config`;
CREATE TABLE `config`
(
    `CONFIG_ID`            bigint(20) NOT NULL COMMENT '主键',
    `BUSI_SERVICE_TYPE_ID` int(11) NOT NULL COMMENT '业务服务类型id',
    `CONFIG_CODE`          varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '配置编码',
    `CONFIG_NAME`          varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '配置名',
    `CONFIG_VALUE`         varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置值',
    `CONFIG_TYPE`          int(11) NOT NULL DEFAULT 1 COMMENT '配置类型;0明文1密文',
    `MAINTAIN_FLAG`        int(11) NOT NULL DEFAULT 1 COMMENT '可维护标志;0：不可维护1：可维护',
    `VISIBLE_FLAG`         int(11) NOT NULL DEFAULT 1 COMMENT '可见标志；0：不可见；1：可见',
    `SORD_NUM`             int(11) NOT NULL COMMENT '排序',
    `REMARK`               varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`            bigint(20) NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`          varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`            bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`          varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`CONFIG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of config
-- ----------------------------
INSERT INTO `config`
VALUES (1, 7, 'sys_operation_mode', '系统运行模式', '2', 0, 0, 0, 1, '1：传统模式、2：租户模式', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (2, 7, 'app_select_group', '应用业务选择组类型', '2', 0, 0, 0, 2, '1：设备组、2：服务组、3：全选', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (3, 5, 'forceUpdatePassword', '是否强制修改默认口令', 'false', 0, 1, 0, 10, '是否强制修改默认口令', NULL, NULL, NULL,
        '2023-04-27 17:32:00');
INSERT INTO `config`
VALUES (5443286102928657799, 5, 'loginFailuresAllowedTimes', '登录失败次数限制', '5', 0, 1, 0, 7, '登录失败次数限制', NULL,
        '2023-02-21 13:49:27', NULL, '2023-04-23 16:38:52');
INSERT INTO `config`
VALUES (5443288543812259206, 5, 'loginErrorLockTime', '登录失败锁定时长(分钟)', '5', 0, 1, 0, 8, '登录失败锁定时长(分钟)', NULL,
        '2023-02-21 13:50:40', NULL, '2023-04-23 16:36:55');
INSERT INTO `config`
VALUES (5443290616301751680, 5, 'authCodeExpireDate', '口令有效期', '365', 0, 1, 0, 5, '口令有效期', NULL, '2023-02-21 13:51:41',
        NULL, '2023-04-23 18:25:45');
INSERT INTO `config`
VALUES (5443291530022489481, 5, 'authCodeExpirationReminder', '口令有效期告警', '1', 0, 1, 0, 6, '口令有效期告警', NULL,
        '2023-02-21 13:52:09', NULL, '2023-04-23 16:44:57');
INSERT INTO `config`
VALUES (5443294032579136905, 5, 'unloggedTime', '长时间未登录禁用账户', '365', 0, 1, 0, 4, '长时间未登录禁用账户', NULL,
        '2023-02-21 13:53:23', NULL, '2023-04-23 16:47:36');
INSERT INTO `config`
VALUES (5443295512296033673, 5, 'authCodeHistoryLimit', '历史口令限制', '1', 0, 1, 0, 3, '历史口令限制', NULL,
        '2023-02-21 13:54:07', NULL, '2023-05-30 10:44:43');
INSERT INTO `config`
VALUES (5443296783572798856, 5, 'openAuthCodeLogin', '是否开启口令登录', 'true', 0, 1, 0, 1, '是否开启口令登录', NULL,
        '2023-02-21 13:54:45', NULL, '2023-04-23 18:24:50');
INSERT INTO `config`
VALUES (5443298085182770564, 5, 'openUKeyLogin', '是否开启UKey登录', 'false', 0, 1, 0, 2, '是否开启UKey登录', NULL,
        '2023-02-21 13:55:24', NULL, '2023-04-27 17:53:27');
INSERT INTO `config`
VALUES (5445743318561983874, 5, 'defaultAuthCode', '默认口令',
        'HTxv8wkDyjjbObx+bvlCFwVP9+Fh+E1kzMob9U1r7TxYXa+7D4DesEfLlVxo9VQuv80mlWEkC7o7RSNkLrPWK+j6fsHLu2UADQu+DSxFGtQ=',
        1, 1, 0, 2, '默认口令', NULL, '2023-02-22 10:09:58', NULL, '2023-04-28 17:25:56');
INSERT INTO `config`
VALUES (5467049690000640111, 0, 'quota_alarm_percentage', '大屏配额告警百分比', '50', 0, 0, 0, 1, '大屏配额告警百分比值为：0-100', NULL,
        '2023-07-07 18:32:57', NULL, NULL);
INSERT INTO `config`
VALUES (5467049690000640222, 0, 'screen_quota_scroll_flag', '大屏滚动告警是否开启', 'false', 0, 0, 0, 1,
        '大屏滚动告警是否开启：true false', NULL, '2023-07-07 18:32:57', NULL, NULL);
INSERT INTO `config`
VALUES (5467049690000640333, 0, 'db_init_check_time', '数据库初始化结果查询最大时间(秒)', '1800', 0, 0, 0, 1, '数据库初始化结果查询最大时间(秒)',
        NULL, '2023-07-07 18:32:57', NULL, NULL);
INSERT INTO `config`
VALUES (5467049690000640603, 0, 'screen_template_code', '大屏模板编号', 'default', 0, 0, 0, 1, '大屏模板编号', NULL,
        '2023-07-07 18:32:57', NULL, NULL);
INSERT INTO `config`
VALUES (5467049693950640600, 0, 'platformId', '平台ID', 'Rb8XPMwCex+Lt42qjVO37A==', 1, 1, 0, 15,
        '平台ID', NULL, '2023-03-01 18:32:57', NULL, '2023-06-27 15:14:04');
INSERT INTO `config`
VALUES (5467049693950640601, 0, 'start_time', '有效期开始时间', 'Rb8XPMwCex+Lt42qjVO37A==', 1, 1, 0, 16, '平台有效期', NULL,
        '2023-03-01 18:32:57', NULL, '2023-06-27 15:18:02');
INSERT INTO `config`
VALUES (5467049693950640602, 0, 'end_time', '有效期结束时间', 'Rb8XPMwCex+Lt42qjVO37A==', 1, 1, 0, 17, '平台有效期', NULL,
        '2023-03-01 18:32:57', NULL, '2023-06-27 15:18:02');
INSERT INTO `config`
VALUES (5467049693950640603, 0, 'platform_period_type', '平台许可类型', '1', 0, 0, 0, 16, '平台许可类型1-永久授权, 2-按年授权', NULL,
        '2023-03-01 18:32:57', NULL, NULL);
INSERT INTO `config`
VALUES (5467049693950640604, 0, 'server_period_type', '服务许可类型', '1', 0, 0, 0, 17, '服务许可类型1-永久授权, 2-按年授权', NULL,
        '2023-03-01 18:32:57', NULL, NULL);
INSERT INTO `config`
VALUES (5467049693950640605, 0, 'platfrom_alarm_day', '平台许可告警天数', '30', 0, 1, 1, 18, '平台有效期告警天数', NULL,
        '2023-03-01 18:32:57', NULL, '2023-05-30 10:44:59');
INSERT INTO `config`
VALUES (5467049693950640606, 0, 'server_alarm_day', '服务许可告警天数', '90', 0, 1, 1, 19, '平台服务有效期告警天数', NULL,
        '2023-03-01 18:32:57', NULL, NULL);
INSERT INTO `config`
VALUES (5467049693950640642, 0, 'auth_pk', '认证公钥',
        '5aoHRmRiyIsyKHMETWDYlfk8Y56EFz3nZia5s0w7on0mIzej7y/YYF5cpkjS+zqcoHy3Rph8MnAcml9DhsznsEcGGjFiKhNFvmBv0nbJ9LxnWOafZzjRlDvQMcQ8pi3p',
        1, 0, 0, 20, '认证公钥', NULL, '2023-03-01 18:32:57', NULL, NULL);
INSERT INTO `config`
VALUES (5467051745233732105, 0, 'auth_prik', '认证私钥', 'EV8Nm+fZWFFDxSIRR01Fd/l6leCmvHlugm0Em79R6Uoj+XXXDE0v6iEhdyouNDOJ',
        1, 0, 0, 21, '认证私钥', NULL, '2023-03-01 18:33:58', NULL, NULL);
INSERT INTO `config`
VALUES (5467205735288409733, 0, 'auth_pk_algo', '认证公钥算法', 'SM2', 0, 0, 0, 23, '认证公钥算法', NULL, '2023-03-01 06:50:27',
        NULL, NULL);
INSERT INTO `config`
VALUES (5492671023313322497, 2, 'auth_pk_algo_device', '设备认证公钥算法', '180fKjE62Ga+fJ0S/G7PmA==', 1, 0, 0, 23, '设备认证公钥算法',
        NULL, '2023-03-10 14:39:12', NULL, NULL);
INSERT INTO `config`
VALUES (5492747784646558208, 2, 'auth_pk_device', '设备认证公钥',
        'rggIj2Nrf5CZKt8jW1HU9OaFSPBeuSyYvZyqDWBfuQp12gWS85TsP6NbLvgyzM4Nrl7eWjhdp5Pby6pVP2dY2g==', 0, 0, 0, 21,
        '设备认证公钥', NULL, '2023-03-10 15:17:20', NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406976, 2, 'auth_prik_device', '设备认证私钥', 'nzuGjbRg72gCBRA/3UpF+MhdrDrQkIziepE8nmFZfPo=', 0, 0, 0,
        23, '设备认证私钥', NULL, '2023-03-10 15:17:50', NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406977, 2, 'async_pk_device', '设备2号非对称公钥',
        'fn54acAlL+H85iDLiAhv0IkNetMphiLh0goQOBDxnBDaOjDPlqY0Zn5wcSxll67f/mdzSmKrDFh2DCRt8p13Ur5YQWU4At5kEOA5mTzKH6KWKSMQgRw8251p7BEGgKHL',
        1, 0, 0, 24, '设备导入2号非对称公钥', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406978, 2, 'async_prik_device', '设备2号非对称私钥',
        'DHou5TK4PeUj4NsdUqm0V5BoIVtjrAa4Ycg8hoKd4Q+dsEkQ7rwmT7rj9SGDYNkD', 1, 0, 0, 25, '设备导入2号非对称私钥', NULL, NULL,
        NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406979, 2, 'sync_key_component', '设备2号对称密钥分量',
        'A5c9MfgftIuuqhuKrm3ghKpDfBlpoQvB6//+JMy2fEa58bpvAfyLv9RuZUpgTP/7', 1, 0, 0, 26, '设备2号对称密钥分量', NULL, NULL, NULL,
        NULL);
INSERT INTO `config`
VALUES (5492748794601406980, 2, 'async_key_device_flag', '是否开启导入2号非对称密钥', 'true', 0, 0, 0, 27, '是否开启导入2号非对称密钥', NULL,
        NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406981, 2, 'sync_key_component_flag', '是否开启导入2号对称密钥', 'true', 0, 0, 0, 28, '是否开启导入2号对称密钥', NULL,
        NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406982, 2, 'x_api_key_val', 'admin API请求头参数',
        'zM7MYRiXXu7pgCPspRhR70oMk0sJQgTU7wXIhD0e4NNz8GGRvSvd+Qw/X2NQBq2p', 1, 0, 0, 29, 'admin API请求头参数', NULL, NULL,
        NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406983, 1, 'api_token_expire', '接口token有效期', '10', 0, 1, 1, 30, '接口token有效期', NULL, NULL, NULL,
        '2023-05-30 10:45:07');
INSERT INTO `config`
VALUES (5492748794601406984, 1, 'web_token_expire', '页面token有效期', '10', 0, 1, 1, 31, '页面token有效期', NULL, NULL, NULL,
        '2023-07-07 16:03:47');
INSERT INTO `config`
VALUES (5492748794601406985, 3, 'kms_secstorage_is_merge', '密钥管理与文件加密服务是否合并', 'true', 0, 0, 0, 32,
        '密钥管理与文件加密服务是否合并 true false', NULL, NULL, NULL, '2023-05-05 10:18:04');
INSERT INTO `config`
VALUES (5492748794601406986, 3, 'add_service_is_auto_add_device', '添加服务是否自动添加设备', 'true', 0, 0, 0, 33,
        '添加服务是否自动添加设备 true false', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406987, 8, 'service_is_bind_license', '服务是否绑定许可证', 'true', 0, 0, 0, 34, '服务是否绑定许可证 true false',
        NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406988, 3, 'crypto_service_is_exist_kms', '密码机服务是否存在KMS', 'true', 0, 0, 0, 35,
        '密码机服务是否存在KMS true false', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406989, 1, 'tenant_register_audit_no_busi_type', '注册租户审核时，不选择业务服务类型', 'true', 0, 0, 0, 31,
        '注册租户审核时，不选择业务服务类型 true false', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406990, 4, 'app_service_type_to_busi_type', '添加应用时，上传服务类型ID，换算成业务类型和对应服务组', 'true', 0, 0, 0, 31,
        '添加应用时，上传服务类型ID，换算成业务类型和对应服务组 true false', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406991, 9, 'incre_record_clear_time', '增量记录表清理时间', '1', 0, 0, 0, 38, '清理几天前记录，默认1', NULL, NULL,
        NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406992, 9, 'incre_cal_clear_time', '增量计算表清理时间', '7', 0, 0, 0, 39, '清理几天前记录，默认7', NULL, NULL, NULL,
        NULL);
INSERT INTO `config`
VALUES (5492748794601406994, 9, 'unincre_cal_clear_time', '非增量记录表清理时间', '1', 0, 0, 0, 41, '清理几小时内的记录，默认1', NULL, NULL,
        NULL, '');
INSERT INTO `config`
VALUES (5492748794601406995, 2, 'is_show_device_group', '前端页面是否显示设备组相关数据', 'false', 0, 0, 0, 42,
        '前端页面是否显示设备组相关数据 true false', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406996, 3, 'is_show_service_group', '前端页面是否显示服务组相关数据', 'false', 0, 0, 0, 43,
        '前端页面是否显示服务组相关数据 true false', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406997, 3, 'is_chose_active_standby', '服务操作是否选择主备', 'false', 0, 0, 0, 44, '服务操作是否选择主备 true false',
        NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406998, 9, 'ccsp_init_time', '平台初始化时间', '2023-06-27 16:01:56', 0, 1, 0, 45, '平台初始化时修改该字段', NULL,
        NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601406999, 2, 'device_invoke_port', '服务下发设备端口取值方式', 'busi', 0, 0, 0, 46,
        '给服务下发设备连接端口的取值：busi：服务端口；extend：扩展端口固定为8008', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601407000, 2, 'device_auto_add_no_type', '自动添加设备时排除类型', '0', 0, 0, 0, 46,
        '添加服务自动添加分配设备时，排除配置的设备类型；输入设备类型ID字符串逗号隔开', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601407001, 3, 'serviceGroupOnlyBusi', '服务组单业务类型', 'false', 0, 0, 0, 47, '服务组单业务类型', NULL, NULL, NULL,
        NULL);
INSERT INTO `config`
VALUES (5492748794601407002, 3, 'serviceGroupDataIsolation', '服务组是否数据隔离', 'false', 0, 0, 0, 48, '服务组数据隔离', NULL, NULL,
        NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601407003, 3, 'crypto_service_use_kms_type', '密码机服务调用kms的形式', '1', 0, 0, 0, 49,
        '密码机服务调用kms的形式 1调用本机kms  2只调用远程kms   3先调本机kms报错再调远程kms', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601407004, 3, 'is_need_db_map_ip', '连接数据库是否需要映射地址', 'false', 0, 0, 0, 50, '连接数据库是否需要映射地址', NULL, NULL,
        NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601407005, 3, 'need_db_map_ip_service_type_id', '哪些服务类型需要使用映射地址连接数据库', '0', 0, 0, 0, 51,
        '哪些服务类型需要使用映射地址连接数据库', NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601407006, 7, 'db_gauss_casename', '高斯数据库实例名称', 'sansec', 0, 0, 0, 52, '若数据库是gauss，需要多配置一个实例名称', NULL,
        NULL, NULL, NULL);
INSERT INTO config
(CONFIG_ID, BUSI_SERVICE_TYPE_ID, CONFIG_CODE, CONFIG_NAME, CONFIG_VALUE, CONFIG_TYPE, MAINTAIN_FLAG, VISIBLE_FLAG, SORD_NUM, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (5492748794601407007, 8, 'pt_config_mode', '平台配置策略', '1', 0, 0, 0, 53, '平台配置策略：0；平台标准策略；1：移动定制配置', NULL, NULL, NULL, NULL);

INSERT INTO config
VALUES (5492748794601407008, 4, 'app_aksk_key_num', '应用可添加AKSK密钥数量', '10', 0, 0, 0, 54, '应用可添加AKSK密钥数量', NULL, NULL, NULL, NULL);



-- ----------------------------
-- Table structure for config_regular
-- ----------------------------
DROP TABLE IF EXISTS `config_regular`;
CREATE TABLE `config_regular`  (
   `ID` bigint(20) NOT NULL COMMENT 'ID',
   `CONFIG_CODE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '配置编码',
   `REGULAR` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正则表达式',
   `INVALID_FLAG` int(11) NULL DEFAULT 0 COMMENT '是否作废;默认为0',
   `REMARK` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
   `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
   `CREATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
   `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
   `UPDATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新时间',
   PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '配置表正则校验' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of config_regular
-- ----------------------------
INSERT INTO `config_regular` VALUES (1, 'openAuthCodeLogin', '^(true)|(false)$', 0, '是否开启口令登录，true\false', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (2, 'defaultAuthCode', NULL, 1, '默认口令', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (3, 'openUKeyLogin', '^(true)|(false)$', 0, '是否开启UKey登录,，true\false', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (4, 'authCodeHistoryLimit', '^([1-9]|[1-9][0-9]|100)$', 0, '历史口令限制，[1,100]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (5, 'unloggedTime', '^([1-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '长时间未登录禁用租户，[1,365]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (6, 'authCodeExpireDate', '^([1-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '口令有效期，[1,365]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (7, 'authCodeExpirationReminder', '^([1-9]|[1-2][0-9]|30)$', 0, '口令有效期告警，[1,30]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (8, 'loginFailuresAllowedTimes', '^([3-9]|[1-2][0-9]|30)$', 0, '登陆失败次数限制，[3,30]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (9, 'loginErrorLockTime', '^([5-9]|[1-2][0-9]|30)$', 0, '登陆失败锁定时长(分钟)，[5,30]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (10, 'forceUpdatePassword', '^(true)|(false)$', 0, '是否强制修改默认口令，true\false', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (11, 'screen_template_code', NULL, 1, '大屏模板编号', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (12, 'quota_alarm_percentage', '^([1-9]|[1-9][0-9]|100)$', 0, '大屏配额告警百分比，[1,100]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (13, 'screen_quota_scroll_flag', '^(true)|(false)$', 0, '大屏滚动告警是否开启，true\false', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (14, 'platfrom_alarm_day', '^([7-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '平台许可告警天数，[7,365]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (15, 'server_alarm_day', '^([7-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '服务许可告警天数，[7,365]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (16, 'api_token_expire', '^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|1[0-9][0-9][0-7][0-9])|10080+$', 0, '接口token有效期，[1,10080]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (17, 'web_token_expire', '^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|1[0-9][0-9][0-7][0-9])|10080+$', 0, '页面token有效期，[1,10080]', NULL, NULL, NULL, NULL);


-- ----------------------------
-- Table structure for dic_busi_service_type
-- ----------------------------
DROP TABLE IF EXISTS `dic_busi_service_type`;
CREATE TABLE `dic_busi_service_type`
(
    `ID`                     bigint                                                 NOT NULL COMMENT '主键',
    `PARENT_ID`              int                                                    NOT NULL COMMENT '父级ID',
    `BUSI_SERVICE_TYPE_CODE` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '业务服务类型编码',
    `BUSI_SERVICE_TYPE_NAME` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '业务服务类型名称',
    `SORD_NUM`               int                                                    NOT NULL COMMENT '排序',
    `REMARK`                 varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`              bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`            varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`              bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`            varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务服务类型字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_busi_service_type
-- ----------------------------
INSERT INTO `dic_busi_service_type`
VALUES (0, 1, 'common', '公共', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_service_type`
VALUES (1, 1, 'tenant', '租户', 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_service_type`
VALUES (2, 1, 'device', '设备', 2, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_service_type`
VALUES (3, 1, 'servicemgt', '服务', 3, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_service_type`
VALUES (4, 1, 'app', '应用', 4, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_service_type`
VALUES (5, 1, 'login', '登录', 5, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_service_type`
VALUES (6, 1, 'user', '用户', 6, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_service_type`
VALUES (7, 0, 'sysparam', '系统配置', 7, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_service_type`
VALUES (8, 1, 'license', '许可证', 8, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_service_type`
VALUES (9, 1, 'statistic', '统计', 9, NULL, NULL, NULL, NULL, NULL);


-- ----------------------------
-- Table structure for dic_busi_type
-- ----------------------------
DROP TABLE IF EXISTS `dic_busi_type`;
CREATE TABLE `dic_busi_type`
(
    `ID`             bigint                                                 NOT NULL COMMENT 'ID',
    `BUSI_TYPE_NAME` varchar(270) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '业务类型名称',
    `REMARK`         varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`      bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`      bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务类型字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_busi_type
-- ----------------------------
INSERT INTO `dic_busi_type`
VALUES (1, '数据加解密', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_type`
VALUES (2, '签名验签', NULL, NULL, NULL, NULL, NULL);
-- INSERT INTO `dic_busi_type`
-- VALUES (3, '杂凑业务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_type`
VALUES (4, '密钥管理', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_type`
VALUES (5, '时间戳', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_type`
VALUES (6, '协同签名', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_type`
VALUES (7, '动态令牌', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_type`
VALUES (8, '数据库加密', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_type`
VALUES (9, '文件加密', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_type`
VALUES (10, '电子签章', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_type`
VALUES (11, 'SSLVPN加密通道', NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_device_type
-- ----------------------------
DROP TABLE IF EXISTS `dic_device_type`;
CREATE TABLE `dic_device_type`
(
    `ID`                      bigint                                                 NOT NULL COMMENT '主键',
    `DEVICE_TYPE_NAME`        varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `REMARK`                  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `DEFAULT_FLAG`            int                                                    NOT NULL DEFAULT 0 COMMENT '是否默认 1默认 0不默认',
    `DEVICE_TYPE_VALUE`       int NULL DEFAULT NULL,
    `SUPPORT_BUSI_TYPES`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '支持的业务类型id，多个类型以逗号分隔',
    `CODE`                    varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '设备类型编码，与厂商编码共同使用，获取设备信息，不可修改',
    `IS_PHYSICAL`             decimal(8, 0) NULL DEFAULT NULL COMMENT '业务端口是否需要密码1需要 0不需要',
    `VENDOR_ID`               bigint                                                 NOT NULL COMMENT '所属厂商',
    `READ_INFO`               int NULL DEFAULT NULL COMMENT '读取设备信息 1需要 0不需要',
    `NEED_PASSWORD`           int NULL DEFAULT NULL,
    `KEY_TEMPLET_IDS`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '该类型设备支持的密钥模板id，多个以逗号分隔',
    `SUPPORT_SNMP`            int NULL DEFAULT NULL COMMENT '是否支持SNMP监控0不支持 1支持',
    `HCCS_IMAGE`              varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '超融合虚拟机镜像类型',
    `SUPPORT_MAIN_KEY`        int NULL DEFAULT NULL COMMENT '是否支持生成主密钥0不支持 1支持',
    `SUPPORT_SEC_MANAGE`      int NULL DEFAULT NULL COMMENT '是否支持安全管理，0不支持 1支持',
    `DEFAULT_KEY_TEMPLET_IDS` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '默认自动生成密钥时支持的密钥模板id，多个以逗号分隔',
    `SUPPORT_GEN_KEY`         int NULL DEFAULT NULL COMMENT '是否支持生成密钥',
    `PARENT_ID`               int NULL DEFAULT NULL COMMENT '上级ID顶级默认0',
    `INVALID_FLAG`            int NULL DEFAULT 0 COMMENT '无效标识 0可用 1无效',
    `FAMILY_TYPE`             int NULL DEFAULT NULL COMMENT '1云密码机、2物理机、3虚拟机、4门禁卡 99其他',
    `OWN_MANAGE_FLAG`         int NULL DEFAULT NULL COMMENT '(是否监管 1监管 0只登记不监管,用于资产登记) 备用',
    `MACHINE_TYPE`            int NULL DEFAULT NULL COMMENT '密码机服务类型(提供密码服务使用) 6服务器密码机 12签名验签服务器 15时间戳服务器',
    `CREATE_BY`               bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`             varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`               bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`             varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    `EXTEND1`                 varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备用',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '设备类型字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_device_type
-- ----------------------------

-- ----------------------------
-- Table structure for dic_gentype
-- ----------------------------
DROP TABLE IF EXISTS `dic_gentype`;
CREATE TABLE `dic_gentype`
(
    `ID`           bigint                                                 NOT NULL COMMENT '主键;',
    `D_NAME`       varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `D_VALUE`      int                                                    NOT NULL COMMENT '1 随机数 0 分量',
    `ISSYM`        int NULL DEFAULT NULL COMMENT '1 对称',
    `DEFAULT_FLAG` int                                                    NOT NULL DEFAULT 0 COMMENT '是否默认 1默认 0不默认',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '密钥生成类型字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_gentype
-- ----------------------------

-- ----------------------------
-- Table structure for dic_keyalgo
-- ----------------------------
DROP TABLE IF EXISTS `dic_keyalgo`;
CREATE TABLE `dic_keyalgo`
(
    `ID`           bigint                                                 NOT NULL,
    `D_NAME`       varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `D_VALUE`      int                                                    NOT NULL COMMENT '1 对称 0 非对称',
    `D_LENGTH_ID`  varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '长度id 多个，分割',
    `D_GENTYPE_ID` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '生成方式 0 分量 1 随机数',
    `D_KEYUSE_ID`  varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '密钥用途 多个,分割',
    `KMS_VALUE`    int                                                    NOT NULL COMMENT 'KMS字典值',
    `DEFAULT_FLAG` int                                                    NOT NULL DEFAULT 0 COMMENT '是否默认 1默认 0不默认',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '密钥算法字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_keyalgo
-- ----------------------------

-- ----------------------------
-- Table structure for dic_keylen
-- ----------------------------
DROP TABLE IF EXISTS `dic_keylen`;
CREATE TABLE `dic_keylen`
(
    `ID`           bigint                                                 NOT NULL,
    `D_NAME`       varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '算法名称',
    `D_VALUE`      int                                                    NOT NULL COMMENT '算法长度',
    `DEFAULT_FLAG` int                                                    NOT NULL DEFAULT 0 COMMENT '是否默认 1默认 0不默认',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '密钥长度字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_keylen
-- ----------------------------

-- ----------------------------
-- Table structure for dic_keylifeattribute
-- ----------------------------
DROP TABLE IF EXISTS `dic_keylifeattribute`;
CREATE TABLE `dic_keylifeattribute`
(
    `ID`           bigint                                                 NOT NULL,
    `D_NAME`       varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `D_VALUE`      int                                                    NOT NULL COMMENT '1 对称 0 非对称',
    `DEFAULT_FLAG` int                                                    NOT NULL DEFAULT 0 COMMENT '是否默认 1默认 0不默认',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '密钥生命周期属性字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_keylifeattribute
-- ----------------------------

-- ----------------------------
-- Table structure for dic_keytype
-- ----------------------------
DROP TABLE IF EXISTS `dic_keytype`;
CREATE TABLE `dic_keytype`
(
    `ID`           bigint                                                 NOT NULL,
    `D_NAME`       varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `D_VALUE`      int                                                    NOT NULL COMMENT '1 对称 0 非对称',
    `DEFAULT_FLAG` int                                                    NOT NULL DEFAULT 0 COMMENT '是否默认 1默认 0不默认',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '密钥类型字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_keytype
-- ----------------------------

-- ----------------------------
-- Table structure for dic_keyuse
-- ----------------------------
DROP TABLE IF EXISTS `dic_keyuse`;
CREATE TABLE `dic_keyuse`
(
    `ID`           bigint                                                 NOT NULL,
    `D_NAME`       varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `D_VALUE`      int                                                    NOT NULL COMMENT '1 对称 0 非对称',
    `ISSYM`        int                                                    NOT NULL COMMENT '1对称 0 非对称',
    `KMS_VALUE`    int                                                    NOT NULL COMMENT 'kms字典值',
    `DEFAULT_FLAG` int                                                    NOT NULL DEFAULT 0 COMMENT '是否默认 1默认 0不默认',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '密钥用途字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_keyuse
-- ----------------------------

-- ----------------------------
-- Table structure for dic_sys_data
-- ----------------------------
DROP TABLE IF EXISTS `dic_sys_data`;
CREATE TABLE `dic_sys_data`
(
    `ID`           bigint                                                 NOT NULL COMMENT '主键',
    `DICT_TYPE`    varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典类型',
    `DICT_LABEL`   varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典标签',
    `DICT_VALUE`   varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典值',
    `DEFAULT_FLAG` int                                                    NOT NULL DEFAULT 0 COMMENT '是否默认 1默认 0不默认',
    `SORD_NUM`     int                                                    NOT NULL COMMENT '排序',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '系统数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_sys_data
-- ----------------------------

-- ----------------------------
-- Table structure for dic_tenant_status
-- ----------------------------
DROP TABLE IF EXISTS `dic_tenant_status`;
CREATE TABLE `dic_tenant_status`
(
    `ID`          bigint                                                 NOT NULL COMMENT '主键',
    `S_NAME`      varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '状态名1初始化、2启动中、3启动失败、4运行、5扩展中、6恢复中、7停服中8、停服、9销毁中、10销毁失败',
    `S_VALUE`     int NULL DEFAULT NULL COMMENT '状态值',
    `CREATE_BY`   bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`   bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    `EXTEND1`     varchar(180) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备用1',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户状态字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_tenant_status
-- ----------------------------

-- ----------------------------
-- Table structure for organization_info
-- ----------------------------
DROP TABLE IF EXISTS `organization_info`;
CREATE TABLE `organization_info`
(
    `ORGANIZATION_ID`   bigint                                                 NOT NULL COMMENT '组织机构ID',
    `ORGANIZATION_CODE` varchar(900) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '组织机构代码',
    `ORGANIZATION_NAME` varchar(900) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '组织机构名称',
    `PARENT_ID`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '父ID',
    `SORD_NUM`          int NULL DEFAULT NULL COMMENT '排序序号',
    `INVALID_FLAG`      int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`            varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`         bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`         bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ORGANIZATION_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '组织字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of organization_info
-- ----------------------------

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`
(
    `JOB_ID`          bigint                                                 NOT NULL COMMENT '任务号',
    `JOB_NAME`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务名称',
    `JOB_GROUP`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务组名',
    `SERVER_ID`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务模块',
    `METHOD_URL`      varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调用接口',
    `JSON_PARAM`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'json格式参数',
    `CRON_EXPRESSION` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'CRON执行表达式',
    `MISFIRE_POLICY`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '3' COMMENT '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
    `CONCURRENT`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）',
    `JOB_STATUS`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
    `CREATED_BY`      bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATED_BY`      bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    `REMARK`          varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`JOB_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '定时任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job`
VALUES (2, '2', 'common', 'ccsp-base-common', NULL, 'userManagerTaskServiceImpl.userUnLockTask()', '0/10 * * * * ? ',
        '3', '1', '0', NULL, '2023-03-04 11:22:23', NULL, NULL, 'test');
INSERT INTO `sys_job`
VALUES (3, '3', 'common', 'ccsp-base-common', NULL, 'userManagerTaskServiceImpl.disableUserTask()', '0 0/1 * * * ?',
        '3', '1', '0', NULL, '2023-03-04 11:22:23', NULL, NULL, 'test');
INSERT INTO `sys_job`
VALUES (4, '4', 'common', 'ccsp-base-common', NULL, 'userManagerTaskServiceImpl.authCodeExpireDateCheckTask()',
        '0 0/1 * * * ?', '3', '1', '0', NULL, '2023-03-04 11:22:23', NULL, NULL, 'test');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`
(
    `JOB_LOG_ID`     bigint                                                NOT NULL COMMENT '任务日志ID',
    `JOB_ID`         bigint                                                NOT NULL COMMENT '任务ID',
    `JOB_MESSAGE`    varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志信息',
    `STATUS`         varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '执行状态（0失败 1正常）',
    `EXCEPTION_INFO` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '异常信息',
    `CREATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建时间',
    `TRIGGER_TIME`   bigint NULL DEFAULT NULL COMMENT '触发时间',
    PRIMARY KEY (`JOB_LOG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '定时任务执行日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`
(
    `MENU_ID`      bigint(20) NOT NULL COMMENT '菜单ID',
    `MENU_NAME`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '菜单名称',
    `PARENT_ID`    bigint(20) NOT NULL COMMENT '父菜单ID',
    `PATH`         varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '路由地址',
    `COMPONENT`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '组件路径',
    `IS_CACHE`     int(11) NULL DEFAULT NULL COMMENT '是否缓存',
    `MENU_TYPE`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '菜单类型（M目录 C菜单 F按钮）',
    `VISIBLE`      int(11) NULL DEFAULT NULL COMMENT '菜单状态（0显示 1隐藏）',
    `STATUS`       int(11) NULL DEFAULT NULL COMMENT '菜单状态（0正常 1停用）',
    `PERMS`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限标识',
    `ICON`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '菜单图标',
    `ACTIVE_MENU`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '菜单分组',
    `ORDER_NUM`    int(11) NULL DEFAULT NULL COMMENT '排序序号',
    `IS_IFRAME`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '是否iframe',
    `IFRAME_URL`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'iframe路径',
    `INVALID_FLAG` int(11) NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint(20) NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`MENU_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu`
VALUES (100100100, '平台首页', 0, '/ptindex', NULL, 0, 'M', 1, 0, 'pt:index:nolicensequota', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (101100100, '租户首页', 0, '/tenantindex', NULL, 0, 'M', 1, 0, 'tenant:index:nolicensequota', NULL, NULL, 2, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (102100100, '许可授权管理', 0, '/license', 'Layout', 0, 'M', 0, 0, NULL, 'license', NULL, 3, NULL, NULL, 0, NULL, NULL,
        NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (102101100, '许可申请', 102100100, '/system/license/apply', '/system/license/apply.vue', 0, 'C', 0, 0,
        'license:apply:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (102101101, '申请', 102101100, NULL, NULL, 0, 'F', 0, 0, 'license:apply:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (102101102, '下载凭证', 102101100, NULL, NULL, 0, 'F', 0, 0, 'license:apply:download', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (102102100, '许可管理', 102100100, '/system/license', '/system/license/index.vue', 0, 'C', 0, 0, 'license:use:list',
        NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (102102101, '解析许可内容', 102102100, NULL, NULL, 0, 'F', 0, 0, 'license:use:parse', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (102102102, '导入许可', 102102100, NULL, NULL, 0, 'F', 0, 0, 'license:use:import', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (102103100, '服务续期管理', 102100100, '/license/services/renewal', '/services/renewal/index.vue', 0, 'C', 0, 0,
        'license:use:serverList', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (102103101, '续约', 102103100, NULL, NULL, 0, 'F', 0, 0, 'license:use:renewServer', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103100100, '租户管理', 0, '/tenant', 'Layout', 0, 'M', 0, 0, NULL, 'tenant', NULL, 4, NULL, NULL, 0, NULL, NULL,
        NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101100, '租户管理', 103100100, '/tenant/manage', '/tenant/manage/index.vue', 0, 'C', 0, 0, 'common:tenant:list',
        NULL, 'tenantManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101101, '详情', 103101100, '/tenant/manage/detail', '/tenant/manage/detail/index.vue', 0, 'F', 1, 0,
        'common:tenant:info', NULL, 'tenantManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101102, '编辑', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101103, '启动', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:start', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101104, '停服', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:stop', NULL, NULL, 4, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101105, '删除', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:delete', NULL, NULL, 5, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101106, '设备管理', 103101100, '/tenant/manage/devicelist', '/tenant/manage/devicelist.vue', 0, 'F', 1, 0,
        'common:tenant:deviceList', NULL, 'tenantManage', 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101107, '绑定设备', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:addDevice', NULL, NULL, 7, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101108, '释放设备', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:delDevice', NULL, NULL, 8, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101109, '服务管理', 103101100, '/tenant/manage/servicelist', '/tenant/manage/servicelist.vue', 0, 'F', 1, 0,
        'common:tenant:serviceList', NULL, 'tenantManage', 9, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101110, '绑定服务', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:addService', NULL, NULL, 10, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101111, '释放服务', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:delService', NULL, NULL, 11, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101112, '配置地址', 103101100, '/tenant/manage/busiurl', '/tenant/manage/busiurl.vue', 0, 'F', 1, 0,
        'common:busiurl:infoList', NULL, 'tenantManage', 12, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101113, '新增业务地址', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:add', NULL, NULL, 13, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101114, '编辑业务地址', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:edit', NULL, NULL, 14, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101115, '删除业务地址', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:delete', NULL, NULL, 15, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101116, '配额管理', 103101100, '/tenant/manage/quota', '/tenant/manage/quota.vue', 0, 'F', 1, 0,
        'quota:info:list', NULL, 'tenantManage', 13, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103101117, '设置配额', 103101100, NULL, NULL, 0, 'F', 0, 0, 'quota:info:edit', NULL, NULL, 16, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103102100, '待审核租户', 103100100, '/tenant/audit/waiting', '/tenant/audit/waiting.vue', 0, 'C', 0, 0,
        'common:tenantAudit:auditList', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103102101, '审核', 103102100, NULL, NULL, 0, 'F', 0, 0, 'common:tenantAudit:audit', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103103100, '已审核租户', 103100100, '/tenant/audit/audited', '/tenant/audit/audited.vue', 0, 'C', 0, 0,
        'common:tenantAudit:auditList', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103103101, '详情', 103103100, NULL, NULL, 0, 'F', 0, 0, 'common:tenantAutid:info', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (103103102, '删除', 103103100, NULL, NULL, 0, 'F', 0, 0, 'common:tenantAutid:delete', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104100100, '服务管理', 0, '/services', 'Layout', 0, 'M', 0, 0, NULL, 'myapp', NULL, 5, NULL, NULL, 0, NULL, NULL,
        NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104101100, '服务信息', 104100100, '/tenant/serviceinfo', '/tenant/serviceinfo/index.vue', 0, 'C', 0, 0,
        'servicemgt:tenantServiceType:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104102100, '服务组管理', 104100100, '/services/group', '/services/group/index.vue', 0, 'C', 0, 0,
        'servicemgt:servicegroup:list', NULL, 'serviceGroup', 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104102101, '新建', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:add', NULL, NULL, 1, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104102102, '编辑', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:edit', NULL, NULL, 2, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104102103, '服务管理', 104102100, '/services/group/servicelist', '/services/group/servicelist.vue', 0, 'F', 1, 0,
        'servicemgt:servicegroup:serviceList', NULL, 'serviceGroup', 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104102104, '删除', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:delete', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104102105, '可绑定服务', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:canBindServiceList', NULL,
        NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104102106, '绑定服务', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:addService', NULL, NULL, 6,
        NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104102107, '释放服务', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:delService', NULL, NULL, 7,
        NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104103100, '服务管理', 104100100, '/tenant/service', '/tenant/service/index.vue', 0, 'C', 0, 0,
        'servicemgt:tenantService:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104103101, '释放服务', 104103100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenant:delService', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104103102, '编辑', 104103100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenant:serviceEdit', NULL, NULL, 2, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104104100, '服务管理', 104100100, '/services/service', '/services/service/index.vue', 0, 'C', 0, 0,
        'servicemgt:serviceinfo:list', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104104101, '新建', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:add', NULL, NULL, 1, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104104102, '编辑', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:edit', NULL, NULL, 2, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104104103, '删除', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:delete', NULL, NULL, 3, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104104104, '启动', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:start', NULL, NULL, 4, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104104105, '停止', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:stop', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104105100, '服务类型管理', 104100100, '/services/serviceType', '/services/serviceType/index.vue', 0, 'C', 0, 0,
        'servicemgt:servicetype:list', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104105101, '编辑', 104105100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicetype:edit', NULL, NULL, 1, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104106100, '网关管理', 104100100, '/services/gateway', '/services/gateway/index.vue', 0, 'C', 0, 0,
        'servicemgt:gateway:list', NULL, 'gateway', 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104106101, '新建', 104106100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:gateway:add', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104106102, '编辑', 104106100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:gateway:edit', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104106103, '删除', 104106100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:gateway:delete', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104107100, '路由管理', 104100100, '/services/gateway/routes', '/services/gateway/routes.vue', 0, 'F', 1, 0,
        'servicemgt:route:list', NULL, 'gateway', 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104107101, '详情', 104107100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:route:detail', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104108100, '服务日志', 104100100, '/services/operlog', '/services/operlog/index.vue', 0, 'C', 0, 0,
        'servicemgt:logs:list', NULL, NULL, 9, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104109100, '数据库管理', 104100100, '/database/list', '/database/list/index.vue', 0, 'C', 0, 0,
        'servicemgt:dbinfo:list', NULL, 'databaseMenu', 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104109101, '新建', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbinfo:add', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104109102, '编辑', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbinfo:edit', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104109103, '删除', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbinfo:delete', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104109104, '实例列表', 104109100, '/database/dbunit/list', '/database/dbunit/index.vue', 0, 'F', 1, 0,
        'servicemgt:dbunit:list', NULL, 'databaseMenu', 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104109105, '新建', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbunit:add', NULL, NULL, 5, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (104109107, '删除', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbunit:delete', NULL, NULL, 7, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105100100, '设备管理', 0, '/device', 'Layout', 0, 'M', 0, 0, NULL, 'device', 'vsmHost', 6, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105101100, '云密码机', 105100100, '/device/vsmhost', '/device/vsmhost/index.vue', 0, 'C', 0, 0,
        'device:vsmHost:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105101101, '新建', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsmHost:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105101102, '编辑', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsmHost:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105101103, '删除', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsmHost:delete', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105101104, '详情', 105101100, '/device/vsmhost/detail', '/device/vsmhost/detail.vue', 0, 'F', 1, 0,
        'device:vsmHost:detail', NULL, 'vsmHost', 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105101105, '网络配置', 105101100, '/device/vsmhost/vsmNetwork', '/device/vsmhost/vsmNetwork.vue', 0, 'F', 1, 0,
        'device:vsmNetConf:list', NULL, 'vsmHost', 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105101106, '删除', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsmNetConf:delete', NULL, NULL, 6, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105101107, '新增', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsmNetConf:add', NULL, NULL, 7, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105101108, '创建虚拟密码机', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:add', NULL, NULL, 8, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105102100, '虚拟密码机', 105100100, '/device/vsm', '/device/vsm/index.vue', 0, 'C', 0, 0, 'device:vsm:list', NULL,
        'vsmManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105102101, '创建虚拟密码机', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105102102, '详情', 105102100, '/device/vsm/detail', '/device/vsm/detail.vue', 0, 'F', 1, 0, 'device:vsm:detail',
        NULL, 'vsmManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105102103, '编辑', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:edit', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105102104, '删除', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:delete', NULL, NULL, 4, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105102105, '启动', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:start', NULL, NULL, 5, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105102106, '停止', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:stop', NULL, NULL, 6, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105102107, '重启', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:restart', NULL, NULL, 7, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105103100, '物理密码机', 105100100, '/device/host', '/device/host/index.vue', 0, 'C', 0, 0, 'device:hsm:list', NULL,
        'hostMange', 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105103101, '新建', 105103100, NULL, NULL, 0, 'F', 0, 0, 'device:hsm:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105103102, '编辑', 105103100, NULL, NULL, 0, 'F', 0, 0, 'device:hsm:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105103103, '删除', 105103100, NULL, NULL, 0, 'F', 0, 0, 'device:hsm:delete', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105103104, '详情', 105103100, '/device/host/detail', '/device/host/detail.vue', 0, 'F', 1, 0, 'device:hsm:detail',
        NULL, 'hostMange', 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105103105, '重启', 105103100, NULL, NULL, 0, 'F', 0, 0, 'device:hsm:restart', NULL, NULL, 5, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105103106, '配置网络', 105103100, NULL, NULL, 0, 'F', 0, 0, 'device:hsm:netConfig', NULL, NULL, 6, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105104100, '设备信息', 105100100, '/tenant/device', '/tenant/device/index.vue', 0, 'C', 0, 0,
        'device:tenant:deviceList', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105104108, '释放设备', 105104100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:delDevice', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105105100, '设备组管理', 105100100, '/device/deviceGroup', '/device/deviceGroup/index.vue', 0, 'C', 0, 0,
        'device:group:list', NULL, 'deviceGroup', 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105105101, '新建', 105105100, NULL, NULL, 0, 'F', 0, 0, 'device:group:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105105102, '编辑', 105105100, NULL, NULL, 0, 'F', 0, 0, 'device:group:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105105103, '删除', 105105100, NULL, NULL, 0, 'F', 0, 0, 'device:group:delete', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105105104, '设备管理', 105105100, '/device/deviceGroup/detail', '/device/deviceGroup/detail.vue', 0, 'F', 1, 0,
        'device:group:deviceList', NULL, 'deviceGroup', 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105105105, '绑定设备', 105105100, NULL, NULL, 0, 'F', 0, 0, 'device:group:deviceBind', NULL, NULL, 5, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105105106, '释放设备', 105105100, NULL, NULL, 0, 'F', 0, 0, 'device:group:deviceDelete', NULL, NULL, 6, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105106100, '厂商管理', 105100100, '/device/vendor', '/device/vendor/index.vue', 0, 'C', 0, 0, 'device:vendor:list',
        NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105106101, '新增', 105106100, NULL, NULL, 0, 'F', 0, 0, 'device:vendor:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105106102, '编辑', 105106100, NULL, NULL, 0, 'F', 0, 0, 'device:vendor:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105106103, '删除', 105106100, NULL, NULL, 0, 'F', 0, 0, 'device:vendor:delete', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105107100, '地址资源管理', 105100100, '/device/ipsource', '/device/ipsource/index.vue', 0, 'C', 0, 0,
        'device:net:list', NULL, NULL, 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105107101, '新增', 105107100, NULL, NULL, 0, 'F', 0, 0, 'device:net:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105107102, '删除', 105107100, NULL, NULL, 0, 'F', 0, 0, 'device:net:delete', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105108100, '设备类型管理', 105100100, '/device/devicetypes', '/device/devicetypes/index.vue', 0, 'C', 0, 0,
        'device:type:list', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105108101, '新增', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:type:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105108102, '编辑', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:type:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105108103, '删除', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:type:delete', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105108104, '停用', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:type:disable', NULL, NULL, 4, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (105108105, '启用', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:type:enable', NULL, NULL, 5, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106100100, '应用管理', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'app', NULL, 7, NULL, NULL, 0, NULL, NULL,
        NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106101100, '待审核应用', 106100100, '/application/audit', '/application/audit/index.vue', 0, 'C', 0, 0,
        'app:waitAudit:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106101101, '审批', 106101100, NULL, NULL, 0, 'F', 0, 0, 'app:waitAudit:audit', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106102100, '应用信息', 106100100, '/application/list', '/application/list/index.vue', 0, 'C', 0, 0,
        'app:audited:list', NULL, 'appManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106102101, '新建', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106102102, '详情', 106102100, '/application/detail', '/application/list/detail.vue', 0, 'F', 1, 0,
        'app:audited:detail', NULL, 'appManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106102103, '编辑', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:edit', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106102104, '删除', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:delete', NULL, NULL, 4, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106102105, '业务管理', 106102100, '/application/appbuss/detail', '/application/list/appBussList.vue', 0, 'F', 1, 0,
        'app:audited:busiTypeList', NULL, 'appManage', 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106102106, '新增', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:busiTypeAdd', NULL, NULL, 6, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106102107, '删除', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:busiTypeDelete', NULL, NULL, 7, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106102108, '编辑', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:busiTypeEdit', NULL, NULL, 8, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES(106102109, '凭证管理', 106102100, '/application/auth', '/application/list/auth.vue', 0, 'F', 1, 0, 'app:aksk:list', NULL, 'appManage', 9, NULL, NULL, 0,
       NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES(106102110, '新增', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:aksk:add', NULL, NULL, 10, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES(106102111, '编辑', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:aksk:edit', NULL, NULL, 11, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES(106102112, '启用', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:aksk:start', NULL, NULL, 12, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES(106102113, '禁用', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:aksk:stop', NULL, NULL, 13, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES(106102114, '删除', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:aksk:delete', NULL, NULL, 14, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (106103100, '审批历史', 106100100, '/application/audit/history', '/application/audit/history.vue', 0, 'C', 0, 0,
        'app:auditHistory:list', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (107100100, '我的应用', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'app', NULL, 8, NULL, NULL, 0, NULL, NULL,
        NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (107101100, '应用注册', 107100100, '/application/register', '/application/register/index.vue', 0, 'C', 0, 0,
        'app:appAdd:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (107102100, '申请历史', 107100100, '/application/register/history', '/application/register/history.vue', 0, 'C', 0,
        0, 'app:applyHistory:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (107103100, '我的应用', 107100100, '/application/myapp', '/application/myapp/index.vue', 0, 'C', 1, 0,
        'app:myApp:list', NULL, 'myAppManage', 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (107103101, '详情', 107103100, '/application/myapp/detail', '/application/myapp/detail.vue', 0, 'F', 1, 0,
        'app:myApp:detail', NULL, 'myAppManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (107103102, '编辑', 107103100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (108100100, '证书管理', 0, '/cert', 'Layout', 0, 'M', 0, 0, NULL, 'cert', NULL, 9, NULL, NULL, 0, NULL, NULL, NULL,
        NULL, NULL);
INSERT INTO `sys_menu`
VALUES (108101100, '信任域管理', 108100100, '/cert/domain', '/cert/domain/index.vue', 0, 'C', 0, 0, 'cert:domain:list', NULL,
        NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (108101101, '新建', 108101100, NULL, NULL, 0, 'F', 0, 0, 'cert:domain:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (108101102, '编辑', 108101100, NULL, NULL, 0, 'F', 0, 0, 'cert:domain:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (108101103, '删除', 108101100, NULL, NULL, 0, 'F', 0, 0, 'cert:domain:delete', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109100100, '数据加解密', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'jiajiemi', NULL, 10, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109101100, '资源信息', 109100100, '/services/resource/pki', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109101101, '根据业务类型查询业务地址', 109100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109101102, 'SDK下载', 109101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109101103, 'API下载', 109101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109101104, '服务列表', 109101100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109101105, '设备列表', 109101100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109102100, '应用管理', 109100100, '/application/child/list/pki', '/application/childlist/index.vue', 0, 'C', 0, 0,
        'app:audited:list', NULL, 'appManagePki', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109102101, '新建', 109102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109102102, '详情', 109102100, '/application/child/detail/pki', '/application/list/detail.vue', 0, 'F', 1, 0,
        'app:audited:detail', NULL, 'appManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109102103, '编辑', 109102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:edit', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109102104, '删除', 109102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:busiTypeDelete', NULL, NULL, 4, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (109102105, '绑定应用', 109102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:binding', NULL, NULL, 5, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110100100, '签名验签', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'yanqian', NULL, 11, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110101100, '信任域管理', 110100100, '/cert/domain', '/cert/domain/index.vue', 0, 'C', 0, 0, 'cert:domain:list', NULL,
        NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110101101, '新建', 110101100, NULL, NULL, 0, 'F', 0, 0, 'cert:domain:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110101102, '编辑', 110101100, NULL, NULL, 0, 'F', 0, 0, 'cert:domain:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110101103, '删除', 110101100, NULL, NULL, 0, 'F', 0, 0, 'cert:domain:delete', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110102100, '资源信息', 110100100, '/services/resource/svs', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110102101, '根据业务类型查询业务地址', 110100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110102102, 'SDK下载', 110102100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110102103, 'API下载', 110102100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110102104, '服务列表', 110102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110102105, '设备列表', 110102100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110103100, '应用管理', 110100100, '/application/child/list/svs', '/application/childlist/index.vue', 0, 'C', 0, 0,
        'app:audited:list', NULL, 'appManageSvs', 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110103101, '新建', 110103100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110103102, '详情', 110103100, '/application/child/detail/svs', '/application/list/detail.vue', 0, 'F', 1, 0,
        'app:audited:detail', NULL, 'appManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110103103, '编辑', 110103100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:edit', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110103104, '删除', 110103100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:busiTypeDelete', NULL, NULL, 4, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (110103105, '绑定应用', 110103100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:binding', NULL, NULL, 5, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111100100, '杂凑计算', 0, '/application', 'Layout', 0, 'M', 1, 0, NULL, 'jiajiemi', NULL, 12, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111101100, '资源信息', 111100100, '/services/resource/digest', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111101101, '根据业务类型查询业务地址', 111100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111101102, 'SDK下载', 111101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111101103, 'API下载', 111101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111101104, '服务列表', 111101100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111101105, '设备列表', 111101100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111102100, '应用管理', 111100100, '/application/child/list/digest', '/application/childlist/index.vue', 0, 'C', 0,
        0, 'app:audited:list', NULL, 'appManagePki', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111102101, '新建', 111102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111102102, '详情', 111102100, '/application/child/detail/digest', '/application/list/detail.vue', 0, 'F', 1, 0,
        'app:audited:detail', NULL, 'appManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111102103, '编辑', 111102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:edit', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111102104, '删除', 111102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:busiTypeDelete', NULL, NULL, 4, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (111102105, '绑定应用', 111102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:binding', NULL, NULL, 5, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112100100, '密钥管理', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'kms', NULL, 13, NULL, NULL, 0, NULL, NULL,
        NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112101100, '资源信息', 112100100, '/services/resource/kms', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112101101, '根据业务类型查询业务地址', 112100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112101102, 'SDK下载', 112101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112101103, 'API下载', 112101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112101104, '服务列表', 112101100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112101105, '设备列表', 112101100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112102100, '应用管理', 112100100, '/application/child/list/kms', '/application/childlist/index.vue', 0, 'C', 0, 0,
        'app:audited:list', NULL, 'appManageKms', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112102101, '新建', 112102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112102102, '详情', 112102100, '/application/child/detail/kms', '/application/list/detail.vue', 0, 'F', 1, 0,
        'app:audited:detail', NULL, 'appManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112102103, '编辑', 112102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:edit', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112102104, '删除', 112102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:busiTypeDelete', NULL, NULL, 4, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (112102105, '绑定应用', 112102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:binding', NULL, NULL, 5, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (113100100, '文件加密', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'file-encrypt', NULL, 14, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (113101100, '资源信息', 113100100, '/services/resource/secstorage', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (113101101, '根据业务类型查询业务地址', 113100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (113101102, 'SDK下载', 113101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (113101103, 'API下载', 113101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (113101104, '服务列表', 113101100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (113101105, '设备列表', 113101100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (114100100, '数据库加密', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'db-encrypt', NULL, 15, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (114101100, '资源信息', 114100100, '/services/resource/secdb', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (114101101, '根据业务类型查询业务地址', 114100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (114101102, 'SDK下载', 114101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (114101103, 'API下载', 114101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (114101104, '服务列表', 114101100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (114101105, '设备列表', 114101100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (115100100, '时间戳', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'time1', NULL, 16, NULL, NULL, 0, NULL, NULL,
        NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (115101100, '资源信息', 115100100, '/services/resource/tsa', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (115101101, '根据业务类型查询业务地址', 115100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (115101102, 'SDK下载', 115101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (115101103, 'API下载', 115101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (115101104, '服务列表', 115101100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (115101105, '设备列表', 115101100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (116100100, '协同签名', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'xieqian', NULL, 17, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (116101100, '资源信息', 116100100, '/services/resource/sms', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (116101101, '根据业务类型查询业务地址', 116100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (116101102, 'SDK下载', 116101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (116101103, 'API下载', 116101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (116101104, '服务列表', 116101100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (116101105, '设备列表', 116101100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (117100100, '动态令牌', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'user-check', NULL, 18, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (117101100, '资源信息', 117100100, '/services/resource/secauth', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (117101101, '根据业务类型查询业务地址', 117100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (117101102, 'SDK下载', 117101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (117101103, 'API下载', 117101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (117101104, '服务列表', 117101100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (117101105, '设备列表', 117101100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (118100100, '电子签章', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'qianzhang', NULL, 19, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (118101100, '资源信息', 118100100, '/services/resource/tsc', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (118101101, '根据业务类型查询业务地址', 118100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (118101102, 'SDK下载', 118101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (118101103, 'API下载', 118101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (118101104, '服务列表', 118101100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (118101105, '设备列表', 118101100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (119100100, 'SSLVPN加密通道', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'vpn', NULL, 20, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (119101100, '资源信息', 119100100, '/services/resource/vpn', '/services/resource/index.vue', 0, 'C', 0, 0,
        'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (119101101, '根据业务类型查询业务地址', 119100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (119101102, 'SDK下载', 119101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (119101103, 'API下载', 119101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (119101104, '服务列表', 119101100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:tenantService:list', NULL, NULL, 4, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (119101105, '设备列表', 119101100, NULL, NULL, 0, 'F', 0, 0, 'device:tenant:deviceList', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120100100, '用户管理', 0, '/sysuser', 'Layout', 0, 'M', 0, 0, NULL, 'tree', NULL, 21, NULL, NULL, 0, NULL, NULL,
        NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120101100, '用户信息', 120100100, '/system/user', '/system/user/index.vue', 0, 'C', 0, 0, 'common:user:list', NULL,
        NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120101102, '编辑', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120101103, '删除', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:delete', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120101104, '绑定UKey', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:bindUkey', NULL, NULL, 4, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120101105, '设置账号有效期', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:expiryDate', NULL, NULL, 5, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120101106, '启用', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:enable', NULL, NULL, 6, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120101107, '禁用', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:disable', NULL, NULL, 7, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120101108, '解锁', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:unlock', NULL, NULL, 8, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120101109, '重置口令', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:resetAuthCode', NULL, NULL, 9, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120102100, '注册记录', 120100100, '/system/user/register', '/system/user/register/index.vue', 0, 'C', 0, 0,
        'common:userRegister:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120102101, '注册', 120102100, NULL, NULL, 0, 'F', 0, 0, 'common:userRegister:add', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120102102, '审核', 120102100, NULL, NULL, 0, 'F', 0, 0, 'common:userRegister:audit', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (120102103, '删除', 120102100, NULL, NULL, 0, 'F', 0, 0, 'common:userRegister:delete', NULL, NULL, 3, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121100100, '系统管理', 0, '/system', 'Layout', 0, 'M', 0, 0, NULL, 'setting-line', NULL, 22, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121101100, '菜单管理', 121100100, '/system/menu', '/system/menu/index.vue', 0, 'C', 0, 0, 'common:menu:list', NULL,
        NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121101101, '添加菜单', 121101100, NULL, NULL, 0, 'F', 0, 0, 'common:menu:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121101102, '编辑菜单', 121101100, NULL, NULL, 0, 'F', 0, 0, 'common:menu:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121101103, '删除菜单', 121101100, NULL, NULL, 0, 'F', 0, 0, 'common:menu:delete', NULL, NULL, 3, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121102100, '角色管理', 121100100, '/system/role', '/system/role/index.vue', 0, 'C', 0, 0, 'common:role:list', NULL,
        NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121102101, '添加', 121102100, NULL, NULL, 0, 'F', 0, 0, 'common:role:add', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121102102, '编辑', 121102100, NULL, NULL, 0, 'F', 0, 0, 'common:role:edit', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121102103, '删除', 121102100, NULL, NULL, 0, 'F', 0, 0, 'common:role:delete', NULL, NULL, 3, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121103100, '口令黑名单', 121100100, '/system/config/unsafekl', '/system/config/unsafekl.vue', 0, 'C', 0, 0,
        'common:authCodeBlackList:list', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121103101, '添加', 121103100, NULL, NULL, 0, 'F', 0, 0, 'common:authCodeBlackList:add', NULL, NULL, 1, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121103102, '编辑', 121103100, NULL, NULL, 0, 'F', 0, 0, 'common:authCodeBlackList:edit', NULL, NULL, 2, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121103103, '删除', 121103100, NULL, NULL, 0, 'F', 0, 0, 'common:authCodeBlackList:delete', NULL, NULL, 3, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121104100, '人员管理配置', 121100100, '/system/config/login', '/system/config/login.vue', 0, 'C', 0, 0,
        'common:userManagerConfig:list', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121104101, '修改配置', 121104100, NULL, NULL, 0, 'F', 0, 0, 'common:userManagerConfig:edit', NULL, NULL, 1, NULL,
        NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121105100, '系统配置', 121100100, '/system/config', '/system/config/index.vue', 0, 'C', 0, 0, NULL, NULL, NULL, 5,
        NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121105101, '修改配置', 121105100, NULL, NULL, 0, 'F', 0, 0, 'common:sysConfig:edit', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121106100, '主密钥管理', 121100100, '/system/backup/mainkey', '/system/backup/mainkey/index.vue', 0, 'C', 0, 0,
        'common:masterKey:list', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121106101, '备份', 121106100, NULL, NULL, 0, 'F', 0, 0, 'common:masterKey:backup', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121106102, '恢复', 121106100, NULL, NULL, 0, 'F', 0, 0, 'common:masterKey:recover', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121107100, '关于平台', 121100100, '/system/about', '/system/about/index.vue', 0, 'C', 0, 0, 'common:pt:info', NULL,
        NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121108100, '业务地址', 121100100, '/system/busiurl', '/system/busiurl/index.vue', 0, 'C', 0, 0,
        'common:busiurl:infoList', NULL, 'tenantManage', 12, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121108101, '新增业务地址', 121108100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:add', NULL, NULL, 13, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121108102, '编辑业务地址', 121108100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:edit', NULL, NULL, 14, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (121108103, '删除业务地址', 121108100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:delete', NULL, NULL, 15, NULL, NULL,
        0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (122100100, '日志管理', 0, '/audit', 'Layout', 0, 'M', 0, 0, NULL, 'documentation', NULL, 23, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (122101100, '管理日志', 122100100, '/monitor/managelog/index', '/monitor/managelog/index.vue', 0, 'C', 0, 0,
        'log:operate:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (122101101, '批量审计', 122101100, NULL, NULL, 0, 'F', 0, 0, 'log:operate:audit', NULL, NULL, 1, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (122101102, '导出', 122101100, NULL, NULL, 0, 'F', 0, 0, 'log:operate:export', NULL, NULL, 2, NULL, NULL, 0, NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (122102100, '租户管理日志', 122100100, '/monitor/operlog/index', '/monitor/operlog/index.vue', 0, 'C', 0, 0,
        'log:operateTenant:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (122102101, '批量审计', 122102100, NULL, NULL, 0, 'F', 0, 0, 'log:operateTenant:audit', NULL, NULL, 1, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (122102102, '导出', 122102100, NULL, NULL, 0, 'F', 0, 0, 'log:operateTenant:export', NULL, NULL, 2, NULL, NULL, 0,
        NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (123100100, '监控管理', 0, '/monitor', 'Layout', 0, 'M', 0, 0, NULL, 'monitor', NULL, 24, NULL, NULL, 0, NULL, NULL,
        NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (123101100, '监控信息', 123100100, '/monitor/service/link', '/monitor/service/link/index.vue', 0, 'C', 0, 0,
        'monitor:monitorConnection:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (123101101, '重试', 123101100, NULL, NULL, 0, 'F', 0, 0, 'monitor:monitorConnection:retryAddMonitorDevice', NULL,
        NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (123101102, '删除', 123101100, NULL, NULL, 0, 'F', 0, 0, 'monitor:monitorConnection:deleteMonitorDevice', NULL,
        NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (123101103, '新建', 123101100, NULL, NULL, 0, 'F', 0, 0, 'monitor:monitorConnection:addMonitorDevice', NULL, NULL,
        2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (123102100, '服务器监控', 123100100, '/monitor/dashboard', '/iframe/swmonitor.vue', 0, 'C', 0, 0, NULL, NULL, NULL, 2,
        NULL, '/swmonitor/front/viewPage?viewName=panel-multiple-ccsp&isShowScreen=1', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_menu`
VALUES (123103100, '大屏', 123100100, '/monitor/screen', '/iframe/screen.vue', 0, 'C', 1, 0, NULL, NULL, NULL, 2, NULL,
        '/swmonitor/front/screen?viewName=bigpanel-ccsp001&isShowScreen=1&target=_blank', 0, NULL, NULL, NULL, NULL,
        NULL);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`
(
    `ID`          bigint(20) NOT NULL,
    `MENU_ID`     bigint(20) NULL DEFAULT NULL COMMENT '菜单ID',
    `ROLE_ID`     bigint(20) NULL DEFAULT NULL COMMENT '角色ID',
    `REMARK`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`   bigint(20) NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`   bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色菜单对应关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu`
VALUES (1100100100, 100100100, 1, '平台首页', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1102100100, 102100100, 1, '许可授权管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1102101100, 102101100, 1, '许可申请', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1102101101, 102101101, 1, '申请', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1102101102, 102101102, 1, '下载凭证', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1102102100, 102102100, 1, '许可管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1102102101, 102102101, 1, '解析许可内容', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1102102102, 102102102, 1, '导入许可', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1102103100, 102103100, 1, '服务续期管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1102103101, 102103101, 1, '续约', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103100100, 103100100, 1, '租户管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101100, 103101100, 1, '租户管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101101, 103101101, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101102, 103101102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101103, 103101103, 1, '启动', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101104, 103101104, 1, '停服', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101105, 103101105, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101109, 103101109, 1, '服务管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101111, 103101111, 1, '释放服务', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101112, 103101112, 1, '配置地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101113, 103101113, 1, '新增业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101114, 103101114, 1, '编辑业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103101115, 103101115, 1, '删除业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103102100, 103102100, 1, '待审核租户', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103102101, 103102101, 1, '审核', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103103100, 103103100, 1, '已审核租户', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103103101, 103103101, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1103103102, 103103102, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104100100, 104100100, 1, '服务管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104104100, 104104100, 1, '服务管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104104101, 104104101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104104102, 104104102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104104103, 104104103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104104104, 104104104, 1, '启动', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104104105, 104104105, 1, '停止', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104105100, 104105100, 1, '服务类型管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104105101, 104105101, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104106100, 104106100, 1, '网关管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104106101, 104106101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104106102, 104106102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104106103, 104106103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104107100, 104107100, 1, '路由管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104107101, 104107101, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104109100, 104109100, 1, '数据库管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104109101, 104109101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104109102, 104109102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104109103, 104109103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1104109104, 104109104, 1, '实例列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105100100, 105100100, 1, '设备管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105101100, 105101100, 1, '云密码机', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105101101, 105101101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105101102, 105101102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105101103, 105101103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105101104, 105101104, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105101105, 105101105, 1, '网络配置', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105101106, 105101106, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105101107, 105101107, 1, '新增', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105101108, 105101108, 1, '创建虚拟机', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105102100, 105102100, 1, '虚拟机', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105102101, 105102101, 1, '创建虚拟机', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105102102, 105102102, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105102103, 105102103, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105102104, 105102104, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105102105, 105102105, 1, '启动', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105102106, 105102106, 1, '停止', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105102107, 105102107, 1, '重启', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105103100, 105103100, 1, '物理机', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105103101, 105103101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105103102, 105103102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105103103, 105103103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105103104, 105103104, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105106100, 105106100, 1, '厂商管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105106101, 105106101, 1, '新增', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105106102, 105106102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105106103, 105106103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105107100, 105107100, 1, '地址资源管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105107101, 105107101, 1, '新增', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105107102, 105107102, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105108100, 105108100, 1, '设备类型管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105108104, 105108104, 1, '停用', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1105108105, 105108105, 1, '启用', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1120100100, 120100100, 1, '用户管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1120101100, 120101100, 1, '用户信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1120101102, 120101102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1120102100, 120102100, 1, '注册记录', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1120102101, 120102101, 1, '注册', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1120102103, 120102103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1121100100, 121100100, 1, '系统管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1121107100, 121107100, 1, '关于平台', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1123100100, 123100100, 1, '监控管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1123101100, 123101100, 1, '监控信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1123101101, 123101101, 1, '重试', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1123101102, 123101102, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1123101103, 123101103, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1123102100, 123102100, 1, '服务器监控', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (1123103100, 123103100, 1, '大屏', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2100100100, 100100100, 2, '平台首页', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120100100, 120100100, 2, '用户管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120101100, 120101100, 2, '用户信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120101103, 120101103, 2, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120101104, 120101104, 2, '绑定UKey', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120101105, 120101105, 2, '设置账号有效期', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120101106, 120101106, 2, '启用', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120101107, 120101107, 2, '禁用', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120101108, 120101108, 2, '解锁', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120101109, 120101109, 2, '重置口令', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120102100, 120102100, 2, '注册记录', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2120102102, 120102102, 2, '审核', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2121100100, 121100100, 2, '系统管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2121103100, 121103100, 2, '口令黑名单', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2121103101, 121103101, 2, '添加', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2121103102, 121103102, 2, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2121103103, 121103103, 2, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2121104100, 121104100, 2, '人员管理配置', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2121104101, 121104101, 2, '修改配置', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2121105100, 121105100, 2, '系统配置', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2121105101, 121105101, 2, '修改配置', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (2121107100, 121107100, 2, '关于平台', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (3100100100, 100100100, 3, '平台首页', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (3122100100, 122100100, 3, '日志管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (3122101100, 122101100, 3, '管理日志', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (3122101101, 122101101, 3, '批量审计', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (3122101102, 122101102, 3, '导出', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4101100100, 101100100, 4, '租户首页', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4106100100, 106100100, 4, '应用管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4106102100, 106102100, 4, '应用信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4106102101, 106102101, 4, '新建', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4106102102, 106102102, 4, '详情', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4106102103, 106102103, 4, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4106102104, 106102104, 4, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4106102105, 106102105, 4, '业务管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4106102106, 106102106, 4, '新增', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4106102107, 106102107, 4, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES(4106102109, 106102109, 4, '凭证管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES(4106102110, 106102110, 4, '新增', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES(4106102111, 106102111, 4, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES(4106102112, 106102112, 4, '启用', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES(4106102113, 106102113, 4, '禁用', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES(4106102114, 106102114, 4, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4109100100, 109100100, 4, '数据加解密', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4109101100, 109101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4109101101, 109101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4109101102, 109101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4109101103, 109101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4109101104, 109101104, 4, '服务列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4109101105, 109101105, 4, '设备列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110100100, 110100100, 4, '签名验签', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110101100, 110101100, 4, '信任域管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110101101, 110101101, 4, '新建', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110101102, 110101102, 4, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110101103, 110101103, 4, '删除', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110102100, 110102100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110102101, 110102101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110102102, 110102102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110102103, 110102103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110102104, 110102104, 4, '服务列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4110102105, 110102105, 4, '设备列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4112100100, 112100100, 4, '密钥管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4112101100, 112101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4112101101, 112101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4112101102, 112101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4112101103, 112101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4112101104, 112101104, 4, '服务列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4112101105, 112101105, 4, '设备列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4113100100, 113100100, 4, '文件加密', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4113101100, 113101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4113101101, 113101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4113101102, 113101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4113101103, 113101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4113101104, 113101104, 4, '服务列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4113101105, 113101105, 4, '设备列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4114100100, 114100100, 4, '数据库加密', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4114101100, 114101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4114101101, 114101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4114101102, 114101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4114101103, 114101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4114101104, 114101104, 4, '服务列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4114101105, 114101105, 4, '设备列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4115100100, 115100100, 4, '时间戳', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4115101100, 115101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4115101101, 115101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4115101102, 115101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4115101103, 115101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4115101104, 115101104, 4, '服务列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4115101105, 115101105, 4, '设备列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4116100100, 116100100, 4, '协同签名', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4116101100, 116101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4116101101, 116101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4116101102, 116101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4116101103, 116101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4116101104, 116101104, 4, '服务列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4116101105, 116101105, 4, '设备列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4117100100, 117100100, 4, '动态令牌', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4117101100, 117101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4117101101, 117101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4117101102, 117101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4117101103, 117101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4117101104, 117101104, 4, '服务列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4117101105, 117101105, 4, '设备列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4118100100, 118100100, 4, '电子签章', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4118101100, 118101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4118101101, 118101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4118101102, 118101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4118101103, 118101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4118101104, 118101104, 4, '服务列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4118101105, 118101105, 4, '设备列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4119100100, 119100100, 4, 'SSLVPN加密通道', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4119101100, 119101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4119101101, 119101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4119101102, 119101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4119101103, 119101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4119101104, 119101104, 4, '服务列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4119101105, 119101105, 4, '设备列表', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4121100100, 121100100, 4, '系统管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4121106100, 121106100, 4, '主密钥管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4121106101, 121106101, 4, '备份', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4121106102, 121106102, 4, '恢复', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4121107100, 121107100, 4, '关于平台', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (4123103100, 123103100, 4, '大屏', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (5101100100, 101100100, 5, '租户首页', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (5120100100, 120100100, 5, '用户管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (5120101100, 120101100, 5, '用户信息', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (5120101102, 120101102, 5, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (5120101104, 120101104, 5, '绑定UKey', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (5120101105, 120101105, 5, '设置账号有效期', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (5120101108, 120101108, 5, '解锁', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (5120101109, 120101109, 5, '重置口令', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (6101100100, 101100100, 6, '租户首页', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (6122100100, 122100100, 6, '日志管理', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (6122102100, 122102100, 6, '租户管理日志', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (6122102101, 122102101, 6, '批量审计', NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_menu`
VALUES (6122102102, 122102102, 6, '导出', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS `sys_task`;
CREATE TABLE `sys_task`
(
    `TASK_ID`     bigint                                                 NOT NULL COMMENT '任务号',
    `TASK_NAME`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务名称',
    `TASK_GROUP`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务组名;执行任务串行',
    `SERVER_ID`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务模块',
    `METHOD_URL`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调用接口',
    `JSON_PARAM`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'json格式参数',
    `TASK_STATUS` int                                                    NOT NULL DEFAULT 0 COMMENT '状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时)',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    `REMARK`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `TIMEOUT`     int NULL DEFAULT NULL COMMENT '超时时间;单位秒',
    `START_TIME`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开始时间',
    `END_TIME`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结束时间',
    `POLICY`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   NOT NULL DEFAULT '0' COMMENT '是否允许重复执行;0-不允许，1允许',
    PRIMARY KEY (`TASK_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '异步任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_task_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_task_log`;
CREATE TABLE `sys_task_log`
(
    `TASK_LOG_ID`    bigint                                               NOT NULL COMMENT '任务日志ID',
    `TASK_ID`        bigint                                               NOT NULL COMMENT '任务ID',
    `TASK_MESSAGE`   varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志信息',
    `STATUS`         varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '执行状态（0失败 1正常）',
    `EXCEPTION_INFO` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '异常信息',
    `CREATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间;单位毫秒',
    `TRIGGER_TIME`   bigint NULL DEFAULT NULL COMMENT '触发时间;任务服务上送',
    PRIMARY KEY (`TASK_LOG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '异步任务执行日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_task_log
-- ----------------------------

-- ----------------------------
-- Table structure for user_auth_code
-- ----------------------------
DROP TABLE IF EXISTS `user_auth_code`;
CREATE TABLE `user_auth_code`
(
    `ID`           bigint                                                 NOT NULL,
    `USER_ID`      bigint                                                 NOT NULL COMMENT '账号ID',
    `AUTH_CODE`    varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '口令;加密存储',
    `CREATE_TYPE`  int                                                    NOT NULL COMMENT '来源 1初始化 2修改口令 3重置口令',
    `INVALID_FLAG` int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户历史口令' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_auth_code
-- ----------------------------

-- ----------------------------
-- Table structure for user_cert
-- ----------------------------
DROP TABLE IF EXISTS `user_cert`;
CREATE TABLE `user_cert`
(
    `ID`           bigint                                                  NOT NULL COMMENT 'ID',
    `USER_ID`      bigint                                                  NOT NULL,
    `CERT`         varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '证书;加密存储',
    `SERIAL`       varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书;加密存储',
    `CERT_TYPE`    int NULL DEFAULT NULL COMMENT '证书类型;1UKEY证书',
    `INVALID_FLAG` int                                                     NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户认证证书表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_cert
-- ----------------------------

-- ----------------------------
-- Table structure for user_info
-- ----------------------------
DROP TABLE IF EXISTS `user_info`;
CREATE TABLE `user_info`
(
    `USER_ID`           bigint                                                 NOT NULL COMMENT '账户ID',
    `USER_CODE`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录账号',
    `USER_NAME`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '账号名称',
    `USER_STATUS`       int                                                    NOT NULL COMMENT '用户状态;1启用 2禁用 3锁定',
    `DISABLE_REASON_ID` int                                                    NOT NULL DEFAULT 0 COMMENT '禁用原因;0非禁用状态 1手动禁用 2长期未登录禁用',
    `PHONE_NUM`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号码;加密存储',
    `EMAIL_ADDRESS`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱;加密存储',
    `TENANT_ID`         bigint                                                 NOT NULL COMMENT '租户ID',
    `TENANT_CODE`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户标识',
    `ORGANIZATION_ID`   bigint NULL DEFAULT NULL COMMENT '组织机构ID',
    `ROLE_ID`           bigint                                                 NOT NULL COMMENT '角色ID',
    `SALT`              varchar(270) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户盐值;加密存储',
    `INVALID_FLAG`      int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`            varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`         bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`         bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`USER_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_info
-- ----------------------------

-- ----------------------------
-- Table structure for user_register
-- ----------------------------
DROP TABLE IF EXISTS `user_register`;
CREATE TABLE `user_register`
(
    `USER_REGISTER_ID` bigint                                                 NOT NULL COMMENT '账户注册ID',
    `USER_CODE`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录账号',
    `USER_NAME`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '账号名称',
    `PHONE_NUM`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号码',
    `EMAIL_ADDRESS`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱',
    `TENANT_ID`        bigint                                                 NOT NULL COMMENT '租户ID',
    `TENANT_CODE`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户标识',
    `ORGANIZATION_ID`  bigint NULL DEFAULT NULL COMMENT '部门ID',
    `ROLE_ID`          bigint                                                 NOT NULL COMMENT '角色ID',
    `UKEY_CERT`        varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'UKEY证书',
    `UKEY_SERIAL`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'UKEY序列号',
    `AUTH_CODE`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户口令',
    `USER_TYPE`        int NULL DEFAULT NULL COMMENT '1平台管理员 2租户用户',
    `AUDIT_STATUS`     int                                                    NOT NULL COMMENT '0 待审核 1 审核通过 2 拒绝',
    `AUDIT_TIME`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核时间',
    `AUDIT_BY`         bigint NULL DEFAULT NULL COMMENT '审核人',
    `AUDIT_SUGGESTION` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核意见',
    `INVALID_FLAG`     int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `HMAC`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性',
    `REMARK`           varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`        bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`        bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`USER_REGISTER_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户注册信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_register
-- ----------------------------

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role`
(
    `ROLE_ID`      bigint                                                 NOT NULL COMMENT '角色ID',
    `ROLE_NAME`    varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色名称',
    `SORD_NUM`     int                                                    NOT NULL COMMENT '排序序号',
    `TENANT_ID`    bigint NULL DEFAULT NULL COMMENT '租户ID',
    `ROLE_TYPE`    int NULL DEFAULT NULL COMMENT '角色类别;1平台角色 2租户角色',
    `INVALID_FLAG` int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ROLE_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_role
-- ----------------------------
INSERT INTO `user_role`
VALUES (1, '操作员', 1, NULL, 1, 0, '初始化管理员', 1, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO `user_role`
VALUES (2, '管理员', 2, NULL, 1, 0, '初始化安全员', 1, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO `user_role`
VALUES (3, '审计员', 3, NULL, 1, 0, '初始化审计员', 1, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO `user_role`
VALUES (4, '租户操作员', 1, NULL, 2, 0, NULL, NULL, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO `user_role`
VALUES (5, '租户管理员', 2, NULL, 2, 0, NULL, NULL, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO `user_role`
VALUES (6, '租户审计员', 3, NULL, 2, 0, NULL, NULL, '2023-02-19 20:41:51', NULL, NULL);

-- ----------------------------
-- Table structure for user_root_ca
-- ----------------------------
DROP TABLE IF EXISTS `user_root_ca`;
CREATE TABLE `user_root_ca`
(
    `ID`          bigint NOT NULL,
    `CERT_TYPE`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书类型;1 ukey证书',
    `CERT_FORMAT` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书格式;证书类型 x509',
    `START_TIME`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书起始时间;证书起始时间',
    `END_TIME`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书过期时间;证书过期时间',
    `ISSUER`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '颁发者;颁发者',
    `PUBLIC_KEY`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书公钥;证书公钥',
    `VERIFY_CERT` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '验签证书（BASE64编码）;验签证书（BASE64编码）',
    `VERSION`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本;版本',
    `REMARK`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`   bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`   bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_root_ca
-- ----------------------------
INSERT INTO `user_root_ca`
VALUES (1, '1', 'X.509', 'Wed Sep 04 14:47:14 CST 2019', 'Fri Aug 23 14:47:14 CST 2069',
        'C=CN,ST=北京,L=北京,O=三未信安,CN=SANSEC PD ROOT,E=<EMAIL>',
        'SM2 Public Key      AffineX: f2a8cc6de14910861549e0f537e639670b40d881aeae1adc3e2af1233a703415      AffineY: b2596e160f798e384376afbca6c855b64ff629fcb35f7556e84b99745c4376e3',
        'MIICOzCCAd6gAwIBAgIIbAbZnrBMKUgwDAYIKoEcz1UBg3UFADCBhTELMAkGA1UEBhMCQ04xDzANBgNVBAgMBuWMl+S6rDEPMA0GA1UEBwwG5YyX5LqsMRUwEwYDVQQKDAzkuInmnKrkv6HlrokxFzAVBgNVBAMMDlNBTlNFQyBQRCBST09UMSQwIgYJKoZIhvcNAQkBFhVzdXBwb3J0QHNhbnNlYy5jb20uY24wIBcNMTkwOTA0MDY0NzE0WhgPMjA2OTA4MjMwNjQ3MTRaMIGFMQswCQYDVQQGEwJDTjEPMA0GA1UECAwG5YyX5LqsMQ8wDQYDVQQHDAbljJfkuqwxFTATBgNVBAoMDOS4ieacquS/oeWuiTEXMBUGA1UEAwwOU0FOU0VDIFBEIFJPT1QxJDAiBgkqhkiG9w0BCQEWFXN1cHBvcnRAc2Fuc2VjLmNvbS5jbjBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABPKozG3hSRCGFUng9TfmOWcLQNiBrq4a3D4q8SM6cDQVslluFg95jjhDdq+8pshVtk/2KfyzX3VW6EuZdFxDduOjMjAwMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFELufEtM6TYgi+igpD6H062y1dOKMAwGCCqBHM9VAYN1BQADSQAwRgIhAJVvgnYYtxib6Og51spY0EcWmEZttRh2e+l207ygPicyAiEAu8a2kdv0H5YjWFAQtp7WI4inQdpLpssBJu0w19iVaLQ=',
        '3', '三未信安PD根证', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for user_security_extend
-- ----------------------------
DROP TABLE IF EXISTS `user_security_extend`;
CREATE TABLE `user_security_extend`
(
    `ID`                   bigint NOT NULL,
    `USER_ID`              bigint NOT NULL COMMENT '账户ID',
    `HMAC`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性校验值',
    `LAST_ACTIVE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '最后一次活跃时间',
    `LAST_UPDATE_PWD_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '最后一次修改密码时间',
    `ACCOUNT_START_TIME`   varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '账户有效期开始时间',
    `ACCOUNT_END_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '账户有效期结束时间',
    `LOGIN_ERROR_TIMES`    int NULL DEFAULT NULL COMMENT '登录失败次数',
    `UNLOCK_TIME`          bigint NULL DEFAULT NULL COMMENT '解锁时间（时间戳）',
    `PASSWORD_CHANGE_FLAG` int    NOT NULL DEFAULT 1 COMMENT '口令是否需要修改',
    `INVALID_FLAG`         int    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`               varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`            bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`          varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`            bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`          varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户安全信息扩展表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_security_extend
-- ----------------------------
DROP TABLE IF EXISTS `dic_statistic`;
CREATE TABLE `dic_statistic`
(
    `ID`                      bigint(20) NOT NULL COMMENT 'ID',
    `SERVICE_CODE`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '服务简称',
    `STATISTIC_TYPE`          int(1) NOT NULL COMMENT '统计类型 1增量 2非增量数量计算类 3非增量非计算类',
    `STATISTIC_SHOW_NAME`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '统计指标展示名称',
    `STATISTIC_NAME`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '统计指标名称',
    `IS_AVAILABLE`            int(1) NOT NULL COMMENT '是否启用 1启用 0停用',
    `IS_INVOKE_MULTI_SERVICE` int(1) NOT NULL COMMENT '是否同服务类型全服务调用 1是 2否',
    `OID`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'OID',
    `UNIT`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '单位',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '统计指标字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_statistic
-- ----------------------------
INSERT INTO `dic_statistic`
VALUES (1, 'kms', 1, '调用次数', 'kmsBusiStatistic', 1, 0, '.*******.4.1.746366.4.1.0', '次');
INSERT INTO `dic_statistic`
VALUES (2, 'kms', 2, '密钥数量', 'kmsKeyNumStatistic', 1, 0, '.*******.4.1.746366.6.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (3, 'kms', 2, '密钥状态分布', 'kmsKeyStateStatistic', 1, 0, '.*******.4.1.746366.5.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (4, 'pki', 1, '调用次数', 'encBusiStatistic', 1, 1, '.*******.4.1.746374.2.1.0', '次');
INSERT INTO `dic_statistic`
VALUES (5, 'pki', 2, '密钥数量', 'encKeyNumStatistic', 1, 0, '.*******.4.1.746374.1.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (6, 'svs', 1, '调用次数', 'signBusiStatistic', 1, 1, '.*******.4.1.746375.3.1.0', '次');
INSERT INTO `dic_statistic`
VALUES (7, 'svs', 2, '应用证书数量', 'signAppCertStatistic', 1, 0, '.*******.4.1.746375.1.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (8, 'svs', 2, '用户证书数量', 'signUserCertStatistic', 1, 0, '.*******.4.1.746375.2.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (9, 'tsa', 1, '调用次数', 'timeBusiStatistic', 1, 0, '.*******.4.1.746370.5.1.0', '次');
INSERT INTO `dic_statistic`
VALUES (10, 'sms', 1, '用户签名次数', 'splitBusiStatistic', 1, 0, '.*******.4.1.746367.12.1.0', '次');
INSERT INTO `dic_statistic`
VALUES (11, 'sms', 2, '用户数', 'userNumStatistic', 1, 0, '.*******.4.1.746367.11.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (12, 'secauth', 1, '用户签名次数', 'secauthBusiStatistic', 1, 0, '.*******.4.1.746371.5.1.0', '次');
INSERT INTO `dic_statistic`
VALUES (13, 'tsc', 1, '签章次数', 'sealSignBusiStatistic', 1, 0, '.*******.4.1.746372.5.1.0', '次');
INSERT INTO `dic_statistic`
VALUES (14, 'tsc', 1, '验章次数', 'sealVerifyBusiStatistic', 1, 0, '.*******.4.1.746372.6.1.0', '次');
INSERT INTO `dic_statistic`
VALUES (15, 'tsc', 2, '印章个数', 'sealNumStatistic', 1, 0, '.*******.4.1.746372.7.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (16, 'tsc', 2, '签章状态分布', 'sealStateStatistic', 1, 0, '.*******.4.1.746372.8.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (17, 'secdb', 2, '数据库加密个数', 'dbStatistic', 1, 0, '.*******.4.1.746376.1.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (18, 'secstorage', 2, '文件加密个数', 'serverStatistic', 1, 0, '.*******.4.1.746377.1.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (19, 'vpn', 3, '新建连接数', 'vpnNewConnectNumStatistic', 1, 1, '.*******.4.1.746369.3.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (20, 'vpn', 3, '吞吐量(收)', 'vpnThroughputRNumStatistic', 1, 1, '.*******.4.1.746369.4.1.0', 'Kbps');
INSERT INTO `dic_statistic`
VALUES (21, 'vpn', 3, '吞吐量(发)', 'vpnThroughputSNumStatistic', 1, 1, '.*******.4.1.746369.5.1.0', 'Kbps');
INSERT INTO `dic_statistic`
VALUES (22, 'vpn', 3, '并发连接数', 'vpnConcurrentConnectNumStatistic', 1, 1, '.*******.4.1.746369.6.1.0', '个');
INSERT INTO `dic_statistic`
VALUES (23, 'secdb', 2, '数据库加密密钥数量', 'secDbKeyNumStatistic ', 1, 0, '.*******.4.1.746376.2.1.0', '个');


-- ----------------------------
-- Table structure for dic_script_path
-- ----------------------------
DROP TABLE IF EXISTS `dic_script_path`;
CREATE TABLE `dic_script_path`
(
    `ID`              bigint(20) NOT NULL COMMENT '主键',
    `SERVICE_TYPE_ID` bigint(20) NULL DEFAULT 1 COMMENT '服务类型主键',
    `SCRIPT_CODE`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '脚本编码',
    `SCRIPT_NAME`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '脚本名',
    `SCRIPT_VALUE`    varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '脚本值',
    `CREATE_BY`       bigint(20) NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`       bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dic_script_path
-- ----------------------------
INSERT INTO `dic_script_path`
VALUES (3, 1, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/pki/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (4, 2, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/svs/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (5, 3, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/digest/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (6, 4, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/kms/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (7, 5, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/tsa/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (8, 6, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/sms/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (9, 7, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/secauth/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (10, 8, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/secdbhsm/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (11, 9, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/storage/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (12, 10, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/electronicSeal/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (13, 11, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/vpn/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (14, 1, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/pki/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (15, 2, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/svs/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (16, 3, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/digest/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (17, 4, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/kms/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (18, 5, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/tsa/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (19, 6, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/sms/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (20, 7, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/secauth/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (21, 8, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/secdbhsm/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (22, 9, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/storage/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (23, 10, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/electronicSeal/setup/startService.sh', NULL, NULL, NULL,
        NULL);
INSERT INTO `dic_script_path`
VALUES (24, 11, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/vpn/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (25, NULL, 'initEditStartParamScript', '初始化修改自启参数路径', '/opt/sansec/ccsp/startService.sh', NULL, NULL, NULL,
        NULL);
INSERT INTO `dic_script_path`
VALUES (26, NULL, 'initJceScript', '初始化jce脚本路径', '/opt/sansec/ccsp/initJce.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (27, 1, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/pki/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (28, 2, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/svs/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (29, 3, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/digest/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (30, 4, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/kms/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (31, 5, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/tsa/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (32, 6, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/sms/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (33, 7, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/secauth/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (34, 8, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/secdbhsm/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (35, 9, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/storage/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (36, 10, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/electronicSeal/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (37, 11, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/vpn/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (38, NULL, 'initConfigScript', '初始化配置脚本路径', '/opt/sansec/ccsp/initConfig.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (39, NULL, 'configScript', '配置文件路径', '/opt/sansec/config/pt_config.properties', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (40, NULL, 'createDbCaseScript', '创建数据库实例脚本路径', '/opt/ccsp/ccspUtilRemote/shell/createDbCase.sh', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (41, NULL, 'createDbSchemaScript', '创建数据库模式脚本路径', '/opt/ccsp/ccspUtilRemote/shell/createDbSchema.sh', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (42, NULL, 'checkDbCaseOrSchemaScript', '检测数据库实例/模式状态脚本路径',
        '/opt/ccsp/ccspUtilRemote/shell/checkDbCaseOrSchema.sh', NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (43, NULL, 'initDbDataScript', '执行数据库脚本脚本路径', '/opt/ccsp/ccspUtilRemote/shell/initDbData.sh', NULL, NULL, NULL,
        NULL);
INSERT INTO `dic_script_path`
VALUES (44, NULL, 'deleteDbCaseScript', '删除数据库实例脚本路径', '/opt/ccsp/ccspUtilRemote/shell/deleteDbCase.sh', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (45, NULL, 'deleteDbSchemaScript', '删除数据库模式脚本路径', '/opt/ccsp/ccspUtilRemote/shell/deleteDbSchema.sh', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (46, 1, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/pki-mysql.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (47, 2, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/svs-mysql.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (48, 3, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/digest-mysql.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (49, 4, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/kms-mysql.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (50, 5, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/tsa-mysql.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (51, 6, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/sms-mysql.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (52, 7, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/secauth-mysql.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (53, 8, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/secdbhsm-mysql.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (54, 9, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/storage-mysql.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (55, 10, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/electronic_seal-mysql.sql',
        NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (56, 11, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/vpn-mysql.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (57, 1, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/pki-gauss.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (58, 2, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/svs-gauss.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (59, 3, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/digest-gauss.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (60, 4, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/kms-gauss.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (61, 5, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/tsa-gauss.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (62, 6, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/sms-gauss.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (63, 7, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/secauth-gauss.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (64, 8, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/secdbhsm-gauss.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (65, 9, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/storage-gauss.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (66, 10, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/electronic_seal-gauss.sql',
        NULL, NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (67, 11, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/vpn-gauss.sql', NULL, NULL,
        NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (69, NULL, 'checkDbInitDataScript', '检测数据库脚本状态脚本', '/opt/ccsp/ccspUtilRemote/shell/checkDbInitData.sh', NULL,
        NULL, NULL, NULL);
INSERT INTO `dic_script_path`
VALUES (70, NULL, 'checkDbStatusScript', '检测数据库状态脚本', '/opt/ccsp/ccspUtilRemote/shell/checkDbStatus.sh', NULL, NULL,
        NULL, NULL);


SET
FOREIGN_KEY_CHECKS = 1;
