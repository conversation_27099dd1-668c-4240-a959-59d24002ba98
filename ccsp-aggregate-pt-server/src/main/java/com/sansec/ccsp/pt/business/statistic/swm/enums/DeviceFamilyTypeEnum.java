package com.sansec.ccsp.pt.business.statistic.swm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DeviceFamilyTypeEnum {

	/**
	 * 云密码机
	 */
	CHSM(1,"云密码机"),

	/**
	 * 物理机
	 */
	HSM(2,"物理密码机"),

	/**
	 * 虚拟机
	 */
	VSM(3,"虚拟密码机");



	/**
	 * 类型
	 */
	private final Integer type;

	/**
	 * 名称
	 */
	private final String name;

	public static String getNameByType(Integer type){
		if (Objects.equals(type, CHSM.type)){
			return CHSM.name;
		}
		if (Objects.equals(type, HSM.type)){
			return HSM.name;
		}
		if (Objects.equals(type, VSM.type)){
			return VSM.name;
		}
		return "";
	}


}
