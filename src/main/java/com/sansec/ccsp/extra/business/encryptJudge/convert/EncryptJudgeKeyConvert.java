package com.sansec.ccsp.extra.business.encryptJudge.convert;

import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import com.sansec.ccsp.extra.business.encryptJudge.response.EncryptJudgeKeyVO;
import com.sansec.ccsp.extra.business.encryptJudge.entity.EncryptJudgeKeyPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.common.param.response.SecPageVO;
import java.util.List;

 /**
 * @description : 加密判定密钥表;(ENCRYPT_JUDGE_KEY)实体类转换接口
 * <AUTHOR> zhaozhuang
 * @date : 2024-4-11
 */
@Mapper(componentModel = "spring")
public interface EncryptJudgeKeyConvert{

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<EncryptJudgeKeyVO> pagePOToSecPageVOPage(IPage<EncryptJudgeKeyPO> iPage);
    
    @InheritConfiguration(name = "convertVo")
    List<EncryptJudgeKeyVO> convert(List<EncryptJudgeKeyPO> list);
    
    @Mappings({})
    EncryptJudgeKeyVO convertVo(EncryptJudgeKeyPO request);
}