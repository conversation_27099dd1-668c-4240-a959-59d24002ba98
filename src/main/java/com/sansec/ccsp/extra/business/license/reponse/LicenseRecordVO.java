package com.sansec.ccsp.extra.business.license.reponse;

import lombok.Data;

 /**
 * @description : 许可授权记录表（520）;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>aw<PERSON>
 * @date : 2024-4-16
 */
@Data
public class LicenseRecordVO{
    /**
     * 主键
     */
    private Long id;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 许可类型;1永久授权2按年授权3按月授权
     */
    private Long licenseType;
    /**
     * 许可签发时间
     */
    private String issueTime;
    /**
     * 完整性校验
     */
    private String hmac;
    /**
     * 备注
     */
    private String remark;


}