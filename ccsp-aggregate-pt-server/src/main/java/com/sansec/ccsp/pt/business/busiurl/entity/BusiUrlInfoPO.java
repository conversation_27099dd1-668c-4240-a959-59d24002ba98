package com.sansec.ccsp.pt.business.busiurl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

/**
 * @Description: 业务地址类型
 * @CreateTime: 2023-05-09
 * @Author: wangjun<PERSON>e
 */
@TableName("BUSI_URL_INFO")
@Data
public class BusiUrlInfoPO extends BasePO {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 业务地址类型
     */
    private Long busiUrlTypeId;
    /**
     * 名称
     */
    private String name;
    /**
     * 请求协议
     */
    private Integer protocol;
    /**
     * IP地址
     */
    private String ip;
    /**
     * 端口
     */
    private Integer port;
    /**
     * 服务组ID
     */
    private Long serviceGroupId;
    /**
     * 区域ID
     */
    private Long regionId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}