package com.sansec.ccsp.common.business.dic.service;

import com.sansec.ccsp.common.dic.request.DicSysDataDTO;
import com.sansec.ccsp.common.dic.request.DicSysDataPageDTO;
import com.sansec.ccsp.common.dic.response.DicSysDataVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * @Description: 系统数据表;(DIC_SYS_DATA)表服务接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
public interface DicSysDataService{
    /**
     * 分页查询
     *
     * @param dicSysDataPageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<DicSysDataVO>> find(DicSysDataPageDTO dicSysDataPageDTO);
    /**
     * 新增数据
     *
     * @param dicSysDataDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(DicSysDataDTO dicSysDataDTO);
    /**
     * 更新数据
     *
     * @param dicSysDataDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(DicSysDataDTO dicSysDataDTO);
    /**
     * 通过主键删除数据
     *
     * @param dicSysDataDTO
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(DicSysDataDTO dicSysDataDTO);

    /**
     * 获取系统字典
     *
     * @param dicType
     * @return
     */
    SecRestResponse<List<DicSysDataVO>> getSysDictByType(String dicType);
}
