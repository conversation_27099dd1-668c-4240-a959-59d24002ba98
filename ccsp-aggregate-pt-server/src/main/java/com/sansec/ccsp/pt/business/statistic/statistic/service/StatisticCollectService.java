package com.sansec.ccsp.pt.business.statistic.statistic.service;

import com.sansec.ccsp.staticapi.monitor.request.StatisticReportMonitorDataDTO;
import com.sansec.ccsp.staticapi.monitor.response.StatisticInfoResVO;
import com.sansec.ccsp.staticapi.monitor.response.StatisticMonitorDataCommonVO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 首页统计服务
 * @Author: wang<PERSON><PERSON>e
 * @CreateTime: 2023/05/14  20:10
 */
public interface StatisticCollectService {

    void statisticCollect(StatisticInfoResVO statisticInfoResVO);

    void addMonitorStatisticReportData(List<StatisticReportMonitorDataDTO> statisticReportMonitorDataDTOS);

}
