package com.sansec.ccsp.common.business.user.api;

import com.sansec.ccsp.common.business.user.convert.UserInfoConvert;
import com.sansec.ccsp.common.business.user.entity.UserInfoPO;
import com.sansec.ccsp.common.business.user.service.LoginService;
import com.sansec.ccsp.common.user.api.LoginApi;
import com.sansec.ccsp.common.user.request.*;
import com.sansec.ccsp.common.user.response.CaptchaCheckVO;
import com.sansec.ccsp.common.user.response.GetRandomVO;
import com.sansec.ccsp.common.user.response.UserInfoVO;
import com.sansec.ccsp.security.annotation.IgnoreToken;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 登录 实现
 *
 * <AUTHOR>
 */
@RestController
@Validated
@IgnoreToken
public class LoginApiImpl implements LoginApi {
	@Resource
	LoginService loginService;
	@Resource
	UserInfoConvert userInfoConvert;

	/**
	 * 口令登录
	 *
	 * @param authCodeLoginDTO 口令登录信息
	 * @return 配置值
	 */
	@IgnoreToken
	@Override
	public SecRestResponse<String> authCodeLogin(AuthCodeLoginDTO authCodeLoginDTO) {
        return loginService.authCodeLogin(authCodeLoginDTO);
    }

	@IgnoreToken
	@Override
	public SecRestResponse<String> getLoginToken(AuthCodeLoginDTO authCodeLoginDTO) {
		return loginService.getLoginToken(authCodeLoginDTO);
	}

    /**
     * 获取验证码
     *
     * @return
     */
    @IgnoreToken
    @Override
    public SecRestResponse<Object> getCaptcha() {
        return loginService.getCaptcha();
	}

	/**
	 * 校验码检测
	 *
	 * @param captchaCheckDTO
	 * @return 校验结果
	 */
	@IgnoreToken
	@Override
	public SecRestResponse<CaptchaCheckVO> captchaCheck(CaptchaCheckDTO captchaCheckDTO) {
		return loginService.captchaCheck(captchaCheckDTO);
	}

	/**
	 * 获取验证码
	 *
	 * @param getLoginRandomDTO
	 * @return 校验结果
	 */
	@IgnoreToken
	@Override
	public SecRestResponse<GetRandomVO> getRandom(GetLoginRandomDTO getLoginRandomDTO) {
		return loginService.getRandom(getLoginRandomDTO);
	}

	/**
	 * Ukey证书验签登录
	 *
	 * @param uKeyCertLoginDTO
	 * @return token
	 */
	@IgnoreToken
	@Override
	public SecRestResponse<String> uKeyLogin(UKeyCertLoginDTO uKeyCertLoginDTO) {
		return loginService.uKeyLogin(uKeyCertLoginDTO);
	}

	/**
	 * 根据UKey获取用户信息
	 *
	 * @param uKeyCertLoginDTO
	 * @return
	 */
	@IgnoreToken
	@Override
	public SecRestResponse<UserInfoVO> getUkeyTenantId(UKeyCertLoginDTO uKeyCertLoginDTO) {
		UserInfoPO userInfoPO = loginService.getUkeyTenantId(uKeyCertLoginDTO);
		return ResultUtil.ok(userInfoConvert.convertVo(userInfoPO));
	}

	/**
	 * 退出登录
	 */
	@Override
	public SecRestResponse<Object> logout() {
		return loginService.logout();
	}

	/**
	 * 获取SIM 登录token，带有登录状态检测
	 * @param userInfoDTO
	 * @return
	 */
    @Override
    public SecRestResponse<String> getSimLoginTokenWithCheck(UserInfoDTO userInfoDTO) {
		return loginService.getSimLoginTokenWithCheck(userInfoDTO);
    }



}
