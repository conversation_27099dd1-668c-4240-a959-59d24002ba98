package com.sansec.ccsp.servicemgt.business.serviceport.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.servicemgt.business.serviceport.entity.ServiceDockerPortMappingPO;
import com.sansec.ccsp.servicemgt.business.serviceport.entity.ServiceGroupDockerPortMappingPO;
import com.sansec.ccsp.servicemgt.serviceport.request.ServiceDockerPortMappingDTO;
import com.sansec.ccsp.servicemgt.serviceport.response.ServiceDockerPortMappingVO;
import com.sansec.ccsp.servicemgt.serviceport.response.ServiceGroupDockerPortMappingVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR> weic
 * @description : 密码服务实例Docker端口映射表;(SERVICE_DOCKER_PORT_MAPPING)实体类转换接口
 * @date : 2024-4-15
 */
@Mapper(componentModel = "spring")
public interface ServiceDockerPortMappingConvert {
    /**
     * dtoToPo
     *
     * @param serviceDockerPortMappingDTO
     * @return
     */
    @Mappings({})
    ServiceDockerPortMappingPO dtoToPo(ServiceDockerPortMappingDTO serviceDockerPortMappingDTO);

    /**
     * poToDto
     *
     * @param serviceDockerPortMappingPO
     * @return
     */
    ServiceDockerPortMappingDTO poToDto(ServiceDockerPortMappingPO serviceDockerPortMappingPO);

    /**
     * poToDto-list
     *
     * @param list
     * @return
     */
    List<ServiceDockerPortMappingDTO> poToDto(List<ServiceDockerPortMappingPO> list);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<ServiceDockerPortMappingVO> pagePOToSecPageVOPage(IPage<ServiceDockerPortMappingPO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<ServiceDockerPortMappingVO> convert(List<ServiceDockerPortMappingPO> list);

    @Mappings({})
    ServiceDockerPortMappingVO convertVo(ServiceDockerPortMappingPO request);
    

    List<ServiceDockerPortMappingPO> dtoToPo(List<ServiceDockerPortMappingDTO> serviceDockerPortMappingDTOList);

    @Mappings({})
    List<ServiceGroupDockerPortMappingVO> poToVO(List<ServiceGroupDockerPortMappingPO> list);
}