package com.sansec.ccsp.pt.business.docker.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.ccsp.pt.common.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 镜像上传记录表;
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2024-4-17
 */
@TableName("IMAGE_UPLOAD_RECORD")
@EqualsAndHashCode(callSuper = true)
@Data
public class ImageUploadRecordPO extends CommonEntity {
    /**
     * 
     */
    private Long id;
    /**
     * 镜像id
     */
    private Long imageId;
    /**
     * 镜像状态
     */
    private Integer status;
    /**
     * 错误信息
     */
    private String msg;

}