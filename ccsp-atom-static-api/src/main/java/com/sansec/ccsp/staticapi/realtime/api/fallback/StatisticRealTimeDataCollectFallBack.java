package com.sansec.ccsp.staticapi.realtime.api.fallback;

import com.sansec.ccsp.staticapi.realtime.api.StatisticRealTimeDataCollectApi;
import com.sansec.ccsp.staticapi.realtime.request.GetCallNumDTO;
import com.sansec.ccsp.staticapi.realtime.request.StatisticRealTimeCollectDTO;
import com.sansec.ccsp.staticapi.realtime.request.StatisticRealTimeVpnCollectDTO;
import com.sansec.common.param.response.SecRestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 采集实时数据，对接采集插件，熔断处理
 * @Author: zhangweicheng
 * @Date: 2024/3/6
 */

@Slf4j
@Component
public class StatisticRealTimeDataCollectFallBack implements FallbackFactory<StatisticRealTimeDataCollectApi> {


    @Override
    public StatisticRealTimeDataCollectApi create(Throwable throwable) {

        return new StatisticRealTimeDataCollectApi() {

            /**
             * @Description: 添加
             * @Param: [statisticRealTimeCollectDTO]
             * @return: com.sansec.common.param.response.SecRestResponse<java.lang.Object>
             * @Author: zhangweicheng
             * @Date: 2024/3/6
             */

            @Override
            public SecRestResponse<Object> add(StatisticRealTimeCollectDTO statisticRealTimeCollectDTO) {
                log.error("统计插件调用接口，传递数据失败:{}", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> addReal(StatisticRealTimeCollectDTO statisticRealTimeCollectDTO) {
                log.error("统计插件调用接口，传递真实数据失败:{}", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> addVpnRealTimeData(List<StatisticRealTimeVpnCollectDTO> vpnCollectDTOList) {
                log.error("新增VPN统计数据异常:{}", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Long> getCallNumByServiceType(GetCallNumDTO getCallNumDTO) {
                log.error("统计插件调用接口，获取服务调用量失败:{}", throwable);
                return SecRestResponse.fallback(throwable);
            }
        };
    }
}
