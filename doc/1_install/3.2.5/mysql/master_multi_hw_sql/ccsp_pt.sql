/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 100608
 Source Host           : ************:3306
 Source Schema         : ccsp_pt2

 Target Server Type    : MySQL
 Target Server Version : 100608
 File Encoding         : 65001

 Date: 14/07/2023 20:16:16
*/

SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_busi_to_group
-- ----------------------------
DROP TABLE IF EXISTS `app_busi_to_group`;
CREATE TABLE `app_busi_to_group`
(
    `ID`           bigint NOT NULL COMMENT 'ID',
    `APP_ID`       bigint NOT NULL COMMENT '应用ID',
    `BUSI_TYPE_ID` bigint NOT NULL COMMENT '业务类型ID',
    `GROUP_TYPE`   bigint NOT NULL COMMENT '组类型1:设备组；2：服务组',
    `GROUP_ID`     bigint NOT NULL COMMENT '组ID',
    `TENANT_ID`    bigint NOT NULL COMMENT '应用所属租户ID',
    `INVALID_FLAG` int    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用业务和设备组/服务组关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_busi_to_group
-- ----------------------------

-- ----------------------------
-- Table structure for app_register_to_tenant
-- ----------------------------
DROP TABLE IF EXISTS `app_register_to_tenant`;
CREATE TABLE `app_register_to_tenant`
(
    `ID`              bigint NOT NULL COMMENT 'ID',
    `APP_REGISTER_ID` bigint NOT NULL COMMENT '应用申请信息ID',
    `APP_ID`          bigint NOT NULL COMMENT '应用ID',
    `TENANT_ID`       bigint NOT NULL COMMENT '租户ID',
    `INVALID_FLAG`    int    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`          varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`       bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`       bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用申请和租户关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_register_to_tenant
-- ----------------------------

-- ----------------------------
-- Table structure for app_to_kms_relation
-- ----------------------------
DROP TABLE IF EXISTS `app_to_kms_relation`;
CREATE TABLE `app_to_kms_relation`
(
    `ID`              bigint                                                 NOT NULL COMMENT 'ID',
    `TENANT_ID`       bigint                                                 NOT NULL COMMENT '租户ID',
    `TENANT_CODE`     varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '租户标识',
    `SERVICE_TYPE_ID` bigint NULL DEFAULT NULL COMMENT '服务类型ID',
    `APP_ID`          bigint                                                 NOT NULL COMMENT '应用ID',
    `APP_CODE`        varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '应用标识',
    `KMS_APP_CODE`    varchar(270) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'KMS用户名称',
    `INVALID_FLAG`    int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`          varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`       bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`       bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用与KMS用户关联关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_to_kms_relation
-- ----------------------------

-- ----------------------------
-- Table structure for app_to_tenant
-- ----------------------------
DROP TABLE IF EXISTS `app_to_tenant`;
CREATE TABLE `app_to_tenant`
(
    `ID`          bigint NOT NULL COMMENT 'ID',
    `APP_ID`      bigint NOT NULL COMMENT '应用ID',
    `TENANT_ID`   bigint NOT NULL COMMENT '租户ID',
    `REMARK`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`   bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`   bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用和租户关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of app_to_tenant
-- ----------------------------

-- ----------------------------
-- Table structure for busi_url_info
-- ----------------------------
DROP TABLE IF EXISTS `busi_url_info`;
CREATE TABLE `busi_url_info`
(
    `ID`               bigint                                                 NOT NULL COMMENT '主键',
    `TENANT_ID`        bigint                                                 NOT NULL COMMENT '租户ID',
    `BUSI_URL_TYPE_ID` bigint                                                 NOT NULL COMMENT '业务地址类型',
    `NAME`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
    `PROTOCOL`         int                                                    NOT NULL COMMENT '请求协议',
    `IP`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'IP地址',
    `PORT`             int                                                    NOT NULL COMMENT '端口',
    `SERVICE_GROUP_ID` bigint(20) NULL DEFAULT NULL COMMENT '服务组ID',
    `REMARK`           varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`        bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`        bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务地址对象' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of busi_url_info
-- ----------------------------

-- ----------------------------
-- Table structure for busi_url_to_gateway
-- ----------------------------
DROP TABLE IF EXISTS `busi_url_to_gateway`;
CREATE TABLE `busi_url_to_gateway`
(
    `ID`                 bigint NOT NULL COMMENT '主键',
    `BUSI_URL_ID`        bigint NOT NULL COMMENT '业务地址ID',
    `SERVICE_GATEWAY_ID` bigint NOT NULL COMMENT '网关ID',
    `REMARK`             varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`          bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`          bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务地址和网关关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of busi_url_to_gateway
-- ----------------------------

-- ----------------------------
-- Table structure for busi_url_type_to_busi
-- ----------------------------
DROP TABLE IF EXISTS `busi_url_type_to_busi`;
CREATE TABLE `busi_url_type_to_busi`
(
    `ID`           bigint NOT NULL COMMENT '主键',
    `URL_TYPE_ID`  bigint NOT NULL COMMENT '业务地址类型ID',
    `BUSI_TYPE_ID` bigint NOT NULL COMMENT '业务类型ID',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务地址类型和业务类型关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of busi_url_type_to_busi
-- ----------------------------
INSERT INTO `busi_url_type_to_busi`
VALUES (1, 1, 1, '业务地址-加解密', 1, '1', 1, '1');
INSERT INTO `busi_url_type_to_busi`
VALUES (2, 1, 2, '业务地址-签名验签', 1, '1', 1, '1');
INSERT INTO `busi_url_type_to_busi`
VALUES (3, 1, 4, '业务地址-密钥管理', NULL, NULL, NULL, NULL);
INSERT INTO `busi_url_type_to_busi`
VALUES (4, 1, 5, '业务地址-时间戳', NULL, NULL, NULL, NULL);
INSERT INTO `busi_url_type_to_busi`
VALUES (5, 1, 7, '业务地址-动态令牌', NULL, NULL, NULL, NULL);
INSERT INTO `busi_url_type_to_busi`
VALUES (6, 1, 8, '业务地址-数据库加密', NULL, NULL, NULL, NULL);
INSERT INTO `busi_url_type_to_busi`
VALUES (7, 5, 9, '业务地址-文件加密', NULL, NULL, NULL, NULL);
INSERT INTO `busi_url_type_to_busi`
VALUES (8, 1, 10, '业务地址-电子签章', NULL, NULL, NULL, NULL);
INSERT INTO `busi_url_type_to_busi`
VALUES (9, 2, 11, '业务地址-SSLVPN加密通道', NULL, NULL, NULL, NULL);
INSERT INTO `busi_url_type_to_busi`
VALUES (10, 4, 4, '业务地址-密钥管理', NULL, NULL, NULL, NULL);
INSERT INTO `busi_url_type_to_busi`
VALUES (11, 3, 6, '业务地址-协同签名', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for ca_cert
-- ----------------------------
DROP TABLE IF EXISTS `ca_cert`;
CREATE TABLE `ca_cert`
(
    `CA_ID`              bigint                                                 NOT NULL COMMENT '证书ID',
    `ALIAS`              varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '证书标签',
    `SIGN_ALGORITHM`     varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '签名算法',
    `SERIALNUMBER`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '序列号(16进制数)',
    `VALID_TIME`         varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '证书起始时间',
    `EXPIRE_TIME`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '证书过期时间',
    `SUBJECT_DN`         varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '证书主题',
    `ISSUER_DN`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '颁发者DN',
    `CERTIFICATE`        longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '证书内容（base64编码)',
    `VERIFY_TYPE`        int                                                    NOT NULL COMMENT '校验类型（0.不校验1.CA证书校验2.CA证书+crl校验,3.ocsp校验）',
    `ALLOW_EXPIRED_FLAG` int                                                    NOT NULL DEFAULT 0 COMMENT '是否与允许过期（0允许，1不允许）;默认0',
    `CRL_TYPE`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'CRL认证时，CRL来源',
    `CRL_LENGTH`         int NULL DEFAULT NULL COMMENT 'CRL的长度',
    `CRL_INFO`           longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'CRL',
    `CRL_UPDATE_DAY`     int NULL DEFAULT NULL COMMENT '从CRL发布站点更新CRL时间0：每天，1.周一...7.周日',
    `CRL_UPDATE_HOUR`    int NULL DEFAULT NULL COMMENT '从CRL发布站点更新CRL的时间0:0点...23:23点',
    `CRL_UPDATE_TIME`    int NULL DEFAULT NULL COMMENT '从CRL发布站点更新CRL的时间',
    `STATUS`             int NULL DEFAULT NULL COMMENT '证书状态（1.有效，2.无效）',
    `DOMAIN_CERT_TYPE`   int NULL DEFAULT NULL COMMENT '信任域作用类型：1应用证书信任域，2平台证书信任域',
    `OCSP_CLIENT_CERT`   longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'OCSP客户端证书',
    `OCSP_AUTH_CODE`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'OCSP口令',
    `OCSP_URL`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'OCSP地址',
    `TENANT_ID`          bigint NULL DEFAULT NULL COMMENT '租户ID，平台添加的信任域租户id为空',
    `INVALID_FLAG`       int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`             varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`          bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`          bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    `OCSP_CA_CERT`       longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'ocspCA证书',
    `OCSP_NODE_CA_CERT`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'ocsp节点证书',
    PRIMARY KEY (`CA_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '信任域证书信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of ca_cert
-- ----------------------------

-- ----------------------------
-- Table structure for dic_busi_url_type
-- ----------------------------
DROP TABLE IF EXISTS `dic_busi_url_type`;
CREATE TABLE `dic_busi_url_type`
(
    `ID`          bigint                                                 NOT NULL COMMENT '主键',
    `NAME`        varchar(270) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型名称',
    `PROTOCOL`    int                                                    NOT NULL COMMENT '1 http 2https 3tcp',
    `REMARK`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
    `CREATE_BY`   bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`   bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务地址类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dic_busi_url_type
-- ----------------------------
INSERT INTO `dic_busi_url_type`
VALUES (1, '基础业务地址', 2, '数据加解密、签名验签、密钥管理、时间戳、文件加密、数据库加密、电子签章业务请求地址', NULL, '', NULL, '');
INSERT INTO `dic_busi_url_type`
VALUES (2, 'SSLVPN连接地址', 2, '使用VPN服务配置地址', NULL, '', NULL, '');
INSERT INTO `dic_busi_url_type`
VALUES (3, '协同签名业务地址', 2, '使用协同签名配置或请求地址', NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_url_type`
VALUES (4, '密钥管理KMIP地址', 3, '密钥管理KMIP配置地址', NULL, NULL, NULL, NULL);
INSERT INTO `dic_busi_url_type`
VALUES (5, '文件加密配置地址', 3, '文件加密客户端配置的服务器地址', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_service_quota
-- ----------------------------
DROP TABLE IF EXISTS `dic_service_quota`;
CREATE TABLE `dic_service_quota`
(
    `ID`              bigint                                                 NOT NULL COMMENT '主键',
    `QUOTA_NAME`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配额名称;key值',
    `SHOW_NAME`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配额显示名称;展示名称',
    `VALUE_UNIT`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配额值单位',
    `SERVICE_TYPE_ID` bigint                                                 NOT NULL COMMENT '服务类型ID',
    `SERVICE_CODE`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务标识',
    `TIME_VALID`      int                                                    NOT NULL DEFAULT 0 COMMENT '时间是否生效;1：有效，0：不生效',
    `DEFAULT_VALUE`   int                                                    NOT NULL DEFAULT -1 COMMENT '默认配额值',
    `MIN_VALUE`       int NULL DEFAULT NULL COMMENT '允许设置的最小值',
    `MAX_VALUE`       int NULL DEFAULT NULL COMMENT '允许设置的最大值',
    `MANAGE_TYPE`     int                                                    NOT NULL COMMENT '管理侧，1：服务侧管理，2：平台侧管理',
    `IS_ENABLE`       int                                                    NOT NULL DEFAULT 0 COMMENT '是否有效;1：有效；0：无效',
    `REMARK`          varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`       bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`       bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务服务配额信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of dic_service_quota
-- ----------------------------
INSERT INTO `dic_service_quota`
VALUES (1, 'serverNumbers', '服务数量', '个', 11, 'vpn', 0, 1000, 0, 1000, 1, 1, 'SSL VPN加密通道服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (2, 'userNumbers', '用户数量', '个', 6, 'sms', 0, 10000, 0, -1, 1, 1, '协同签名服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (4, 'userNumbers', '用户数量', '个', 7, 'secauth', 0, 10000, 0, -1, 1, 1, '动态令牌服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (7, 'appNumbers', '应用数量', '个', 5, 'tsa', 0, 99999999, 0, -1, 1, 1, '时间戳服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (10, 'userNumbers', '用户数量', '个', 10, 'tsc', 0, 99999999, 0, 99999999, 1, 1, '电子签章服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (11, 'appNumbers', '应用数量', '个', 10, 'tsc', 0, 99999999, 0, 99999999, 1, 1, '电子签章服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (12, 'databaseNumbers', '数据库数量', '个', 8, 'secdb', 0, 99999999, 0, -1, 1, 1, '数据库加密服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (13, 'fileServerNumbers', '文件服务器数量', '个', 9, 'secstorage', 0, 1000, 0, 1000, 1, 1, '文件加密服务', NULL, NULL, NULL,
        NULL);
INSERT INTO `dic_service_quota`
VALUES (14, 'nasServerNumbers', 'Nas服务器数量', '个', 9, 'secstorage', 0, 1000, 0, 1000, 1, 1, '文件加密服务', NULL, NULL, NULL,
        NULL);
INSERT INTO `dic_service_quota`
VALUES (15, 'keyNumbers', '密钥数量', '个', 1, 'pki', 0, 100000, 0, 100000, 1, 1, '加解密服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (17, 'appNumbers', '应用数量', '个', 1, 'pki', 0, 100000, 0, 100000, 2, 1, '加解密服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (18, 'caNumbers', '证书数量', '个', 12, 'ca', 0, 100000, 0, 100000, 1, 1, '数字证书认证服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (20, 'appNumbers', '应用数量', '个', 2, 'svs', 0, 10000, 0, 10000, 2, 1, '签名验签服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (21, 'keyNumbers', '密钥数量', '个', 4, 'kms', 0, 100000, 0, 100000, 1, 1, '密钥管理服务', NULL, NULL, NULL, NULL);
INSERT INTO `dic_service_quota`
VALUES (23, 'appNumbers', '应用数量', '个', 4, 'kms', 0, 10000, 0, 10000, 2, 1, '密钥管理服务', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for doc_download_path
-- ----------------------------
DROP TABLE IF EXISTS `doc_download_path`;
CREATE TABLE `doc_download_path`
(
    `ID`              bigint NOT NULL COMMENT '主键',
    `SERVICE_TYPE_ID` bigint NOT NULL COMMENT '服务类型id',
    `CONTEXT_PATH`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '服务类型路径(sdk和api路径中公共的地方)',
    `SDK_PATH`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'sdk下载路径',
    `API_PATH`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'api下载路径',
    `REMARK`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`       bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`       bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '服务sdk api文档路径表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of doc_download_path
-- ----------------------------
INSERT INTO `doc_download_path`
VALUES (1, 1, '/pki', '', '/restful_api.pdf', '加解密服务', NULL, NULL, NULL, NULL);
INSERT INTO `doc_download_path`
VALUES (2, 2, '/svs', '', '/restful_api.pdf', '签名验签服务', NULL, NULL, NULL, NULL);
INSERT INTO `doc_download_path`
VALUES (3, 3, '/digist', '', '', '杂凑服务', NULL, NULL, NULL, NULL);
INSERT INTO `doc_download_path`
VALUES (4, 4, '/kms', '/sdk.zip', '/restful_api.zip', '密钥管理服务', NULL, NULL, NULL, NULL);
INSERT INTO `doc_download_path`
VALUES (5, 5, '/tsa', '', '/restful_api.docx', '时间戳服务', NULL, NULL, NULL, NULL);
INSERT INTO `doc_download_path`
VALUES (6, 6, '/sms', '/Android_SDK.zip', '/restful_api.zip', '协同签名服务', NULL, NULL, NULL, NULL);
INSERT INTO `doc_download_path`
VALUES (7, 7, '/otp', '/sdk.zip', '/restful_api.pdf', '动态令牌服务', NULL, NULL, NULL, NULL);
INSERT INTO `doc_download_path`
VALUES (8, 8, '/secdb', '', '', '数据库加密服务', NULL, NULL, NULL, NULL);
INSERT INTO `doc_download_path`
VALUES (9, 9, '/secstorage', '/sdk.zip', '', '文件加密服务', NULL, NULL, NULL, NULL);
INSERT INTO `doc_download_path`
VALUES (10, 10, '/tsc', '', '/restful_api.docx', '电子签章服务', NULL, NULL, NULL, NULL);
INSERT INTO `doc_download_path`
VALUES (11, 11, '/vpn', '/sdk.zip', '/api.zip', 'SSL VPN加密通道服务', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for license_apply
-- ----------------------------
DROP TABLE IF EXISTS `license_apply`;
CREATE TABLE `license_apply`
(
    `ID`          bigint                                                 NOT NULL COMMENT '主键;许可证标识',
    `KEY_ID`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '密钥ID',
    `PRI_KEY`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '私钥;平台临时私钥',
    `PERIOD_TYPE` int                                                    NOT NULL COMMENT '周期类型;1-永久授权, 2-按年授权',
    `PERIOD_NUM`  int                                                    NOT NULL COMMENT '服务周期',
    `SERVER_TYPE` int                                                    NOT NULL COMMENT '许可证类型;1-业务服务，2-平台服务',
    `SERVER_NUM`  int                                                    NOT NULL COMMENT '服务数量',
    `STATUS`      int                                                    NOT NULL COMMENT '状态;1-申请中 2-已使用 3-作废',
    `LICENSE`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '许可证内容;加密数据',
    `HMAC`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性校验值',
    `REMARK`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`   bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '许可证申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of license_apply
-- ----------------------------

-- ----------------------------
-- Table structure for license_info
-- ----------------------------
DROP TABLE IF EXISTS `license_info`;
CREATE TABLE `license_info`
(
    `ID`          bigint NOT NULL COMMENT '主键;许可证标识',
    `SERVER_TYPE` int    NOT NULL COMMENT '服务类型;1-业务服务，2-平台服务',
    `SERVER_NUM`  int    NOT NULL COMMENT '服务数量',
    `PERIOD_TYPE` int    NOT NULL COMMENT '周期类型;1-永久授权, 2-按年授权',
    `PERIOD_NUM`  int    NOT NULL COMMENT '服务周期',
    `LICENSE`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '许可证内容',
    `CONTENT`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '内容;解析的LICENSE原始数据',
    `HMAC`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性校验值',
    `REMARK`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`   bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '许可证表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of license_info
-- ----------------------------

-- ----------------------------
-- Table structure for license_use
-- ----------------------------
DROP TABLE IF EXISTS `license_use`;
CREATE TABLE `license_use`
(
    `ID`          bigint                                                 NOT NULL COMMENT '主键',
    `LICENSE_ID`  bigint                                                 NOT NULL COMMENT '许可证ID',
    `NAME`        varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '许可证名称;L+yyyyMMddHHmmss+num',
    `SERVER_TYPE` int                                                    NOT NULL COMMENT '服务类型;1-业务服务，2-平台服务',
    `PERIOD_TYPE` int                                                    NOT NULL COMMENT '周期类型;1-永久授权, 2-按年授权',
    `PERIOD_NUM`  int                                                    NOT NULL COMMENT '服务周期',
    `START_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开始时间;开始时间;YYYY-MM-DD',
    `END_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结束时间;结束时间;YYYY-MM-DD，永久为空',
    `STATUS`      int                                                    NOT NULL COMMENT '状态;1-未使用，2-已使用，3-作废',
    `HMAC`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '完整性校验值',
    `REMARK`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`   bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '创建时间',
    `UPDATE_BY`   bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '许可证使用表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of license_use
-- ----------------------------

-- ----------------------------
-- Table structure for license_use_rel
-- ----------------------------
DROP TABLE IF EXISTS `license_use_rel`;
CREATE TABLE `license_use_rel`
(
    `ID`          bigint                                                 NOT NULL COMMENT '主键',
    `LICENSE_ID`  bigint                                                 NOT NULL COMMENT '许可证使用ID;已使用状态的LICENSE的ID',
    `SERVER_ID`   bigint                                                 NOT NULL COMMENT '服务实例ID',
    `HMAC`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '完整性校验值',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '许可证服务使用关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of license_use_rel
-- ----------------------------

-- ----------------------------
-- Table structure for license_use_time
-- ----------------------------
DROP TABLE IF EXISTS `license_use_time`;
CREATE TABLE `license_use_time`
(
    `ID`          bigint                                                 NOT NULL COMMENT '主键服务实例ID',
    `START_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '开始时间;开始时间;YYYY-MM-DD',
    `END_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结束时间;结束时间;YYYY-MM-DD，永久为空',
    `LICENSE_NUM` int                                                    NOT NULL COMMENT '使用license个数',
    `HMAC`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '完整性校验值',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '服务license时间' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of license_use_time
-- ----------------------------

-- ----------------------------
-- Table structure for route_server_port
-- ----------------------------
DROP TABLE IF EXISTS `route_server_port`;
CREATE TABLE `route_server_port`
(
    `ID`                 bigint NOT NULL COMMENT '主键',
    `SERVER_PORT`        int    NOT NULL COMMENT 'NGINX TCP服务端口',
    `APISIX_SERVER_PORT` int    NOT NULL COMMENT 'APISIX TCP服务端口',
    `REMARK`             varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`          bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`          bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '路由TCP服务端口资源表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of route_server_port
-- ----------------------------
INSERT INTO `route_server_port`
VALUES (1, 41000, 51000, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (2, 41001, 51001, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (3, 41002, 51002, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (4, 41003, 51003, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (5, 41004, 51004, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (6, 41005, 51005, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (7, 41006, 51006, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (8, 41007, 51007, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (9, 41008, 51008, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (10, 41009, 51009, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (11, 41010, 51010, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (12, 41011, 51011, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (13, 41012, 51012, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (14, 41013, 51013, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (15, 41014, 51014, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (16, 41015, 51015, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (17, 41016, 51016, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (18, 41017, 51017, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (19, 41018, 51018, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (20, 41019, 51019, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (21, 41020, 51020, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (22, 41021, 51021, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (23, 41022, 51022, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (24, 41023, 51023, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (25, 41024, 51024, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (26, 41025, 51025, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (27, 41026, 51026, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (28, 41027, 51027, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (29, 41028, 51028, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (30, 41029, 51029, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (31, 41030, 51030, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (32, 41031, 51031, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (33, 41032, 51032, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (34, 41033, 51033, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (35, 41034, 51034, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (36, 41035, 51035, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (37, 41036, 51036, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (38, 41037, 51037, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (39, 41038, 51038, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (40, 41039, 51039, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (41, 41040, 51040, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (42, 41041, 51041, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (43, 41042, 51042, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (44, 41043, 51043, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (45, 41044, 51044, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (46, 41045, 51045, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (47, 41046, 51046, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (48, 41047, 51047, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (49, 41048, 51048, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (51, 41049, 51049, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (52, 41050, 51050, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (53, 41051, 51051, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (54, 41052, 51052, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (55, 41053, 51053, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (56, 41054, 51054, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (57, 41055, 51055, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (58, 41056, 51056, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (59, 41057, 51057, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (60, 41058, 51058, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `route_server_port`
VALUES (61, 41059, 51059, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for route_to_app
-- ----------------------------
DROP TABLE IF EXISTS `route_to_app`;
CREATE TABLE `route_to_app`
(
    `ID`          bigint NOT NULL COMMENT '主键',
    `ROUTE_ID`    bigint NOT NULL COMMENT '路由表主键',
    `APP_ID`      bigint NULL DEFAULT NULL COMMENT '应用主键',
    `APP_CODE`    varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '应用标识',
    `REMARK`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`   bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`   bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '路由与应用关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of route_to_app
-- ----------------------------

-- ----------------------------
-- Table structure for route_to_service
-- ----------------------------
DROP TABLE IF EXISTS `route_to_service`;
CREATE TABLE `route_to_service`
(
    `ID`           bigint NOT NULL COMMENT '主键',
    `ROUTE_ID`     bigint NOT NULL COMMENT '路由表主键',
    `SERVICE_ID`   bigint NULL DEFAULT NULL COMMENT '服务主键/设备主键',
    `SERVICE_TYPE` int NULL DEFAULT NULL COMMENT '1服务 2设备',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '路由与服务或设备关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of route_to_service
-- ----------------------------

-- ----------------------------
-- Table structure for service_gateway
-- ----------------------------
DROP TABLE IF EXISTS `service_gateway`;
CREATE TABLE `service_gateway`
(
    `ID`           bigint                                                 NOT NULL COMMENT '主键',
    `GATEWAY_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '网关组件标识(apisix中的id)',
    `GATEWAY_NAME` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '网关名称',
    `IP`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '网关IP',
    `PORT`         int                                                    NOT NULL COMMENT '网关端口',
    `GATEWAY_TYPE` int NULL DEFAULT NULL COMMENT '网关类型，1：管理，2：业务',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'API网关表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of service_gateway
-- ----------------------------

-- ----------------------------
-- Table structure for service_gateway_route
-- ----------------------------
DROP TABLE IF EXISTS `service_gateway_route`;
CREATE TABLE `service_gateway_route`
(
    `ID`                 bigint                                                 NOT NULL COMMENT '主键',
    `ROUTE_CODE`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '路由组件标识(apisix中的id)',
    `ROUTE_NAME`         varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '路由名称',
    `ROUTE_TYPE`         int NULL DEFAULT NULL COMMENT '路由类型 1管理 2业务 3业务二 4TCP 5UDP',
    `TENANT_ID`          bigint NULL DEFAULT NULL COMMENT '租户id',
    `BUSI_TYPE_ID`       bigint NULL DEFAULT NULL COMMENT '业务/服务类型',
    `GROUP_ID`           bigint NULL DEFAULT NULL COMMENT '服务/设备组',
    `GROUP_TYPE`         int NULL DEFAULT NULL COMMENT '组类型 1服务组 2设备组',
    `GATEWAY_ID`         bigint NULL DEFAULT NULL COMMENT '网关表主键',
    `URIS`               varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '一组URL路径',
    `METHODS`            varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '一组请求限制',
    `HOSTS`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '一组host域名',
    `VARS`               varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '一组元素列表',
    `UPSTREAM_CODE`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'upstream组件标识(apisix中的id)',
    `UPSTREAM`           varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'upstream',
    `REMOTE_ADDRS`       varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '一组客户端请求 IP 地址',
    `TIMEOUT`            varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '为route 设置 upstream 的连接、发送消息、接收消息的超时时间',
    `PLUGINS`            varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'route 绑定插件',
    `SERVER_PORT`        int NULL DEFAULT NULL COMMENT 'NGINX TCP服务端口',
    `APISIX_SERVER_PORT` int NULL DEFAULT NULL COMMENT 'APISIX TCP服务端口',
    `FILTER_FUNC`        varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户自定义的过滤函数',
    `SERVICE_CODE`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '网关中服务组件标识(apisix中的id)',
    `REMARK`             varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`          bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`          bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'API网关路由表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of service_gateway_route
-- ----------------------------

-- ----------------------------
-- Table structure for service_interface_api_record
-- ----------------------------
DROP TABLE IF EXISTS `service_interface_api_record`;
CREATE TABLE `service_interface_api_record`
(
    `id`            bigint NOT NULL COMMENT '主键 uuid',
    `service_id`    bigint NULL DEFAULT NULL COMMENT '服务id',
    `api_name`      varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '接口名称',
    `ip`            varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求ip',
    `port`          int NULL DEFAULT NULL COMMENT '请求端口',
    `headers`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '请求头',
    `request_info`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '请求体',
    `response_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '响应体',
    `create_time`   varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '服务接口日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of service_interface_api_record
-- ----------------------------

-- ----------------------------
-- Table structure for service_to_device_group
-- ----------------------------
DROP TABLE IF EXISTS `service_to_device_group`;
CREATE TABLE `service_to_device_group`
(
    `ID`              bigint NOT NULL COMMENT 'ID',
    `SERVICE_ID`      bigint NOT NULL COMMENT '服务ID',
    `DEVICE_GROUP_ID` bigint NOT NULL COMMENT '设备组ID',
    `INVALID_FLAG`    int    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`          varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`       bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`       bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '服务和设备组关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of service_to_device_group
-- ----------------------------

-- ----------------------------
-- Table structure for service_type_to_device_group
-- ----------------------------
DROP TABLE IF EXISTS `service_type_to_device_group`;
CREATE TABLE `service_type_to_device_group`
(
    `ID`              bigint NOT NULL COMMENT '主键',
    `TENANT_ID`       bigint NOT NULL COMMENT '租户ID',
    `SERVICE_TYPE_ID` bigint NOT NULL COMMENT '服务类型ID',
    `DEVICE_GROUP_ID` bigint NULL DEFAULT NULL COMMENT '设备组ID',
    `REMARK`          varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`       bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`       bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '服务类型绑定设备组关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of service_type_to_device_group
-- ----------------------------

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`
(
    `JOB_ID`          bigint                                                 NOT NULL COMMENT '任务号',
    `JOB_NAME`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务名称',
    `JOB_GROUP`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务组名',
    `SERVER_ID`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务模块',
    `METHOD_URL`      varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调用接口',
    `JSON_PARAM`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'json格式参数',
    `CRON_EXPRESSION` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'CRON执行表达式',
    `MISFIRE_POLICY`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '3' COMMENT '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
    `CONCURRENT`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）',
    `JOB_STATUS`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
    `CREATED_BY`      bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATED_BY`      bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    `REMARK`          varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`JOB_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '定时任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job`
VALUES (1, 'getServiceStates', 'serviceStates', 'ccsp-aggregate-pt', NULL, 'serviceInvokeManage.getServiceState()',
        '0/10 * * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_job`
VALUES (2, 'clearServiceInterfaceApiRecord', 'clearServiceInterfaceApiRecord', 'ccsp-aggregate-pt', NULL,
        'serviceInvokeManage.clearServiceInterfaceApiRecord()', '10 0 0 1/1 * ?', '3', '1', '0', NULL, NULL, NULL, NULL,
        NULL);
INSERT INTO `sys_job`
VALUES (3, 'getServiceOperationLog', 'serviceLogs', 'ccsp-aggregate-pt', NULL,
        'serviceInvokeManage.getServiceOperationLog()', '0 0/5 * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_job`
VALUES (4, 'getTenantQuota', 'getTenantQuota', 'ccsp-aggregate-pt', NULL, 'serviceInvokeManage.getTenantQuota()',
        '0/30 * * * * ?', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_job`
VALUES (5, 'getStatisticIncreInfo', 'getStatisticIncreInfo', 'ccsp-aggregate-pt', NULL,
        'serviceInvokeManage.getStatisticIncreInfo()', '0 0/10 * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_job`
VALUES (6, 'getStatisticUnIncreInfo', 'getStatisticUnIncreInfo', 'ccsp-aggregate-pt', NULL,
        'serviceInvokeManage.getStatisticUnIncreInfo()', '0 0/1 * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_job` VALUES (7, 'getServiceLoginMenu', 'serviceLoginMenu', 'ccsp-aggregate-pt', NULL, 'serviceMenuLoginUrlTask.getServiceLoginMenu()', '0 0/10 * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, '获取各服务菜单');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`
(
    `JOB_LOG_ID`     bigint                                                NOT NULL COMMENT '任务日志ID',
    `JOB_ID`         bigint                                                NOT NULL COMMENT '任务ID',
    `JOB_MESSAGE`    varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志信息',
    `STATUS`         varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '执行状态（0失败 1正常）',
    `EXCEPTION_INFO` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '异常信息',
    `CREATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建时间',
    `TRIGGER_TIME`   bigint NULL DEFAULT NULL COMMENT '触发时间',
    PRIMARY KEY (`JOB_LOG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '定时任务执行日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS `sys_task`;
CREATE TABLE `sys_task`
(
    `TASK_ID`     bigint                                                 NOT NULL COMMENT '任务号',
    `TASK_NAME`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务名称',
    `TASK_GROUP`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务组名;执行任务串行',
    `SERVER_ID`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务模块',
    `METHOD_URL`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调用接口',
    `JSON_PARAM`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'json格式参数',
    `TASK_STATUS` int                                                    NOT NULL DEFAULT 0 COMMENT '状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时)',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    `REMARK`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `TIMEOUT`     int NULL DEFAULT NULL COMMENT '超时时间;单位秒',
    `START_TIME`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开始时间',
    `END_TIME`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结束时间',
    `POLICY`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   NOT NULL DEFAULT '0' COMMENT '是否允许重复执行;0-不允许，1允许',
    PRIMARY KEY (`TASK_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '异步任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_task
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_task_log`;
CREATE TABLE `sys_task_log`
(
    `TASK_LOG_ID`    bigint                                               NOT NULL COMMENT '任务日志ID',
    `TASK_ID`        bigint                                               NOT NULL COMMENT '任务ID',
    `TASK_MESSAGE`   varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志信息',
    `STATUS`         varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '执行状态（0失败 1正常）',
    `EXCEPTION_INFO` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '异常信息',
    `CREATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间;单位毫秒',
    `TRIGGER_TIME`   bigint NULL DEFAULT NULL COMMENT '触发时间;任务服务上送',
    PRIMARY KEY (`TASK_LOG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '异步任务执行日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_task_log
-- ----------------------------

-- ----------------------------
-- Table structure for tenant
-- ----------------------------
DROP TABLE IF EXISTS `tenant`;
CREATE TABLE `tenant`
(
    `TENANT_ID`        bigint                                                 NOT NULL COMMENT '租户ID',
    `TENANT_CODE`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '租户标识',
    `TENANT_NAME`      varchar(270) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
    `TENANT_LEVEL`     int                                                    NOT NULL COMMENT '租户级别',
    `SENIOR_TENANT_ID` bigint NULL DEFAULT NULL COMMENT '上级租户ID',
    `TENANT_STATUS`    int NULL DEFAULT NULL COMMENT '租户状态;1：初始化（未添加设备或服务）；2：运行中；3：停用中；4：停用; 5：启动中；',
    `DEVICE_GROUP_ID`  bigint                                                 NOT NULL COMMENT '租户设备资源组',
    `SERVICE_GROUP_ID` bigint                                                 NOT NULL COMMENT '租户服务资源组',
    `ORGAN`            varchar(270) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '机构名称',
    `HMAC`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性校验',
    `INVALID_FLAG`     int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`           varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`        bigint                                                 NOT NULL COMMENT '创建人',
    `CREATE_TIME`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '创建时间',
    `UPDATE_BY`        bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`TENANT_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant
-- ----------------------------
INSERT INTO `tenant`
VALUES (1, 'ccsp_tenant', '密服平台顶级租户', 0, 0, 2, 1, 1, '三未信安', NULL, 0, '', 1, '2023-03-03 06:11:21', 5469505260282187143,
        '2023-03-16 20:56:29');

-- ----------------------------
-- Table structure for tenant_key
-- ----------------------------
DROP TABLE IF EXISTS `tenant_key`;
CREATE TABLE `tenant_key`
(
    `ID`          bigint                                                  NOT NULL COMMENT '主键',
    `TENANT_ID`   bigint                                                  NOT NULL COMMENT '租户ID',
    `KEY_TYPE`    int                                                     NOT NULL COMMENT '密钥类型：1-2号非对称公钥，2-2号非对称私钥，3-2号对称密钥分量',
    `CONTENT`     varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '密钥内容',
    `CREATE_BY`   bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户密钥关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_key
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_quota_info
-- ----------------------------
DROP TABLE IF EXISTS `tenant_quota_info`;
CREATE TABLE `tenant_quota_info`
(
    `ID`           bigint                                                NOT NULL COMMENT '主键',
    `TENANT_ID`    bigint                                                NOT NULL COMMENT '租户ID',
    `SERVICE_CODE` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务标识;服务标识+配额信息key对应唯一配额信息',
    `QUOTA_NAME`   varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配额信息key',
    `QUOTA_VALUE`  int                                                   NOT NULL COMMENT '配额值',
    `START_TIME`   varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开始时间',
    `END_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结束数据',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    `HMAC`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户业务服务配额限制信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_quota_info
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_quota_use_info
-- ----------------------------
DROP TABLE IF EXISTS `tenant_quota_use_info`;
CREATE TABLE `tenant_quota_use_info`
(
    `ID`            bigint                                                NOT NULL COMMENT '主键',
    `TENANT_ID`     bigint                                                NOT NULL COMMENT '租户ID',
    `SERVICE_CODE`  varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务标识;服务标识+配额信息key对应唯一配额信息',
    `QUOTA_NAME`    varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配额信息key',
    `TOTAL_QUOTA`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '配额总量',
    `USED_QUOTA`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '已使用量',
    `RESIDUE_QUOTA` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '剩余量',
    `REMARK`        varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`     bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`   varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`     bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`   varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务服务配额使用信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_quota_use_info
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_register
-- ----------------------------
DROP TABLE IF EXISTS `tenant_register`;
CREATE TABLE `tenant_register`
(
    `ID`               bigint                                                 NOT NULL,
    `TENANT_ID`        bigint                                                 NOT NULL COMMENT '租户ID',
    `TENANT_CODE`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '租户标识',
    `TENANT_NAME`      varchar(270) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
    `TENANT_LEVEL`     int                                                    NOT NULL COMMENT '租户级别',
    `SENIOR_TENANT_ID` bigint NULL DEFAULT NULL COMMENT '上级租户ID',
    `TENANT_STATUS`    int NULL DEFAULT NULL COMMENT '租户状态;1：初始化；2：运行中；3：停用;4:销毁',
    `PARTITION_ID`     bigint NULL DEFAULT NULL COMMENT '租户资源分区ID',
    `ORGAN`            varchar(270) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '机构名称',
    `STATUS`           int NULL DEFAULT NULL COMMENT '状态；1:待审核；2：通过；3：拒绝',
    `AUDIT_BY`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批人',
    `AUDIT_REMARK`     varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审批意见',
    `INVALID_FLAG`     int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `HMAC`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性',
    `REMARK`           varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`        bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '创建时间',
    `UPDATE_BY`        bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户注册表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_register
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_register_user
-- ----------------------------
DROP TABLE IF EXISTS `tenant_register_user`;
CREATE TABLE `tenant_register_user`
(
    `ID`          bigint NULL DEFAULT NULL COMMENT '主键',
    `TENANT_ID`   bigint NULL DEFAULT NULL COMMENT '租户ID',
    `USER_TYPE`   int NULL DEFAULT NULL COMMENT '用户类型',
    `USER_NAME`   varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户名称',
    `USER_CODE`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '账号',
    `AUTH_CODE`   varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '密码;加密存储',
    `CERT`        varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '证书内容',
    `SERIAL`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Ukey序列号',
    `RANDOM`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Ukey验证随机数',
    `SIGNATURE`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Ukey验证签名值',
    `REMARK`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`   bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`   bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户注册用户信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_register_user
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_secret_key_resource
-- ----------------------------
DROP TABLE IF EXISTS `tenant_secret_key_resource`;
CREATE TABLE `tenant_secret_key_resource`
(
    `ID`             bigint NOT NULL COMMENT '主键',
    `TENANT_ID`      bigint NOT NULL COMMENT '租户ID',
    `SECRET_NUM`     int NULL DEFAULT NULL COMMENT '密钥数量;-1表示无限',
    `CERT_NUM`       int NULL DEFAULT NULL COMMENT '证书数量;-1表示无限',
    `USE_SECRET_NUM` int NULL DEFAULT NULL COMMENT '使用密钥数量',
    `USE_CERT_NUM`   int NULL DEFAULT NULL COMMENT '使用证书数量',
    `REMARK`         varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`      bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`      bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户密钥资源管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_secret_key_resource
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_to_busi_type
-- ----------------------------
DROP TABLE IF EXISTS `tenant_to_busi_type`;
CREATE TABLE `tenant_to_busi_type`
(
    `ID`           bigint                                                NOT NULL COMMENT 'ID',
    `TENANT_ID`    bigint                                                NOT NULL COMMENT '租户ID',
    `BUSI_TYPE_ID` bigint                                                NOT NULL COMMENT '业务类型ID',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint                                                NOT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户和业务类型关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_to_busi_type
-- ----------------------------


-- ----------------------------
-- Table structure for tenant_to_database
-- ----------------------------
DROP TABLE IF EXISTS `tenant_to_database`;
CREATE TABLE `tenant_to_database`
(
      `ID` bigint(20) NOT NULL COMMENT '主键',
      `TENANT_ID` bigint(20) NOT NULL COMMENT '租户ID',
      `DATABASE_ID` bigint(20) DEFAULT NULL COMMENT '数据库服务ID',
      `CREATE_BY` bigint(20) NOT NULL COMMENT '创建人',
      `CREATE_TIME` varchar(30) COLLATE utf8mb4_bin NOT NULL COMMENT '创建时间',
      `UPDATE_BY` bigint(20) DEFAULT NULL COMMENT '更新人',
      `UPDATE_TIME` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新时间',
      PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='租户和数据库关联表';

-- ----------------------------
-- Records of tenant_to_database
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_to_kms_relation
-- ----------------------------
DROP TABLE IF EXISTS `tenant_to_kms_relation`;
CREATE TABLE `tenant_to_kms_relation`
(
    `ID`              bigint                                                 NOT NULL COMMENT 'ID',
    `TENANT_ID`       bigint                                                 NOT NULL COMMENT '租户id',
    `TENANT_CODE`     varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '租户标识',
    `KMS_TENANT_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'KMS租户名称',
    `INVALID_FLAG`    int                                                    NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
    `REMARK`          varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`       bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`       bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '租户与kms租户关联关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tenant_to_kms_relation
-- ----------------------------

-- ----------------------------
-- Table structure for version
-- ----------------------------
DROP TABLE IF EXISTS `version`;
CREATE TABLE `version`
(
    `ID`           bigint                                                 NOT NULL COMMENT '主键',
    `PRODUCT_CODE` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品简称',
    `PRODUCT_NAME` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品名称',
    `VERSION`      varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '版本',
    `SORD_NUM`     int                                                    NOT NULL COMMENT '排序',
    `REMARK`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`    bigint NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`    bigint NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`, `PRODUCT_CODE`, `PRODUCT_NAME`, `VERSION`, `SORD_NUM`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '版本表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of version
-- ----------------------------
INSERT INTO `version`
VALUES (1, 'CCSP', '密码服务管理平台', 'V3.2.5', 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (2, 'KMS', '密钥管理', 'V4.0.0', 2, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (3, 'PKI', '数据加解密', 'V3.2.5.2', 3, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (4, 'SVS', '签名验签', 'V3.2.5.2', 4, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (5, 'TSA', '时间戳', 'V4.0.2.1', 5, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (6, 'OTP', '动态令牌', 'V3.2.0', 6, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (7, 'SMS', '协同签名', 'V4.6.3', 7, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (8, 'SECDB', '数据库加密', 'V3.3.1.1', 8, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (9, 'VPN', 'SSLVPN加密通道', 'V2.2.9', 9, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (10, 'SECSTORAGE', '文件加密', 'V4.0.0', 10, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (11, 'TSC', '电子签章', 'V1.1.0', 11, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `version`
VALUES (12, 'WEB', '统一web平台', 'V1.1.2', 12, NULL, NULL, NULL, NULL, NULL);


-- ----------------------------
-- Table structure for service_version
-- ----------------------------
DROP TABLE IF EXISTS `service_version`;
CREATE TABLE `service_version`
(
    `ID`               bigint(20) NOT NULL COMMENT '主键',
    `SERVICE_TYPE_ID`  bigint(20) NOT NULL COMMENT '服务类型ID',
    `SERVICE_GROUP_ID` bigint(20) NULL DEFAULT NULL COMMENT '服务组ID',
    `SERVICE_NAME`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '产品名称',
    `SERVICE_MODEL`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '产品型号',
    `SERVICE_VERSION`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '产品版本',
    `REMARK`           varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `CREATE_BY`        bigint(20) NULL DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`      varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
    `UPDATE_BY`        bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`      varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '业务服务版本信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of service_version
-- ----------------------------
INSERT INTO `service_version`
VALUES (1, 1, NULL, '服务器密码机', 'SJJ1012-A V6', 'V3.2.5.2', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_version`
VALUES (2, 2, NULL, '签名验签服务器', 'SRJ1909 V6', 'V3.2.5.2', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_version`
VALUES (4, 4, NULL, '云服务端密钥管理系统', 'SYT1931', 'V4.0.0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_version`
VALUES (5, 5, NULL, '时间戳服务器', 'SFJ1803', 'V4.0.2.1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_version`
VALUES (6, 6, NULL, '协同签名系统服务端密码模块', 'SRT1922-G', 'V4.6.3', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_version`
VALUES (7, 7, NULL, '动态令牌认证系统', 'SecAuth V2', 'V3.2.0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_version`
VALUES (8, 8, NULL, '数据库加密机', 'SJJ19127-G V2', 'V3.3.1.1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_version`
VALUES (9, 9, NULL, '文件加密服务（密码模块）', 'SecStorage V4', 'V4.0.0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_version`
VALUES (10, 10, NULL, '电子签章系统', 'SFT1924-G', 'V1.1.0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `service_version`
VALUES (11, 11, NULL, 'SSL VPN 综合安全网关', 'SecGW G1300', 'V2.2.9', NULL, NULL, NULL, NULL, NULL);


SET
FOREIGN_KEY_CHECKS = 1;
