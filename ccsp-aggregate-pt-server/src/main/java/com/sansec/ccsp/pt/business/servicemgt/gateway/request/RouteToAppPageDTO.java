package com.sansec.ccsp.pt.business.servicemgt.gateway.request;

import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Size;

 /**
 * @Description: 路由与应用关联表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-2-24
 */
@Data
public class RouteToAppPageDTO extends SecPageDTO{
   /**
     * 应用名
     */
    @Size(max = 50, message = "名称长度限制50")
    private String name;
}