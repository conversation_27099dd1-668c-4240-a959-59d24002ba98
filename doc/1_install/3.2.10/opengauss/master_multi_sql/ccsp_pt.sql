/*
 Navicat Premium Data Transfer

 Source Server         : ***********opengauss
 Source Server Type    : PostgreSQL
 Source Server Version : 90204
 Source Host           : ***********:5432
 Source Catalog        : mfptyky
 Source Schema         : ccsp_pt

 Target Server Type    : PostgreSQL
 Target Server Version : 90204
 File Encoding         : 65001

 Date: 07/10/2023 18:01:53
*/


-- ----------------------------
-- Table structure for app_busi_to_group
-- ----------------------------
DROP TABLE IF EXISTS "app_busi_to_group";
CREATE TABLE "app_busi_to_group" (
  "id" int8 NOT NULL,
  "app_id" int8 NOT NULL,
  "busi_type_id" int8 NOT NULL,
  "group_type" int8 NOT NULL,
  "group_id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "app_busi_to_group"."id" IS 'ID';
COMMENT ON COLUMN "app_busi_to_group"."app_id" IS '应用ID';
COMMENT ON COLUMN "app_busi_to_group"."busi_type_id" IS '业务类型ID';
COMMENT ON COLUMN "app_busi_to_group"."group_type" IS '组类型1:设备组；2：服务组';
COMMENT ON COLUMN "app_busi_to_group"."group_id" IS '组ID';
COMMENT ON COLUMN "app_busi_to_group"."tenant_id" IS '应用所属租户ID';
COMMENT ON COLUMN "app_busi_to_group"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "app_busi_to_group"."remark" IS '备注';
COMMENT ON COLUMN "app_busi_to_group"."create_by" IS '创建人';
COMMENT ON COLUMN "app_busi_to_group"."create_time" IS '创建时间';
COMMENT ON COLUMN "app_busi_to_group"."update_by" IS '更新人';
COMMENT ON COLUMN "app_busi_to_group"."update_time" IS '更新时间';
COMMENT ON TABLE "app_busi_to_group" IS '应用业务和设备组/服务组关联表';

-- ----------------------------
-- Records of app_busi_to_group
-- ----------------------------

-- ----------------------------
-- Table structure for app_register_to_tenant
-- ----------------------------
DROP TABLE IF EXISTS "app_register_to_tenant";
CREATE TABLE "app_register_to_tenant" (
  "id" int8 NOT NULL,
  "app_register_id" int8 NOT NULL,
  "app_id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "app_register_to_tenant"."id" IS 'ID';
COMMENT ON COLUMN "app_register_to_tenant"."app_register_id" IS '应用申请信息ID';
COMMENT ON COLUMN "app_register_to_tenant"."app_id" IS '应用ID';
COMMENT ON COLUMN "app_register_to_tenant"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "app_register_to_tenant"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "app_register_to_tenant"."remark" IS '备注';
COMMENT ON COLUMN "app_register_to_tenant"."create_by" IS '创建人';
COMMENT ON COLUMN "app_register_to_tenant"."create_time" IS '创建时间';
COMMENT ON COLUMN "app_register_to_tenant"."update_by" IS '更新人';
COMMENT ON COLUMN "app_register_to_tenant"."update_time" IS '更新时间';
COMMENT ON TABLE "app_register_to_tenant" IS '应用申请和租户关联表';

-- ----------------------------
-- Records of app_register_to_tenant
-- ----------------------------

-- ----------------------------
-- Table structure for app_to_kms_relation
-- ----------------------------
DROP TABLE IF EXISTS "app_to_kms_relation";
CREATE TABLE "app_to_kms_relation" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "tenant_code" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
  "service_type_id" int8,
  "app_id" int8 NOT NULL,
  "app_code" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
  "kms_app_code" varchar(270) COLLATE "pg_catalog"."default" NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "app_to_kms_relation"."id" IS 'ID';
COMMENT ON COLUMN "app_to_kms_relation"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "app_to_kms_relation"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "app_to_kms_relation"."service_type_id" IS '服务类型ID';
COMMENT ON COLUMN "app_to_kms_relation"."app_id" IS '应用ID';
COMMENT ON COLUMN "app_to_kms_relation"."app_code" IS '应用标识';
COMMENT ON COLUMN "app_to_kms_relation"."kms_app_code" IS 'KMS用户名称';
COMMENT ON COLUMN "app_to_kms_relation"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "app_to_kms_relation"."remark" IS '备注';
COMMENT ON COLUMN "app_to_kms_relation"."create_by" IS '创建人';
COMMENT ON COLUMN "app_to_kms_relation"."create_time" IS '创建时间';
COMMENT ON COLUMN "app_to_kms_relation"."update_by" IS '更新人';
COMMENT ON COLUMN "app_to_kms_relation"."update_time" IS '更新时间';
COMMENT ON TABLE "app_to_kms_relation" IS '应用与KMS用户关联关系表';

-- ----------------------------
-- Records of app_to_kms_relation
-- ----------------------------

-- ----------------------------
-- Table structure for app_to_tenant
-- ----------------------------
DROP TABLE IF EXISTS "app_to_tenant";
CREATE TABLE "app_to_tenant" (
  "id" int8 NOT NULL,
  "app_id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "app_to_tenant"."id" IS 'ID';
COMMENT ON COLUMN "app_to_tenant"."app_id" IS '应用ID';
COMMENT ON COLUMN "app_to_tenant"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "app_to_tenant"."remark" IS '备注';
COMMENT ON COLUMN "app_to_tenant"."create_by" IS '创建人';
COMMENT ON COLUMN "app_to_tenant"."create_time" IS '创建时间';
COMMENT ON COLUMN "app_to_tenant"."update_by" IS '更新人';
COMMENT ON COLUMN "app_to_tenant"."update_time" IS '更新时间';
COMMENT ON TABLE "app_to_tenant" IS '应用和租户关联表';

-- ----------------------------
-- Records of app_to_tenant
-- ----------------------------

-- ----------------------------
-- Table structure for busi_url_info
-- ----------------------------
DROP TABLE IF EXISTS "busi_url_info";
CREATE TABLE "busi_url_info" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "busi_url_type_id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "protocol" int4 NOT NULL,
  "ip" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "port" int4 NOT NULL,
  "region_id" int8,
  "service_group_id" int8,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "busi_url_info"."id" IS '主键';
COMMENT ON COLUMN "busi_url_info"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "busi_url_info"."busi_url_type_id" IS '业务地址类型';
COMMENT ON COLUMN "busi_url_info"."name" IS '名称';
COMMENT ON COLUMN "busi_url_info"."protocol" IS '请求协议';
COMMENT ON COLUMN "busi_url_info"."ip" IS 'IP地址';
COMMENT ON COLUMN "busi_url_info"."port" IS '端口';
COMMENT ON COLUMN "busi_url_info"."region_id" IS '区域ID';
COMMENT ON COLUMN "busi_url_info"."service_group_id" IS '服务组ID';
COMMENT ON COLUMN "busi_url_info"."remark" IS '备注';
COMMENT ON COLUMN "busi_url_info"."create_by" IS '创建人';
COMMENT ON COLUMN "busi_url_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "busi_url_info"."update_by" IS '更新人';
COMMENT ON COLUMN "busi_url_info"."update_time" IS '更新时间';
COMMENT ON TABLE "busi_url_info" IS '业务地址对象';

-- ----------------------------
-- Records of busi_url_info
-- ----------------------------

-- ----------------------------
-- Table structure for busi_url_to_gateway
-- ----------------------------
DROP TABLE IF EXISTS "busi_url_to_gateway";
CREATE TABLE "busi_url_to_gateway" (
  "id" int8 NOT NULL,
  "busi_url_id" int8 NOT NULL,
  "service_gateway_id" int8 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "busi_url_to_gateway"."id" IS '主键';
COMMENT ON COLUMN "busi_url_to_gateway"."busi_url_id" IS '业务地址ID';
COMMENT ON COLUMN "busi_url_to_gateway"."service_gateway_id" IS '网关ID';
COMMENT ON COLUMN "busi_url_to_gateway"."remark" IS '备注';
COMMENT ON COLUMN "busi_url_to_gateway"."create_by" IS '创建人';
COMMENT ON COLUMN "busi_url_to_gateway"."create_time" IS '创建时间';
COMMENT ON COLUMN "busi_url_to_gateway"."update_by" IS '更新人';
COMMENT ON COLUMN "busi_url_to_gateway"."update_time" IS '更新时间';
COMMENT ON TABLE "busi_url_to_gateway" IS '业务地址和网关关联表';

-- ----------------------------
-- Records of busi_url_to_gateway
-- ----------------------------

-- ----------------------------
-- Table structure for busi_url_type_to_busi
-- ----------------------------
DROP TABLE IF EXISTS "busi_url_type_to_busi";
CREATE TABLE "busi_url_type_to_busi" (
  "id" int8 NOT NULL,
  "url_type_id" int8 NOT NULL,
  "busi_type_id" int8 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "busi_url_type_to_busi"."id" IS '主键';
COMMENT ON COLUMN "busi_url_type_to_busi"."url_type_id" IS '业务地址类型ID';
COMMENT ON COLUMN "busi_url_type_to_busi"."busi_type_id" IS '业务类型ID';
COMMENT ON COLUMN "busi_url_type_to_busi"."remark" IS '备注';
COMMENT ON COLUMN "busi_url_type_to_busi"."create_by" IS '创建人';
COMMENT ON COLUMN "busi_url_type_to_busi"."create_time" IS '创建时间';
COMMENT ON COLUMN "busi_url_type_to_busi"."update_by" IS '更新人';
COMMENT ON COLUMN "busi_url_type_to_busi"."update_time" IS '更新时间';
COMMENT ON TABLE "busi_url_type_to_busi" IS '业务地址类型和业务类型关联表';

-- ----------------------------
-- Records of busi_url_type_to_busi
-- ----------------------------
INSERT INTO "busi_url_type_to_busi" VALUES (1, 1, 1, '业务地址-加解密', 1, '1', 1, '1');
INSERT INTO "busi_url_type_to_busi" VALUES (2, 1, 2, '业务地址-签名验签', 1, '1', 1, '1');
INSERT INTO "busi_url_type_to_busi" VALUES (3, 1, 4, '业务地址-密钥管理', NULL, NULL, NULL, NULL);
INSERT INTO "busi_url_type_to_busi" VALUES (4, 1, 5, '业务地址-时间戳', NULL, NULL, NULL, NULL);
INSERT INTO "busi_url_type_to_busi" VALUES (5, 1, 7, '业务地址-动态令牌', NULL, NULL, NULL, NULL);
INSERT INTO "busi_url_type_to_busi" VALUES (6, 1, 8, '业务地址-数据库加密', NULL, NULL, NULL, NULL);
INSERT INTO "busi_url_type_to_busi"
VALUES (7, 5, 9, '业务地址-文件加密', NULL, NULL, NULL, NULL);
INSERT INTO "busi_url_type_to_busi"
VALUES (8, 1, 10, '业务地址-电子签章', NULL, NULL, NULL, NULL);
INSERT INTO "busi_url_type_to_busi"
VALUES (9, 2, 11, '业务地址-SSLVPN加密通道', NULL, NULL, NULL, NULL);
INSERT INTO "busi_url_type_to_busi"
VALUES (10, 4, 4, '业务地址-密钥管理', NULL, NULL, NULL, NULL);
INSERT INTO "busi_url_type_to_busi"
VALUES (11, 3, 6, '业务地址-协同签名', NULL, NULL, NULL, NULL);
INSERT INTO "busi_url_type_to_busi"
VALUES (12, 1, 12, '业务地址-数字证书认证', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for ca_cert
-- ----------------------------
DROP TABLE IF EXISTS "ca_cert";
CREATE TABLE "ca_cert"
(
    "ca_id"            int8                                        NOT NULL,
    "alias"            varchar(128) COLLATE "pg_catalog"."default" NOT NULL,
    "sign_algorithm"   varchar(96) COLLATE "pg_catalog"."default"  NOT NULL,
    "serialnumber"     varchar(64) COLLATE "pg_catalog"."default"  NOT NULL,
  "valid_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "expire_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "subject_dn" varchar(512) COLLATE "pg_catalog"."default" NOT NULL,
  "issuer_dn" varchar(255) COLLATE "pg_catalog"."default",
  "certificate" text COLLATE "pg_catalog"."default" NOT NULL,
  "verify_type" int4 NOT NULL,
  "allow_expired_flag" int4 NOT NULL DEFAULT 0,
  "crl_type" varchar(255) COLLATE "pg_catalog"."default",
  "crl_length" int4,
  "crl_info" text COLLATE "pg_catalog"."default",
  "crl_update_day" int4,
  "crl_update_hour" int4,
  "crl_update_time" int4,
  "status" int4,
  "domain_cert_type" int4,
  "ocsp_client_cert" text COLLATE "pg_catalog"."default",
  "ocsp_auth_code" varchar(255) COLLATE "pg_catalog"."default",
  "ocsp_url" varchar(255) COLLATE "pg_catalog"."default",
  "tenant_id" int8,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "ocsp_ca_cert" text COLLATE "pg_catalog"."default",
  "ocsp_node_ca_cert" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ca_cert"."ca_id" IS '证书ID';
COMMENT ON COLUMN "ca_cert"."alias" IS '证书标签';
COMMENT ON COLUMN "ca_cert"."sign_algorithm" IS '签名算法';
COMMENT ON COLUMN "ca_cert"."serialnumber" IS '序列号(16进制数)';
COMMENT ON COLUMN "ca_cert"."valid_time" IS '证书起始时间';
COMMENT ON COLUMN "ca_cert"."expire_time" IS '证书过期时间';
COMMENT ON COLUMN "ca_cert"."subject_dn" IS '证书主题';
COMMENT ON COLUMN "ca_cert"."issuer_dn" IS '颁发者DN';
COMMENT ON COLUMN "ca_cert"."certificate" IS '证书内容（base64编码)';
COMMENT ON COLUMN "ca_cert"."verify_type" IS '校验类型（0.不校验1.CA证书校验2.CA证书+crl校验,3.ocsp校验）';
COMMENT ON COLUMN "ca_cert"."allow_expired_flag" IS '是否与允许过期（0允许，1不允许）;默认0';
COMMENT ON COLUMN "ca_cert"."crl_type" IS 'CRL认证时，CRL来源';
COMMENT ON COLUMN "ca_cert"."crl_length" IS 'CRL的长度';
COMMENT ON COLUMN "ca_cert"."crl_info" IS 'CRL';
COMMENT ON COLUMN "ca_cert"."crl_update_day" IS '从CRL发布站点更新CRL时间0：每天，1.周一...7.周日';
COMMENT ON COLUMN "ca_cert"."crl_update_hour" IS '从CRL发布站点更新CRL的时间0:0点...23:23点';
COMMENT ON COLUMN "ca_cert"."crl_update_time" IS '从CRL发布站点更新CRL的时间';
COMMENT ON COLUMN "ca_cert"."status" IS '证书状态（1.有效，2.无效）';
COMMENT ON COLUMN "ca_cert"."domain_cert_type" IS '信任域作用类型：1应用证书信任域，2平台证书信任域';
COMMENT ON COLUMN "ca_cert"."ocsp_client_cert" IS 'OCSP客户端证书';
COMMENT ON COLUMN "ca_cert"."ocsp_auth_code" IS 'OCSP口令';
COMMENT ON COLUMN "ca_cert"."ocsp_url" IS 'OCSP地址';
COMMENT ON COLUMN "ca_cert"."tenant_id" IS '租户ID，平台添加的信任域租户id为空';
COMMENT ON COLUMN "ca_cert"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ca_cert"."remark" IS '备注';
COMMENT ON COLUMN "ca_cert"."create_by" IS '创建人';
COMMENT ON COLUMN "ca_cert"."create_time" IS '创建时间';
COMMENT ON COLUMN "ca_cert"."update_by" IS '更新人';
COMMENT ON COLUMN "ca_cert"."update_time" IS '更新时间';
COMMENT ON COLUMN "ca_cert"."ocsp_ca_cert" IS 'ocspCA证书';
COMMENT ON COLUMN "ca_cert"."ocsp_node_ca_cert" IS 'ocsp节点证书';
COMMENT ON TABLE "ca_cert" IS '信任域证书信息';

-- ----------------------------
-- Records of ca_cert
-- ----------------------------

-- ----------------------------
-- Table structure for dic_busi_url_type
-- ----------------------------
DROP TABLE IF EXISTS "dic_busi_url_type";
CREATE TABLE "dic_busi_url_type" (
  "id" int8 NOT NULL,
  "name" varchar(270) COLLATE "pg_catalog"."default" NOT NULL,
  "protocol" int4 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "dic_busi_url_type"."id" IS '主键';
COMMENT ON COLUMN "dic_busi_url_type"."name" IS '类型名称';
COMMENT ON COLUMN "dic_busi_url_type"."protocol" IS '1 http 2https 3tcp';
COMMENT ON COLUMN "dic_busi_url_type"."remark" IS '描述';
COMMENT ON COLUMN "dic_busi_url_type"."create_by" IS '创建人';
COMMENT ON COLUMN "dic_busi_url_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "dic_busi_url_type"."update_by" IS '更新人';
COMMENT ON COLUMN "dic_busi_url_type"."update_time" IS '更新时间';
COMMENT ON TABLE "dic_busi_url_type" IS '业务地址类型';

-- ----------------------------
-- Records of dic_busi_url_type
-- ----------------------------
INSERT INTO "dic_busi_url_type" VALUES (1, '基础业务地址', 2, '数据加解密、签名验签、密钥管理、时间戳、文件加密、数据库加密、电子签章业务请求地址', NULL, NULL, NULL, NULL);
INSERT INTO "dic_busi_url_type" VALUES (2, 'SSLVPN连接地址', 2, '使用VPN服务配置地址', NULL, NULL, NULL, NULL);
INSERT INTO "dic_busi_url_type" VALUES (3, '协同签名业务地址', 2, '使用协同签名配置或请求地址', NULL, NULL, NULL, NULL);
INSERT INTO "dic_busi_url_type" VALUES (4, '密钥管理KMIP地址', 3, '密钥管理KMIP配置地址', NULL, NULL, NULL, NULL);
INSERT INTO "dic_busi_url_type" VALUES (5, '文件加密配置地址', 3, '文件加密客户端配置的服务器地址', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_service_quota
-- ----------------------------
DROP TABLE IF EXISTS "dic_service_quota";
CREATE TABLE "dic_service_quota" (
  "id" int8 NOT NULL,
  "quota_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "show_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "value_unit" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "service_type_id" int8 NOT NULL,
  "service_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "time_valid" int4 NOT NULL DEFAULT 0,
  "default_value" int4 NOT NULL,
  "min_value" int4,
  "max_value" int4,
  "manage_type" int4 NOT NULL,
  "is_enable" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "dic_service_quota"."id" IS '主键';
COMMENT ON COLUMN "dic_service_quota"."quota_name" IS '配额名称;key值';
COMMENT ON COLUMN "dic_service_quota"."show_name" IS '配额显示名称;展示名称';
COMMENT ON COLUMN "dic_service_quota"."value_unit" IS '配额值单位';
COMMENT ON COLUMN "dic_service_quota"."service_type_id" IS '服务类型ID';
COMMENT ON COLUMN "dic_service_quota"."service_code" IS '服务标识';
COMMENT ON COLUMN "dic_service_quota"."time_valid" IS '时间是否生效;1：有效，0：不生效';
COMMENT ON COLUMN "dic_service_quota"."default_value" IS '默认配额值';
COMMENT ON COLUMN "dic_service_quota"."min_value" IS '允许设置的最小值';
COMMENT ON COLUMN "dic_service_quota"."max_value" IS '允许设置的最大值';
COMMENT ON COLUMN "dic_service_quota"."manage_type" IS '管理侧，1：服务侧管理，2：平台侧管理';
COMMENT ON COLUMN "dic_service_quota"."is_enable" IS '是否有效;1：有效；0：无效';
COMMENT ON COLUMN "dic_service_quota"."remark" IS '备注';
COMMENT ON COLUMN "dic_service_quota"."create_by" IS '创建人';
COMMENT ON COLUMN "dic_service_quota"."create_time" IS '创建时间';
COMMENT ON COLUMN "dic_service_quota"."update_by" IS '更新人';
COMMENT ON COLUMN "dic_service_quota"."update_time" IS '更新时间';
COMMENT ON TABLE "dic_service_quota" IS '业务服务配额信息';

-- ----------------------------
-- Records of dic_service_quota
-- ----------------------------
INSERT INTO "dic_service_quota" VALUES (1, 'serverNumbers', '服务数量', '个', 11, 'vpn', 0, 1000, 0, 1000, 1, 1, 'SSL VPN加密通道服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (2, 'userNumbers', '用户数量', '个', 6, 'sms', 0, 10000, 0, -1, 1, 1, '协同签名服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (4, 'userNumbers', '用户数量', '个', 7, 'secauth', 0, 10000, 0, -1, 1, 1, '动态令牌服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (7, 'appNumbers', '应用数量', '个', 5, 'tsa', 0, 99999999, 0, -1, 1, 1, '时间戳服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (10, 'userNumbers', '用户数量', '个', 10, 'tsc', 0, 99999999, 0, 99999999, 1, 1, '电子签章服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (11, 'appNumbers', '应用数量', '个', 10, 'tsc', 0, 99999999, 0, 99999999, 1, 1, '电子签章服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (12, 'databaseNumbers', '数据库数量', '个', 8, 'secdb', 0, 99999999, 0, -1, 1, 1, '数据库加密服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (13, 'fileServerNumbers', '文件服务器数量', '个', 9, 'secstorage', 0, 1000, 0, 1000, 1, 1, '文件加密服务', NULL, NULL, NULL,
        NULL);
INSERT INTO "dic_service_quota"
VALUES (14, 'nasServerNumbers', 'Nas服务器数量', '个', 9, 'secstorage', 0, 1000, 0, 1000, 1, 1, '文件加密服务', NULL, NULL, NULL,
        NULL);
INSERT INTO "dic_service_quota"
VALUES (15, 'keyNumbers', '密钥数量', '个', 1, 'pki', 0, 100000, 0, 100000, 1, 1, '加解密服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (17, 'appNumbers', '应用数量', '个', 1, 'pki', 0, 100000, 0, 100000, 2, 1, '加解密服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (18, 'caNumbers', '证书数量', '个', 12, 'ca', 0, 100000, 0, 100000, 1, 1, '数字证书认证服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (20, 'appNumbers', '应用数量', '个', 2, 'svs', 0, 10000, 0, 10000, 2, 1, '签名验签服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (21, 'keyNumbers', '密钥数量', '个', 4, 'kms', 0, 100000, 0, 100000, 1, 1, '密钥管理服务', NULL, NULL, NULL, NULL);
INSERT INTO "dic_service_quota"
VALUES (23, 'appNumbers', '应用数量', '个', 4, 'kms', 0, 10000, 0, 10000, 2, 1, '密钥管理服务', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for disaster_recover_db
-- ----------------------------
DROP TABLE IF EXISTS "disaster_recover_db";
CREATE TABLE "disaster_recover_db"
(
    "id"              int8                                     NOT NULL,
  "disaster_name" varchar(200) COLLATE "pg_catalog"."default",
  "tenant_id" int8 NOT NULL,
  "service_type_id" int8 NOT NULL,
  "service_group_id" int8,
  "source_db_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "source_db_unit_id" int8 NOT NULL,
  "target_db_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "target_db_ip" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "target_db_port" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
  "target_db_user" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "target_db_auth_code" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "target_db_case_name" varchar(200) COLLATE "pg_catalog"."default",
  "target_db_unit_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "target_db_map_ip" varchar(300) COLLATE "pg_catalog"."default",
  "target_db_map_port" varchar(60) COLLATE "pg_catalog"."default",
  "status" int4 NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "disaster_recover_db"."id" IS '主键';
COMMENT ON COLUMN "disaster_recover_db"."disaster_name" IS '灾备任务名称';
COMMENT ON COLUMN "disaster_recover_db"."tenant_id" IS '租户id';
COMMENT ON COLUMN "disaster_recover_db"."service_type_id" IS '服务类型id';
COMMENT ON COLUMN "disaster_recover_db"."service_group_id" IS '服务组id';
COMMENT ON COLUMN "disaster_recover_db"."source_db_type" IS '源数据库类型';
COMMENT ON COLUMN "disaster_recover_db"."source_db_unit_id" IS '源数据库实例/模式id';
COMMENT ON COLUMN "disaster_recover_db"."target_db_type" IS '目标数据库类型';
COMMENT ON COLUMN "disaster_recover_db"."target_db_ip" IS '目标数据库IP(逗号分隔)';
COMMENT ON COLUMN "disaster_recover_db"."target_db_port" IS '目标数据库端口(逗号分隔)';
COMMENT ON COLUMN "disaster_recover_db"."target_db_user" IS '目标数据库用户';
COMMENT ON COLUMN "disaster_recover_db"."target_db_auth_code" IS '目标数据库密码';
COMMENT ON COLUMN "disaster_recover_db"."target_db_case_name" IS '目标数据库实例名称';
COMMENT ON COLUMN "disaster_recover_db"."target_db_unit_name" IS '目标数据库实例/模式名称';
COMMENT ON COLUMN "disaster_recover_db"."target_db_map_ip" IS '目标数据库映射IP';
COMMENT ON COLUMN "disaster_recover_db"."target_db_map_port" IS '目标数据库映射端口';
COMMENT ON COLUMN "disaster_recover_db"."status" IS '任务状态;默认开启，0-未运行；1-RUNNING: 任务正在运行，即正在处理输入数据；2- FINISHED: 任务已成功完成，所有输入数据已处理完毕；3-CANCELED: 任务被取消，可能是由于用户主动取消或系统异常导致；4-FAILED: 任务执行失败，可能是由于程序错误、资源不足或其他异常情况引起；5-CREATED: 任务已创建，但尚未提交执行。';
COMMENT ON COLUMN "disaster_recover_db"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "disaster_recover_db"."remark" IS '备注';
COMMENT ON COLUMN "disaster_recover_db"."create_by" IS '创建人';
COMMENT ON COLUMN "disaster_recover_db"."create_time" IS '创建时间';
COMMENT ON COLUMN "disaster_recover_db"."update_by" IS '更新人';
COMMENT ON COLUMN "disaster_recover_db"."update_time" IS '更新时间';
COMMENT ON TABLE "disaster_recover_db" IS '数据灾备任务主表（1030）';

-- ----------------------------
-- Records of disaster_recover_db
-- ----------------------------

-- ----------------------------
-- Table structure for disaster_recover_db_sub
-- ----------------------------
DROP TABLE IF EXISTS "disaster_recover_db_sub";
CREATE TABLE "disaster_recover_db_sub" (
  "id" int8 NOT NULL,
  "disaster_recover_db_id" int8 NOT NULL,
  "disaster_sub_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "status" int4,
  "data_sync_type" int4 NOT NULL,
  "cron" varchar(255) COLLATE "pg_catalog"."default",
  "last_job_time" varchar(30) COLLATE "pg_catalog"."default",
  "fail_retry_count" int4,
  "source_db_table_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "target_db_table_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "source_db_column" text COLLATE "pg_catalog"."default",
  "target_db_column" text COLLATE "pg_catalog"."default",
  "transformer_sql" text COLLATE "pg_catalog"."default",
  "db_json" text COLLATE "pg_catalog"."default",
  "job_id" varchar(255) COLLATE "pg_catalog"."default",
  "where_sql" text COLLATE "pg_catalog"."default",
  "pre_sql" text COLLATE "pg_catalog"."default",
  "job_extend1" varchar(500) COLLATE "pg_catalog"."default",
  "job_extend2" varchar(500) COLLATE "pg_catalog"."default",
  "job_extend3" varchar(500) COLLATE "pg_catalog"."default",
  "jvm_param" varchar(200) COLLATE "pg_catalog"."default",
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "disaster_recover_db_sub"."id" IS '主键';
COMMENT ON COLUMN "disaster_recover_db_sub"."disaster_recover_db_id" IS '数据灾备任务主表id';
COMMENT ON COLUMN "disaster_recover_db_sub"."disaster_sub_name" IS '数据灾备子任务名称';
COMMENT ON COLUMN "disaster_recover_db_sub"."status" IS '任务状态;默认开启，0-未运行；1-RUNNING: 任务正在运行，即正在处理输入数据；2- FINISHED: 任务已成功完成，所有输入数据已处理完毕；3-CANCELED: 任务被取消，可能是由于用户主动取消或系统异常导致；4-FAILED: 任务执行失败，可能是由于程序错误、资源不足或其他异常情况引起；5-CREATED: 任务已创建，但尚未提交执行。';
COMMENT ON COLUMN "disaster_recover_db_sub"."data_sync_type" IS '全量1、增量2';
COMMENT ON COLUMN "disaster_recover_db_sub"."cron" IS '任务周期';
COMMENT ON COLUMN "disaster_recover_db_sub"."last_job_time" IS '上一次任务时间';
COMMENT ON COLUMN "disaster_recover_db_sub"."fail_retry_count" IS '失败次数';
COMMENT ON COLUMN "disaster_recover_db_sub"."source_db_table_name" IS '源数据库表名称';
COMMENT ON COLUMN "disaster_recover_db_sub"."target_db_table_name" IS '目标数据库表名称';
COMMENT ON COLUMN "disaster_recover_db_sub"."source_db_column" IS '源数据表字段 默认全部字段';
COMMENT ON COLUMN "disaster_recover_db_sub"."target_db_column" IS '目标数据表字段 默认全部字段';
COMMENT ON COLUMN "disaster_recover_db_sub"."transformer_sql" IS '转换sql';
COMMENT ON COLUMN "disaster_recover_db_sub"."db_json" IS '数据同步json';
COMMENT ON COLUMN "disaster_recover_db_sub"."job_id" IS '任务id';
COMMENT ON COLUMN "disaster_recover_db_sub"."where_sql" IS '源数据查询条件';
COMMENT ON COLUMN "disaster_recover_db_sub"."pre_sql" IS '目标数据前置执行sql';
COMMENT ON COLUMN "disaster_recover_db_sub"."job_extend1" IS '任务扩展字段1';
COMMENT ON COLUMN "disaster_recover_db_sub"."job_extend2" IS '任务扩展字段2';
COMMENT ON COLUMN "disaster_recover_db_sub"."job_extend3" IS '任务扩展字段3';
COMMENT ON COLUMN "disaster_recover_db_sub"."jvm_param" IS 'jvm参数  -Xms256m -Xmx256m';
COMMENT ON COLUMN "disaster_recover_db_sub"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "disaster_recover_db_sub"."remark" IS '备注';
COMMENT ON COLUMN "disaster_recover_db_sub"."create_by" IS '创建人';
COMMENT ON COLUMN "disaster_recover_db_sub"."create_time" IS '创建时间';
COMMENT ON COLUMN "disaster_recover_db_sub"."update_by" IS '更新人';
COMMENT ON COLUMN "disaster_recover_db_sub"."update_time" IS '更新时间';
COMMENT ON TABLE "disaster_recover_db_sub" IS '数据灾备子任务表（1030）';

-- ----------------------------
-- Records of disaster_recover_db_sub
-- ----------------------------

-- ----------------------------
-- Table structure for disaster_recover_db_sub_tem
-- ----------------------------
DROP TABLE IF EXISTS "disaster_recover_db_sub_tem";
CREATE TABLE "disaster_recover_db_sub_tem" (
  "id" int8 NOT NULL,
  "service_type_id" int8 NOT NULL,
  "data_sync_type" int4 NOT NULL,
  "cron" varchar(255) COLLATE "pg_catalog"."default",
  "fail_retry_count" int4,
  "source_db_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "target_db_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "source_db_table_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "target_db_table_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "source_db_column" text COLLATE "pg_catalog"."default",
  "target_db_column" text COLLATE "pg_catalog"."default",
  "transformer_sql" text COLLATE "pg_catalog"."default",
  "where_sql" text COLLATE "pg_catalog"."default",
  "pre_sql" text COLLATE "pg_catalog"."default",
  "job_extend1" varchar(500) COLLATE "pg_catalog"."default",
  "job_extend2" varchar(500) COLLATE "pg_catalog"."default",
  "job_extend3" varchar(500) COLLATE "pg_catalog"."default",
  "jvm_param" varchar(200) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."id" IS '主键';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."service_type_id" IS '服务类型id';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."data_sync_type" IS '全量1、增量2';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."cron" IS '任务周期';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."fail_retry_count" IS '失败次数';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."source_db_type" IS '源数据库类型';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."target_db_type" IS '目标数据库类型';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."source_db_table_name" IS '源数据库表名称';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."target_db_table_name" IS '目标数据库表名称';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."source_db_column" IS '源数据表字段 默认全部字段';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."target_db_column" IS '目标数据表字段 默认全部字段';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."transformer_sql" IS '转换sql';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."where_sql" IS '源数据查询条件';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."pre_sql" IS '目标数据前置执行sql';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."job_extend1" IS '任务扩展字段1';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."job_extend2" IS '任务扩展字段2';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."job_extend3" IS '任务扩展字段3';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."jvm_param" IS 'jvm参数  -Xms256m -Xmx256m';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."remark" IS '备注';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."create_by" IS '创建人';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."create_time" IS '创建时间';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."update_by" IS '更新人';
COMMENT ON COLUMN "disaster_recover_db_sub_tem"."update_time" IS '更新时间';
COMMENT ON TABLE "disaster_recover_db_sub_tem" IS '数据灾备子任务模板（1030）';

-- ----------------------------
-- Records of disaster_recover_db_sub_tem
-- ----------------------------

-- ----------------------------
-- Table structure for doc_download_path
-- ----------------------------
DROP TABLE IF EXISTS "doc_download_path";
CREATE TABLE "doc_download_path" (
  "id" int8 NOT NULL,
  "service_type_id" int8 NOT NULL,
  "context_path" varchar(500) COLLATE "pg_catalog"."default",
  "sdk_path" varchar(255) COLLATE "pg_catalog"."default",
  "api_path" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(300) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "doc_download_path"."id" IS '主键';
COMMENT ON COLUMN "doc_download_path"."service_type_id" IS '服务类型id';
COMMENT ON COLUMN "doc_download_path"."context_path" IS '服务类型路径(sdk和api路径中公共的地方)';
COMMENT ON COLUMN "doc_download_path"."sdk_path" IS 'sdk下载路径';
COMMENT ON COLUMN "doc_download_path"."api_path" IS 'api下载路径';
COMMENT
ON COLUMN "doc_download_path"."remark" IS '备注';
COMMENT
ON COLUMN "doc_download_path"."create_by" IS '创建人';
COMMENT
ON COLUMN "doc_download_path"."create_time" IS '创建时间';
COMMENT
ON COLUMN "doc_download_path"."update_by" IS '更新人';
COMMENT
ON COLUMN "doc_download_path"."update_time" IS '更新时间';
COMMENT
ON TABLE "doc_download_path" IS '服务sdk api文档路径表';

-- ----------------------------
-- Records of doc_download_path
-- ----------------------------
INSERT INTO "doc_download_path"
VALUES (1, 1, '/pki', NULL, '/restful_api.zip', '加解密服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (2, 2, '/svs', NULL, '/restful_api.zip', '签名验签服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (3, 3, '/digist', NULL, NULL, '杂凑服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (4, 4, '/kms', '/sdk.zip', '/restful_api.zip', '密钥管理服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (5, 5, '/tsa', NULL, '/restful_api.docx', '时间戳服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (6, 6, '/sms', '/sdk.zip', '/restful_api.zip', '协同签名服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (7, 7, '/otp', '/sdk.zip', '/restful_api.zip', '动态令牌服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (8, 8, '/secdb', NULL, NULL, '数据库加密服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (9, 9, '/secstorage', '/sdk.zip', NULL, '文件加密服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (10, 10, '/tsc', NULL, '/restful_api.docx', '电子签章服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (11, 11, '/vpn', '/sdk.zip', '/api.zip', 'SSL VPN加密通道服务', NULL, NULL, NULL, NULL);
INSERT INTO "doc_download_path"
VALUES (12, 12, '/ca', NULL, '/api.zip', '数字证书认证服务', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for license_apply
-- ----------------------------
DROP TABLE IF EXISTS "license_apply";
CREATE TABLE "license_apply"
(
    "id"          int8                                        NOT NULL,
    "key_id"      varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
    "pri_key"     text COLLATE "pg_catalog"."default"         NOT NULL,
    "period_type" int4                                        NOT NULL,
  "period_num" int4 NOT NULL,
  "server_type" int4 NOT NULL,
  "server_num" int4 NOT NULL,
  "status" int4 NOT NULL,
  "license" text COLLATE "pg_catalog"."default" NOT NULL,
  "hmac" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "license_apply"."id" IS '主键;许可证标识';
COMMENT ON COLUMN "license_apply"."key_id" IS '密钥ID';
COMMENT ON COLUMN "license_apply"."pri_key" IS '私钥;平台临时私钥';
COMMENT ON COLUMN "license_apply"."period_type" IS '周期类型;1-永久授权, 2-按年授权';
COMMENT ON COLUMN "license_apply"."period_num" IS '服务周期';
COMMENT ON COLUMN "license_apply"."server_type" IS '许可证类型;1-业务服务，2-平台服务';
COMMENT ON COLUMN "license_apply"."server_num" IS '服务数量';
COMMENT ON COLUMN "license_apply"."status" IS '状态;1-申请中 2-已使用 3-作废';
COMMENT ON COLUMN "license_apply"."license" IS '许可证内容;加密数据';
COMMENT ON COLUMN "license_apply"."hmac" IS '完整性校验值';
COMMENT ON COLUMN "license_apply"."remark" IS '备注';
COMMENT ON COLUMN "license_apply"."create_by" IS '创建人';
COMMENT ON COLUMN "license_apply"."create_time" IS '创建时间';
COMMENT ON TABLE "license_apply" IS '许可证申请表';

-- ----------------------------
-- Records of license_apply
-- ----------------------------

-- ----------------------------
-- Table structure for license_info
-- ----------------------------
DROP TABLE IF EXISTS "license_info";
CREATE TABLE "license_info" (
  "id" int8 NOT NULL,
  "server_type" int4 NOT NULL,
  "server_num" int4 NOT NULL,
  "period_type" int4 NOT NULL,
  "period_num" int4 NOT NULL,
  "license" text COLLATE "pg_catalog"."default" NOT NULL,
  "content" text COLLATE "pg_catalog"."default",
  "hmac" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "license_info"."id" IS '主键;许可证标识';
COMMENT ON COLUMN "license_info"."server_type" IS '服务类型;1-业务服务，2-平台服务';
COMMENT ON COLUMN "license_info"."server_num" IS '服务数量';
COMMENT ON COLUMN "license_info"."period_type" IS '周期类型;1-永久授权, 2-按年授权';
COMMENT ON COLUMN "license_info"."period_num" IS '服务周期';
COMMENT ON COLUMN "license_info"."license" IS '许可证内容';
COMMENT ON COLUMN "license_info"."content" IS '内容;解析的LICENSE原始数据';
COMMENT ON COLUMN "license_info"."hmac" IS '完整性校验值';
COMMENT ON COLUMN "license_info"."remark" IS '备注';
COMMENT ON COLUMN "license_info"."create_by" IS '创建人';
COMMENT ON COLUMN "license_info"."create_time" IS '创建时间';
COMMENT ON TABLE "license_info" IS '许可证表';

-- ----------------------------
-- Records of license_info
-- ----------------------------

-- ----------------------------
-- Table structure for license_use
-- ----------------------------
DROP TABLE IF EXISTS "license_use";
CREATE TABLE "license_use" (
  "id" int8 NOT NULL,
  "license_id" int8 NOT NULL,
  "name" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "server_type" int4 NOT NULL,
  "period_type" int4 NOT NULL,
  "period_num" int4 NOT NULL,
  "start_time" varchar(30) COLLATE "pg_catalog"."default",
  "end_time" varchar(30) COLLATE "pg_catalog"."default",
  "status" int4 NOT NULL,
  "hmac" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "license_use"."id" IS '主键';
COMMENT ON COLUMN "license_use"."license_id" IS '许可证ID';
COMMENT ON COLUMN "license_use"."name" IS '许可证名称;L+yyyyMMddHHmmss+num';
COMMENT ON COLUMN "license_use"."server_type" IS '服务类型;1-业务服务，2-平台服务';
COMMENT ON COLUMN "license_use"."period_type" IS '周期类型;1-永久授权, 2-按年授权';
COMMENT ON COLUMN "license_use"."period_num" IS '服务周期';
COMMENT ON COLUMN "license_use"."start_time" IS '开始时间;开始时间;YYYY-MM-DD';
COMMENT ON COLUMN "license_use"."end_time" IS '结束时间;结束时间;YYYY-MM-DD，永久为空';
COMMENT ON COLUMN "license_use"."status" IS '状态;1-未使用，2-已使用，3-作废';
COMMENT ON COLUMN "license_use"."hmac" IS '完整性校验值';
COMMENT ON COLUMN "license_use"."remark" IS '备注';
COMMENT ON COLUMN "license_use"."create_by" IS '创建人';
COMMENT ON COLUMN "license_use"."create_time" IS '创建时间';
COMMENT ON COLUMN "license_use"."update_by" IS '更新人';
COMMENT ON COLUMN "license_use"."update_time" IS '更新时间';
COMMENT ON TABLE "license_use" IS '许可证使用表';

-- ----------------------------
-- Records of license_use
-- ----------------------------

-- ----------------------------
-- Table structure for license_use_rel
-- ----------------------------
DROP TABLE IF EXISTS "license_use_rel";
CREATE TABLE "license_use_rel" (
  "id" int8 NOT NULL,
  "license_id" int8 NOT NULL,
  "server_id" int8 NOT NULL,
  "hmac" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "license_use_rel"."id" IS '主键';
COMMENT ON COLUMN "license_use_rel"."license_id" IS '许可证使用ID;已使用状态的LICENSE的ID';
COMMENT ON COLUMN "license_use_rel"."server_id" IS '服务实例ID';
COMMENT ON COLUMN "license_use_rel"."hmac" IS '完整性校验值';
COMMENT ON COLUMN "license_use_rel"."create_time" IS '创建时间';
COMMENT ON TABLE "license_use_rel" IS '许可证服务使用关联表';

-- ----------------------------
-- Records of license_use_rel
-- ----------------------------

-- ----------------------------
-- Table structure for license_use_time
-- ----------------------------
DROP TABLE IF EXISTS "license_use_time";
CREATE TABLE "license_use_time" (
  "id" int8 NOT NULL,
  "start_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "end_time" varchar(30) COLLATE "pg_catalog"."default",
  "license_num" int4 NOT NULL,
  "hmac" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "license_use_time"."id" IS '主键服务实例ID';
COMMENT ON COLUMN "license_use_time"."start_time" IS '开始时间;开始时间;YYYY-MM-DD';
COMMENT ON COLUMN "license_use_time"."end_time" IS '结束时间;结束时间;YYYY-MM-DD，永久为空';
COMMENT ON COLUMN "license_use_time"."license_num" IS '使用license个数';
COMMENT ON COLUMN "license_use_time"."hmac" IS '完整性校验值';
COMMENT ON TABLE "license_use_time" IS '服务license时间';

-- ----------------------------
-- Records of license_use_time
-- ----------------------------

-- ----------------------------
-- Table structure for region
-- ----------------------------
DROP TABLE IF EXISTS "region";
CREATE TABLE "region" (
  "region_id" int8 NOT NULL,
  "region_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "region_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "region_type" int4,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "region"."region_id" IS '区域ID';
COMMENT ON COLUMN "region"."region_code" IS '区域标识';
COMMENT ON COLUMN "region"."region_name" IS '区域名称';
COMMENT ON COLUMN "region"."region_type" IS '区域类型;1：普通区域；2：平台区域';
COMMENT ON COLUMN "region"."remark" IS '备注';
COMMENT ON COLUMN "region"."create_by" IS '创建人';
COMMENT ON COLUMN "region"."create_time" IS '创建时间';
COMMENT ON COLUMN "region"."update_by" IS '更新人';
COMMENT ON COLUMN "region"."update_time" IS '更新时间';
COMMENT ON TABLE "region" IS '区域表(1030)';

-- ----------------------------
-- Records of region
-- ----------------------------

-- ----------------------------
-- Table structure for route_server_port
-- ----------------------------
DROP TABLE IF EXISTS "route_server_port";
CREATE TABLE "route_server_port" (
  "id" int8 NOT NULL,
  "server_port" int4 NOT NULL,
  "apisix_server_port" int4 NOT NULL,
  "region_id" int8,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "route_server_port"."id" IS '主键';
COMMENT ON COLUMN "route_server_port"."server_port" IS 'NGINX TCP服务端口';
COMMENT ON COLUMN "route_server_port"."apisix_server_port" IS 'APISIX TCP服务端口';
COMMENT ON COLUMN "route_server_port"."region_id" IS '区域ID';
COMMENT ON COLUMN "route_server_port"."remark" IS '备注';
COMMENT ON COLUMN "route_server_port"."create_by" IS '创建人';
COMMENT ON COLUMN "route_server_port"."create_time" IS '创建时间';
COMMENT ON COLUMN "route_server_port"."update_by" IS '更新人';
COMMENT ON COLUMN "route_server_port"."update_time" IS '更新时间';
COMMENT ON TABLE "route_server_port" IS '路由TCP服务端口资源表';

-- ----------------------------
-- Records of route_server_port
-- ----------------------------

-- ----------------------------
-- Table structure for route_to_app
-- ----------------------------
DROP TABLE IF EXISTS "route_to_app";
CREATE TABLE "route_to_app" (
  "id" int8 NOT NULL,
  "route_id" int8 NOT NULL,
  "app_id" int8,
  "app_code" varchar(90) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "route_to_app"."id" IS '主键';
COMMENT ON COLUMN "route_to_app"."route_id" IS '路由表主键';
COMMENT ON COLUMN "route_to_app"."app_id" IS '应用主键';
COMMENT ON COLUMN "route_to_app"."app_code" IS '应用标识';
COMMENT ON COLUMN "route_to_app"."remark" IS '备注';
COMMENT ON COLUMN "route_to_app"."create_by" IS '创建人';
COMMENT ON COLUMN "route_to_app"."create_time" IS '创建时间';
COMMENT ON COLUMN "route_to_app"."update_by" IS '更新人';
COMMENT ON COLUMN "route_to_app"."update_time" IS '更新时间';
COMMENT ON TABLE "route_to_app" IS '路由与应用关联表';

-- ----------------------------
-- Records of route_to_app
-- ----------------------------

-- ----------------------------
-- Table structure for route_to_service
-- ----------------------------
DROP TABLE IF EXISTS "route_to_service";
CREATE TABLE "route_to_service" (
  "id" int8 NOT NULL,
  "route_id" int8 NOT NULL,
  "service_id" int8,
  "service_type" int4,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "route_to_service"."id" IS '主键';
COMMENT ON COLUMN "route_to_service"."route_id" IS '路由表主键';
COMMENT ON COLUMN "route_to_service"."service_id" IS '服务主键/设备主键';
COMMENT ON COLUMN "route_to_service"."service_type" IS '1服务 2设备';
COMMENT ON COLUMN "route_to_service"."remark" IS '备注';
COMMENT ON COLUMN "route_to_service"."create_by" IS '创建人';
COMMENT ON COLUMN "route_to_service"."create_time" IS '创建时间';
COMMENT ON COLUMN "route_to_service"."update_by" IS '更新人';
COMMENT ON COLUMN "route_to_service"."update_time" IS '更新时间';
COMMENT ON TABLE "route_to_service" IS '路由与服务或设备关联表';

-- ----------------------------
-- Records of route_to_service
-- ----------------------------

-- ----------------------------
-- Table structure for service_auto_bind_device
-- ----------------------------
DROP TABLE IF EXISTS "service_auto_bind_device";
CREATE TABLE "service_auto_bind_device" (
  "id" int8 NOT NULL,
  "service_id" int8,
  "device_id" int8,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "service_auto_bind_device"."create_by" IS '创建人';
COMMENT ON COLUMN "service_auto_bind_device"."create_time" IS '创建时间';
COMMENT ON TABLE "service_auto_bind_device" IS '服务自动绑定设备关联关系';

-- ----------------------------
-- Records of service_auto_bind_device
-- ----------------------------

-- ----------------------------
-- Table structure for service_gateway
-- ----------------------------
DROP TABLE IF EXISTS "service_gateway";
CREATE TABLE "service_gateway" (
  "id" int8 NOT NULL,
  "region_id" int8,
  "gateway_code" varchar(100) COLLATE "pg_catalog"."default",
  "gateway_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "ip" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "port" int4 NOT NULL,
  "api_port" int4 NOT NULL DEFAULT 0,
  "gateway_type" int4,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "service_gateway"."id" IS '主键';
COMMENT ON COLUMN "service_gateway"."region_id" IS '区域ID';
COMMENT ON COLUMN "service_gateway"."gateway_code" IS '网关组件标识(apisix中的id)';
COMMENT ON COLUMN "service_gateway"."gateway_name" IS '网关名称';
COMMENT ON COLUMN "service_gateway"."ip" IS '网关IP';
COMMENT ON COLUMN "service_gateway"."port" IS '网关端口';
COMMENT ON COLUMN "service_gateway"."api_port" IS 'API端口';
COMMENT ON COLUMN "service_gateway"."gateway_type" IS '网关类型，1：管理，2：业务';
COMMENT ON COLUMN "service_gateway"."remark" IS '备注';
COMMENT ON COLUMN "service_gateway"."create_by" IS '创建人';
COMMENT ON COLUMN "service_gateway"."create_time" IS '创建时间';
COMMENT ON COLUMN "service_gateway"."update_by" IS '更新人';
COMMENT ON COLUMN "service_gateway"."update_time" IS '更新时间';
COMMENT ON TABLE "service_gateway" IS 'API网关表';

-- ----------------------------
-- Records of service_gateway
-- ----------------------------

-- ----------------------------
-- Table structure for service_gateway_route
-- ----------------------------
DROP TABLE IF EXISTS "service_gateway_route";
CREATE TABLE "service_gateway_route" (
  "id" int8 NOT NULL,
  "route_code" varchar(100) COLLATE "pg_catalog"."default",
  "route_name" varchar(600) COLLATE "pg_catalog"."default" NOT NULL,
  "route_type" int4,
  "tenant_id" int8,
  "region_id" int8,
  "busi_type_id" int8,
  "group_id" int8,
  "group_type" int4,
  "gateway_id" int8,
  "uris" varchar(500) COLLATE "pg_catalog"."default",
  "methods" varchar(100) COLLATE "pg_catalog"."default",
  "hosts" varchar(500) COLLATE "pg_catalog"."default",
  "vars" varchar(1000) COLLATE "pg_catalog"."default",
  "upstream_code" varchar(100) COLLATE "pg_catalog"."default",
  "upstream" varchar(1000) COLLATE "pg_catalog"."default",
  "remote_addrs" varchar(500) COLLATE "pg_catalog"."default",
  "timeout" varchar(500) COLLATE "pg_catalog"."default",
  "plugins" varchar(500) COLLATE "pg_catalog"."default",
  "server_port" int4,
  "apisix_server_port" int4,
  "filter_func" varchar(1000) COLLATE "pg_catalog"."default",
  "service_code" varchar(100) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "service_gateway_route"."id" IS '主键';
COMMENT ON COLUMN "service_gateway_route"."route_code" IS '路由组件标识(apisix中的id)';
COMMENT ON COLUMN "service_gateway_route"."route_name" IS '路由名称';
COMMENT ON COLUMN "service_gateway_route"."route_type" IS '路由类型 1管理 2业务 3业务二 4TCP 5UDP';
COMMENT ON COLUMN "service_gateway_route"."tenant_id" IS '租户id';
COMMENT ON COLUMN "service_gateway_route"."region_id" IS '区域ID';
COMMENT ON COLUMN "service_gateway_route"."busi_type_id" IS '业务/服务类型';
COMMENT ON COLUMN "service_gateway_route"."group_id" IS '服务/设备组';
COMMENT ON COLUMN "service_gateway_route"."group_type" IS '组类型 1服务组 2设备组';
COMMENT ON COLUMN "service_gateway_route"."gateway_id" IS '网关表主键';
COMMENT ON COLUMN "service_gateway_route"."uris" IS '一组URL路径';
COMMENT ON COLUMN "service_gateway_route"."methods" IS '一组请求限制';
COMMENT ON COLUMN "service_gateway_route"."hosts" IS '一组host域名';
COMMENT ON COLUMN "service_gateway_route"."vars" IS '一组元素列表';
COMMENT ON COLUMN "service_gateway_route"."upstream_code" IS 'upstream组件标识(apisix中的id)';
COMMENT ON COLUMN "service_gateway_route"."upstream" IS 'upstream';
COMMENT ON COLUMN "service_gateway_route"."remote_addrs" IS '一组客户端请求 IP 地址';
COMMENT ON COLUMN "service_gateway_route"."timeout" IS '为route 设置 upstream 的连接、发送消息、接收消息的超时时间';
COMMENT ON COLUMN "service_gateway_route"."plugins" IS 'route 绑定插件';
COMMENT ON COLUMN "service_gateway_route"."server_port" IS 'NGINX TCP服务端口';
COMMENT ON COLUMN "service_gateway_route"."apisix_server_port" IS 'APISIX TCP服务端口';
COMMENT ON COLUMN "service_gateway_route"."filter_func" IS '用户自定义的过滤函数';
COMMENT ON COLUMN "service_gateway_route"."service_code" IS '网关中服务组件标识(apisix中的id)';
COMMENT ON COLUMN "service_gateway_route"."remark" IS '备注';
COMMENT ON COLUMN "service_gateway_route"."create_by" IS '创建人';
COMMENT ON COLUMN "service_gateway_route"."create_time" IS '创建时间';
COMMENT ON COLUMN "service_gateway_route"."update_by" IS '更新人';
COMMENT ON COLUMN "service_gateway_route"."update_time" IS '更新时间';
COMMENT ON TABLE "service_gateway_route" IS 'API网关路由表';

-- ----------------------------
-- Records of service_gateway_route
-- ----------------------------

-- ----------------------------
-- Table structure for service_interface_api_record
-- ----------------------------
DROP TABLE IF EXISTS "service_interface_api_record";
CREATE TABLE "service_interface_api_record" (
  "id" int8 NOT NULL,
  "service_id" int8,
  "api_name" varchar(150) COLLATE "pg_catalog"."default",
  "ip" varchar(20) COLLATE "pg_catalog"."default",
  "port" int4,
  "headers" text COLLATE "pg_catalog"."default",
  "request_info" text COLLATE "pg_catalog"."default",
  "response_info" text COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "service_interface_api_record"."id" IS '主键 uuid';
COMMENT ON COLUMN "service_interface_api_record"."service_id" IS '服务id';
COMMENT ON COLUMN "service_interface_api_record"."api_name" IS '接口名称';
COMMENT ON COLUMN "service_interface_api_record"."ip" IS '请求ip';
COMMENT ON COLUMN "service_interface_api_record"."port" IS '请求端口';
COMMENT ON COLUMN "service_interface_api_record"."headers" IS '请求头';
COMMENT ON COLUMN "service_interface_api_record"."request_info" IS '请求体';
COMMENT ON COLUMN "service_interface_api_record"."response_info" IS '响应体';
COMMENT ON COLUMN "service_interface_api_record"."create_time" IS '创建时间';
COMMENT ON TABLE "service_interface_api_record" IS '服务接口日志表';

-- ----------------------------
-- Records of service_interface_api_record
-- ----------------------------

-- ----------------------------
-- Table structure for service_to_device_group
-- ----------------------------
DROP TABLE IF EXISTS "service_to_device_group";
CREATE TABLE "service_to_device_group" (
  "id" int8 NOT NULL,
  "service_id" int8 NOT NULL,
  "device_group_id" int8 NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "service_to_device_group"."id" IS 'ID';
COMMENT ON COLUMN "service_to_device_group"."service_id" IS '服务ID';
COMMENT ON COLUMN "service_to_device_group"."device_group_id" IS '设备组ID';
COMMENT ON COLUMN "service_to_device_group"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "service_to_device_group"."remark" IS '备注';
COMMENT ON COLUMN "service_to_device_group"."create_by" IS '创建人';
COMMENT ON COLUMN "service_to_device_group"."create_time" IS '创建时间';
COMMENT ON COLUMN "service_to_device_group"."update_by" IS '更新人';
COMMENT ON COLUMN "service_to_device_group"."update_time" IS '更新时间';
COMMENT ON TABLE "service_to_device_group" IS '服务和设备组关联表';

-- ----------------------------
-- Records of service_to_device_group
-- ----------------------------

-- ----------------------------
-- Table structure for service_type_to_device_group
-- ----------------------------
DROP TABLE IF EXISTS "service_type_to_device_group";
CREATE TABLE "service_type_to_device_group" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "service_type_id" int8 NOT NULL,
  "device_group_id" int8,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "service_type_to_device_group"."id" IS '主键';
COMMENT ON COLUMN "service_type_to_device_group"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "service_type_to_device_group"."service_type_id" IS '服务类型ID';
COMMENT ON COLUMN "service_type_to_device_group"."device_group_id" IS '设备组ID';
COMMENT ON COLUMN "service_type_to_device_group"."remark" IS '备注';
COMMENT ON COLUMN "service_type_to_device_group"."create_by" IS '创建人';
COMMENT ON COLUMN "service_type_to_device_group"."create_time" IS '创建时间';
COMMENT ON COLUMN "service_type_to_device_group"."update_by" IS '更新人';
COMMENT ON COLUMN "service_type_to_device_group"."update_time" IS '更新时间';
COMMENT ON TABLE "service_type_to_device_group" IS '服务类型绑定设备组关联表';

-- ----------------------------
-- Records of service_type_to_device_group
-- ----------------------------

-- ----------------------------
-- Table structure for service_version
-- ----------------------------
DROP TABLE IF EXISTS "service_version";
CREATE TABLE "service_version" (
  "id" int8 NOT NULL,
  "service_type_id" int8 NOT NULL,
  "service_group_id" int8,
  "service_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "service_model" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "service_version" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "service_version"."id" IS '主键';
COMMENT ON COLUMN "service_version"."service_type_id" IS '服务类型ID';
COMMENT ON COLUMN "service_version"."service_group_id" IS '服务组ID';
COMMENT ON COLUMN "service_version"."service_name" IS '产品名称';
COMMENT ON COLUMN "service_version"."service_model" IS '产品型号';
COMMENT ON COLUMN "service_version"."service_version" IS '产品版本';
COMMENT
ON COLUMN "service_version"."remark" IS '备注';
COMMENT
ON COLUMN "service_version"."create_by" IS '创建人';
COMMENT
ON COLUMN "service_version"."create_time" IS '创建时间';
COMMENT
ON COLUMN "service_version"."update_by" IS '更新人';
COMMENT
ON COLUMN "service_version"."update_time" IS '更新时间';
COMMENT
ON TABLE "service_version" IS '业务服务版本信息';

-- ----------------------------
-- Records of service_version
-- ----------------------------
INSERT INTO "service_version"
VALUES (1, 1, NULL, '服务器密码机', 'SJJ1012-A V6', 'V1.0.2', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "service_version"
VALUES (2, 2, NULL, '签名验签服务器', 'SRJ1909 V6', 'V1.0.2', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "service_version"
VALUES (4, 4, NULL, '云服务端密钥管理系统', 'SYT1931', 'V4.0.0-XC-CCSP', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "service_version"
VALUES (5, 5, NULL, '时间戳服务器', 'SFJ1803', 'V4.0.2.2', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "service_version"
VALUES (6, 6, NULL, '协同签名系统服务端密码模块', 'SRT1922-G', 'V4.6.4', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "service_version"
VALUES (7, 7, NULL, '动态令牌认证系统', 'SecAuth V2', 'V3.2.1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "service_version"
VALUES (8, 8, NULL, '数据库加密机', 'SJJ19127-G V2', 'V3.4.1.1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "service_version"
VALUES (9, 9, NULL, '文件加密服务（密码模块）', 'SecStorage V4', 'V4.0.2-XC-CCSP', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "service_version"
VALUES (10, 10, NULL, '电子签章系统', 'SFT1924-G', 'V1.1.0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "service_version"
VALUES (11, 11, NULL, 'SSL VPN 综合安全网关', 'SecGW G1300', 'V2.2.11', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "service_version"
VALUES (12, 12, NULL, '数字证书认证服务', 'SZT1701', 'V5.0.1.1', NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sim_flow_data
-- ----------------------------
DROP TABLE IF EXISTS "sim_flow_data";
CREATE TABLE "sim_flow_data"
(
    "id"          int8 NOT NULL,
    "flow_type"   int4,
    "phone"       varchar(50) COLLATE "pg_catalog"."default",
    "oper_type"   varchar(100) COLLATE "pg_catalog"."default",
  "func_name" varchar(200) COLLATE "pg_catalog"."default",
  "func_type" varchar(30) COLLATE "pg_catalog"."default",
  "request_data" text COLLATE "pg_catalog"."default",
  "response_data" text COLLATE "pg_catalog"."default",
  "status" int4,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "tenant_id" int8,
  "log_hmac" varchar(255) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "sim_flow_data"."id" IS 'id';
COMMENT ON COLUMN "sim_flow_data"."flow_type" IS '日志类型;1sim盾  2simkey';
COMMENT ON COLUMN "sim_flow_data"."phone" IS '手机号';
COMMENT ON COLUMN "sim_flow_data"."oper_type" IS '操作类型(验签、证书等)';
COMMENT ON COLUMN "sim_flow_data"."func_name" IS '接口名称';
COMMENT ON COLUMN "sim_flow_data"."func_type" IS '1密码平台接口  2SIM盾接口 3业务系统接口';
COMMENT ON COLUMN "sim_flow_data"."request_data" IS '请求参数';
COMMENT ON COLUMN "sim_flow_data"."response_data" IS '返回结果';
COMMENT ON COLUMN "sim_flow_data"."status" IS '操作结果 0成功 其他失败';
COMMENT ON COLUMN "sim_flow_data"."remark" IS '备注';
COMMENT ON COLUMN "sim_flow_data"."tenant_id" IS '租户id';
COMMENT ON COLUMN "sim_flow_data"."log_hmac" IS 'hmac';
COMMENT ON COLUMN "sim_flow_data"."create_by" IS '创建人';
COMMENT ON COLUMN "sim_flow_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "sim_flow_data"."update_by" IS '更新人';
COMMENT ON COLUMN "sim_flow_data"."update_time" IS '更新时间';
COMMENT ON TABLE "sim_flow_data" IS 'SIM操作日志';

-- ----------------------------
-- Records of sim_flow_data
-- ----------------------------

-- ----------------------------
-- Table structure for sim_key_cert
-- ----------------------------
DROP TABLE IF EXISTS "sim_key_cert";
CREATE TABLE "sim_key_cert" (
  "id" int8 NOT NULL,
  "phone" varchar(40) COLLATE "pg_catalog"."default",
  "alias" varchar(128) COLLATE "pg_catalog"."default",
  "cert_type" varchar(255) COLLATE "pg_catalog"."default",
  "sign_algorithm" varchar(255) COLLATE "pg_catalog"."default",
  "start_time" varchar(255) COLLATE "pg_catalog"."default",
  "expire_time" varchar(255) COLLATE "pg_catalog"."default",
  "status" int4,
  "sim_key_user_id" int8,
  "public_key" varchar(255) COLLATE "pg_catalog"."default",
  "sign_cert" varchar(600) COLLATE "pg_catalog"."default",
  "tran_cert" varchar(600) COLLATE "pg_catalog"."default",
  "enc_envelop" varchar(1500) COLLATE "pg_catalog"."default",
  "sign_sn" varchar(255) COLLATE "pg_catalog"."default",
  "tran_sn" varchar(255) COLLATE "pg_catalog"."default",
  "tenant_id" int8,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "sim_key_cert"."id" IS 'id';
COMMENT ON COLUMN "sim_key_cert"."phone" IS '手机号';
COMMENT ON COLUMN "sim_key_cert"."alias" IS '证书别名';
COMMENT ON COLUMN "sim_key_cert"."cert_type" IS '证书类型;备用  RSA2048/SM2等';
COMMENT ON COLUMN "sim_key_cert"."sign_algorithm" IS '签名算法';
COMMENT ON COLUMN "sim_key_cert"."start_time" IS '证书开始时间';
COMMENT ON COLUMN "sim_key_cert"."expire_time" IS '过期时间';
COMMENT ON COLUMN "sim_key_cert"."status" IS '证书状态;1启用 2禁用';
COMMENT ON COLUMN "sim_key_cert"."sim_key_user_id" IS 'SIMKEY用户ID';
COMMENT ON COLUMN "sim_key_cert"."public_key" IS '签名证书公钥';
COMMENT ON COLUMN "sim_key_cert"."sign_cert" IS '签名证书';
COMMENT ON COLUMN "sim_key_cert"."tran_cert" IS '加密证书';
COMMENT ON COLUMN "sim_key_cert"."enc_envelop" IS '加密私钥数字信封';
COMMENT ON COLUMN "sim_key_cert"."sign_sn" IS '签名证书序列号';
COMMENT ON COLUMN "sim_key_cert"."tran_sn" IS '加密证书序列号';
COMMENT ON COLUMN "sim_key_cert"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "sim_key_cert"."create_by" IS '创建人';
COMMENT ON COLUMN "sim_key_cert"."create_time" IS '创建时间';
COMMENT ON COLUMN "sim_key_cert"."update_by" IS '更新人';
COMMENT ON COLUMN "sim_key_cert"."update_time" IS '更新时间';
COMMENT ON TABLE "sim_key_cert" IS 'SIMKEY证书信息表';

-- ----------------------------
-- Records of sim_key_cert
-- ----------------------------

-- ----------------------------
-- Table structure for sim_key_user
-- ----------------------------
DROP TABLE IF EXISTS "sim_key_user";
CREATE TABLE "sim_key_user" (
  "id" int8 NOT NULL,
  "phone" varchar(40) COLLATE "pg_catalog"."default",
  "auth_code" varchar(300) COLLATE "pg_catalog"."default",
  "cert_id" int8,
  "sim_flag" int4,
  "user_status" int4,
  "comm_name" varchar(150) COLLATE "pg_catalog"."default",
  "phone_uuid" varchar(100) COLLATE "pg_catalog"."default",
  "email" varchar(100) COLLATE "pg_catalog"."default",
  "user_id" int8,
  "tenant_id" int8,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "sim_key_user"."id" IS 'id';
COMMENT ON COLUMN "sim_key_user"."phone" IS '手机号';
COMMENT ON COLUMN "sim_key_user"."auth_code" IS '口令（加密存储）';
COMMENT ON COLUMN "sim_key_user"."cert_id" IS '证书序列号';
COMMENT ON COLUMN "sim_key_user"."sim_flag" IS '是否开通;0 未申请 1已申请';
COMMENT ON COLUMN "sim_key_user"."user_status" IS '用户状态;1启用 2禁用';
COMMENT ON COLUMN "sim_key_user"."comm_name" IS '预留字段';
COMMENT ON COLUMN "sim_key_user"."phone_uuid" IS '手机序列号';
COMMENT ON COLUMN "sim_key_user"."email" IS '邮件';
COMMENT ON COLUMN "sim_key_user"."user_id" IS '账户ID';
COMMENT ON COLUMN "sim_key_user"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "sim_key_user"."create_by" IS '创建人';
COMMENT ON COLUMN "sim_key_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "sim_key_user"."update_by" IS '更新人';
COMMENT ON COLUMN "sim_key_user"."update_time" IS '更新时间';
COMMENT ON TABLE "sim_key_user" IS 'SIMKEY用户信息表';

-- ----------------------------
-- Records of sim_key_user
-- ----------------------------

-- ----------------------------
-- Table structure for sim_shield_cert
-- ----------------------------
DROP TABLE IF EXISTS "sim_shield_cert";
CREATE TABLE "sim_shield_cert" (
  "id" int8 NOT NULL,
  "phone" varchar(40) COLLATE "pg_catalog"."default",
  "alias" varchar(128) COLLATE "pg_catalog"."default",
  "cert_type" varchar(255) COLLATE "pg_catalog"."default",
  "sign_algorithm" varchar(255) COLLATE "pg_catalog"."default",
  "start_time" varchar(255) COLLATE "pg_catalog"."default",
  "expire_time" varchar(255) COLLATE "pg_catalog"."default",
  "status" int4,
  "user_id" int8,
  "certificate" text COLLATE "pg_catalog"."default",
  "serial_num" varchar(255) COLLATE "pg_catalog"."default",
  "public_key" varchar(255) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "sim_shield_cert"."id" IS 'id';
COMMENT ON COLUMN "sim_shield_cert"."phone" IS '手机号';
COMMENT ON COLUMN "sim_shield_cert"."alias" IS '证书别名';
COMMENT ON COLUMN "sim_shield_cert"."cert_type" IS '证书类型;备用  RSA2048/SM2等';
COMMENT ON COLUMN "sim_shield_cert"."sign_algorithm" IS '签名算法';
COMMENT ON COLUMN "sim_shield_cert"."start_time" IS '证书开始时间';
COMMENT ON COLUMN "sim_shield_cert"."expire_time" IS '过期时间';
COMMENT ON COLUMN "sim_shield_cert"."status" IS '用户状态;1启用 2禁用';
COMMENT ON COLUMN "sim_shield_cert"."user_id" IS '账户ID';
COMMENT ON COLUMN "sim_shield_cert"."certificate" IS '证书内容';
COMMENT ON COLUMN "sim_shield_cert"."serial_num" IS '证书序列号';
COMMENT ON COLUMN "sim_shield_cert"."public_key" IS '签名证书公钥';
COMMENT ON COLUMN "sim_shield_cert"."create_by" IS '创建人';
COMMENT ON COLUMN "sim_shield_cert"."create_time" IS '创建时间';
COMMENT ON COLUMN "sim_shield_cert"."update_by" IS '更新人';
COMMENT ON COLUMN "sim_shield_cert"."update_time" IS '更新时间';
COMMENT ON TABLE "sim_shield_cert" IS 'SIM盾证书信息表';

-- ----------------------------
-- Records of sim_shield_cert
-- ----------------------------

-- ----------------------------
-- Table structure for sim_white_list
-- ----------------------------
DROP TABLE IF EXISTS "sim_white_list";
CREATE TABLE "sim_white_list" (
  "id" int8 NOT NULL,
  "gender" int4,
  "user_name" varchar(150) COLLATE "pg_catalog"."default",
  "position" varchar(90) COLLATE "pg_catalog"."default",
  "organization" varchar(90) COLLATE "pg_catalog"."default",
  "department" varchar(90) COLLATE "pg_catalog"."default",
  "phone" varchar(40) COLLATE "pg_catalog"."default",
  "remark" varchar(300) COLLATE "pg_catalog"."default",
  "tenant_id" int8,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "sim_white_list"."id" IS 'id';
COMMENT ON COLUMN "sim_white_list"."gender" IS '性别 1：男 2：女';
COMMENT ON COLUMN "sim_white_list"."user_name" IS '姓名';
COMMENT ON COLUMN "sim_white_list"."position" IS '职务';
COMMENT ON COLUMN "sim_white_list"."organization" IS '组织';
COMMENT ON COLUMN "sim_white_list"."department" IS '部门';
COMMENT ON COLUMN "sim_white_list"."phone" IS '手机号';
COMMENT ON COLUMN "sim_white_list"."remark" IS '备注';
COMMENT ON COLUMN "sim_white_list"."tenant_id" IS '租户id';
COMMENT ON COLUMN "sim_white_list"."create_by" IS '创建人';
COMMENT ON COLUMN "sim_white_list"."create_time" IS '创建时间';
COMMENT ON COLUMN "sim_white_list"."update_by" IS '更新人';
COMMENT ON COLUMN "sim_white_list"."update_time" IS '更新时间';
COMMENT ON TABLE "sim_white_list" IS 'SIM白名单';

-- ----------------------------
-- Records of sim_white_list
-- ----------------------------

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS "sys_job";
CREATE TABLE "sys_job" (
  "job_id" int8 NOT NULL,
  "job_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "job_group" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "server_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "method_url" varchar(1500) COLLATE "pg_catalog"."default",
  "json_param" text COLLATE "pg_catalog"."default" NOT NULL,
  "cron_expression" varchar(255) COLLATE "pg_catalog"."default",
  "misfire_policy" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 3,
  "concurrent" varchar(1) COLLATE "pg_catalog"."default" DEFAULT 1,
  "job_status" varchar(1) COLLATE "pg_catalog"."default" DEFAULT 0,
  "created_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "updated_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(1500) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "sys_job"."job_id" IS '任务号';
COMMENT ON COLUMN "sys_job"."job_name" IS '任务名称';
COMMENT ON COLUMN "sys_job"."job_group" IS '任务组名';
COMMENT ON COLUMN "sys_job"."server_id" IS '服务模块';
COMMENT ON COLUMN "sys_job"."method_url" IS '调用接口';
COMMENT ON COLUMN "sys_job"."json_param" IS 'json格式参数';
COMMENT ON COLUMN "sys_job"."cron_expression" IS 'CRON执行表达式';
COMMENT ON COLUMN "sys_job"."misfire_policy" IS '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）';
COMMENT ON COLUMN "sys_job"."concurrent" IS '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）';
COMMENT ON COLUMN "sys_job"."job_status" IS '状态（0正常 1暂停）';
COMMENT ON COLUMN "sys_job"."created_by" IS '创建人';
COMMENT ON COLUMN "sys_job"."create_time" IS '创建时间';
COMMENT ON COLUMN "sys_job"."updated_by" IS '更新人';
COMMENT ON COLUMN "sys_job"."update_time" IS '更新时间';
COMMENT ON COLUMN "sys_job"."remark" IS '备注';
COMMENT ON TABLE "sys_job" IS '定时任务表';

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO "sys_job" VALUES (1, 'getServiceStates', 'serviceStates', 'ccsp-aggregate-pt', NULL, 'serviceInvokeManage.getServiceState()', '0/10 * * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "sys_job" VALUES (2, 'clearServiceInterfaceApiRecord', 'clearServiceInterfaceApiRecord', 'ccsp-aggregate-pt', NULL, 'serviceInvokeManage.clearServiceInterfaceApiRecord()', '10 0 0 1/1 * ?', '3', '1', '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "sys_job" VALUES (3, 'getServiceOperationLog', 'serviceLogs', 'ccsp-aggregate-pt', NULL, 'serviceInvokeManage.getServiceOperationLog()', '0 0/5 * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "sys_job" VALUES (4, 'getTenantQuota', 'getTenantQuota', 'ccsp-aggregate-pt', NULL, 'serviceInvokeManage.getTenantQuota()', '0/30 * * * * ?', '3', '1', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "sys_job" VALUES (5, 'getStatisticIncreInfo', 'getStatisticIncreInfo', 'ccsp-aggregate-pt', NULL, 'serviceInvokeManage.getStatisticIncreInfo()', '0 0/10 * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "sys_job" VALUES (6, 'getStatisticUnIncreInfo', 'getStatisticUnIncreInfo', 'ccsp-aggregate-pt', NULL, 'serviceInvokeManage.getStatisticUnIncreInfo()', '0 0/1 * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "sys_job" VALUES (7, 'getServiceLoginMenu', 'serviceLoginMenu', 'ccsp-aggregate-pt', NULL, 'serviceMenuLoginUrlTask.getServiceLoginMenu()', '0 0/10 * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, '获取各服务菜单');
INSERT INTO sys_job   VALUES (8, 'checkGateWayLive', 'checkGateWayLive', 'ccsp-aggregate-pt', NULL, 'checkLiveTask.checkGateWayLive()', '*/30 * * * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, '每30检测一次网关状态');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS "sys_job_log";
CREATE TABLE "sys_job_log" (
  "job_log_id" int8 NOT NULL,
  "job_id" int8 NOT NULL,
  "job_message" varchar(1500) COLLATE "pg_catalog"."default",
  "status" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_info" varchar(6000) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "trigger_time" int8
)
;
COMMENT ON COLUMN "sys_job_log"."job_log_id" IS '任务日志ID';
COMMENT ON COLUMN "sys_job_log"."job_id" IS '任务ID';
COMMENT ON COLUMN "sys_job_log"."job_message" IS '日志信息';
COMMENT ON COLUMN "sys_job_log"."status" IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN "sys_job_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "sys_job_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "sys_job_log"."trigger_time" IS '触发时间';
COMMENT ON TABLE "sys_job_log" IS '定时任务执行日志表';

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS "sys_task";
CREATE TABLE "sys_task" (
  "task_id" int8 NOT NULL,
  "task_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "task_group" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "server_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "method_url" varchar(1000) COLLATE "pg_catalog"."default",
  "json_param" text COLLATE "pg_catalog"."default" NOT NULL,
  "task_status" int4 NOT NULL DEFAULT 0,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(300) COLLATE "pg_catalog"."default",
  "timeout" int4,
  "start_time" varchar(255) COLLATE "pg_catalog"."default",
  "end_time" varchar(255) COLLATE "pg_catalog"."default",
  "policy" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "sys_task"."task_id" IS '任务号';
COMMENT ON COLUMN "sys_task"."task_name" IS '任务名称';
COMMENT ON COLUMN "sys_task"."task_group" IS '任务组名;执行任务串行';
COMMENT ON COLUMN "sys_task"."server_id" IS '服务模块';
COMMENT ON COLUMN "sys_task"."method_url" IS '调用接口';
COMMENT ON COLUMN "sys_task"."json_param" IS 'json格式参数';
COMMENT ON COLUMN "sys_task"."task_status" IS '状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时)';
COMMENT ON COLUMN "sys_task"."create_time" IS '创建时间';
COMMENT ON COLUMN "sys_task"."update_time" IS '更新时间';
COMMENT ON COLUMN "sys_task"."remark" IS '备注';
COMMENT ON COLUMN "sys_task"."timeout" IS '超时时间;单位秒';
COMMENT ON COLUMN "sys_task"."start_time" IS '开始时间';
COMMENT ON COLUMN "sys_task"."end_time" IS '结束时间';
COMMENT ON COLUMN "sys_task"."policy" IS '是否允许重复执行;0-不允许，1允许';
COMMENT ON TABLE "sys_task" IS '异步任务表';

-- ----------------------------
-- Records of sys_task
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task_log
-- ----------------------------
DROP TABLE IF EXISTS "sys_task_log";
CREATE TABLE "sys_task_log" (
  "task_log_id" int8 NOT NULL,
  "task_id" int8 NOT NULL,
  "task_message" varchar(3000) COLLATE "pg_catalog"."default",
  "status" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_info" varchar(6000) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "trigger_time" int8
)
;
COMMENT ON COLUMN "sys_task_log"."task_log_id" IS '任务日志ID';
COMMENT ON COLUMN "sys_task_log"."task_id" IS '任务ID';
COMMENT ON COLUMN "sys_task_log"."task_message" IS '日志信息';
COMMENT ON COLUMN "sys_task_log"."status" IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN "sys_task_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "sys_task_log"."create_time" IS '创建时间;单位毫秒';
COMMENT ON COLUMN "sys_task_log"."trigger_time" IS '触发时间;任务服务上送';
COMMENT ON TABLE "sys_task_log" IS '异步任务执行日志表';

-- ----------------------------
-- Records of sys_task_log
-- ----------------------------

-- ----------------------------
-- Table structure for tenant
-- ----------------------------
DROP TABLE IF EXISTS "tenant";
CREATE TABLE "tenant" (
  "tenant_id" int8 NOT NULL,
  "tenant_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "tenant_name" varchar(270) COLLATE "pg_catalog"."default" NOT NULL,
  "tenant_level" int4 NOT NULL,
  "senior_tenant_id" int8,
  "tenant_status" int4,
  "region_id" int8,
  "device_group_id" int8 NOT NULL,
  "service_group_id" int8 NOT NULL,
  "organ" varchar(270) COLLATE "pg_catalog"."default",
  "hmac" varchar(255) COLLATE "pg_catalog"."default",
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8 NOT NULL,
  "create_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "tenant"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "tenant"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "tenant"."tenant_name" IS '租户名称';
COMMENT ON COLUMN "tenant"."tenant_level" IS '租户级别';
COMMENT ON COLUMN "tenant"."senior_tenant_id" IS '上级租户ID';
COMMENT ON COLUMN "tenant"."tenant_status" IS '租户状态;1：初始化（未添加设备或服务）；2：运行中；3：停用中；4：停用; 5：启动中；';
COMMENT ON COLUMN "tenant"."region_id" IS '区域ID';
COMMENT ON COLUMN "tenant"."device_group_id" IS '租户设备资源组';
COMMENT ON COLUMN "tenant"."service_group_id" IS '租户服务资源组';
COMMENT ON COLUMN "tenant"."organ" IS '机构名称';
COMMENT ON COLUMN "tenant"."hmac" IS '完整性校验';
COMMENT ON COLUMN "tenant"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "tenant"."remark" IS '备注';
COMMENT ON COLUMN "tenant"."create_by" IS '创建人';
COMMENT ON COLUMN "tenant"."create_time" IS '创建时间';
COMMENT ON COLUMN "tenant"."update_by" IS '更新人';
COMMENT ON COLUMN "tenant"."update_time" IS '更新时间';
COMMENT ON TABLE "tenant" IS '租户表';

-- ----------------------------
-- Records of tenant
-- ----------------------------
INSERT INTO "tenant" VALUES (1, 'ccsp_tenant', '密服平台顶级租户', 0, 0, 2, NULL, 1, 1, '三未信安', NULL, 0, NULL, 1, '2023-03-03 06:11:21', 5469505260282187143, '2023-03-16 20:56:29');

-- ----------------------------
-- Table structure for tenant_key
-- ----------------------------
DROP TABLE IF EXISTS "tenant_key";
CREATE TABLE "tenant_key" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "key_type" int4 NOT NULL,
  "content" varchar(1000) COLLATE "pg_catalog"."default" NOT NULL,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "tenant_key"."id" IS '主键';
COMMENT ON COLUMN "tenant_key"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "tenant_key"."key_type" IS '密钥类型：1-2号非对称公钥，2-2号非对称私钥，3-2号对称密钥分量';
COMMENT ON COLUMN "tenant_key"."content" IS '密钥内容';
COMMENT ON COLUMN "tenant_key"."create_by" IS '创建人';
COMMENT ON COLUMN "tenant_key"."create_time" IS '创建时间';
COMMENT ON TABLE "tenant_key" IS '租户密钥关系表';

-- ----------------------------
-- Records of tenant_key
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_quota_info
-- ----------------------------
DROP TABLE IF EXISTS "tenant_quota_info";
CREATE TABLE "tenant_quota_info" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "service_code" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
  "quota_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "quota_value" int4 NOT NULL,
  "start_time" varchar(30) COLLATE "pg_catalog"."default",
  "end_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "hmac" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "tenant_quota_info"."id" IS '主键';
COMMENT ON COLUMN "tenant_quota_info"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "tenant_quota_info"."service_code" IS '服务标识;服务标识+配额信息key对应唯一配额信息';
COMMENT ON COLUMN "tenant_quota_info"."quota_name" IS '配额信息key';
COMMENT ON COLUMN "tenant_quota_info"."quota_value" IS '配额值';
COMMENT ON COLUMN "tenant_quota_info"."start_time" IS '开始时间';
COMMENT ON COLUMN "tenant_quota_info"."end_time" IS '结束数据';
COMMENT ON COLUMN "tenant_quota_info"."remark" IS '备注';
COMMENT ON COLUMN "tenant_quota_info"."create_by" IS '创建人';
COMMENT ON COLUMN "tenant_quota_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "tenant_quota_info"."update_by" IS '更新人';
COMMENT ON COLUMN "tenant_quota_info"."update_time" IS '更新时间';
COMMENT ON TABLE "tenant_quota_info" IS '租户业务服务配额限制信息';

-- ----------------------------
-- Records of tenant_quota_info
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_quota_use_info
-- ----------------------------
DROP TABLE IF EXISTS "tenant_quota_use_info";
CREATE TABLE "tenant_quota_use_info" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "service_code" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
  "quota_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "total_quota" varchar(255) COLLATE "pg_catalog"."default",
  "used_quota" varchar(255) COLLATE "pg_catalog"."default",
  "residue_quota" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "tenant_quota_use_info"."id" IS '主键';
COMMENT ON COLUMN "tenant_quota_use_info"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "tenant_quota_use_info"."service_code" IS '服务标识;服务标识+配额信息key对应唯一配额信息';
COMMENT ON COLUMN "tenant_quota_use_info"."quota_name" IS '配额信息key';
COMMENT ON COLUMN "tenant_quota_use_info"."total_quota" IS '配额总量';
COMMENT ON COLUMN "tenant_quota_use_info"."used_quota" IS '已使用量';
COMMENT ON COLUMN "tenant_quota_use_info"."residue_quota" IS '剩余量';
COMMENT ON COLUMN "tenant_quota_use_info"."remark" IS '备注';
COMMENT ON COLUMN "tenant_quota_use_info"."create_by" IS '创建人';
COMMENT ON COLUMN "tenant_quota_use_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "tenant_quota_use_info"."update_by" IS '更新人';
COMMENT ON COLUMN "tenant_quota_use_info"."update_time" IS '更新时间';
COMMENT ON TABLE "tenant_quota_use_info" IS '业务服务配额使用信息';

-- ----------------------------
-- Records of tenant_quota_use_info
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_register
-- ----------------------------
DROP TABLE IF EXISTS "tenant_register";
CREATE TABLE "tenant_register" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "tenant_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "tenant_name" varchar(270) COLLATE "pg_catalog"."default" NOT NULL,
  "tenant_level" int4 NOT NULL,
  "senior_tenant_id" int8,
  "tenant_status" int4,
  "partition_id" int8,
  "organ" varchar(270) COLLATE "pg_catalog"."default",
  "status" int4,
  "audit_by" varchar(255) COLLATE "pg_catalog"."default",
  "audit_remark" varchar(1000) COLLATE "pg_catalog"."default",
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "hmac" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "tenant_register"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "tenant_register"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "tenant_register"."tenant_name" IS '租户名称';
COMMENT ON COLUMN "tenant_register"."tenant_level" IS '租户级别';
COMMENT ON COLUMN "tenant_register"."senior_tenant_id" IS '上级租户ID';
COMMENT ON COLUMN "tenant_register"."tenant_status" IS '租户状态;1：初始化；2：运行中；3：停用;4:销毁';
COMMENT ON COLUMN "tenant_register"."partition_id" IS '租户资源分区ID';
COMMENT ON COLUMN "tenant_register"."organ" IS '机构名称';
COMMENT ON COLUMN "tenant_register"."status" IS '状态；1:待审核；2：通过；3：拒绝';
COMMENT ON COLUMN "tenant_register"."audit_by" IS '审批人';
COMMENT ON COLUMN "tenant_register"."audit_remark" IS '审批意见';
COMMENT ON COLUMN "tenant_register"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "tenant_register"."hmac" IS '完整性';
COMMENT ON COLUMN "tenant_register"."remark" IS '备注';
COMMENT ON COLUMN "tenant_register"."create_by" IS '创建人';
COMMENT ON COLUMN "tenant_register"."create_time" IS '创建时间';
COMMENT ON COLUMN "tenant_register"."update_by" IS '更新人';
COMMENT ON COLUMN "tenant_register"."update_time" IS '更新时间';
COMMENT ON TABLE "tenant_register" IS '租户注册表';

-- ----------------------------
-- Records of tenant_register
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_register_user
-- ----------------------------
DROP TABLE IF EXISTS "tenant_register_user";
CREATE TABLE "tenant_register_user" (
  "id" int8,
  "tenant_id" int8,
  "user_type" int4,
  "user_name" varchar(150) COLLATE "pg_catalog"."default",
  "user_code" varchar(255) COLLATE "pg_catalog"."default",
  "auth_code" varchar(300) COLLATE "pg_catalog"."default",
  "cert" varchar(2000) COLLATE "pg_catalog"."default",
  "serial" varchar(100) COLLATE "pg_catalog"."default",
  "random" varchar(255) COLLATE "pg_catalog"."default",
  "signature" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "tenant_register_user"."id" IS '主键';
COMMENT ON COLUMN "tenant_register_user"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "tenant_register_user"."user_type" IS '用户类型';
COMMENT ON COLUMN "tenant_register_user"."user_name" IS '用户名称';
COMMENT ON COLUMN "tenant_register_user"."user_code" IS '账号';
COMMENT ON COLUMN "tenant_register_user"."auth_code" IS '密码;加密存储';
COMMENT ON COLUMN "tenant_register_user"."cert" IS '证书内容';
COMMENT ON COLUMN "tenant_register_user"."serial" IS 'Ukey序列号';
COMMENT ON COLUMN "tenant_register_user"."random" IS 'Ukey验证随机数';
COMMENT ON COLUMN "tenant_register_user"."signature" IS 'Ukey验证签名值';
COMMENT ON COLUMN "tenant_register_user"."remark" IS '备注';
COMMENT ON COLUMN "tenant_register_user"."create_by" IS '创建人';
COMMENT ON COLUMN "tenant_register_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "tenant_register_user"."update_by" IS '更新人';
COMMENT ON COLUMN "tenant_register_user"."update_time" IS '更新时间';
COMMENT ON TABLE "tenant_register_user" IS '租户注册用户信息';

-- ----------------------------
-- Records of tenant_register_user
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_secret_key_resource
-- ----------------------------
DROP TABLE IF EXISTS "tenant_secret_key_resource";
CREATE TABLE "tenant_secret_key_resource" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "secret_num" int4,
  "cert_num" int4,
  "use_secret_num" int4,
  "use_cert_num" int4,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "tenant_secret_key_resource"."id" IS '主键';
COMMENT ON COLUMN "tenant_secret_key_resource"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "tenant_secret_key_resource"."secret_num" IS '密钥数量;-1表示无限';
COMMENT ON COLUMN "tenant_secret_key_resource"."cert_num" IS '证书数量;-1表示无限';
COMMENT ON COLUMN "tenant_secret_key_resource"."use_secret_num" IS '使用密钥数量';
COMMENT ON COLUMN "tenant_secret_key_resource"."use_cert_num" IS '使用证书数量';
COMMENT ON COLUMN "tenant_secret_key_resource"."remark" IS '备注';
COMMENT ON COLUMN "tenant_secret_key_resource"."create_by" IS '创建人';
COMMENT ON COLUMN "tenant_secret_key_resource"."create_time" IS '创建时间';
COMMENT ON COLUMN "tenant_secret_key_resource"."update_by" IS '更新人';
COMMENT ON COLUMN "tenant_secret_key_resource"."update_time" IS '更新时间';
COMMENT ON TABLE "tenant_secret_key_resource" IS '租户密钥资源管理';

-- ----------------------------
-- Records of tenant_secret_key_resource
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_to_busi_type
-- ----------------------------
DROP TABLE IF EXISTS "tenant_to_busi_type";
CREATE TABLE "tenant_to_busi_type" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "busi_type_id" int8 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8 NOT NULL,
  "create_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "tenant_to_busi_type"."id" IS 'ID';
COMMENT ON COLUMN "tenant_to_busi_type"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "tenant_to_busi_type"."busi_type_id" IS '业务类型ID';
COMMENT ON COLUMN "tenant_to_busi_type"."remark" IS '备注';
COMMENT ON COLUMN "tenant_to_busi_type"."create_by" IS '创建人';
COMMENT ON COLUMN "tenant_to_busi_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "tenant_to_busi_type"."update_by" IS '更新人';
COMMENT ON COLUMN "tenant_to_busi_type"."update_time" IS '更新时间';
COMMENT ON TABLE "tenant_to_busi_type" IS '租户和业务类型关联表';

-- ----------------------------
-- Records of tenant_to_busi_type
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_to_database
-- ----------------------------
DROP TABLE IF EXISTS "tenant_to_database";
CREATE TABLE "tenant_to_database" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "database_id" int8,
  "create_by" int8 NOT NULL,
  "create_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "tenant_to_database"."id" IS '主键';
COMMENT ON COLUMN "tenant_to_database"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "tenant_to_database"."database_id" IS '数据库服务ID';
COMMENT ON COLUMN "tenant_to_database"."create_by" IS '创建人';
COMMENT ON COLUMN "tenant_to_database"."create_time" IS '创建时间';
COMMENT ON COLUMN "tenant_to_database"."update_by" IS '更新人';
COMMENT ON COLUMN "tenant_to_database"."update_time" IS '更新时间';
COMMENT ON TABLE "tenant_to_database" IS '租户和数据库关联表';

-- ----------------------------
-- Records of tenant_to_database
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_to_kms_relation
-- ----------------------------
DROP TABLE IF EXISTS "tenant_to_kms_relation";
CREATE TABLE "tenant_to_kms_relation" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "tenant_code" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
  "kms_tenant_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "tenant_to_kms_relation"."id" IS 'ID';
COMMENT ON COLUMN "tenant_to_kms_relation"."tenant_id" IS '租户id';
COMMENT ON COLUMN "tenant_to_kms_relation"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "tenant_to_kms_relation"."kms_tenant_name" IS 'KMS租户名称';
COMMENT ON COLUMN "tenant_to_kms_relation"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "tenant_to_kms_relation"."remark" IS '备注';
COMMENT ON COLUMN "tenant_to_kms_relation"."create_by" IS '创建人';
COMMENT ON COLUMN "tenant_to_kms_relation"."create_time" IS '创建时间';
COMMENT ON COLUMN "tenant_to_kms_relation"."update_by" IS '更新人';
COMMENT ON COLUMN "tenant_to_kms_relation"."update_time" IS '更新时间';
COMMENT ON TABLE "tenant_to_kms_relation" IS '租户与kms租户关联关系表';

-- ----------------------------
-- Records of tenant_to_kms_relation
-- ----------------------------

-- ----------------------------
-- Table structure for version
-- ----------------------------
DROP TABLE IF EXISTS "version";
CREATE TABLE "version" (
  "id" int8 NOT NULL,
  "product_code" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "product_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "version" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "sord_num" int4 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "version"."id" IS '主键';
COMMENT ON COLUMN "version"."product_code" IS '产品简称';
COMMENT ON COLUMN "version"."product_name" IS '产品名称';
COMMENT ON COLUMN "version"."version" IS '版本';
COMMENT ON COLUMN "version"."sord_num" IS '排序';
COMMENT
ON COLUMN "version"."remark" IS '备注';
COMMENT
ON COLUMN "version"."create_by" IS '创建人';
COMMENT
ON COLUMN "version"."create_time" IS '创建时间';
COMMENT
ON COLUMN "version"."update_by" IS '更新人';
COMMENT
ON COLUMN "version"."update_time" IS '更新时间';
COMMENT
ON TABLE "version" IS '版本表';

-- ----------------------------
-- Records of version
-- ----------------------------
INSERT INTO "version"
VALUES (1, 'CCSP', '密码服务管理平台', 'V3.2.10', 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (2, 'KMS', '密钥管理', '4.0.0-XC-CCSP', 2, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (3, 'PKI', '数据加解密', 'V1.0.2', 3, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (4, 'SVS', '签名验签', 'V1.0.2', 4, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (5, 'TSA', '时间戳', 'V4.0.2.2', 5, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (6, 'OTP', '动态令牌', 'V3.2.1', 6, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (7, 'SMS', '协同签名', 'V4.6.4', 7, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (8, 'SECDB', '数据库加密', 'V3.4.1.1', 8, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (9, 'VPN', 'SSLVPN加密通道', 'V2.2.11', 9, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (10, 'SECSTORAGE', '文件加密', '4.0.2-XC-CCSP', 10, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (11, 'TSC', '电子签章', 'V1.1.0', 11, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (12, 'WEB', '统一web平台', 'V1.2.3', 13, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "version"
VALUES (13, 'CA', '数字证书认证服务', 'V5.0.1.1', 12, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Primary Key structure for table app_busi_to_group
-- ----------------------------
ALTER TABLE "app_busi_to_group"
    ADD CONSTRAINT "app_busi_to_group_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table app_register_to_tenant
-- ----------------------------
ALTER TABLE "app_register_to_tenant"
    ADD CONSTRAINT "app_register_to_tenant_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table app_to_kms_relation
-- ----------------------------
ALTER TABLE "app_to_kms_relation" ADD CONSTRAINT "app_to_kms_relation_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table app_to_tenant
-- ----------------------------
ALTER TABLE "app_to_tenant" ADD CONSTRAINT "app_to_tenant_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table busi_url_info
-- ----------------------------
ALTER TABLE "busi_url_info" ADD CONSTRAINT "busi_url_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table busi_url_to_gateway
-- ----------------------------
ALTER TABLE "busi_url_to_gateway" ADD CONSTRAINT "busi_url_to_gateway_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table busi_url_type_to_busi
-- ----------------------------
ALTER TABLE "busi_url_type_to_busi" ADD CONSTRAINT "busi_url_type_to_busi_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table ca_cert
-- ----------------------------
ALTER TABLE "ca_cert" ADD CONSTRAINT "ca_cert_pkey" PRIMARY KEY ("ca_id");

-- ----------------------------
-- Primary Key structure for table dic_busi_url_type
-- ----------------------------
ALTER TABLE "dic_busi_url_type" ADD CONSTRAINT "dic_busi_url_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_service_quota
-- ----------------------------
ALTER TABLE "dic_service_quota" ADD CONSTRAINT "dic_service_quota_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table disaster_recover_db
-- ----------------------------
ALTER TABLE "disaster_recover_db" ADD CONSTRAINT "disaster_recover_db_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table disaster_recover_db_sub
-- ----------------------------
ALTER TABLE "disaster_recover_db_sub" ADD CONSTRAINT "disaster_recover_db_sub_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table disaster_recover_db_sub_tem
-- ----------------------------
ALTER TABLE "disaster_recover_db_sub_tem" ADD CONSTRAINT "disaster_recover_db_sub_tem_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table doc_download_path
-- ----------------------------
ALTER TABLE "doc_download_path" ADD CONSTRAINT "doc_download_path_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table license_apply
-- ----------------------------
ALTER TABLE "license_apply" ADD CONSTRAINT "license_apply_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table license_info
-- ----------------------------
ALTER TABLE "license_info" ADD CONSTRAINT "license_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table license_use
-- ----------------------------
ALTER TABLE "license_use" ADD CONSTRAINT "license_use_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table license_use_rel
-- ----------------------------
ALTER TABLE "license_use_rel" ADD CONSTRAINT "license_use_rel_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table license_use_time
-- ----------------------------
ALTER TABLE "license_use_time" ADD CONSTRAINT "license_use_time_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table region
-- ----------------------------
ALTER TABLE "region" ADD CONSTRAINT "region_pkey" PRIMARY KEY ("region_id");

-- ----------------------------
-- Primary Key structure for table route_server_port
-- ----------------------------
ALTER TABLE "route_server_port" ADD CONSTRAINT "route_server_port_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table route_to_app
-- ----------------------------
ALTER TABLE "route_to_app" ADD CONSTRAINT "route_to_app_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table route_to_service
-- ----------------------------
ALTER TABLE "route_to_service" ADD CONSTRAINT "route_to_service_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_auto_bind_device
-- ----------------------------
ALTER TABLE "service_auto_bind_device" ADD CONSTRAINT "service_auto_bind_device_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_gateway
-- ----------------------------
ALTER TABLE "service_gateway" ADD CONSTRAINT "service_gateway_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_gateway_route
-- ----------------------------
ALTER TABLE "service_gateway_route" ADD CONSTRAINT "service_gateway_route_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_interface_api_record
-- ----------------------------
ALTER TABLE "service_interface_api_record" ADD CONSTRAINT "service_interface_api_record_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_to_device_group
-- ----------------------------
ALTER TABLE "service_to_device_group" ADD CONSTRAINT "service_to_device_group_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_type_to_device_group
-- ----------------------------
ALTER TABLE "service_type_to_device_group" ADD CONSTRAINT "service_type_to_device_group_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_version
-- ----------------------------
ALTER TABLE "service_version" ADD CONSTRAINT "service_version_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sim_flow_data
-- ----------------------------
ALTER TABLE "sim_flow_data" ADD CONSTRAINT "sim_flow_data_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sim_key_cert
-- ----------------------------
ALTER TABLE "sim_key_cert" ADD CONSTRAINT "sim_key_cert_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sim_key_user
-- ----------------------------
ALTER TABLE "sim_key_user" ADD CONSTRAINT "sim_key_user_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sim_shield_cert
-- ----------------------------
ALTER TABLE "sim_shield_cert" ADD CONSTRAINT "sim_shield_cert_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sim_white_list
-- ----------------------------
ALTER TABLE "sim_white_list" ADD CONSTRAINT "sim_white_list_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_job
-- ----------------------------
ALTER TABLE "sys_job" ADD CONSTRAINT "sys_job_pkey" PRIMARY KEY ("job_id");

-- ----------------------------
-- Primary Key structure for table sys_job_log
-- ----------------------------
ALTER TABLE "sys_job_log" ADD CONSTRAINT "sys_job_log_pkey" PRIMARY KEY ("job_log_id");

-- ----------------------------
-- Primary Key structure for table sys_task
-- ----------------------------
ALTER TABLE "sys_task" ADD CONSTRAINT "sys_task_pkey" PRIMARY KEY ("task_id");

-- ----------------------------
-- Primary Key structure for table sys_task_log
-- ----------------------------
ALTER TABLE "sys_task_log" ADD CONSTRAINT "sys_task_log_pkey" PRIMARY KEY ("task_log_id");

-- ----------------------------
-- Primary Key structure for table tenant
-- ----------------------------
ALTER TABLE "tenant" ADD CONSTRAINT "tenant_pkey" PRIMARY KEY ("tenant_id");

-- ----------------------------
-- Primary Key structure for table tenant_key
-- ----------------------------
ALTER TABLE "tenant_key" ADD CONSTRAINT "tenant_key_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tenant_quota_info
-- ----------------------------
ALTER TABLE "tenant_quota_info" ADD CONSTRAINT "tenant_quota_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tenant_quota_use_info
-- ----------------------------
ALTER TABLE "tenant_quota_use_info" ADD CONSTRAINT "tenant_quota_use_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tenant_register
-- ----------------------------
ALTER TABLE "tenant_register" ADD CONSTRAINT "tenant_register_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tenant_secret_key_resource
-- ----------------------------
ALTER TABLE "tenant_secret_key_resource" ADD CONSTRAINT "tenant_secret_key_resource_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tenant_to_busi_type
-- ----------------------------
ALTER TABLE "tenant_to_busi_type" ADD CONSTRAINT "tenant_to_busi_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tenant_to_database
-- ----------------------------
ALTER TABLE "tenant_to_database" ADD CONSTRAINT "tenant_to_database_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tenant_to_kms_relation
-- ----------------------------
ALTER TABLE "tenant_to_kms_relation" ADD CONSTRAINT "tenant_to_kms_relation_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table version
-- ----------------------------
ALTER TABLE "version" ADD CONSTRAINT "version_pkey" PRIMARY KEY ("id", "product_code", "product_name", "version", "sord_num");
