package com.sansec.ccsp.pt.business.sim.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.pt.business.sim.constant.SimConstant;
import com.sansec.ccsp.pt.business.sim.entity.SimShieldCertPO;
import com.sansec.ccsp.pt.business.sim.mapper.SimShieldCertMapper;
import com.sansec.ccsp.pt.business.sim.service.SimShieldCertService;
import org.springframework.stereotype.Service;

/**
 * @Description: SIM盾证书实现类
 * @CreateTime: 2023-09-15
 * @Author: wangjunjie
 */
@Service
public class SimShieldCertServiceImpl extends ServiceImpl<SimShieldCertMapper, SimShieldCertPO> implements SimShieldCertService {

    public SimShieldCertPO getCertByUserId(Long userId) {
        // 查询原证书
        LambdaQueryWrapper<SimShieldCertPO> queryCertByIdWrapper = Wrappers.lambdaQuery();
        queryCertByIdWrapper.eq(SimShieldCertPO::getUserId, userId)
                .eq(SimShieldCertPO::getStatus, SimConstant.CERT_STATUS_ENABLE);
        SimShieldCertPO simShieldCertDB = this.getOne(queryCertByIdWrapper);
        return simShieldCertDB;
    }

    @Override
    public Boolean enableCert(Long certId) {
        LambdaUpdateWrapper<SimShieldCertPO> updateCertStatusWrapper = Wrappers.lambdaUpdate();
        updateCertStatusWrapper.eq(SimShieldCertPO::getId, certId)
                .set(SimShieldCertPO::getStatus, SimConstant.CERT_STATUS_ENABLE);
        boolean update = this.update(updateCertStatusWrapper);
        return update;
    }

    @Override
    public Boolean disableCert(Long certId) {
        LambdaUpdateWrapper<SimShieldCertPO> updateCertStatusWrapper = Wrappers.lambdaUpdate();
        updateCertStatusWrapper.eq(SimShieldCertPO::getId, certId)
                .set(SimShieldCertPO::getStatus, SimConstant.CERT_STATUS_DISABLE);
        boolean update = this.update(updateCertStatusWrapper);
        return update;
    }


}
