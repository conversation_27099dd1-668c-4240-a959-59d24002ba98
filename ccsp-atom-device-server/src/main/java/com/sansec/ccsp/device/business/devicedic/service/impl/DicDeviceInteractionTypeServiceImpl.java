package com.sansec.ccsp.device.business.devicedic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.device.business.devicedic.convert.DicDeviceInteractionTypeConvert;
import com.sansec.ccsp.device.business.devicedic.entity.DicDeviceInteractionTypePO;
import com.sansec.ccsp.device.business.devicedic.mapper.DicDeviceInteractionTypeMapper;
import com.sansec.ccsp.device.business.devicedic.request.DicDeviceInteractionTypeDTO;
import com.sansec.ccsp.device.business.devicedic.request.DicDeviceInteractionTypePageDTO;
import com.sansec.ccsp.device.business.devicedic.response.DicDeviceInteractionTypeVO;
import com.sansec.ccsp.device.business.devicedic.service.DicDeviceInteractionTypeService;
import com.sansec.ccsp.device.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.device.devicedic.request.DicDeviceInteractionTypeListDTO;
import com.sansec.ccsp.device.devicedic.response.DicDeviceInteractionTypeApiVO;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 设备交互类型字典表;(DIC_DEVICE_INTERACTION_TYPE)表服务实现类
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Service
@Slf4j
public class DicDeviceInteractionTypeServiceImpl extends ServiceImpl<DicDeviceInteractionTypeMapper, DicDeviceInteractionTypePO> implements DicDeviceInteractionTypeService{
    @Resource
    private DicDeviceInteractionTypeConvert dicDeviceInteractionTypeConvert;
    
    /** 
     * 分页查询
     *
     * @param dicDeviceInteractionTypePageDTO 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<DicDeviceInteractionTypeVO>> find(DicDeviceInteractionTypePageDTO dicDeviceInteractionTypePageDTO){
        QueryWrapper<DicDeviceInteractionTypePO> queryWrapper = Wrappers.query();
        IPage<DicDeviceInteractionTypePO> page = new Page<>(dicDeviceInteractionTypePageDTO.getPageNum(),dicDeviceInteractionTypePageDTO.getPageSize());
        IPage<DicDeviceInteractionTypePO> dicDeviceInteractionTypePOPage = baseMapper.selectPage(page, queryWrapper);

        SecPageVO<DicDeviceInteractionTypeVO> dicDeviceInteractionTypePageVO = dicDeviceInteractionTypeConvert.pagePOToSecPageVOPage(dicDeviceInteractionTypePOPage);
        return ResultUtil.ok(dicDeviceInteractionTypePageVO);
    }


     @Override
     public SecRestResponse<List<DicDeviceInteractionTypeApiVO>> findList(DicDeviceInteractionTypeListDTO deviceInteractionTypeListDTO) {

         QueryWrapper<DicDeviceInteractionTypePO> queryWrapper = Wrappers.query();
         queryWrapper.eq("INVALID_FLAG",0);
         queryWrapper.eq("FAMILY_TYPE",deviceInteractionTypeListDTO.getFamilyType());
         List<DicDeviceInteractionTypePO> poList= baseMapper.selectList(queryWrapper);
         List<DicDeviceInteractionTypeApiVO> voList = dicDeviceInteractionTypeConvert.poToApiVoList(poList);
         return ResultUtil.ok(voList);
     }

     /**
     * 新增数据
     *
     * @param dicDeviceInteractionTypeDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> add(DicDeviceInteractionTypeDTO dicDeviceInteractionTypeDTO){
        DicDeviceInteractionTypePO dicDeviceInteractionTypePO = dicDeviceInteractionTypeConvert.dtoToPo(dicDeviceInteractionTypeDTO);
        dicDeviceInteractionTypePO.setId(IdGenerator.ins().generator());
        dicDeviceInteractionTypePO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.insert(dicDeviceInteractionTypePO);
        return ResultUtil.ok();
    }
    
    /** 
     * 更新数据
     *
     * @param dicDeviceInteractionTypeDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(DicDeviceInteractionTypeDTO dicDeviceInteractionTypeDTO){
        DicDeviceInteractionTypePO dicDeviceInteractionTypePO = dicDeviceInteractionTypeConvert.dtoToPo(dicDeviceInteractionTypeDTO);
        dicDeviceInteractionTypePO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.updateById(dicDeviceInteractionTypePO);
        return ResultUtil.ok();
    }
    
    /** 
     * 通过主键删除数据
     *
     * @param dicDeviceInteractionTypeDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> deleteById(DicDeviceInteractionTypeDTO dicDeviceInteractionTypeDTO){
        baseMapper.deleteById(dicDeviceInteractionTypeDTO.getId());
        return ResultUtil.ok();
    }

     @Override
     public Boolean hasDicDeviceInteractionType(String interactionSerialNumber, Integer familyType) {

         QueryWrapper<DicDeviceInteractionTypePO> queryWrapper = Wrappers.query();
         queryWrapper.eq("INVALID_FLAG", 0);
         queryWrapper.eq("FAMILY_TYPE", familyType);
         List<DicDeviceInteractionTypePO> dicDeviceInteractionTypePOList = baseMapper.selectList(queryWrapper);
         List<String> dicDeviceInteractionTypeInteractionSerialNumberList = dicDeviceInteractionTypePOList.stream().map(DicDeviceInteractionTypePO::getInteractionSerialNumber).collect(Collectors.toList());
         if(dicDeviceInteractionTypeInteractionSerialNumberList.contains(interactionSerialNumber)) {
             return true;
         } else {
             throw new BusinessException(SecErrorCodeConstant.DEVICE_TYPE_INTERACTION_SERIAL_NUMBER_ERROR);
         }

     }
 }