package com.sansec.ccsp.datastatic.business.statistic.realtime.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

/**
 * <AUTHOR> http://www.chiner.pro
 * @description : 按照小时拆分实时数据（330）;
 * @date : 2024-3-7
 */
@TableName("STATISTIC_HOUR_DATA_REAL")
@Data
public class StatisticHourDataRealPO extends BasePO {
    /**
     * ID
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 拆分数据小时时间;采集时间：时间戳格式
     */
    private String collectDateTime;

    /**
     * 拆分数据小时时间;采集时间：字符串格式
     */
    private String collectTime;
    /**
     * 调用次数
     */
    private Long callNum;
    /**
     * 区域标识
     */
    private String regionCode;
    /**
     * 租户标识
     */
    private String tenantCode;
    /**
     * 应用标识
     */
    private String appCode;
    /**
     * 服务IP
     */
    private String serviceIp;
    /**
     * 服务端口
     */
    private Integer servicePort;
    /**
     * 服务标识
     */
    private String serviceTypeCode;
    /**
     * 服务URL
     */
    private String serviceUrl;
    /**
     * 平均耗时
     */
    private Long avgCostTime;
    /**
     * 最大耗时
     */
    private Long maxCostTime;
    /**
     * 流量
     */
    private Long serviceFlowNum;
    /**
     * 峰值（tps）
     */
    private Integer peakTps;
    /**
     * 峰值（Mb/s）
     */
    private Double peakMbps;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}