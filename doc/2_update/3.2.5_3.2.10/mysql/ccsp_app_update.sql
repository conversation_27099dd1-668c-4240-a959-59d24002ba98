-- 开启事务
START TRANSACTION;

-- 关闭自动提交
SET
autocommit = 0;

-- 结构修改
DROP TABLE IF EXISTS `app_auth_aksk`;
CREATE TABLE `app_auth_aksk`
(
    `ID`           bigint(20) NOT NULL COMMENT 'ID',
    `TENANT_CODE`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '租户标识',
    `APP_ID`       bigint(20) NOT NULL COMMENT '应用id',
    `ACCESSKEY_ID` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '访问密钥id',
    `SECRETKEY`    varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '访问密钥',
    `STATUS`       int(11) NOT NULL COMMENT '状态（启用、停用）',
    `REMARK`       varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '描述',
    `CREATE_TIME`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
    `LAST_TIME`    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '最后使用时间',
    `HMAC`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'hmac',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用aksk认证' ROW_FORMAT = DYNAMIC;

ALTER TABLE `app_info`
    ADD COLUMN `REGION_ID` bigint(20) NULL DEFAULT NULL COMMENT '区域ID' AFTER `APP_SHORT`;

-- 提交
COMMIT;
-- 回滚
ROLLBACK;

-- 恢复自动提交
SET
autocommit = 1;