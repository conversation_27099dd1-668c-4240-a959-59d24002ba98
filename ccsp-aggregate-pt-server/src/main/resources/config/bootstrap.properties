#sansec.jce.configPath=/opt/ccsp/ccsp_config/swsds

server.port=18090
spring.application.name=ccsp-aggregate-pt-zz
server.servlet.context-path=/ccsp/pt
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true

#中英文切换
spring.messages.basename=i18n.messages,i18n.common,i18n.ccsp-common,i18n.ccsp-app,i18n.ccsp-device,i18n.ccsp-servicemgt,i18n.ccsp-static

#用来控制所有的feign调用 走的协议类型
spring.protocol.prefix=https://

#日志路径
sec.log.logPath=config/log.ini

#外边微服务名称
spring.application.servicemgt.name=ccsp-atom-servicemgt
spring.application.common.name=ccsp-base-common
spring.application.device.name=ccsp-atom-device
spring.application.app.name=ccsp-atom-app
spring.application.static.name=ccsp-atom-static

#认证中心url
sansec.ccsp.server.auth.address=https://************:28081

#监控服务url
sansec.ccsp.server.monitor.address=https://************:19441
#Nacos
spring.cloud.nacos.server-addr=https://************:28848
spring.cloud.nacos.discovery.enabled=true
spring.cloud.nacos.discovery.namespace=public
spring.cloud.nacos.config.enabled=true
spring.cloud.nacos.config.refresh-enabled=true
spring.cloud.nacos.config.namespace=
spring.cloud.nacos.username= sNfl0KWIYkfyyu5k5HfPXA==
spring.cloud.nacos.password= Mc1ql7fRrYz61uWT5WiHUw==
environment.decryptionListKey[8]=spring.cloud.nacos.username
environment.decryptionListKey[9]=spring.cloud.nacos.password
tls.enable=true

#Nacos共享配置
spring.cloud.nacos.config.shared-configs[0].data-id=config-common-feign.properties
spring.cloud.nacos.config.shared-configs[0].refresh=true

mybatis-plus.mapper-locations=classpath*:mapper/*/*.xml
mybatis-plus.type-aliases-package=com.sansec.ccsp.pt.business.*.entity

# redis配置
#单机配置
spring.redis.ssl=true
spring.redis.host=************
spring.redis.port=6379
spring.redis.password=P2VhputoC550lMRQ2cGBqu0shNzL9ZuHtMmN9EIR6EM=
spring.redis.database=0
spring.redis.timeout=50000
spring.redis.lettuce.pool.max-active=10
spring.redis.lettuce.pool.max-idle=5
spring.redis.lettuce.pool.max-wait=10000
spring.redis.lettuce.pool.min-idle=3
# 哨兵配置
#spring.redis.sentinel.master=mymaster
## 非ssl 模式
##spring.redis.sentinel.nodes=************:26379,************:26379,************:26379
## ssl 模式
#spring.redis.sentinel.nodes=rediss://***********:26379,rediss://***********:26379,rediss://***********:26379
#spring.redis.sentinel.password=P2VhputoC550lMRQ2cGBqu0shNzL9ZuHtMmN9EIR6EM=

#数据库配置
spring.profiles.active=dm

#ssl server
server.ssl.key-store=config/rsa_server.pfx
server.ssl.key-store-password=t6b9SYjbGSEpm+V8S3t7Fg==
server.ssl.key-store-type=PKCS12
#server.ssl.trust-store=config/rsa_trust.jks
#server.ssl.trust-store-password=S1wquvxW1q+nqAJ3B+Ej/w==
#server.ssl.trust-store-type=JKS
server.ssl.protocol=TLS
server.ssl.client-auth= none
server.ssl.enabled=true
server.ssl.enabled-protocols=TLSv1.2,TLSv1.3,GMTLSv1.1
server.ssl.ciphers=SM2-WITH-SMS4-SM3,SM2-WITH-SMS4-GCM-SM3,ECDHE-ECDSA-AES256-GCM-SHA384,ECDHE-RSA-AES256-GCM-SHA384																												


#平台管控服务配置
remote.service.host=************
remote.service.port=28086

#host逗号分隔
brokers.host=************
brokers.port=9093
brokers.sasl_config.user=ccsp
brokers.sasl_config.auth=P2VhputoC550lMRQ2cGBqu0shNzL9ZuHtMmN9EIR6EM=
#分量
key.lmk1.value=+Rvtz3+unEEL0RfTtEsDBQ==
key.lmk2.path=config/lmk2
key.jwt.privateKey=EV8Nm+fZWFFDxSIRR01Fd/l6leCmvHlugm0Em79R6Uoj+XXXDE0v6iEhdyouNDOJ
key.jwt.publicKey=5aoHRmRiyIsyKHMETWDYlfk8Y56EFz3nZia5s0w7on0mIzej7y/YYF5cpkjS+zqcoHy3Rph8MnAcml9DhsznsEcGGjFiKhNFvmBv0nbJ9LxnWOafZzjRlDvQMcQ8pi3p


# 将配置项 放置 系统变量里面
environment.systemPropertyListKey[0]=key.lmk1.value
environment.systemPropertyListKey[1]=key.lmk2.path
environment.systemPropertyListKey[2]=tls.enable

# 需要解密的字段，只需要配置此处即可
environment.decryptionListKey[0]=spring.datasource.userName
environment.decryptionListKey[1]=spring.datasource.passWord
environment.decryptionListKey[2]=key.jwt.privateKey
environment.decryptionListKey[3]=key.jwt.publicKey
environment.decryptionListKey[4]=server.ssl.key-store-password
environment.decryptionListKey[5]=spring.redis.password
environment.decryptionListKey[6]=spring.redis.sentinel.password
environment.decryptionListKey[7]=brokers.sasl_config.auth
environment.decryptionListKey[10]=spring.kafka.producer.ssl.key-store-password
# httpsclinet 参数
http.connectTimeout=30000
http.socketTimeout=30000
http.connectionRequestTimeout=30000

# apisix 参数
apiSix.apiSsl=true
apiSix.apiPort=9180

spring.web.resources.static-locations=file:/opt/ccsp/download/

# 管理网关地址
gateway.mgt=************:8866
# 业务网关地址
gateway.busi=************:8866
svs.pt.gateway=************:8866
region.mode=1
#允许上传文件大小
spring.servlet.multipart.max-file-size=3GB
spring.servlet.multipart.max-request-size=3GB

spring.datasource.minEvictableIdleTimeMillis = 300000

# hadoop
hadoop.name-node=hdfs://************:9000
hadoop.namespace=/
hadoop.replication=3
hadoop.user=ccsp

# docker信息
# 允许上传的所有镜像最大数量GB
docker.image.allow-total-size=80

spring.kafka.bootstrap-servers=************:9092
spring.kafka.producer.retries=1
spring.kafka.producer.batch-size=16384
spring.kafka.producer.buffer-memory=33554432
spring.kafka.producer.acks=1
spring.kafka.producer.properties.request.timeout.ms=10000
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.consumer.group-id=ccspGroup
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

