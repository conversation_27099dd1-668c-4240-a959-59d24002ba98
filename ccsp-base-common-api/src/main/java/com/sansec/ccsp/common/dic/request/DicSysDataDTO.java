package com.sansec.ccsp.common.dic.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>aw<PERSON>
 * @Description: 系统数据表;
 * @Date: 2023-2-18
 */
@Data
public class DicSysDataDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 字典类型
     */
    private String dictType;
    /**
     * 字典标签
     */
    private String dictLabel;
    /**
     * 字典值
     */
    private String dictValue;
    /**
     * 是否默认 1默认 0不默认
     */
    private Integer defaultFlag;
    /**
     * 排序
     */
    private Integer sordNum;
    /**
     * 备注
     */
    @Size(max = 100, message = "备注长度限制100")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "备注请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
}
