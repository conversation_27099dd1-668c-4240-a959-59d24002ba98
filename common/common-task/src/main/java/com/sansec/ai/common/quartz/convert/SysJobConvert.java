package com.sansec.ai.common.quartz.convert;

import com.sansec.ai.common.quartz.entity.po.SysJobPO;
import com.sansec.ai.common.quartz.entity.request.JobCreateRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 定时任务表;(SYS_JOB)实体类转换接口
 * @Date: 2023-2-28
 */
@Mapper(componentModel = "spring")
public interface SysJobConvert {
	/**
	 * dtoToPo
	 *
	 * @param request
	 * @return
	 */
	@Mappings({})
	SysJobPO dtoToPo(JobCreateRequest request);

	/**
	 * poToDto
	 *
	 * @param sysJobPO
	 * @return
	 */
	JobCreateRequest poToDto(SysJobPO sysJobPO);

	/**
	 * poToDto-list
	 *
	 * @param list
	 * @return
	 */
	List<JobCreateRequest> poToDto(List<SysJobPO> list);
}