package com.sansec.ccsp.extra.business.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @description : 产品场景;
 * <AUTHOR> 李文波
 * @date : 2023-11-21
 */
@TableName("PRODUCT_SCENE")
@Data
public class ProductScenePO extends BasePO{
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 标题
     */
    private String title;
    /**
     * 场景图片;图片地址
     */
    private Long sceneIconId;
    /**
     * 排序
     */
    private Integer sordNum;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}