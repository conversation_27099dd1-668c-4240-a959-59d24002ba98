package com.sansec.ccsp.staticapi.screen.api;

import com.sansec.ccsp.staticapi.screen.api.fallback.ScreenIndexFallBack;
import com.sansec.ccsp.staticapi.screen.request.ScreenIndexDTO;
import com.sansec.ccsp.staticapi.screen.request.ScreenIndexPageDTO;
import com.sansec.ccsp.staticapi.screen.response.ScreenIndexVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description: 大屏展示指标管理接口
 * @Author: wwl
 * @CreateTime: 2023/07/07  15:10
 */
@FeignClient(name = "${spring.protocol.prefix}${spring.application.static.name}", path = "/ccsp/static", fallbackFactory = ScreenIndexFallBack.class)
public interface ScreenIndexApi {
    /**
     * @param screenIndexPageDTO
     * @return SecPageVO<ScreenIndexVO> 返回类型
     * @Description: 指标分页查询
     */
    @PostMapping(value = "/api/screenindex/v1/find")
    SecRestResponse<SecPageVO<ScreenIndexVO>> find(@RequestBody ScreenIndexPageDTO screenIndexPageDTO);

    /**
     * @param screenIndexDTO
     * @return SecRestResponse<Object> 返回类型
     * @Description: 指标添加
     */
    @PostMapping(value = "/api/screenindex/v1/add")
    SecRestResponse<Long> add(@RequestBody ScreenIndexDTO screenIndexDTO);

    /**
     * @param screenIndexDTO
     * @return SecRestResponse<Object> 返回类型
     * @Description: 指标编辑
     */
    @PostMapping(value = "/api/screenindex/v1/edit")
    SecRestResponse<Long> edit(@RequestBody ScreenIndexDTO screenIndexDTO);

    /**
     * @param screenIndexDTO
     * @return SecRestResponse<Object> 返回类型
     * @Description: 指标根据ID删除
     */
    @PostMapping(value = "/api/screenindex/v1/deleteById")
    SecRestResponse<Long> deleteById(@RequestBody ScreenIndexDTO screenIndexDTO);

    /**
     * 根据主键查询实例对象
     *
     * @param id
     * @return 实例对象
     */
    @PostMapping("/api/screenindex/v1/findById/{id}")
    SecRestResponse<ScreenIndexVO> findById(@PathVariable(value = "id") Long id);
}
