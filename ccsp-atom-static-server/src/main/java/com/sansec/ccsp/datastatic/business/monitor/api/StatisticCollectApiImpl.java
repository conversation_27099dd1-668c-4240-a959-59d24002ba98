package com.sansec.ccsp.datastatic.business.monitor.api;

import com.sansec.ccsp.datastatic.business.monitor.service.StatisticCollectService;
import com.sansec.ccsp.datastatic.business.monitor.service.StatisticMonitorDataService;
import com.sansec.ccsp.security.annotation.IgnoreToken;
import com.sansec.ccsp.staticapi.monitor.api.StatisticCollectApi;
import com.sansec.ccsp.staticapi.monitor.request.StatisticMonitorDataDTO;
import com.sansec.ccsp.staticapi.monitor.request.StatisticReportMonitorDataDTO;
import com.sansec.ccsp.staticapi.monitor.response.StatisticInfoResVO;
import com.sansec.ccsp.staticapi.monitor.response.StatisticMonitorDataLastListVo;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class StatisticCollectApiImpl implements StatisticCollectApi {

    @Resource
    StatisticCollectService statisticCollectService;
    @Resource
    StatisticMonitorDataService statisticMonitorDataService;

    @Override
    @IgnoreToken
    public void statisticCollect(StatisticInfoResVO statisticInfoResVO) {
        statisticCollectService.statisticCollect(statisticInfoResVO);
    }

    @Override
    public void addMonitorStatisticReportData(List<StatisticReportMonitorDataDTO> statisticReportMonitorDataDTOS) {
        statisticCollectService.addMonitorStatisticReportData(statisticReportMonitorDataDTOS);
    }

    @Override
    public SecRestResponse<List<StatisticMonitorDataLastListVo>> queryMonitorData(StatisticMonitorDataDTO request){
        return ResultUtil.ok(statisticMonitorDataService.queryMonitorData(request));
    }

}
