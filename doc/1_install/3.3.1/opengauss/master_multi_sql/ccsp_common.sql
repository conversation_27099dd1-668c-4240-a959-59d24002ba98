/*
 Navicat Premium Data Transfer

 Source Server         : ************ope<PERSON>uss
 Source Server Type    : PostgreSQL
 Source Server Version : 90204
 Source Host           : ************:5432
 Source Catalog        : ykytest
 Source Schema         : ccsp_common

 Target Server Type    : PostgreSQL
 Target Server Version : 90204
 File Encoding         : 65001

 Date: 26/04/2024 11:02:40
*/


-- ----------------------------
-- Table structure for auth_code_blacklist
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."auth_code_blacklist";
CREATE TABLE "ccsp_common"."auth_code_blacklist" (
  "id" int8 NOT NULL,
  "auth_code" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "crypto_auth_code" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."auth_code_blacklist"."auth_code" IS '明文';
COMMENT ON COLUMN "ccsp_common"."auth_code_blacklist"."crypto_auth_code" IS '密文';
COMMENT ON COLUMN "ccsp_common"."auth_code_blacklist"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_common"."auth_code_blacklist"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."auth_code_blacklist"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."auth_code_blacklist"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."auth_code_blacklist"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."auth_code_blacklist"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."auth_code_blacklist" IS '口令黑名单';

-- ----------------------------
-- Records of auth_code_blacklist
-- ----------------------------

-- ----------------------------
-- Table structure for ccsp_message
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."ccsp_message";
CREATE TABLE "ccsp_common"."ccsp_message" (
  "id" int8 NOT NULL,
  "source" int4 NOT NULL,
  "type" int4 NOT NULL,
  "status" int4 NOT NULL DEFAULT 1,
  "title" varchar(500) COLLATE "pg_catalog"."default",
  "content" varchar(2000) COLLATE "pg_catalog"."default" NOT NULL,
  "sender_id" int8,
  "sender_role_id" int8,
  "receiver_type" int4 NOT NULL,
  "receiver_id" int8 NOT NULL,
  "receiver_role_id" int8,
  "jump_url" varchar(500) COLLATE "pg_catalog"."default",
  "source_id" int8,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."id" IS '主键';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."source" IS '消息来源;1工单 2告警';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."type" IS '消息类型';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."status" IS '消息状态;1未读 2已读';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."title" IS '消息标题';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."content" IS '消息内容';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."sender_id" IS '消息发送者ID;用户ID、租户ID、或者空';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."sender_role_id" IS '消息发送者角色ID';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."receiver_type" IS '消息接收者类型;1平台 2租户';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."receiver_id" IS '消息接收者ID;0L为通知所有租户、1L为通知平台、租户ID';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."receiver_role_id" IS '消息接收者角色ID';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."jump_url" IS '跳转路径';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."source_id" IS '消息源ID，用于判断新增还是更新';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."ccsp_message"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."ccsp_message" IS '消息表';

-- ----------------------------
-- Records of ccsp_message
-- ----------------------------

-- ----------------------------
-- Table structure for config
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."config";
CREATE TABLE "ccsp_common"."config" (
  "config_id" int8 NOT NULL,
  "busi_service_type_id" int4 NOT NULL,
  "config_code" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "config_name" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "config_value" varchar(4000) COLLATE "pg_catalog"."default" NOT NULL,
  "config_type" int4 NOT NULL DEFAULT 1,
  "maintain_flag" int4 NOT NULL DEFAULT 1,
  "visible_flag" int4 NOT NULL DEFAULT 1,
  "sord_num" int4 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "tenant_id" int8,
  "app_id" int8
)
;
COMMENT ON COLUMN "ccsp_common"."config"."config_id" IS '主键';
COMMENT ON COLUMN "ccsp_common"."config"."busi_service_type_id" IS '业务服务类型id';
COMMENT ON COLUMN "ccsp_common"."config"."config_code" IS '配置编码';
COMMENT ON COLUMN "ccsp_common"."config"."config_name" IS '配置名';
COMMENT ON COLUMN "ccsp_common"."config"."config_value" IS '配置值';
COMMENT ON COLUMN "ccsp_common"."config"."config_type" IS '配置类型;0明文1密文';
COMMENT ON COLUMN "ccsp_common"."config"."maintain_flag" IS '可维护标志;0：不可维护1：可维护';
COMMENT ON COLUMN "ccsp_common"."config"."visible_flag" IS '可见标志；0：不可见；1：可见';
COMMENT ON COLUMN "ccsp_common"."config"."sord_num" IS '排序';
COMMENT ON COLUMN "ccsp_common"."config"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."config"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."config"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."config"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."config"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."config" IS '配置表';

-- ----------------------------
-- Records of config
-- ----------------------------
INSERT INTO "ccsp_common"."config" VALUES (1, 7, 'sys_operation_mode', '系统运行模式', '2', 0, 0, 0, 1, '1：传统模式、2：租户模式', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (2, 7, 'app_select_group', '应用业务选择组类型', '3', 0, 0, 0, 2, '1：设备组、2：服务组、3：全选', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (3, 5, 'forceUpdatePassword', '是否强制修改默认口令', 'false', 0, 1, 0, 10, '是否强制修改默认口令', NULL, NULL, NULL, '2023-04-27 17:32:00', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5443286102928657799, 5, 'loginFailuresAllowedTimes', '登录失败次数限制', '5', 0, 1, 0, 7, '登录失败次数限制', NULL, '2023-02-21 13:49:27', NULL, '2023-04-23 16:38:52', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5443288543812259206, 5, 'loginErrorLockTime', '登录失败锁定时长(分钟)', '5', 0, 1, 0, 8, '登录失败锁定时长(分钟)', NULL, '2023-02-21 13:50:40', NULL, '2023-04-23 16:36:55', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5443290616301751680, 5, 'authCodeExpireDate', '口令有效期', '365', 0, 1, 0, 5, '口令有效期', NULL, '2023-02-21 13:51:41', NULL, '2023-04-23 18:25:45', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5443291530022489481, 5, 'authCodeExpirationReminder', '口令有效期告警', '1', 0, 1, 0, 6, '口令有效期告警', NULL, '2023-02-21 13:52:09', NULL, '2023-04-23 16:44:57', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5443294032579136905, 5, 'unloggedTime', '长时间未登录禁用账户', '365', 0, 1, 0, 4, '长时间未登录禁用账户', NULL, '2023-02-21 13:53:23', NULL, '2023-04-23 16:47:36', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5443295512296033673, 5, 'authCodeHistoryLimit', '历史口令限制', '1', 0, 1, 0, 3, '历史口令限制', NULL, '2023-02-21 13:54:07', NULL, '2023-05-30 10:44:43', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5443296783572798856, 5, 'openAuthCodeLogin', '是否开启口令登录', 'true', 0, 1, 0, 1, '是否开启口令登录', NULL, '2023-02-21 13:54:45', NULL, '2023-04-23 18:24:50', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5443298085182770564, 5, 'openUKeyLogin', '是否开启UKey登录', 'false', 0, 1, 0, 2, '是否开启UKey登录', NULL, '2023-02-21 13:55:24', NULL, '2023-04-27 17:53:27', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5445743318561983874, 5, 'defaultAuthCode', '默认口令', 'HTxv8wkDyjjbObx+bvlCFwVP9+Fh+E1kzMob9U1r7TxYXa+7D4DesEfLlVxo9VQuv80mlWEkC7o7RSNkLrPWK+j6fsHLu2UADQu+DSxFGtQ=', 1, 1, 0, 2, '默认口令', NULL, '2023-02-22 10:09:58', NULL, '2023-04-28 17:25:56', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049690000640111, 0, 'quota_alarm_percentage', '大屏配额告警百分比', '50', 0, 0, 0, 0, '大屏配额告警百分比值为：0-100', NULL, '2023-07-07 18:32:57', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049690000640222, 0, 'screen_quota_scroll_flag', '大屏滚动告警是否开启', 'true', 0, 1, 1, 1, '大屏滚动告警是否开启：true false', NULL, '2023-07-07 18:32:57', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049690000640333, 0, 'db_init_check_time', '数据库初始化结果查询最大时间(秒)', '1800', 0, 0, 0, 1, '数据库初始化结果查询最大时间(秒)', NULL, '2023-07-07 18:32:57', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049690000640603, 0, 'screen_template_code', '大屏模板编号', 'default', 0, 0, 0, 0, '大屏模板编号', NULL, '2023-07-07 18:32:57', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049693950640600, 0, 'platformId', '平台ID', 'Rb8XPMwCex+Lt42qjVO37A==', 1, 1, 0, 15, '平台ID', NULL, '2023-03-01 18:32:57', NULL, '2023-09-26 10:36:04', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049693950640601, 0, 'start_time', '有效期开始时间', 'Rb8XPMwCex+Lt42qjVO37A==', 1, 1, 0, 16, '平台有效期', NULL, '2023-03-01 18:32:57', NULL, '2023-06-27 15:18:02', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049693950640602, 0, 'end_time', '有效期结束时间', 'Rb8XPMwCex+Lt42qjVO37A==', 1, 1, 0, 17, '平台有效期', NULL, '2023-03-01 18:32:57', NULL, '2023-06-27 15:18:02', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049693950640603, 0, 'platform_period_type', '平台许可类型', '2', 0, 0, 0, 16, '平台许可类型1-永久授权, 2-按年授权', NULL, '2023-03-01 18:32:57', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049693950640604, 0, 'server_period_type', '服务许可类型', '2', 0, 0, 0, 17, '服务许可类型1-永久授权, 2-按年授权', NULL, '2023-03-01 18:32:57', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049693950640605, 0, 'platfrom_alarm_day', '平台许可告警天数', '30', 0, 1, 1, 18, '平台有效期告警天数', NULL, '2023-03-01 18:32:57', NULL, '2023-05-30 10:44:59', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049693950640606, 0, 'server_alarm_day', '服务许可告警天数', '30', 0, 1, 1, 19, '平台服务有效期告警天数', NULL, '2023-03-01 18:32:57', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467049693950640642, 0, 'auth_pk', '认证公钥', '5aoHRmRiyIsyKHMETWDYlfk8Y56EFz3nZia5s0w7on0mIzej7y/YYF5cpkjS+zqcoHy3Rph8MnAcml9DhsznsEcGGjFiKhNFvmBv0nbJ9LxnWOafZzjRlDvQMcQ8pi3p', 1, 0, 0, 20, '认证公钥', NULL, '2023-03-01 18:32:57', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467051745233732105, 0, 'auth_prik', '认证私钥', 'EV8Nm+fZWFFDxSIRR01Fd/l6leCmvHlugm0Em79R6Uoj+XXXDE0v6iEhdyouNDOJ', 1, 0, 0, 21, '认证私钥', NULL, '2023-03-01 18:33:58', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5467205735288409733, 0, 'auth_pk_algo', '认证公钥算法', 'SM2', 0, 0, 0, 23, '认证公钥算法', NULL, '2023-03-01 06:50:27', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492671023313322497, 2, 'auth_pk_algo_device', '设备认证公钥算法', '180fKjE62Ga+fJ0S/G7PmA==', 1, 0, 0, 23, '设备认证公钥算法', NULL, '2023-03-10 14:39:12', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492747784646558208, 2, 'auth_pk_device', '设备认证公钥', 'rggIj2Nrf5CZKt8jW1HU9OaFSPBeuSyYvZyqDWBfuQp12gWS85TsP6NbLvgyzM4Nrl7eWjhdp5Pby6pVP2dY2g==', 0, 0, 0, 21, '设备认证公钥', NULL, '2023-03-10 15:17:20', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406976, 2, 'auth_prik_device', '设备认证私钥', 'nzuGjbRg72gCBRA/3UpF+MhdrDrQkIziepE8nmFZfPo=', 0, 0, 0, 23, '设备认证私钥', NULL, '2023-03-10 15:17:50', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406977, 2, 'async_pk_device', '设备2号非对称公钥', 'fn54acAlL+H85iDLiAhv0IkNetMphiLh0goQOBDxnBDaOjDPlqY0Zn5wcSxll67f/mdzSmKrDFh2DCRt8p13Ur5YQWU4At5kEOA5mTzKH6KWKSMQgRw8251p7BEGgKHL', 1, 0, 0, 24, '设备导入2号非对称公钥', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406978, 2, 'async_prik_device', '设备2号非对称私钥', 'DHou5TK4PeUj4NsdUqm0V5BoIVtjrAa4Ycg8hoKd4Q+dsEkQ7rwmT7rj9SGDYNkD', 1, 0, 0, 25, '设备导入2号非对称私钥', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406979, 2, 'sync_key_component', '设备2号对称密钥分量', 'A5c9MfgftIuuqhuKrm3ghKpDfBlpoQvB6//+JMy2fEa58bpvAfyLv9RuZUpgTP/7', 1, 0, 0, 26, '设备2号对称密钥分量', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406980, 2, 'async_key_device_flag', '是否开启导入2号非对称密钥', 'true', 0, 0, 0, 27, '是否开启导入2号非对称密钥', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406981, 2, 'sync_key_component_flag', '是否开启导入2号对称密钥', 'true', 0, 0, 0, 28, '是否开启导入2号对称密钥', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406982, 2, 'x_api_key_val', 'admin API请求头参数', 'zM7MYRiXXu7pgCPspRhR70oMk0sJQgTU7wXIhD0e4NNz8GGRvSvd+Qw/X2NQBq2p', 1, 0, 0, 29, 'admin API请求头参数', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406983, 1, 'api_token_expire', '接口token有效期', '10', 0, 1, 1, 30, '接口token有效期', NULL, NULL, NULL, '2023-05-30 10:45:07', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406984, 1, 'web_token_expire', '页面token有效期', '10', 0, 1, 1, 31, '页面token有效期', NULL, NULL, NULL, '2023-07-07 16:03:47', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406985, 3, 'kms_secstorage_is_merge', '密钥管理与文件加密服务是否合并', 'true', 0, 0, 0, 32, '密钥管理与文件加密服务是否合并 true false', NULL, NULL, NULL, '2023-05-05 10:18:04', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406986, 3, 'add_service_is_auto_add_device', '添加服务是否自动添加设备', 'false', 0, 0, 0, 33, '添加服务是否自动添加设备 true false', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406987, 8, 'service_is_bind_license', '服务是否绑定许可证', 'false', 0, 0, 0, 34, '服务是否绑定许可证 true false', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406988, 3, 'crypto_service_is_exist_kms', '密码机服务是否存在KMS', 'false', 0, 0, 0, 35, '密码机服务是否存在KMS true false', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406989, 1, 'tenant_register_audit_no_busi_type', '注册租户审核时，不选择业务服务类型', 'true', 0, 0, 0, 31, '注册租户审核时，不选择业务服务类型 true false', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406990, 4, 'app_service_type_to_busi_type', '添加应用时，上传服务类型ID，换算成业务类型和对应服务组', 'false', 0, 0, 0, 31, '添加应用时，上传服务类型ID，换算成业务类型和对应服务组 true false', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406991, 9, 'incre_record_clear_time', '增量记录表清理时间', '1', 0, 0, 0, 38, '清理几天前记录，默认1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406992, 9, 'incre_cal_clear_time', '增量计算表清理时间', '7', 0, 0, 0, 39, '清理几天前记录，默认7', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406994, 9, 'unincre_cal_clear_time', '非增量记录表清理时间', '1', 0, 0, 0, 41, '清理几小时内的记录，默认1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406995, 2, 'is_show_device_group', '前端页面是否显示设备组相关数据', 'true', 0, 0, 0, 42, '前端页面是否显示设备组相关数据 true false', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406996, 3, 'is_show_service_group', '前端页面是否显示服务组相关数据', 'true', 0, 0, 0, 43, '前端页面是否显示服务组相关数据 true false', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406997, 3, 'is_chose_active_standby', '服务操作是否选择主备', 'false', 0, 0, 0, 44, '服务操作是否选择主备 true false', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406998, 9, 'ccsp_init_time', '平台初始化时间', '2024-04-24 18:16:52', 0, 1, 0, 45, '平台初始化时修改该字段', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601406999, 2, 'device_invoke_port', '服务下发设备端口取值方式', 'busi', 0, 0, 0, 46, '给服务下发设备连接端口的取值：busi：服务端口；extend：扩展端口固定为8008', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407000, 2, 'device_auto_add_no_type', '自动添加设备时排除类型', '12', 0, 0, 0, 46, '添加服务自动添加分配设备时，排除配置的设备类型；输入设备类型ID字符串逗号隔开', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407001, 3, 'serviceGroupOnlyBusi', '服务组单业务类型', 'false', 0, 0, 0, 47, '服务组单业务类型', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407002, 3, 'serviceGroupDataIsolation', '服务组是否数据隔离', 'true', 0, 0, 0, 48, '服务组数据隔离', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407003, 3, 'crypto_service_use_kms_type', '密码机服务调用kms的形式', '2', 0, 0, 0, 49, '密码机服务调用kms的形式 1调用本机kms  2只调用远程kms   3先调本机kms报错再调远程kms', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407004, 3, 'is_need_db_map_ip', '连接数据库是否需要映射地址', 'false', 0, 1, 1, 50, '连接数据库是否需要映射地址', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407005, 3, 'need_db_map_ip_service_type_id', '使用数据库映射地址的服务类型', '1,2,3,4,5,6,7,8,9,10,11', 0, 0, 0, 51, '服务类型以类型ID逗号隔开输入，服务类型ID对应关系如下：1:加解密服务;2:签名验签服务;4:密钥管理服务;5:时间戳服务;6:协同签名服务7:动态令牌服务;8:数据库加密服务;9:文件加密服务;10:电子签章服务;11:SSLVPN加密通道服务;12:数字证书认证服务;', NULL, NULL, NULL, '2023-10-30 14:26:12', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407006, 7, 'db_gauss_casename', '高斯数据库实例名称', 'sansec', 0, 0, 0, 52, '若数据库是gauss，需要多配置一个实例名称', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407007, 0, 'region_mode', '区域模式', '0', 0, 1, 0, 54, '0：无区域；1：平台内多个区域；2：子平台对应一个区域', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407008, 5, 'openSimShieldLogin', 'SIM盾登录配置', 'false', 0, 1, 0, 55, '0不开启 1开启', NULL, NULL, NULL, '2023-09-26 10:36:54', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407009, 5, 'openSimKeyLogin', 'SIMKEY登录配置', 'false', 0, 1, 0, 56, '0不开启 1开启', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407010, 0, 'ccspSimCallBackUrl', 'SIM登录回调地址', 'https://124.133.247.106:29001/ccsp/pt', 0, 0, 0, 62, 'SIM登录回调地址', NULL, NULL, NULL, '2023-09-26 10:37:42', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407011, 1, 'ccsp_root_cert_private_key', '平台根证私钥', '2WcMAUH6u+ujOwr/jaGxwP9neMyH7NyegFC5Gt7xrYjafveTVsk4WhCFJEXcN1zQ', 1, 0, 0, 58, '平台根证私钥', NULL, '2023-09-20 19:26:38', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407012, 1, 'ccsp_root_cert_public_key', '平台根证公钥', 'd8zr+kZm0L0jXwVJIzigLaCTNWWLtm/BpL+PY9WSvJOcY6VJNffT+ghk4U7CKwl8wsDwODoercKMebvg7bM5zECybPmdBDOZqo4fqRjDNa14Vj4ZJ6FADzqOzELGpvNB', 1, 0, 0, 59, '平台根证公钥', NULL, '2023-09-20 19:27:46', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407013, 3, 'tcpServerPort', '预占nginx-tcp端口', '41000,41001,41002,41003,41004,41005,41006,41007,41008,41009,41010,41011,41012,41013,41014,41015,41016,41017,41018,41019,41020,41021,41022,41023,41024,41025,41026,41027,41028,41029,41030,41031,41032,41033,41034,41035,41036,41037,41038,41039,41040,41041,41042,41043,41044,41045,41046,41047,41048,41049,41050,41051,41052,41053,41054,41055,41056,41057,41058,41059', 0, 0, 0, 60, '预占nginx-tcp端口', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407014, 3, 'tcpApisixServerPort', '预占apisix-tcp端口', '51000,51001,51002,51003,51004,51005,51006,51007,51008,51009,51010,51011,51012,51013,51014,51015,51016,51017,51018,51019,51020,51021,51022,51023,51024,51025,51026,51027,51028,51029,51030,51031,51032,51033,51034,51035,51036,51037,51038,51039,51040,51041,51042,51043,51044,51045,51046,51047,51048,51049,51050,51051,51052,51053,51054,51055,51056,51057,51058,51059', 0, 0, 0, 61, '预占apisix-tcp端口', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407015, 8, 'pt_config_mode', '移动定制接口策略', '0', 0, 0, 0, 53, '平台配置策略：0；平台标准策略；1：移动定制配置', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407016, 4, 'app_aksk_key_num', '应用可添加AKSK密钥数量', '10', 0, 0, 0, 54, '应用可添加AKSK密钥数量', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407017, 7, 'db_kingbase_casename', '金仓数据库实例名称', 'SANSEC', 0, 0, 0, 62, '若数据库是kingbase，需要多配置一个实例名称', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407018, 0, 'pt_has_region', '平台是否包含多区域', 'false', 0, 1, 1, 63, 'false：无区域；true：平台内多个区域；2：子平台对应一个区域', NULL, NULL, NULL, '2023-10-11 21:29:42', NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407019, 0, 'simShieldBaseUrl', 'sim盾域名', 'https://sim.cmccsim.com', 0, 0, 0, 64, 'sim盾域名', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407020, 0, 'simShieldKeyReference', '证书机构key', 'B8FB235084DDBBCCAFE6F2B3B5E63637DBDED6NJ', 0, 0, 0, 65, '证书机构key', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407021, 0, 'simShieldApId', 'sim盾平台应用Id', '0167', 0, 0, 0, 66, 'sim盾平台应用Id', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407022, 0, 'simShieldPlatformPublicKey', 'sim盾平台公钥', 'TZjhmTl//I4drTUodWxh1GsGcfidRWtuuX2YzEdvtNpCzWi2PirL4+UXrWtLWGQIIS1or8E2iRYuvU95zrFR3tlO77sbQoewRhfXQ0OCU38Iu//+mBXCsXy0J+AUihYTI0EGdpjFNZflqh5IKWZVFlD+KICIQF37CFTvU0A6Rof3l8f8Iu98TPw/FvgqDLwQiUk9KP9DBKwVgbOcgRQwBG/hj1iwQX2u7QiH4Ad/H6AGYKgKtbtxtZsvHh1CGpYByzW5jyU8o7sHfysk7bmhK5Im+7M8w3BhcskI1pQWPt8=', 1, 0, 0, 67, 'sim盾平台公钥', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407023, 0, 'simShieldPlatformPrivateKey', 'sim盾平台私钥', '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', 1, 0, 0, 68, 'sim盾平台私钥', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407025, 0, 'upgradeAuthCode', '数据库服务升级密码', 'IdCF5A1OLh7ntrpVIEmd6A==', 1, 0, 0, 70, '数据库服务升级密码', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407026, 0, 'workOrderAttachmentSize', '工单模块附件大小，单位MB', '100', 0, 0, 0, 71, '工单模块附件大小', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407027, 0, 'workOrderAttachmentType', '工单模块附件允许上传文件类型', '.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.jpg,.jpeg,.png,.xml,.pfx,.rar,.zip', 0, 0, 0, 72, '工单模块附件允许上传文件类型', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407028, 0, 'productAttachmentType', '密码产品模块附件允许上传文件类型', '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.rar,.zip,.gif', 0, 0, 0, 73, '密码产品附件允许上传文件类型', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407029, 0, 'is_service_share', '是否共享服务', '1', 0, 0, 0, 74, '0：密码服务不支持共享；1：密码服务支持共享', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407030, 0, 'is_state_mode', '是否政务模式', '0', 0, 0, 0, 75, '0：非政务模式，应用名称保持不变；1：政务模式，应用改为授权账号', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407031, 0, 'has_organization', '是否包含组织管理', '1', 0, 0, 0, 75, '0：不包含，无单位选择；1：包含，租户属于单位', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407032, 0, 'has_product', '是否包含密码产品', '1', 0, 0, 0, 77, '0：不包含，租户绑定共享组；1：包含，租户开通密码产品', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407033, 0, 'download_temp_path', '平台从HDFS下载文件时的暂存目录', '/opt/ccsp/temp/', 0, 1, 1, 78, '平台从HDFS下载文件时的暂存目录', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config" VALUES (5492748794601407034,0,'system_init_end','是否初始化配置完成','0',0,0,0,79,'是否初始化配置完成',NULL,NULL,NULL,NULL,NULL,NULL);

-- ----------------------------
-- Table structure for config_regular
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."config_regular";
CREATE TABLE "ccsp_common"."config_regular" (
  "id" int8 NOT NULL,
  "config_code" varchar(255) COLLATE "pg_catalog"."default",
  "regular" varchar(255) COLLATE "pg_catalog"."default",
  "invalid_flag" int4 DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."config_regular"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_common"."config_regular"."config_code" IS '配置编码';
COMMENT ON COLUMN "ccsp_common"."config_regular"."regular" IS '正则表达式';
COMMENT ON COLUMN "ccsp_common"."config_regular"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_common"."config_regular"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."config_regular"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."config_regular"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."config_regular"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."config_regular"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."config_regular" IS '配置表正则校验';

-- ----------------------------
-- Records of config_regular
-- ----------------------------
INSERT INTO "ccsp_common"."config_regular" VALUES (1, 'openAuthCodeLogin', '^(true)|(false)$', 0, '是否开启口令登录，truefalse', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (2, 'defaultAuthCode', NULL, 1, '默认口令', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (3, 'openUKeyLogin', '^(true)|(false)$', 0, '是否开启UKey登录,，truefalse', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (4, 'authCodeHistoryLimit', '^([1-9]|[1-9][0-9]|100)$', 0, '历史口令限制，[1,100]', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (5, 'unloggedTime', '^([1-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '长时间未登录禁用租户，[1,365]', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (6, 'authCodeExpireDate', '^([1-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '口令有效期，[1,365]', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (7, 'authCodeExpirationReminder', '^([1-9]|[1-2][0-9]|30)$', 0, '口令有效期告警，[1,30]', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (8, 'loginFailuresAllowedTimes', '^([3-9]|[1-2][0-9]|30)$', 0, '登陆失败次数限制，[3,30]', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (9, 'loginErrorLockTime', '^([5-9]|[1-2][0-9]|30)$', 0, '登陆失败锁定时长(分钟)，[5,30]', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (10, 'forceUpdatePassword', '^(true)|(false)$', 0, '是否强制修改默认口令，truefalse', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (11, 'screen_template_code', NULL, 1, '大屏模板编号', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (12, 'quota_alarm_percentage', '^([1-9]|[1-9][0-9]|100)$', 0, '大屏配额告警百分比，[1,100]', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (13, 'screen_quota_scroll_flag', '^(true)|(false)$', 0, '大屏滚动告警是否开启，truefalse', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (14, 'platfrom_alarm_day', '^([7-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '平台许可告警天数，[7,365]', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (15, 'server_alarm_day', '^([7-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '服务许可告警天数，[7,365]', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (16, 'api_token_expire', '^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|1[0-9][0-9][0-7][0-9])|10080+$', 0, '接口token有效期，[1,10080]', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."config_regular" VALUES (17, 'web_token_expire', '^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|1[0-9][0-9][0-7][0-9])|10080+$', 0, '页面token有效期，[1,10080]', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_busi_service_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_busi_service_type";
CREATE TABLE "ccsp_common"."dic_busi_service_type" (
  "id" int8 NOT NULL,
  "parent_id" int4 NOT NULL,
  "busi_service_type_code" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "busi_service_type_name" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "sord_num" int4 NOT NULL,
  "remark" varchar(300) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_busi_service_type"."id" IS '主键';
COMMENT ON COLUMN "ccsp_common"."dic_busi_service_type"."parent_id" IS '父级ID';
COMMENT ON COLUMN "ccsp_common"."dic_busi_service_type"."busi_service_type_code" IS '业务服务类型编码';
COMMENT ON COLUMN "ccsp_common"."dic_busi_service_type"."busi_service_type_name" IS '业务服务类型名称';
COMMENT ON COLUMN "ccsp_common"."dic_busi_service_type"."sord_num" IS '排序';
COMMENT ON COLUMN "ccsp_common"."dic_busi_service_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."dic_busi_service_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_busi_service_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_busi_service_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_busi_service_type"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_busi_service_type" IS '业务服务类型字典表';

-- ----------------------------
-- Records of dic_busi_service_type
-- ----------------------------
INSERT INTO "ccsp_common"."dic_busi_service_type" VALUES (0, 1, 'common', '公共', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_service_type" VALUES (1, 1, 'tenant', '租户', 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_service_type" VALUES (2, 1, 'device', '设备', 2, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_service_type" VALUES (3, 1, 'servicemgt', '服务', 3, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_service_type" VALUES (4, 1, 'app', '应用', 4, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_service_type" VALUES (5, 1, 'login', '登录', 5, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_service_type" VALUES (6, 1, 'user', '用户', 6, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_service_type" VALUES (7, 0, 'sysparam', '系统配置', 7, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_service_type" VALUES (8, 1, 'license', '许可证', 8, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_service_type" VALUES (9, 1, 'statistic', '统计', 9, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_busi_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_busi_type";
CREATE TABLE "ccsp_common"."dic_busi_type" (
  "id" int8 NOT NULL,
  "busi_type_name" varchar(270) COLLATE "pg_catalog"."default" NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_busi_type"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_common"."dic_busi_type"."busi_type_name" IS '业务类型名称';
COMMENT ON COLUMN "ccsp_common"."dic_busi_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."dic_busi_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_busi_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_busi_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_busi_type"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_busi_type" IS '业务类型字典表';

-- ----------------------------
-- Records of dic_busi_type
-- ----------------------------
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (1, '数据加解密', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (2, '签名验签', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (4, '密钥管理', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (5, '时间戳', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (6, '协同签名', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (7, '动态令牌', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (8, '数据库加密', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (9, '文件加密', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (10, '电子签章', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (11, 'SSLVPN加密通道', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_busi_type" VALUES (12, '数字证书认证', NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_device_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_device_type";
CREATE TABLE "ccsp_common"."dic_device_type" (
  "id" int8 NOT NULL,
  "device_type_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "default_flag" int4 NOT NULL DEFAULT 0,
  "device_type_value" int4,
  "support_busi_types" varchar(1000) COLLATE "pg_catalog"."default",
  "code" varchar(120) COLLATE "pg_catalog"."default",
  "is_physical" numeric(8,0),
  "vendor_id" int8 NOT NULL,
  "read_info" int4,
  "need_password" int4,
  "key_templet_ids" text COLLATE "pg_catalog"."default",
  "support_snmp" int4,
  "hccs_image" varchar(120) COLLATE "pg_catalog"."default",
  "support_main_key" int4,
  "support_sec_manage" int4,
  "default_key_templet_ids" varchar(500) COLLATE "pg_catalog"."default",
  "support_gen_key" int4,
  "parent_id" int4,
  "invalid_flag" int4 DEFAULT 0,
  "family_type" int4,
  "own_manage_flag" int4,
  "machine_type" int4,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "extend1" varchar(1000) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."id" IS '主键';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."device_type_name" IS '名称';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."default_flag" IS '是否默认 1默认 0不默认';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."support_busi_types" IS '支持的业务类型id，多个类型以逗号分隔';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."code" IS '设备类型编码，与厂商编码共同使用，获取设备信息，不可修改';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."is_physical" IS '业务端口是否需要密码1需要 0不需要';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."vendor_id" IS '所属厂商';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."read_info" IS '读取设备信息 1需要 0不需要';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."key_templet_ids" IS '该类型设备支持的密钥模板id，多个以逗号分隔';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."support_snmp" IS '是否支持SNMP监控0不支持 1支持';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."hccs_image" IS '超融合虚拟机镜像类型';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."support_main_key" IS '是否支持生成主密钥0不支持 1支持';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."support_sec_manage" IS '是否支持安全管理，0不支持 1支持';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."default_key_templet_ids" IS '默认自动生成密钥时支持的密钥模板id，多个以逗号分隔';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."support_gen_key" IS '是否支持生成密钥';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."parent_id" IS '上级ID顶级默认0';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."invalid_flag" IS '无效标识 0可用 1无效';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."family_type" IS '1云密码机、2物理机、3虚拟机、4门禁卡 99其他';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."own_manage_flag" IS '(是否监管 1监管 0只登记不监管,用于资产登记) 备用';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."machine_type" IS '密码机服务类型(提供密码服务使用) 6服务器密码机 12签名验签服务器 15时间戳服务器';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_common"."dic_device_type"."extend1" IS '备用';
COMMENT ON TABLE "ccsp_common"."dic_device_type" IS '设备类型字典表';

-- ----------------------------
-- Records of dic_device_type
-- ----------------------------

-- ----------------------------
-- Table structure for dic_gentype
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_gentype";
CREATE TABLE "ccsp_common"."dic_gentype" (
  "id" int8 NOT NULL,
  "d_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "d_value" int4 NOT NULL,
  "issym" int4,
  "default_flag" int4 NOT NULL DEFAULT 0,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_gentype"."id" IS '主键;';
COMMENT ON COLUMN "ccsp_common"."dic_gentype"."d_name" IS '名称';
COMMENT ON COLUMN "ccsp_common"."dic_gentype"."d_value" IS '1 随机数 0 分量';
COMMENT ON COLUMN "ccsp_common"."dic_gentype"."issym" IS '1 对称';
COMMENT ON COLUMN "ccsp_common"."dic_gentype"."default_flag" IS '是否默认 1默认 0不默认';
COMMENT ON COLUMN "ccsp_common"."dic_gentype"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_gentype"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_gentype"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_gentype"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_gentype" IS '密钥生成类型字典表';

-- ----------------------------
-- Records of dic_gentype
-- ----------------------------

-- ----------------------------
-- Table structure for dic_keyalgo
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_keyalgo";
CREATE TABLE "ccsp_common"."dic_keyalgo" (
  "id" int8 NOT NULL,
  "d_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "d_value" int4 NOT NULL,
  "d_length_id" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "d_gentype_id" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "d_keyuse_id" varchar(120) COLLATE "pg_catalog"."default" NOT NULL,
  "kms_value" int4 NOT NULL,
  "default_flag" int4 NOT NULL DEFAULT 0,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."d_name" IS '名称';
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."d_value" IS '1 对称 0 非对称';
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."d_length_id" IS '长度id 多个，分割';
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."d_gentype_id" IS '生成方式 0 分量 1 随机数';
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."d_keyuse_id" IS '密钥用途 多个,分割';
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."kms_value" IS 'KMS字典值';
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."default_flag" IS '是否默认 1默认 0不默认';
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_keyalgo"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_keyalgo" IS '密钥算法字典表';

-- ----------------------------
-- Records of dic_keyalgo
-- ----------------------------

-- ----------------------------
-- Table structure for dic_keylen
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_keylen";
CREATE TABLE "ccsp_common"."dic_keylen" (
  "id" int8 NOT NULL,
  "d_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "d_value" int4 NOT NULL,
  "default_flag" int4 NOT NULL DEFAULT 0,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_keylen"."d_name" IS '算法名称';
COMMENT ON COLUMN "ccsp_common"."dic_keylen"."d_value" IS '算法长度';
COMMENT ON COLUMN "ccsp_common"."dic_keylen"."default_flag" IS '是否默认 1默认 0不默认';
COMMENT ON COLUMN "ccsp_common"."dic_keylen"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_keylen"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_keylen"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_keylen"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_keylen" IS '密钥长度字典表';

-- ----------------------------
-- Records of dic_keylen
-- ----------------------------

-- ----------------------------
-- Table structure for dic_keylifeattribute
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_keylifeattribute";
CREATE TABLE "ccsp_common"."dic_keylifeattribute" (
  "id" int8 NOT NULL,
  "d_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "d_value" int4 NOT NULL,
  "default_flag" int4 NOT NULL DEFAULT 0,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_keylifeattribute"."d_name" IS '名称';
COMMENT ON COLUMN "ccsp_common"."dic_keylifeattribute"."d_value" IS '1 对称 0 非对称';
COMMENT ON COLUMN "ccsp_common"."dic_keylifeattribute"."default_flag" IS '是否默认 1默认 0不默认';
COMMENT ON COLUMN "ccsp_common"."dic_keylifeattribute"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_keylifeattribute"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_keylifeattribute"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_keylifeattribute"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_keylifeattribute" IS '密钥生命周期属性字典表';

-- ----------------------------
-- Records of dic_keylifeattribute
-- ----------------------------

-- ----------------------------
-- Table structure for dic_keytype
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_keytype";
CREATE TABLE "ccsp_common"."dic_keytype" (
  "id" int8 NOT NULL,
  "d_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "d_value" int4 NOT NULL,
  "default_flag" int4 NOT NULL DEFAULT 0,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_keytype"."d_name" IS '名称';
COMMENT ON COLUMN "ccsp_common"."dic_keytype"."d_value" IS '1 对称 0 非对称';
COMMENT ON COLUMN "ccsp_common"."dic_keytype"."default_flag" IS '是否默认 1默认 0不默认';
COMMENT ON COLUMN "ccsp_common"."dic_keytype"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_keytype"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_keytype"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_keytype"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_keytype" IS '密钥类型字典表';

-- ----------------------------
-- Records of dic_keytype
-- ----------------------------

-- ----------------------------
-- Table structure for dic_keyuse
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_keyuse";
CREATE TABLE "ccsp_common"."dic_keyuse" (
  "id" int8 NOT NULL,
  "d_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "d_value" int4 NOT NULL,
  "issym" int4 NOT NULL,
  "kms_value" int4 NOT NULL,
  "default_flag" int4 NOT NULL DEFAULT 0,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_keyuse"."d_name" IS '名称';
COMMENT ON COLUMN "ccsp_common"."dic_keyuse"."d_value" IS '1 对称 0 非对称';
COMMENT ON COLUMN "ccsp_common"."dic_keyuse"."issym" IS '1对称 0 非对称';
COMMENT ON COLUMN "ccsp_common"."dic_keyuse"."kms_value" IS 'kms字典值';
COMMENT ON COLUMN "ccsp_common"."dic_keyuse"."default_flag" IS '是否默认 1默认 0不默认';
COMMENT ON COLUMN "ccsp_common"."dic_keyuse"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_keyuse"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_keyuse"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_keyuse"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_keyuse" IS '密钥用途字典表';

-- ----------------------------
-- Records of dic_keyuse
-- ----------------------------

-- ----------------------------
-- Table structure for dic_script_path
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_script_path";
CREATE TABLE "ccsp_common"."dic_script_path" (
  "id" int8 NOT NULL,
  "service_type_id" int8 DEFAULT 1,
  "script_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "script_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "script_value" varchar(1000) COLLATE "pg_catalog"."default" NOT NULL,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_script_path"."id" IS '主键';
COMMENT ON COLUMN "ccsp_common"."dic_script_path"."service_type_id" IS '服务类型主键';
COMMENT ON COLUMN "ccsp_common"."dic_script_path"."script_code" IS '脚本编码';
COMMENT ON COLUMN "ccsp_common"."dic_script_path"."script_name" IS '脚本名';
COMMENT ON COLUMN "ccsp_common"."dic_script_path"."script_value" IS '脚本值';
COMMENT ON COLUMN "ccsp_common"."dic_script_path"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_script_path"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_script_path"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_script_path"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_script_path" IS '配置表';

-- ----------------------------
-- Records of dic_script_path
-- ----------------------------
INSERT INTO "ccsp_common"."dic_script_path" VALUES (3, 1, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/pki/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (4, 2, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/svs/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (5, 3, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/digest/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (6, 4, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/kms/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (7, 5, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/tsa/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (8, 6, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/sms/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (9, 7, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/secauth/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (10, 8, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/secdbhsm/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (11, 9, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/storage/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (12, 10, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/electronicSeal/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (13, 11, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/vpn/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (14, 1, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/pki/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (15, 2, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/svs/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (16, 3, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/digest/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (17, 4, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/kms/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (18, 5, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/tsa/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (19, 6, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/sms/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (20, 7, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/secauth/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (21, 8, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/secdbhsm/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (22, 9, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/storage/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (23, 10, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/electronicSeal/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (24, 11, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/vpn/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (25, NULL, 'initEditStartParamScript', '初始化修改自启参数路径', '/opt/sansec/ccsp/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (26, NULL, 'initJceScript', '初始化jce脚本路径', '/opt/sansec/ccsp/initJce.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (27, 1, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/pki/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (28, 2, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/svs/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (29, 3, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/digest/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (30, 4, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/kms/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (31, 5, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/tsa/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (32, 6, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/sms/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (33, 7, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/secauth/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (34, 8, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/secdbhsm/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (35, 9, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/storage/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (36, 10, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/electronicSeal/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (37, 11, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/vpn/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (38, NULL, 'initConfigScript', '初始化配置脚本路径', '/opt/sansec/ccsp/initConfig.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (39, NULL, 'configScript', '配置文件路径', '/opt/sansec/config/pt_config.properties', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (40, NULL, 'createDbCaseScript', '创建数据库实例脚本路径', '/opt/ccsp/ccspUtilRemote/shell/createDbCase.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (41, NULL, 'createDbSchemaScript', '创建数据库模式脚本路径', '/opt/ccsp/ccspUtilRemote/shell/createDbSchema.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (42, NULL, 'checkDbCaseOrSchemaScript', '检测数据库实例/模式状态脚本路径', '/opt/ccsp/ccspUtilRemote/shell/checkDbCaseOrSchema.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (43, NULL, 'initDbDataScript', '执行数据库脚本脚本路径', '/opt/ccsp/ccspUtilRemote/shell/initDbData.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (44, NULL, 'deleteDbCaseScript', '删除数据库实例脚本路径', '/opt/ccsp/ccspUtilRemote/shell/deleteDbCase.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (45, NULL, 'deleteDbSchemaScript', '删除数据库模式脚本路径', '/opt/ccsp/ccspUtilRemote/shell/deleteDbSchema.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (46, 1, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/pki-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (47, 2, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/svs-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (48, 3, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/digest-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (49, 4, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/kms-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (50, 5, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/tsa-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (51, 6, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/sms-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (52, 7, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/secauth-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (53, 8, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/secdbhsm-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (54, 9, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/storage-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (55, 10, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/electronic_seal-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (56, 11, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/vpn-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (57, 1, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/pki-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (58, 2, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/svs-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (59, 3, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/digest-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (60, 4, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/kms-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (61, 5, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/tsa-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (62, 6, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/sms-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (63, 7, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/secauth-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (64, 8, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/secdbhsm-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (65, 9, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/storage-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (66, 10, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/electronic_seal-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (67, 11, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/vpn-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (69, NULL, 'checkDbInitDataScript', '检测数据库脚本状态脚本', '/opt/ccsp/ccspUtilRemote/shell/checkDbInitData.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (70, NULL, 'checkDbStatusScript', '检测数据库状态脚本', '/opt/ccsp/ccspUtilRemote/shell/checkDbStatus.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (71, 12, 'initParamScript', '初始化参数脚本路径', '/opt/sansec/ca/setup/setup.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (72, 12, 'initStartScript', '初始化启动脚本路径', '/opt/sansec/ca/setup/startService.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (73, 12, 'jceIniScript', 'jce ini文件路径', '/opt/sansec/ca/jce/swsds.ini', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (74, 12, 'mysqlSqlScript', 'mysql脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/mysql/ca-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (75, 12, 'gaussSqlScript', 'gauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/gauss/ca-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (80, 1, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/pki-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (81, 2, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/svs-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (82, 3, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/digest-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (83, 4, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/kms-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (84, 5, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/tsa-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (85, 6, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/sms-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (86, 7, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/secauth-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (87, 8, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/secdbhsm-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (88, 9, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/storage-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (89, 10, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/electronic_seal-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (90, 11, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/vpn-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (91, 12, 'opengaussSqlScript', 'opengauss脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/opengauss/ca-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (100, 1, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/pki-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (101, 2, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/svs-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (102, 3, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/digest-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (103, 4, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/kms-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (104, 5, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/tsa-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (105, 6, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/sms-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (106, 7, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/secauth-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (107, 8, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/secdbhsm-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (108, 9, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/storage-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (109, 10, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/electronic_seal-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (110, 11, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/vpn-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (111, 12, 'dmSqlScript', 'dm脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/dm/ca-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (120, 1, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/pki-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (121, 2, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/svs-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (122, 3, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/digest-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (123, 4, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/kms-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (124, 5, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/tsa-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (125, 6, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/sms-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (126, 7, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/secauth-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (127, 8, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/secdbhsm-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (128, 9, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/storage-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (129, 10, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/electronic_seal-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (130, 11, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/vpn-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (131, 12, 'kingbaseSqlScript', 'kingbase脚本路径', '/opt/ccsp/ccspUtilRemote/shell/sql/kingbase/ca-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (140, NULL, 'upgradeDbDataScript', '升级数据库脚本路径', '/opt/ccsp/ccspUtilRemote/shell/upgradeDb.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (141, NULL, 'dbBackUpOrRestoreScript', '数据库备份还原脚本路径', '/opt/ccsp/ccspUtilRemote/shell/dbBackUpOrRestore.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (142, NULL, 'dbCheckBackUpOrRestoreScript', '检测数据库备份还原状态路径', '/opt/ccsp/ccspUtilRemote/shell/dbCheckBackUpOrRestore.sh', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (150, 1, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/pki-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (151, 2, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/svs-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (152, 3, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/digest-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (153, 4, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/kms-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (154, 5, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/tsa-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (155, 6, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/sms-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (156, 7, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/secauth-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (157, 8, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/secdbhsm-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (158, 9, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/storage-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (159, 10, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/electronic_seal-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (160, 11, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/vpn-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (161, 12, 'mysqlUpgradeSqlScript', 'mysql更新脚本路径', '/opt/ansible-upgrade/sql/service/mysql/ca-mysql.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (162, 1, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/pki-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (163, 2, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/svs-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (164, 3, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/digest-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (165, 4, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/kms-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (166, 5, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/tsa-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (167, 6, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/sms-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (168, 7, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/secauth-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (169, 8, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/secdbhsm-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (170, 9, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/storage-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (171, 10, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/electronic_seal-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (172, 11, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/vpn-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (173, 12, 'opengaussUpgradeSqlScript', 'opengauss更新脚本路径', '/opt/ansible-upgrade/sql/service/opengauss/ca-opengauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (174, 1, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/pki-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (175, 2, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/svs-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (176, 3, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/digest-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (177, 4, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/kms-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (178, 5, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/tsa-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (179, 6, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/sms-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (180, 7, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/secauth-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (181, 8, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/secdbhsm-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (182, 9, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/storage-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (183, 10, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/electronic_seal-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (184, 11, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/vpn-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (185, 12, 'gaussUpgradeSqlScript', 'gauss更新脚本路径', '/opt/ansible-upgrade/sql/service/gauss/ca-gauss.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (186, 1, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/pki-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (187, 2, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/svs-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (188, 3, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/digest-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (189, 4, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/kms-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (190, 5, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/tsa-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (191, 6, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/sms-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (192, 7, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/secauth-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (193, 8, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/secdbhsm-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (194, 9, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/storage-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (195, 10, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/electronic_seal-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (196, 11, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/vpn-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (197, 12, 'kingbaseUpgradeSqlScript', 'kingbase更新脚本路径', '/opt/ansible-upgrade/sql/service/kingbase/ca-kingbase.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (198, 1, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/pki-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (199, 2, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/svs-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (200, 3, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/digest-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (201, 4, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/kms-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (202, 5, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/tsa-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (203, 6, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/sms-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (204, 7, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/secauth-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (205, 8, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/secdbhsm-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (206, 9, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/storage-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (207, 10, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/electronic_seal-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (208, 11, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/vpn-dm.sql', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_script_path" VALUES (209, 12, 'dmUpgradeSqlScript', 'dm更新脚本路径', '/opt/ansible-upgrade/sql/service/dm/ca-dm.sql', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_share_group_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_share_group_type";
CREATE TABLE "ccsp_common"."dic_share_group_type" (
  "id" int8 NOT NULL,
  "busi_type_id" int8 NOT NULL,
  "is_need_kms" int4 NOT NULL,
  "is_share" int4 NOT NULL
)
;
COMMENT ON COLUMN "ccsp_common"."dic_share_group_type"."is_need_kms" IS '0:不需要；1：需要';
COMMENT ON COLUMN "ccsp_common"."dic_share_group_type"."is_share" IS '0:不支持共享；1：支持共享';
COMMENT ON TABLE "ccsp_common"."dic_share_group_type" IS '支持创建共享服务类型（330）';

-- ----------------------------
-- Records of dic_share_group_type
-- ----------------------------
INSERT INTO "ccsp_common"."dic_share_group_type" VALUES (1, 1, 1, 1);
INSERT INTO "ccsp_common"."dic_share_group_type" VALUES (2, 2, 1, 1);
INSERT INTO "ccsp_common"."dic_share_group_type" VALUES (3, 4, 0, 1);
INSERT INTO "ccsp_common"."dic_share_group_type" VALUES (4, 5, 0, 1);
INSERT INTO "ccsp_common"."dic_share_group_type" VALUES (5, 6, 0, 1);
INSERT INTO "ccsp_common"."dic_share_group_type" VALUES (6, 7, 0, 1);
INSERT INTO "ccsp_common"."dic_share_group_type" VALUES (7, 9, 0, 1);
INSERT INTO "ccsp_common"."dic_share_group_type" VALUES (8, 12, 0, 1);

-- ----------------------------
-- Table structure for dic_statistic
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_statistic";
CREATE TABLE "ccsp_common"."dic_statistic" (
  "id" int8 NOT NULL,
  "service_code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "statistic_type" int4 NOT NULL,
  "statistic_show_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "statistic_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "is_available" int4 NOT NULL,
  "is_invoke_multi_service" int4 NOT NULL,
  "oid" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "unit" varchar(50) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "ccsp_common"."dic_statistic"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_common"."dic_statistic"."service_code" IS '服务简称';
COMMENT ON COLUMN "ccsp_common"."dic_statistic"."statistic_type" IS '统计类型 1增量 2非增量数量计算类 3非增量非计算类';
COMMENT ON COLUMN "ccsp_common"."dic_statistic"."statistic_show_name" IS '统计指标展示名称';
COMMENT ON COLUMN "ccsp_common"."dic_statistic"."statistic_name" IS '统计指标名称';
COMMENT ON COLUMN "ccsp_common"."dic_statistic"."is_available" IS '是否启用 1启用 0停用';
COMMENT ON COLUMN "ccsp_common"."dic_statistic"."is_invoke_multi_service" IS '是否同服务类型全服务调用 1是 2否';
COMMENT ON COLUMN "ccsp_common"."dic_statistic"."oid" IS 'OID';
COMMENT ON COLUMN "ccsp_common"."dic_statistic"."unit" IS '单位';
COMMENT ON TABLE "ccsp_common"."dic_statistic" IS '统计指标字典表';

-- ----------------------------
-- Records of dic_statistic
-- ----------------------------
INSERT INTO "ccsp_common"."dic_statistic" VALUES (1, 'kms', 1, '调用次数', 'kmsBusiStatistic', 1, 0, '.1.3.6.1.4.1.746366.4.1.0', '次');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (2, 'kms', 2, '密钥数量', 'kmsKeyNumStatistic', 1, 0, '.1.3.6.1.4.1.746366.6.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (3, 'kms', 2, '密钥状态分布', 'kmsKeyStateStatistic', 1, 0, '.1.3.6.1.4.1.746366.5.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (4, 'pki', 1, '调用次数', 'encBusiStatistic', 1, 1, '.1.3.6.1.4.1.746374.2.1.0', '次');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (5, 'pki', 2, '密钥数量', 'encKeyNumStatistic', 1, 0, '.1.3.6.1.4.1.746374.1.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (6, 'svs', 1, '调用次数', 'signBusiStatistic', 1, 1, '.1.3.6.1.4.1.746375.3.1.0', '次');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (7, 'svs', 2, '应用证书数量', 'signAppCertStatistic', 1, 0, '.1.3.6.1.4.1.746375.1.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (8, 'svs', 2, '用户证书数量', 'signUserCertStatistic', 1, 0, '.1.3.6.1.4.1.746375.2.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (9, 'tsa', 1, '调用次数', 'timeBusiStatistic', 1, 0, '.1.3.6.1.4.1.746370.5.1.0', '次');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (10, 'sms', 1, '用户签名次数', 'splitBusiStatistic', 1, 0, '.1.3.6.1.4.1.746367.12.1.0', '次');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (11, 'sms', 2, '用户数', 'userNumStatistic', 1, 0, '.1.3.6.1.4.1.746367.11.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (12, 'secauth', 1, '用户签名次数', 'secauthBusiStatistic', 1, 0, '.1.3.6.1.4.1.746371.5.1.0', '次');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (13, 'tsc', 1, '签章次数', 'sealSignBusiStatistic', 1, 0, '.1.3.6.1.4.1.746372.5.1.0', '次');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (14, 'tsc', 1, '验章次数', 'sealVerifyBusiStatistic', 1, 0, '.1.3.6.1.4.1.746372.6.1.0', '次');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (15, 'tsc', 2, '印章个数', 'sealNumStatistic', 1, 0, '.1.3.6.1.4.1.746372.7.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (16, 'tsc', 2, '签章状态分布', 'sealStateStatistic', 1, 0, '.1.3.6.1.4.1.746372.8.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (17, 'secdb', 2, '数据库加密个数', 'dbStatistic', 1, 0, '.1.3.6.1.4.1.746376.1.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (18, 'secstorage', 2, '文件加密个数', 'serverStatistic', 1, 0, '.1.3.6.1.4.1.746377.1.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (19, 'vpn', 3, '新建连接数', 'vpnNewConnectNumStatistic', 1, 1, '.1.3.6.1.4.1.746369.3.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (20, 'vpn', 3, '吞吐量(收)', 'vpnThroughputRNumStatistic', 1, 1, '.1.3.6.1.4.1.746369.4.1.0', 'Kbps');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (21, 'vpn', 3, '吞吐量(发)', 'vpnThroughputSNumStatistic', 1, 1, '.1.3.6.1.4.1.746369.5.1.0', 'Kbps');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (22, 'vpn', 3, '并发连接数', 'vpnConcurrentConnectNumStatistic', 1, 1, '.1.3.6.1.4.1.746369.6.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (23, 'secdb', 2, '数据库加密密钥数量', 'secDbKeyNumStatistic ', 1, 0, '.1.3.6.1.4.1.746376.2.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (24, 'ca', 2, '用户证书数量', 'caUserCertStatistic ', 1, 0, '.1.3.6.1.4.1.746378.9.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (25, 'kms', 2, 'pki密钥数量', 'kmsToPkiEncKeyNumStatistic', 1, 0, '.1.3.6.1.4.1.746366.17.1.0', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (26, 'kms', 2, 'svs应用证书数量', 'kmsToSvsSignAppCertStatistic', 1, 0, '.1.3.6.1.4.1.746366.17.1.1', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (27, 'kms', 2, 'svs用户证书数量', 'kmsToSvsSignUserCertStatistic', 1, 0, '.1.3.6.1.4.1.746366.17.1.2', '个');
INSERT INTO "ccsp_common"."dic_statistic" VALUES (28, 'secauth', 2, '用户数量', 'secauthUserNumStatistic', 1, 0, '.1.3.6.1.4.1.746371.5.1.1', '个');

-- ----------------------------
-- Table structure for dic_sys_data
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_sys_data";
CREATE TABLE "ccsp_common"."dic_sys_data" (
  "id" int8 NOT NULL,
  "dict_type" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "dict_label" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "dict_value" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "default_flag" int4 NOT NULL DEFAULT 0,
  "sord_num" int4 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."id" IS '主键';
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."dict_type" IS '字典类型';
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."dict_label" IS '字典标签';
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."dict_value" IS '字典值';
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."default_flag" IS '是否默认 1默认 0不默认';
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."sord_num" IS '排序';
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_sys_data"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_sys_data" IS '系统数据表';

-- ----------------------------
-- Records of dic_sys_data
-- ----------------------------

-- ----------------------------
-- Table structure for dic_tenant_status
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_tenant_status";
CREATE TABLE "ccsp_common"."dic_tenant_status" (
  "id" int8 NOT NULL,
  "s_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "s_value" int4,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "extend1" varchar(180) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_tenant_status"."id" IS '主键';
COMMENT ON COLUMN "ccsp_common"."dic_tenant_status"."s_name" IS '状态名1初始化、2启动中、3启动失败、4运行、5扩展中、6恢复中、7停服中8、停服、9销毁中、10销毁失败';
COMMENT ON COLUMN "ccsp_common"."dic_tenant_status"."s_value" IS '状态值';
COMMENT ON COLUMN "ccsp_common"."dic_tenant_status"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_tenant_status"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_tenant_status"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_tenant_status"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_common"."dic_tenant_status"."extend1" IS '备用1';
COMMENT ON TABLE "ccsp_common"."dic_tenant_status" IS '租户状态字典表';

-- ----------------------------
-- Records of dic_tenant_status
-- ----------------------------

-- ----------------------------
-- Table structure for dic_user_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."dic_user_type";
CREATE TABLE "ccsp_common"."dic_user_type" (
  "id" int8 NOT NULL,
  "user_type_id" int4,
  "user_type_name" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."dic_user_type"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_common"."dic_user_type"."user_type_id" IS '用户类型id;1平台用户 2租户用户 3应用用户 4单位用户';
COMMENT ON COLUMN "ccsp_common"."dic_user_type"."user_type_name" IS '用户类型名称';
COMMENT ON COLUMN "ccsp_common"."dic_user_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."dic_user_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."dic_user_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."dic_user_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."dic_user_type"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."dic_user_type" IS '用户类型（330）';

-- ----------------------------
-- Records of dic_user_type
-- ----------------------------
INSERT INTO "ccsp_common"."dic_user_type" VALUES (1, 1, '平台用户', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_user_type" VALUES (2, 2, '租户用户', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."dic_user_type" VALUES (4, 4, '单位用户', NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for organization_info
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."organization_info";
CREATE TABLE "ccsp_common"."organization_info" (
  "organization_id" int8 NOT NULL,
  "organization_code" varchar(900) COLLATE "pg_catalog"."default",
  "organization_name" varchar(900) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" int8,
  "sord_num" int4,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."organization_info"."organization_id" IS '组织机构ID';
COMMENT ON COLUMN "ccsp_common"."organization_info"."organization_code" IS '组织机构代码';
COMMENT ON COLUMN "ccsp_common"."organization_info"."organization_name" IS '组织机构名称';
COMMENT ON COLUMN "ccsp_common"."organization_info"."parent_id" IS '父ID';
COMMENT ON COLUMN "ccsp_common"."organization_info"."sord_num" IS '排序序号';
COMMENT ON COLUMN "ccsp_common"."organization_info"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_common"."organization_info"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."organization_info"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."organization_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."organization_info"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."organization_info"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."organization_info" IS '组织字典表';

-- ----------------------------
-- Records of organization_info
-- ----------------------------

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."sys_job";
CREATE TABLE "ccsp_common"."sys_job" (
  "job_id" int8 NOT NULL,
  "job_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "job_group" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "server_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "method_url" varchar(1500) COLLATE "pg_catalog"."default",
  "json_param" text COLLATE "pg_catalog"."default" NOT NULL,
  "cron_expression" varchar(255) COLLATE "pg_catalog"."default",
  "misfire_policy" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 3,
  "concurrent" varchar(1) COLLATE "pg_catalog"."default" DEFAULT 1,
  "job_status" varchar(1) COLLATE "pg_catalog"."default" DEFAULT 0,
  "created_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "updated_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(1500) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."sys_job"."job_id" IS '任务号';
COMMENT ON COLUMN "ccsp_common"."sys_job"."job_name" IS '任务名称';
COMMENT ON COLUMN "ccsp_common"."sys_job"."job_group" IS '任务组名';
COMMENT ON COLUMN "ccsp_common"."sys_job"."server_id" IS '服务模块';
COMMENT ON COLUMN "ccsp_common"."sys_job"."method_url" IS '调用接口';
COMMENT ON COLUMN "ccsp_common"."sys_job"."json_param" IS 'json格式参数';
COMMENT ON COLUMN "ccsp_common"."sys_job"."cron_expression" IS 'CRON执行表达式';
COMMENT ON COLUMN "ccsp_common"."sys_job"."misfire_policy" IS '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）';
COMMENT ON COLUMN "ccsp_common"."sys_job"."concurrent" IS '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）';
COMMENT ON COLUMN "ccsp_common"."sys_job"."job_status" IS '状态（0正常 1暂停）';
COMMENT ON COLUMN "ccsp_common"."sys_job"."created_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."sys_job"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."sys_job"."updated_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."sys_job"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_common"."sys_job"."remark" IS '备注';
COMMENT ON TABLE "ccsp_common"."sys_job" IS '定时任务表';

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO "ccsp_common"."sys_job" VALUES (2, '2', 'common', 'ccsp-base-common', NULL, 'userManagerTaskServiceImpl.userUnLockTask()', '0/10 * * * * ? ', '3', '1', '0', NULL, '2023-03-04 11:22:23', NULL, NULL, 'test');
INSERT INTO "ccsp_common"."sys_job" VALUES (3, '3', 'common', 'ccsp-base-common', NULL, 'userManagerTaskServiceImpl.disableUserTask()', '0 0/1 * * * ?', '3', '1', '0', NULL, '2023-03-04 11:22:23', NULL, NULL, 'test');
INSERT INTO "ccsp_common"."sys_job" VALUES (4, '4', 'common', 'ccsp-base-common', NULL, 'userManagerTaskServiceImpl.authCodeExpireDateCheckTask()', '0 0/1 * * * ?', '3', '1', '0', NULL, '2023-03-04 11:22:23', NULL, NULL, 'test');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."sys_job_log";
CREATE TABLE "ccsp_common"."sys_job_log" (
  "job_log_id" int8 NOT NULL,
  "job_id" int8 NOT NULL,
  "job_message" varchar(1500) COLLATE "pg_catalog"."default",
  "status" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_info" varchar(6000) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "trigger_time" int8
)
;
COMMENT ON COLUMN "ccsp_common"."sys_job_log"."job_log_id" IS '任务日志ID';
COMMENT ON COLUMN "ccsp_common"."sys_job_log"."job_id" IS '任务ID';
COMMENT ON COLUMN "ccsp_common"."sys_job_log"."job_message" IS '日志信息';
COMMENT ON COLUMN "ccsp_common"."sys_job_log"."status" IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN "ccsp_common"."sys_job_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "ccsp_common"."sys_job_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."sys_job_log"."trigger_time" IS '触发时间';
COMMENT ON TABLE "ccsp_common"."sys_job_log" IS '定时任务执行日志表';

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."sys_menu";
CREATE TABLE "ccsp_common"."sys_menu" (
  "menu_id" int8 NOT NULL,
  "menu_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" int8 NOT NULL,
  "path" varchar(200) COLLATE "pg_catalog"."default",
  "component" varchar(255) COLLATE "pg_catalog"."default",
  "is_cache" int4,
  "menu_type" varchar(100) COLLATE "pg_catalog"."default",
  "visible" int4,
  "status" int4,
  "perms" varchar(100) COLLATE "pg_catalog"."default",
  "icon" varchar(100) COLLATE "pg_catalog"."default",
  "active_menu" varchar(100) COLLATE "pg_catalog"."default",
  "order_num" int4,
  "is_iframe" varchar(255) COLLATE "pg_catalog"."default",
  "iframe_url" varchar(255) COLLATE "pg_catalog"."default",
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."sys_menu"."menu_id" IS '菜单ID';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."menu_name" IS '菜单名称';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."parent_id" IS '父菜单ID';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."path" IS '路由地址';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."component" IS '组件路径';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."is_cache" IS '是否缓存';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."menu_type" IS '菜单类型（M目录 C菜单 F按钮）';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."visible" IS '菜单状态（0显示 1隐藏）';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."status" IS '菜单状态（0正常 1停用）';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."perms" IS '权限标识';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."icon" IS '菜单图标';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."active_menu" IS '菜单分组';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."order_num" IS '排序序号';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."is_iframe" IS '是否iframe';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."iframe_url" IS 'iframe路径';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."sys_menu"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."sys_menu" IS '菜单表';

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO "ccsp_common"."sys_menu" VALUES (100100100, '平台首页', 0, '/ptindex', NULL, 0, 'M', 1, 0, 'pt:index', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (101100100, '租户首页', 0, '/tenantindex', NULL, 0, 'M', 1, 0, 'tenant:index', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (101100101, '单位首页', 0, '/organizeindex', NULL, 0, 'M', 1, 0, 'organization:index', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (102100100, '许可授权管理', 0, '/license', 'Layout', 0, 'M', 0, 0, NULL, 'license', NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (102101100, '许可申请', 102100100, '/system/license/apply', '/system/license/apply.vue', 0, 'C', 0, 0, 'license:apply:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (102101101, '申请', 102101100, NULL, NULL, 0, 'F', 0, 0, 'license:apply:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (102101102, '下载凭证', 102101100, NULL, NULL, 0, 'F', 0, 0, 'license:apply:download', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (102102100, '许可管理', 102100100, '/system/license', '/system/license/index.vue', 0, 'C', 0, 0, 'license:use:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (102102101, '解析许可内容', 102102100, NULL, NULL, 0, 'F', 0, 0, 'license:use:parse', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (102102102, '导入许可', 102102100, NULL, NULL, 0, 'F', 0, 0, 'license:use:import', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (102103100, '服务续期管理', 102100100, '/license/services/renewal', '/services/renewal/index.vue', 0, 'C', 0, 0, 'license:use:serverList', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (102103101, '续约', 102103100, NULL, NULL, 0, 'F', 0, 0, 'license:use:renewServer', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103100100, '租户管理', 0, '/tenant', 'Layout', 0, 'M', 0, 0, NULL, 'tenant', NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101100, '租户管理', 103100100, '/tenant/manage', '/tenant/manage/index.vue', 0, 'C', 0, 0, 'common:tenant:list', NULL, 'tenantManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101101, '详情', 103101100, '/tenant/manage/detail', '/tenant/manage/detail/index.vue', 0, 'F', 1, 0, 'common:tenant:info', NULL, 'tenantManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101102, '编辑', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101103, '启动', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:start', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101104, '停服', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:stop', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101105, '删除', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:delete', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101112, '配置地址', 103101100, '/tenant/manage/busiurl', '/tenant/manage/busiurl.vue', 0, 'F', 1, 0, 'common:busiurl:infoList', NULL, 'tenantManage', 12, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101113, '新增业务地址', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:add', NULL, NULL, 13, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101114, '编辑业务地址', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:edit', NULL, NULL, 14, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101115, '删除业务地址', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:delete', NULL, NULL, 15, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101116, '配额管理', 103101100, '/tenant/manage/quota', '/tenant/manage/quota.vue', 0, 'F', 1, 0, 'quota:info:list', NULL, 'tenantManage', 13, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101117, '设置配额', 103101100, NULL, NULL, 0, 'F', 0, 0, 'quota:info:edit', NULL, NULL, 16, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101118, '密码产品配额', 103100100, '/tenant/manage/product', '/product/tenant/index.vue', 0, 'F', 1, 0, 'product:quota:list', NULL, 'tenantManage', 17, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101119, '产品配额开通', 103101100, NULL, NULL, 0, 'F', 0, 0, 'product:quota:open', NULL, NULL, 18, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101120, '产品配额关闭', 103101100, NULL, NULL, 0, 'F', 0, 0, 'product:quota:close', NULL, NULL, 19, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101121, '产品配额编辑', 103101100, NULL, NULL, 0, 'F', 0, 0, 'product:quota:edit', NULL, NULL, 20, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101122, '产品配额详情', 103101100, NULL, NULL, 0, 'F', 0, 0, 'product:quota:info', NULL, NULL, 21, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101123, '产品配额变更记录列表', 103101100, NULL, NULL, 0, 'F', 0, 0, 'product:quota:record', NULL, NULL, 22, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101124, '服务组管理', 103101100, '/tenant/manage/service/group', '/tenant/manage/serviceGroup/index.vue', 0, 'F', 1, 0, 'common:tenant:serviceGroup', NULL, 'tenantManage', 23, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101125, '绑定服务组', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:addServiceGroup', NULL, NULL, 24, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101126, '解绑服务组', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:deleteServiceGroup', NULL, NULL, 25, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101127, '设备组管理', 103101100, '/tenant/manage/device/group', '/tenant/manage/deviceGroup/index.vue', 0, 'F', 1, 0, 'common:tenant:deviceGroup', NULL, 'tenantManage', 26, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101128, '绑定异构设备组', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:addDeviceGroup', NULL, NULL, 27, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103101129, '解绑异构设备组', 103101100, NULL, NULL, 0, 'F', 0, 0, 'common:tenant:deleteDeviceGroup', NULL, NULL, 28, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103102100, '待审核租户', 103100100, '/tenant/audit/waiting', '/tenant/audit/waiting.vue', 0, 'C', 0, 0, 'common:tenantAudit:auditList', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103102101, '审核', 103102100, NULL, NULL, 0, 'F', 0, 0, 'common:tenantAudit:audit', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103103100, '已审核租户', 103100100, '/tenant/audit/audited', '/tenant/audit/audited.vue', 0, 'C', 0, 0, 'common:tenantAudit:auditList', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103103101, '详情', 103103100, NULL, NULL, 0, 'F', 0, 0, 'common:tenantAutid:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (103103102, '删除', 103103100, NULL, NULL, 0, 'F', 0, 0, 'common:tenantAutid:delete', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104100100, '服务管理', 0, '/services', 'Layout', 0, 'M', 0, 0, NULL, 'myapp', NULL, 9, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104102100, '服务组管理', 104100100, '/services/group', '/services/group/index.vue', 0, 'C', 0, 0, 'servicemgt:servicegroup:list', NULL, 'serviceGroup', 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104102101, '新建', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104102102, '编辑', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104102103, '服务管理', 104102100, '/services/group/servicelist', '/services/group/servicelist.vue', 0, 'F', 1, 0, 'servicemgt:servicegroup:serviceList', NULL, 'serviceGroup', 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104102104, '删除', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:delete', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104102105, '可绑定服务', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:canBindServiceList', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104102107, '释放服务', 104102100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicegroup:delService', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104104100, '服务管理', 104100100, '/services/service', '/services/service/index.vue', 0, 'C', 0, 0, 'servicemgt:serviceinfo:list', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104104101, '新建', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104104102, '编辑', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104104103, '删除', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104104104, '启动', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:start', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104104105, '停止', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:stop', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104104106, '重启', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:restart', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104104107, '同步数据库密码', 104104100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:serviceinfo:resetDbAuthCode', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104105100, '服务类型管理', 104100100, '/services/serviceType', '/services/serviceType/index.vue', 0, 'C', 0, 0, 'servicemgt:servicetype:list', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104105101, '编辑', 104105100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:servicetype:edit', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104106100, '网关管理', 104100100, '/services/gateway', '/services/gateway/index.vue', 0, 'C', 0, 0, 'servicemgt:gateway:list', NULL, 'gateway', 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104106101, '新建', 104106100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:gateway:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104106102, '编辑', 104106100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:gateway:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104106103, '删除', 104106100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:gateway:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104107100, '路由管理', 104100100, '/services/gateway/routes', '/services/gateway/routes.vue', 0, 'F', 1, 0, 'servicemgt:route:list', NULL, 'gateway', 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104107101, '详情', 104107100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:route:detail', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104108100, '服务日志', 104100100, '/services/operlog', '/services/operlog/index.vue', 0, 'C', 0, 0, 'servicemgt:logs:list', NULL, NULL, 9, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104109100, '数据库管理', 104100100, '/database/list', '/database/list/index.vue', 0, 'C', 0, 0, 'servicemgt:dbinfo:list', NULL, 'databaseMenu', 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104109101, '新建', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbinfo:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104109102, '编辑', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbinfo:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104109103, '删除', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbinfo:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104109104, '实例列表', 104109100, '/database/dbunit/list', '/database/dbunit/index.vue', 0, 'F', 1, 0, 'servicemgt:dbunit:list', NULL, 'databaseMenu', 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104109105, '新建', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbunit:add', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104109107, '删除', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbunit:delete', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104109108, '修改密码', 104109100, NULL, NULL, 0, 'F', 0, 0, 'servicemgt:dbinfo:updateAuthCode', NULL, NULL, 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104110100, '镜像管理', 104100100, '/mirror/list', '/mirror/list/index.vue', 0, 'C', 0, 0, 'docker:image:list', NULL, 'mirrorImageManage', 9, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104110101, '新增', 104110100, NULL, NULL, 0, 'F', 0, 0, 'docker:image:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104110102, '编辑', 104110100, NULL, NULL, 0, 'F', 0, 0, 'docker:image:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104110103, '删除', 104110100, NULL, NULL, 0, 'F', 0, 0, 'docker:image:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (104110104, '启用/禁用', 104110100, NULL, NULL, 0, 'F', 0, 0, 'docker:image:status', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105100100, '设备管理', 0, '/device', 'Layout', 0, 'M', 0, 0, NULL, 'device', 'vsmHost', 10, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105101100, '云密码机', 105100100, '/device/vsmhost', '/device/vsmhost/index.vue', 0, 'C', 0, 0, 'device:vsmHost:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105101101, '新建', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsmHost:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105101102, '编辑', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsmHost:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105101103, '删除', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsmHost:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105101104, '详情', 105101100, '/device/vsmhost/detail', '/device/vsmhost/detail.vue', 0, 'F', 1, 0, 'device:vsmHost:detail', NULL, 'vsmHost', 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105101105, '网络配置', 105101100, '/device/vsmhost/vsmNetwork', '/device/vsmhost/vsmNetwork.vue', 0, 'F', 1, 0, 'device:vsmNetConf:list', NULL, 'vsmHost', 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105101106, '删除', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsmNetConf:delete', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105101107, '新增', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsmNetConf:add', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105101108, '创建虚拟密码机', 105101100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:add', NULL, NULL, 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105102100, '虚拟密码机', 105100100, '/device/vsm', '/device/vsm/index.vue', 0, 'C', 0, 0, 'device:vsm:list', NULL, 'vsmManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105102101, '创建虚拟密码机', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105102102, '详情', 105102100, '/device/vsm/detail', '/device/vsm/detail.vue', 0, 'F', 1, 0, 'device:vsm:detail', NULL, 'vsmManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105102103, '编辑', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:edit', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105102104, '删除', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:delete', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105102105, '启动', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:start', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105102106, '停止', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:stop', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105102107, '重启', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:restart', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105102108, '强制删除', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:forceDelete', NULL, NULL, 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105102109, '管理系统', 105102100, NULL, NULL, 0, 'F', 0, 0, 'device:vsm:managePt', NULL, NULL, 9, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105103100, '物理密码机', 105100100, '/device/host', '/device/host/index.vue', 0, 'C', 0, 0, 'device:hsm:list', NULL, 'hostMange', 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105103101, '新建', 105103100, NULL, NULL, 0, 'F', 0, 0, 'device:hsm:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105103102, '编辑', 105103100, NULL, NULL, 0, 'F', 0, 0, 'device:hsm:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105103103, '删除', 105103100, NULL, NULL, 0, 'F', 0, 0, 'device:hsm:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105103104, '详情', 105103100, '/device/host/detail', '/device/host/detail.vue', 0, 'F', 1, 0, 'device:hsm:detail', NULL, 'hostMange', 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105103107, '强制删除', 105103100, NULL, NULL, 0, 'F', 0, 0, 'device:hsm:forceDelete', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105103108, '管理系统', 105103100, NULL, NULL, 0, 'F', 0, 0, 'device:hsm:managePt', NULL, NULL, 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105105100, '设备组管理', 105100100, '/device/deviceGroup', '/device/deviceGroup/index.vue', 0, 'C', 0, 0, 'device:group:list', NULL, 'deviceGroup', 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105105101, '新建', 105105100, NULL, NULL, 0, 'F', 0, 0, 'device:group:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105105102, '编辑', 105105100, NULL, NULL, 0, 'F', 0, 0, 'device:group:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105105103, '删除', 105105100, NULL, NULL, 0, 'F', 0, 0, 'device:group:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105105104, '设备管理', 105105100, '/device/deviceGroup/detail', '/device/deviceGroup/detail.vue', 0, 'F', 1, 0, 'device:group:deviceList', NULL, 'deviceGroup', 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105105105, '绑定设备', 105105100, NULL, NULL, 0, 'F', 0, 0, 'device:group:deviceBind', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105105106, '释放设备', 105105100, NULL, NULL, 0, 'F', 0, 0, 'device:group:deviceDelete', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105106100, '厂商管理', 105100100, '/device/vendor', '/device/vendor/index.vue', 0, 'C', 0, 0, 'device:vendor:list', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105106101, '新增', 105106100, NULL, NULL, 0, 'F', 0, 0, 'device:vendor:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105106102, '编辑', 105106100, NULL, NULL, 0, 'F', 0, 0, 'device:vendor:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105106103, '删除', 105106100, NULL, NULL, 0, 'F', 0, 0, 'device:vendor:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105108100, '设备类型管理', 105100100, '/device/devicetypes', '/device/devicetypes/index.vue', 0, 'C', 0, 0, 'device:type:list', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105108101, '新增', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:type:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105108102, '编辑', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:type:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105108103, '删除', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:type:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105108104, '停用', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:type:disable', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105108105, '启用', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:type:enable', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (105108106, '监控配置', 105108100, NULL, NULL, 0, 'F', 0, 0, 'device:monitor:config', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106100100, '应用管理', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'app', NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106101100, '待审核应用', 106100100, '/application/audit', '/application/audit/index.vue', 0, 'C', 0, 0, 'app:waitAudit:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106101101, '审批', 106101100, NULL, NULL, 0, 'F', 0, 0, 'app:waitAudit:audit', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102100, '应用信息', 106100100, '/application/list', '/application/list/index.vue', 0, 'C', 0, 0, 'app:audited:list', NULL, 'appManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102101, '新建', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102102, '详情', 106102100, '/application/detail', '/application/list/detail.vue', 0, 'F', 1, 0, 'app:audited:detail', NULL, 'appManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102103, '编辑', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:edit', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102104, '删除', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:delete', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102105, '业务管理', 106102100, '/application/appbuss/detail', '/application/list/appBussList.vue', 0, 'F', 1, 0, 'app:audited:busiTypeList', NULL, 'appManage', 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102106, '新增', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:busiTypeAdd', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102107, '删除', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:busiTypeDelete', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102109, '凭证管理', 106102100, '/application/auth', '/application/list/auth.vue', 0, 'F', 1, 0, 'app:aksk:list', NULL, 'appManage', 9, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102110, '新增', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:aksk:add', NULL, NULL, 10, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102111, '编辑', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:aksk:edit', NULL, NULL, 11, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102112, '启用', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:aksk:start', NULL, NULL, 12, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102113, '禁用', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:aksk:stop', NULL, NULL, 13, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106102114, '删除', 106102100, NULL, NULL, 0, 'F', 0, 0, 'app:aksk:delete', NULL, NULL, 14, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (106103100, '审批历史', 106100100, '/application/audit/history', '/application/audit/history.vue', 0, 'C', 0, 0, 'app:auditHistory:list', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (107100100, '我的应用', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'app', NULL, 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (107101100, '应用注册', 107100100, '/application/register', '/application/register/index.vue', 0, 'C', 0, 0, 'app:appAdd:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (107102100, '申请历史', 107100100, '/application/register/history', '/application/register/history.vue', 0, 'C', 0, 0, 'app:applyHistory:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (107103100, '我的应用', 107100100, '/application/myapp', '/application/myapp/index.vue', 0, 'C', 1, 0, 'app:myApp:list', NULL, 'myAppManage', 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (107103101, '详情', 107103100, '/application/myapp/detail', '/application/myapp/detail.vue', 0, 'F', 1, 0, 'app:myApp:detail', NULL, 'myAppManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (107103102, '编辑', 107103100, NULL, NULL, 0, 'F', 0, 0, 'app:audited:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (109100100, '数据加解密', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'jiajiemi', NULL, 11, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (109101100, '资源信息', 109100100, '/services/document/pki', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (109101101, '根据业务类型查询业务地址', 109100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (109101102, 'SDK下载', 109101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (109101103, 'API下载', 109101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (110100100, '签名验签', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'yanqian', NULL, 12, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (110101100, '信任域管理', 110100100, '/cert/domain', '/cert/domain/index.vue', 0, 'C', 0, 0, 'cert:domain:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (110101101, '新建', 110101100, NULL, NULL, 0, 'F', 0, 0, 'cert:domain:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (110101102, '编辑', 110101100, NULL, NULL, 0, 'F', 0, 0, 'cert:domain:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (110101103, '删除', 110101100, NULL, NULL, 0, 'F', 0, 0, 'cert:domain:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (110102100, '资源信息', 110100100, '/services/document/svs', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (110102101, '根据业务类型查询业务地址', 110100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (110102102, 'SDK下载', 110102100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (110102103, 'API下载', 110102100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (112100100, '密钥管理', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'kms', NULL, 14, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (112101100, '资源信息', 112100100, '/services/document/kms', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (112101101, '根据业务类型查询业务地址', 112100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (112101102, 'SDK下载', 112101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (112101103, 'API下载', 112101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (113100100, '文件加密', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'file-encrypt', NULL, 15, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (113101100, '资源信息', 113100100, '/services/document/secstorage', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (113101101, '根据业务类型查询业务地址', 113100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (113101102, 'SDK下载', 113101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (113101103, 'API下载', 113101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (114100100, '数据库加密', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'db-encrypt', NULL, 18, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (114101100, '资源信息', 114100100, '/services/document/secdb', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (114101101, '根据业务类型查询业务地址', 114100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (114101102, 'SDK下载', 114101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (114101103, 'API下载', 114101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (115100100, '时间戳', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'time1', NULL, 15, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (115101100, '资源信息', 115100100, '/services/document/tsa', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (115101101, '根据业务类型查询业务地址', 115100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (115101102, 'SDK下载', 115101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (115101103, 'API下载', 115101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (116100100, '协同签名', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'xieqian', NULL, 16, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (116101100, '资源信息', 116100100, '/services/document/sms', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (116101101, '根据业务类型查询业务地址', 116100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (116101102, 'SDK下载', 116101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (116101103, 'API下载', 116101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (117100100, '动态令牌', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'user-check', NULL, 17, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (117101100, '资源信息', 117100100, '/services/document/secauth', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (117101101, '根据业务类型查询业务地址', 117100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (117101102, 'SDK下载', 117101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (117101103, 'API下载', 117101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (118100100, '电子签章', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'qianzhang', NULL, 20, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (118101100, '资源信息', 118100100, '/services/document/tsc', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (118101101, '根据业务类型查询业务地址', 118100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (118101102, 'SDK下载', 118101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (118101103, 'API下载', 118101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (119100100, 'SSLVPN加密通道', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'vpn', NULL, 21, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (119101100, '资源信息', 119100100, '/services/document/vpn', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (119101101, '根据业务类型查询业务地址', 119100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (119101102, 'SDK下载', 119101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (119101103, 'API下载', 119101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120100100, '用户管理', 0, '/sysuser', 'Layout', 0, 'M', 0, 0, NULL, 'tree', NULL, 27, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101100, '用户信息', 120100100, '/system/user', '/system/user/index.vue', 0, 'C', 0, 0, 'common:user:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101102, '编辑', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101103, '删除', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101104, '绑定UKey', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:bindUkey', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101105, '设置账号有效期', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:expiryDate', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101106, '启用', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:enable', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101107, '禁用', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:disable', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101108, '解锁', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:unlock', NULL, NULL, 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101109, '重置口令', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:resetAuthCode', NULL, NULL, 9, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101110, '绑定SIM盾', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:bindSimShield', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120101111, '绑定SIMKEY', 120101100, NULL, NULL, 0, 'F', 0, 0, 'common:user:bindSimKey', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120102100, '注册记录', 120100100, '/system/user/register', '/system/user/register/index.vue', 0, 'C', 0, 0, 'common:userRegister:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120102101, '注册', 120102100, NULL, NULL, 0, 'F', 0, 0, 'common:userRegister:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120102102, '审核', 120102100, NULL, NULL, 0, 'F', 0, 0, 'common:userRegister:audit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120102103, '删除', 120102100, NULL, NULL, 0, 'F', 0, 0, 'common:userRegister:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120103100, '单位管理', 120100100, '/system/organize/index', '/system/organize/index.vue', 0, 'C', 0, 0, 'common:organization:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120103101, '新增', 120103100, NULL, NULL, NULL, 'F', 0, 0, 'common:organization:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120103102, '编辑', 120103100, NULL, NULL, NULL, 'F', 0, 0, 'common:organization:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (120103103, '删除', 120103100, NULL, NULL, NULL, 'F', 0, 0, 'common:organization:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121100100, '系统管理', 0, '/system', 'Layout', 0, 'M', 0, 0, NULL, 'setting-line', NULL, 30, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121103100, '口令黑名单', 121100100, '/system/config/unsafekl', '/system/config/unsafekl.vue', 0, 'C', 0, 0, 'common:authCodeBlackList:list', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121103101, '添加', 121103100, NULL, NULL, 0, 'F', 0, 0, 'common:authCodeBlackList:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121103102, '编辑', 121103100, NULL, NULL, 0, 'F', 0, 0, 'common:authCodeBlackList:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121103103, '删除', 121103100, NULL, NULL, 0, 'F', 0, 0, 'common:authCodeBlackList:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121104100, '人员管理配置', 121100100, '/system/config/login', '/system/config/login.vue', 0, 'C', 0, 0, 'common:userManagerConfig:list', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121104101, '修改配置', 121104100, NULL, NULL, 0, 'F', 0, 0, 'common:userManagerConfig:edit', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121105100, '系统配置', 121100100, '/system/config', '/system/config/index.vue', 0, 'C', 0, 0, NULL, NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121105101, '修改配置', 121105100, NULL, NULL, 0, 'F', 0, 0, 'common:sysConfig:edit', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121106100, '主密钥备份', 121100100, '/system/backup/mainkey', '/system/backup/mainkey/index.vue', 0, 'C', 0, 0, 'common:masterKey:list', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121106101, '备份', 121106100, NULL, NULL, 0, 'F', 0, 0, 'common:masterKey:backup', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121106102, '恢复', 121106100, NULL, NULL, 0, 'F', 0, 0, 'common:masterKey:recover', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121107100, '关于平台', 121100100, '/system/about', '/system/about/index.vue', 0, 'C', 0, 0, 'common:pt:info', NULL, NULL, 9, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121108100, '业务地址', 121100100, '/system/busiurl', '/system/busiurl/index.vue', 0, 'C', 0, 0, 'common:busiurl:infoList', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121108101, '新增业务地址', 121108100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121108102, '编辑业务地址', 121108100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121108103, '删除业务地址', 121108100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121109100, '电子签章密钥备份', 121100100, '/system/backup/innerkey', '/system/backup/mainkey/index.vue', 0, 'C', 0, 0, 'common:masterKey:list', NULL, NULL, 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121109101, '备份', 121109100, NULL, NULL, 0, 'F', 0, 0, 'common:masterKey:backup', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (121109102, '恢复', 121109100, NULL, NULL, 0, 'F', 0, 0, 'common:masterKey:recover', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (122100100, '日志管理', 0, '/audit', 'Layout', 0, 'M', 0, 0, NULL, 'documentation', NULL, 28, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (122101100, '管理日志', 122100100, '/monitor/managelog/index', '/monitor/managelog/index.vue', 0, 'C', 0, 0, 'log:operate:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (122101101, '批量审计', 122101100, NULL, NULL, 0, 'F', 0, 0, 'log:operate:audit', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (122101102, '导出', 122101100, NULL, NULL, 0, 'F', 0, 0, 'log:operate:export', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (122102100, '租户管理日志', 122100100, '/monitor/operlog/index', '/monitor/operlog/index.vue', 0, 'C', 0, 0, 'log:operateTenant:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (122102101, '批量审计', 122102100, NULL, NULL, 0, 'F', 0, 0, 'log:operateTenant:audit', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (122102102, '导出', 122102100, NULL, NULL, 0, 'F', 0, 0, 'log:operateTenant:export', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (123100100, '监控告警', 0, '/monitor', 'Layout', 0, 'M', 0, 0, NULL, 'monitor', NULL, 29, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (123102100, ' 服务器监控', 123100100, '/monitor/dashboard', '/iframe/swmonitor.vue', 0, 'C', 0, 0, NULL, NULL, NULL, 2, NULL, '/swmonitor/front/viewPage?viewName=panel-multiple-ccsp&isShowScreen=1', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (123103100, '大屏', 123100100, '/monitor/screen', '/iframe/screen.vue', 0, 'C', 1, 0, NULL, NULL, NULL, 2, NULL, '/swmonitor/front/screen?viewName=bigpanel-ccsp001&isShowScreen=1&target=_blank', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (123104100, '告警信息', 123100100, '/monitor/alarm', '/monitor/alarm/index.vue', 0, 'C', 0, 0, 'moniter:alarm:find', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (123104101, '处置', 123104100, NULL, NULL, 0, 'F', 0, 0, 'moniter:alarm:hand', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (123105100, '告警历史', 123100100, '/monitor/alarm/history', '/monitor/alarm/history.vue', 0, 'C', 0, 0, 'moniter:alarm:history', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (123106100, '告警配置', 123100100, '/monitor/alarm/config', '/iframe/swmonitor.vue', 0, 'C', 0, 0, NULL, NULL, NULL, 6, NULL, '/swmonitor/front/warnManage/warnRuleList', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124100100, '区域管理', 0, '/subarera', 'Layout', 0, 'M', 0, 0, NULL, 'subarea', NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124101100, '区域管理', 124100100, '/subarea/subarea/index', '/subarea/subarea/index.vue', 0, 'C', 0, 0, 'region:region:list', NULL, 'subareaManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124101101, '新建', 124101100, NULL, NULL, 0, 'F', 0, 0, 'region:region:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124101102, '详情', 124101100, '/subarea/subarea/detail', '/subarea/subarea/detail.vue', 0, 'F', 1, 0, 'region:region:detail', NULL, 'subareaManage', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124101103, '编辑', 124101100, NULL, NULL, 0, 'F', 0, 0, 'region:region:edit', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124101104, '删除', 124101100, NULL, NULL, 0, 'F', 0, 0, 'region:region:delete', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124102100, '认证服务管理', 124100100, '/subarea/subarea/serviceOauth', '/subarea/subarea/serviceOauth.vue', 0, 'F', 1, 0, 'region:authCenter:list', NULL, 'subareaManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124102101, '新增', 124102100, NULL, NULL, 0, 'F', 0, 0, 'region:authCenter:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124102102, '编辑', 124102100, NULL, NULL, 0, 'F', 1, 0, 'region:authCenter:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124102103, '删除', 124102100, NULL, NULL, 0, 'F', 0, 0, 'region:authCenter:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124103100, '管控服务管理', 124100100, '/subarea/subarea/serviceControl', '/subarea/subarea/serviceControl.vue', 0, 'F', 1, 0, 'region:remoteServer:list', NULL, 'subareaManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124103101, '新增', 124103100, NULL, NULL, 0, 'F', 0, 0, 'region:remoteServer:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124103102, '编辑', 124103100, NULL, NULL, 0, 'F', 1, 0, 'region:remoteServer:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124103103, '删除', 124103100, NULL, NULL, 0, 'F', 0, 0, 'region:remoteServer:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124104100, '监控服务管理', 124100100, '/subarea/subarea/serviceMonitor', '/subarea/subarea/serviceMonitor.vue', 0, 'F', 1, 0, 'region:monitorServer:list', NULL, 'subareaManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124104101, '新增', 124104100, NULL, NULL, 0, 'F', 0, 0, 'region:monitorServer:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124104102, '编辑', 124104100, NULL, NULL, 0, 'F', 1, 0, 'region:monitorServer:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124104103, '删除', 124104100, NULL, NULL, 0, 'F', 0, 0, 'region:monitorServer:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124105100, '网关统计组件管理', 124100100, '/subarea/subarea/countModule', '/subarea/subarea/countModule.vue', 0, 'F', 1, 0, 'region:gatewayStatic:list', NULL, 'subareaManage', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124105101, '新增', 124105100, NULL, NULL, 0, 'F', 0, 0, 'region:gatewayStatic:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124105102, '编辑', 124105100, NULL, NULL, 0, 'F', 1, 0, 'region:gatewayStatic:edit', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (124105103, '删除', 124105100, NULL, NULL, 0, 'F', 0, 0, 'region:gatewayStatic:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (126100100, '数字证书认证', 0, '/subarera', 'Layout', 0, 'M', 0, 0, NULL, ' cacheck', NULL, 22, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (126101100, '资源信息', 126100100, '/services/document/ca', '/services/document/index.vue', 0, 'C', 0, 0, 'service:resource:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (126101101, '根据业务类型查询业务地址', 126100100, NULL, NULL, 0, 'F', 0, 0, 'common:busiurl:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (126101102, 'SDK下载', 126101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:sdk', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (126101103, 'API下载', 126101100, NULL, NULL, 0, 'F', 0, 0, 'service:download:api', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (127100100, 'SIM盾管理', 0, '/supersim', 'Layout', 0, 'M', 0, 0, NULL, 'simshield', NULL, 23, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (127101100, 'SIM盾用户管理', 127100100, '/supersim/simshield/user', '/supersim/simshield/user/index.vue', 0, 'C', 0, 0, 'simshield:user:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (127101101, '申请证书', 127101100, NULL, NULL, 0, 'F', 0, 0, 'simshield:cert:apply', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (127101102, '更新证书', 127101100, NULL, NULL, 0, 'F', 0, 0, 'simshield:cert:update', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (127101103, '撤销证书', 127101100, NULL, NULL, 0, 'F', 0, 0, 'simshield:cert:revert', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (127101104, '重置PIN码', 127101100, NULL, NULL, 0, 'F', 0, 0, 'simshield:pin:reset', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (127101105, '设置PIN码', 127101100, NULL, NULL, 0, 'F', 0, 0, 'simshield:pin:set', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (127101106, '修改PIN码', 127101100, NULL, NULL, 0, 'F', 0, 0, 'simshield:pin:update', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (127102100, '业务操作记录', 127100100, '/supersim/simshield/apiused', '/supersim/apiused/index.vue', 0, 'C', 0, 0, 'simshield:log:list', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (127102101, '操作详情', 127102100, NULL, NULL, 0, 'F', 0, 0, 'simshield:log:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128100100, 'SIMKEY管理', 0, '/supersim', 'Layout', 0, 'M', 0, 0, NULL, 'simkey', NULL, 24, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128101100, 'SIMKEY用户管理', 128100100, '/supersim/simkey/user', '/supersim/simkey/user/index.vue', 0, 'C', 0, 0, 'simkey:user:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128101101, '启用/禁用', 128101100, NULL, NULL, 0, 'F', 0, 0, 'simkey:user:status', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128101102, '绑定', 128101100, NULL, NULL, 0, 'F', 0, 0, 'simkey:user:bind', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128101103, '解绑', 128101100, NULL, NULL, 0, 'F', 0, 0, 'simkey:user:unbind', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128101104, '证书详情', 128101100, NULL, NULL, 0, 'F', 0, 0, 'simkey:user:cert', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128102100, 'SIMKEY证书管理', 128100100, '/supersim/simkey/cert', '/supersim/simkey/cert/index.vue', 0, 'C', 0, 0, 'simkey:cert:list', NULL, 'simKeyCert', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128102101, '历史证书', 128100100, '/supersim/simkey/cert/history', '/supersim/simkey/cert/history.vue', 0, 'F', 1, 0, 'simkey:cert:histpry', NULL, 'simKeyCert', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128103100, '白名单管理', 128100100, '/supersim/simkey/whitelist', '/supersim/simkey/whitelist/index.vue', 0, 'C', 0, 0, 'simkey:white:list', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128103101, '新增', 128103100, NULL, NULL, 0, 'F', 0, 0, 'simkey:white:add', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128103102, '批量导入', 128103100, NULL, NULL, 0, 'F', 0, 0, 'simkey:white:import', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128103103, '删除', 128103100, NULL, NULL, 0, 'F', 0, 0, 'simkey:white:delete', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128104100, '业务操作记录', 128100100, '/supersim/simkey/apiused', '/supersim/apiused/index.vue', 0, 'C', 0, 0, 'simkey:log:list', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (128104101, '操作详情', 128104100, NULL, NULL, 0, 'F', 0, 0, 'simkey:log:info', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (129100100, '工单管理', 0, '/workorder', 'Layout', 0, 'M', 0, 0, NULL, 'workorder', NULL, 26, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (129101100, '我的工单', 129100100, '/workorder/my', '/workorder/list/my.vue', 0, 'C', 0, 0, 'work:order:list', NULL, 'myWorkOrder', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (129101101, '新增工单', 129101100, NULL, NULL, 0, 'F', 0, 0, 'work:order:add', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (129101102, '撤销工单', 129101100, NULL, NULL, 0, 'F', 0, 0, 'work:order:cancel', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (129102100, '待处理工单', 129100100, '/workorder', '/workorder/list/index.vue', 0, 'C', 0, 0, 'work:order:pendinglist', NULL, 'workOrder', 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (129102101, '开始处理', 129102100, NULL, NULL, 0, 'F', 0, 0, 'work:order:startprocess', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (129102102, '处理完成', 129102100, NULL, NULL, 0, 'F', 0, 0, 'work:order:endprocess', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (129103100, '已处理工单', 129100100, '/workorder/finished', '/workorder/list/finish.vue', 0, 'C', 0, 0, 'work:order:processed', NULL, 'workOrderFinished', 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (129104100, '工单详情', 129100100, '/workorder/detail', '/workorder/detail/index.vue', 0, 'F', 1, 0, 'work:order:comment', NULL, NULL, 9, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130100100, '密码产品', 0, '/product', 'Layout', 0, 'M', 0, 0, NULL, 'serviceproduct', NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130101100, '产品信息', 130100100, '/product/list', '/product/list/index.vue', 0, 'C', 0, 0, 'product:list', NULL, 'serviceProduct', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130101101, '新建密码产品', 130101100, '/product/add', '/product/add/index.vue', 0, 'F', 1, 0, 'work:product:add', NULL, 'serviceProduct', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130101102, '上架密码产品', 130101100, NULL, NULL, 0, 'F', 0, 0, 'work:product:open', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130101103, '下架密码产品', 130101100, NULL, NULL, 0, 'F', 0, 0, 'work:product:close', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130101104, '预览密码产品', 130101100, '/product/preview', '/product/detail/index.vue', 0, 'F', 1, 0, 'work:product:preview', NULL, 'serviceProduct', 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130101105, '密码产品附件', 130101100, NULL, NULL, 0, 'F', 0, 0, 'work:product:attach', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130101106, '删除密码产品', 130101100, NULL, NULL, 0, 'F', 0, 0, 'work:product:delete', NULL, NULL, 7, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130101107, '编辑密码产品', 130101100, NULL, NULL, 0, 'F', 0, 0, 'work:product:edit', NULL, NULL, 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130102100, '产品信息（租户端）', 130100100, '/product/store/list', '/product/store/index.vue', 0, 'C', 0, 0, 'work:product:tenantlist', NULL, 'serviceProductStore', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130102101, '产品详情', 130102100, '/product/detail', '/product/detail/index.vue', 0, 'F', 1, 0, 'work:product:tenantdetail', NULL, 'serviceProduct', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130103100, '外置许可授权', 130100100, '/product/license', '/product/license/index.vue', 0, 'C', 0, 0, 'outservice:lisence:list', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (130103101, '授权', 130103100, NULL, NULL, 0, 'F', 0, 0, 'outservice:lisence:auth', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (131100100, '消息管理', 0, '/message', 'Layout', 0, 'M', 0, 0, NULL, 'message', NULL, 25, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (131101100, '消息管理', 131100100, '/system/message', '/monitor/message/index.vue', 0, 'C', 0, 0, 'message:list', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (131101101, '消息铃铛', 131101100, NULL, NULL, 0, 'F', 0, 0, 'message:bellList', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (131101102, '已读消息', 131101100, NULL, NULL, 0, 'F', 0, 0, 'message:read', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (131101103, '全部已读', 131101100, NULL, NULL, 0, 'F', 0, 0, 'message:readAll', NULL, NULL, 4, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (131101104, '消息弹窗', 131101100, NULL, NULL, 0, 'F', 0, 0, 'message:find', NULL, NULL, 5, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (131101105, '删除消息', 131101100, NULL, NULL, 0, 'F', 0, 0, 'message:delete', NULL, NULL, 6, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (132100100, '加解密规则引擎', 0, '/application', 'Layout', 0, 'M', 0, 0, NULL, 'enc-rule', NULL, 13, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (132101100, '密钥管理', 132100100, '/encryptJudge/key', '/encryptJudge/key/index.vue', 0, 'C', 0, 0, 'encrypt:judge:keylist', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (132101101, '新增', 132101100, NULL, NULL, 0, 'F', 0, 0, 'encrypt:judge:addkey', NULL, NULL, 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (132101102, '编辑', 132101100, NULL, NULL, 0, 'F', 0, 0, 'encrypt:judge:editkey', NULL, NULL, 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (132101103, '加解密规则引擎删除', 132101100, NULL, NULL, 0, 'F', 0, 0, 'encrypt:judge:deletekey', NULL, NULL, 3, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (133100100, '密码资源', 0, '/enc-source','Layout', 0, 'M', 0, 0, NULL, 'resource-group', NULL, 8, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (133101100, '服务组列表', 133100100, '/services/group', '/services/group/index.vue', 0, 'C', 0, 0, 'servicegroup:tenant:list', NULL, 'serviceGroup', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (133101101, '服务列表', 133101100, '/services/group/servicelist', '/services/group/servicelist.vue', 0, 'F', 1, 0, 'serviceinfo:tenant:list', NULL, 'serviceGroup', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (133102100, '设备组列表', 133100100, '/device/deviceGroup', '/device/deviceGroup/index.vue', 0, 'C', 0, 0, 'devicegroup:tenant:list', NULL, 'deviceGroup', 2, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_menu" VALUES (133102101, '密码资源设备列表', 133102100, '/device/deviceGroup/detail', '/device/deviceGroup/detail.vue', 0, 'F', 1, 0, 'deviceinfo:tenant:list', NULL, 'deviceGroup', 1, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."sys_role_menu";
CREATE TABLE "ccsp_common"."sys_role_menu" (
  "id" int8 NOT NULL,
  "menu_id" int8,
  "role_id" int8,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."sys_role_menu"."menu_id" IS '菜单ID';
COMMENT ON COLUMN "ccsp_common"."sys_role_menu"."role_id" IS '角色ID';
COMMENT ON COLUMN "ccsp_common"."sys_role_menu"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."sys_role_menu"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."sys_role_menu"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."sys_role_menu"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."sys_role_menu"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."sys_role_menu" IS '角色菜单对应关系表';

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1100100100, 100100100, 1, '平台首页', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1102100100, 102100100, 1, '许可授权管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1102101100, 102101100, 1, '许可申请', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1102101101, 102101101, 1, '申请', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1102101102, 102101102, 1, '下载凭证', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1102102100, 102102100, 1, '许可管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1102102101, 102102101, 1, '解析许可内容', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1102102102, 102102102, 1, '导入许可', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1102103100, 102103100, 1, '服务续期管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1102103101, 102103101, 1, '续约', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103100100, 103100100, 1, '租户管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101100, 103101100, 1, '租户管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101101, 103101101, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101102, 103101102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101103, 103101103, 1, '启动', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101104, 103101104, 1, '停服', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101105, 103101105, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101112, 103101112, 1, '配置地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101113, 103101113, 1, '新增业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101114, 103101114, 1, '编辑业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101115, 103101115, 1, '删除业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101118, 103101118, 1, '密码产品配额', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101119, 103101119, 1, '产品配额开通', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101120, 103101120, 1, '产品配额关闭', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101121, 103101121, 1, '产品配额编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101122, 103101122, 1, '产品配额详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101123, 103101123, 1, '产品配额变更记录列表', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101124, 103101124, 1, '服务组管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101127, 103101127, 1, '设备组管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101128, 103101128, 1, '绑定异构设备组', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103101129, 103101129, 1, '解绑异构设备组', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103102100, 103102100, 1, '待审核租户', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103102101, 103102101, 1, '审核', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103103100, 103103100, 1, '已审核租户', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103103101, 103103101, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1103103102, 103103102, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104100100, 104100100, 1, '服务管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104102100, 104102100, 1, '服务组管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104102101, 104102101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104102102, 104102102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104102103, 104102103, 1, '服务管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104102104, 104102104, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104102105, 104102105, 1, '可绑定服务', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104102107, 104102107, 1, '释放服务', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104104100, 104104100, 1, '服务管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104104101, 104104101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104104102, 104104102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104104103, 104104103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104104104, 104104104, 1, '启动', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104104105, 104104105, 1, '停止', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104104106, 104104106, 1, '重启', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104104107, 104104107, 1, '同步数据库密码', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104105100, 104105100, 1, '服务类型管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104105101, 104105101, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104106100, 104106100, 1, '网关管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104106101, 104106101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104106102, 104106102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104106103, 104106103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104107100, 104107100, 1, '路由管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104107101, 104107101, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104109100, 104109100, 1, '数据库管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104109101, 104109101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104109102, 104109102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104109103, 104109103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104109104, 104109104, 1, '实例列表', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104109105, 104109105, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104109107, 104109107, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104109108, 104109108, 1, '修改密码', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104110100, 104110100, 1, '镜像管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104110101, 104110101, 1, '新增', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104110102, 104110102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104110103, 104110103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1104110104, 104110104, 1, '启用/禁用', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105100100, 105100100, 1, '设备管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105101100, 105101100, 1, '云密码机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105101101, 105101101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105101102, 105101102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105101103, 105101103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105101104, 105101104, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105101105, 105101105, 1, '网络配置', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105101106, 105101106, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105101107, 105101107, 1, '新增', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105101108, 105101108, 1, '创建虚拟密码机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105102100, 105102100, 1, '虚拟密码机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105102101, 105102101, 1, '创建虚拟密码机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105102102, 105102102, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105102103, 105102103, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105102104, 105102104, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105102105, 105102105, 1, '启动', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105102106, 105102106, 1, '停止', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105102107, 105102107, 1, '重启', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105102108, 105102108, 1, '强制删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105102109, 105102109, 1, '管理系统', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105103100, 105103100, 1, '物理密码机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105103101, 105103101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105103102, 105103102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105103103, 105103103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105103104, 105103104, 1, '详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105103107, 105103107, 1, '强制删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105103108, 105103108, 1, '管理系统', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105105100, 105105100, 1, '设备组管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105105101, 105105101, 1, '新建', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105105102, 105105102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105105103, 105105103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105105104, 105105104, 1, '设备管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105105105, 105105105, 1, '绑定设备', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105105106, 105105106, 1, '释放设备', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105106100, 105106100, 1, '厂商管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105106101, 105106101, 1, '新增', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105106102, 105106102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105106103, 105106103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105108100, 105108100, 1, '设备类型管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105108101, 105108101, 1, '新增', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105108102, 105108102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105108103, 105108103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105108104, 105108104, 1, '停用', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105108105, 105108105, 1, '启用', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1105108106, 105108106, 1, '监控配置', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1120100100, 120100100, 1, '用户管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1120101100, 120101100, 1, '用户信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1120101102, 120101102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1120102100, 120102100, 1, '注册记录', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1120102101, 120102101, 1, '注册', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1120102103, 120102103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1120103100, 120103100, 1, '单位管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1120103101, 120103101, 1, '新增', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1120103102, 120103102, 1, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1120103103, 120103103, 1, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1121100100, 121100100, 1, '系统管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1121106100, 121106100, 1, '主密钥管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1121106101, 121106101, 1, '备份', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1121106102, 121106102, 1, '恢复', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1121107100, 121107100, 1, '关于平台', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1121109100, 121109100, 1, '电子签章密钥管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1121109101, 121109101, 1, '备份', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1121109102, 121109102, 1, '恢复', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1123100100, 123100100, 1, '监控告警', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1123102100, 123102100, 1, ' 服务器监控', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1123103100, 123103100, 1, '大屏', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1123104100, 123104100, 1, '告警信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1123104101, 123104101, 1, '处置', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1123105100, 123105100, 1, '告警历史', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1123106100, 123106100, 1, '告警配置', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1129100100, 129100100, 1, '工单管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1129102100, 129102100, 1, '待处理工单', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1129102101, 129102101, 1, '开始处理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1129102102, 129102102, 1, '处理完成', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1129103100, 129103100, 1, '已处理工单', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1129104100, 129104100, 1, '工单详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1130100100, 130100100, 1, '密码产品', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1130101100, 130101100, 1, '产品信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1130101101, 130101101, 1, '新建密码产品', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1130101102, 130101102, 1, '上架密码产品', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1130101103, 130101103, 1, '下架密码产品', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1130101104, 130101104, 1, '预览密码产品', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1130101105, 130101105, 1, '密码产品附件', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1130101106, 130101106, 1, '删除密码产品', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1130101107, 130101107, 1, '编辑密码产品', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1131100100, 131100100, 1, '消息管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1131101100, 131101100, 1, '消息管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1131101101, 131101101, 1, '消息铃铛', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1131101102, 131101102, 1, '已读消息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1131101103, 131101103, 1, '全部已读', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1131101104, 131101104, 1, '消息弹窗', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (1131101105, 131101105, 1, '删除消息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2100100100, 100100100, 2, '平台首页', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120100100, 120100100, 2, '用户管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120101100, 120101100, 2, '用户信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120101103, 120101103, 2, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120101104, 120101104, 2, '绑定UKey', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120101105, 120101105, 2, '设置账号有效期', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120101106, 120101106, 2, '启用', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120101107, 120101107, 2, '禁用', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120101108, 120101108, 2, '解锁', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120101109, 120101109, 2, '重置口令', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120102100, 120102100, 2, '注册记录', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2120102102, 120102102, 2, '审核', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2121100100, 121100100, 2, '系统管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2121103100, 121103100, 2, '口令黑名单', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2121103101, 121103101, 2, '添加', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2121103102, 121103102, 2, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2121103103, 121103103, 2, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2121104100, 121104100, 2, '人员管理配置', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2121104101, 121104101, 2, '修改配置', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2121105100, 121105100, 2, '系统配置', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2121105101, 121105101, 2, '修改配置', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2121107100, 121107100, 2, '关于平台', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (2123103100, 123103100, 2, '大屏', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (3100100100, 100100100, 3, '平台首页', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (3121100100, 121100100, 3, '系统管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (3121107100, 121107100, 3, '关于平台', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (3122100100, 122100100, 3, '日志管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (3122101100, 122101100, 3, '管理日志', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (3122101101, 122101101, 3, '批量审计', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (3122101102, 122101102, 3, '导出', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (3123103100, 123103100, 3, '大屏', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4101100100, 101100100, 4, '租户首页', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4106100100, 106100100, 4, '应用管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4106102100, 106102100, 4, '应用信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4106102101, 106102101, 4, '新建', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4106102102, 106102102, 4, '详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4106102103, 106102103, 4, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4106102104, 106102104, 4, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4106102105, 106102105, 4, '业务管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4106102106, 106102106, 4, '新增', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4106102107, 106102107, 4, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4109100100, 109100100, 4, '数据加解密', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4109101100, 109101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4109101101, 109101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4109101102, 109101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4109101103, 109101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4110100100, 110100100, 4, '签名验签', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4110101100, 110101100, 4, '信任域管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4110101101, 110101101, 4, '新建', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4110101102, 110101102, 4, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4110101103, 110101103, 4, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4110102100, 110102100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4110102101, 110102101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4110102102, 110102102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4110102103, 110102103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4112100100, 112100100, 4, '密钥管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4112101100, 112101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4112101101, 112101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4112101102, 112101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4112101103, 112101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4113100100, 113100100, 4, '文件加密', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4113101100, 113101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4113101101, 113101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4113101102, 113101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4113101103, 113101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4114100100, 114100100, 4, '数据库加密', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4114101100, 114101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4114101101, 114101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4114101102, 114101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4114101103, 114101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4115100100, 115100100, 4, '时间戳', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4115101100, 115101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4115101101, 115101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4115101102, 115101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4115101103, 115101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4116100100, 116100100, 4, '协同签名', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4116101100, 116101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4116101101, 116101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4116101102, 116101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4116101103, 116101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4117100100, 117100100, 4, '动态令牌', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4117101100, 117101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4117101101, 117101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4117101102, 117101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4117101103, 117101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4118100100, 118100100, 4, '电子签章', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4118101100, 118101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4118101101, 118101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4118101102, 118101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4118101103, 118101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4119100100, 119100100, 4, 'SSLVPN加密通道', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4119101100, 119101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4119101101, 119101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4119101102, 119101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4119101103, 119101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4120100100, 120100100, 4, '用户管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4120101100, 120101100, 4, '用户信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4120101102, 120101102, 4, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4120102100, 120102100, 4, '注册记录', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4120102101, 120102101, 4, '注册', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4120102103, 120102103, 4, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (4123103100, 123103100, 4, '大屏', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4126100100, 126100100, 4, '数字证书认证', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4126101100, 126101100, 4, '资源信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4126101101, 126101101, 4, '根据业务类型查询业务地址', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4126101102, 126101102, 4, 'SDK下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4126101103, 126101103, 4, 'API下载', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4129100100, 129100100, 4, '工单管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4129101100, 129101100, 4, '我的工单', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4129101101, 129101101, 4, '新增工单', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4129101102, 129101102, 4, '撤销工单', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4129104100, 129104100, 4, '工单详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4130100100, 130100100, 4, '密码产品', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4130102100, 130102100, 4, '产品信息（租户端）', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4130102101, 130102101, 4, '产品详情', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4131100100, 131100100, 4, '消息管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4131101100, 131101100, 4, '消息管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4131101101, 131101101, 4, '消息铃铛', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4131101102, 131101102, 4, '已读消息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4131101103, 131101103, 4, '全部已读', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4131101104, 131101104, 4, '消息弹窗', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4131101105, 131101105, 4, '删除消息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4132100100, 132100100, 4, '加解密规则引擎', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4132101100, 132101100, 4, '密钥管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4132101101, 132101101, 4, '新增', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4132101102, 132101102, 4, '编辑', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (4132101103, 132101103, 4, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu"
VALUES (5101100100, 101100100, 5, '租户首页', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120100100, 120100100, 5, '用户管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120101100, 120101100, 5, '用户信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120101103, 120101103, 5, '删除', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120101104, 120101104, 5, '绑定UKey', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120101105, 120101105, 5, '设置账号有效期', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120101106, 120101106, 5, '启用', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120101107, 120101107, 5, '禁用', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120101108, 120101108, 5, '解锁', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120101109, 120101109, 5, '重置口令', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120102100, 120102100, 5, '注册记录', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5120102102, 120102102, 5, '审核', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5121100100, 121100100, 5, '系统管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5121104100, 121104100, 5, '人员管理配置', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (5123103100, 123103100, 5, '大屏', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (6101100100, 101100100, 6, '租户首页', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (6122100100, 122100100, 6, '日志管理', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (6122102100, 122102100, 6, '租户管理日志', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (6122102101, 122102101, 6, '批量审计', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (6122102102, 122102102, 6, '导出', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (6123103100, 123103100, 6, '大屏', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_common"."sys_role_menu" VALUES (12101100101, 101100101, 12, '单位首页', NULL, NULL, NULL, NULL);
INSERT INTO ccsp_common.sys_role_menu VALUES (1130103100, 130103100, 1, '外置许可授权', NULL, NULL, NULL, NULL);
INSERT INTO ccsp_common.sys_role_menu VALUES (1130103101, 130103101, 1, '授权', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."sys_task";
CREATE TABLE "ccsp_common"."sys_task" (
  "task_id" int8 NOT NULL,
  "task_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "task_group" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "server_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "method_url" varchar(1000) COLLATE "pg_catalog"."default",
  "json_param" text COLLATE "pg_catalog"."default" NOT NULL,
  "task_status" int4 NOT NULL DEFAULT 0,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(300) COLLATE "pg_catalog"."default",
  "timeout" int4,
  "start_time" varchar(255) COLLATE "pg_catalog"."default",
  "end_time" varchar(255) COLLATE "pg_catalog"."default",
  "policy" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "ccsp_common"."sys_task"."task_id" IS '任务号';
COMMENT ON COLUMN "ccsp_common"."sys_task"."task_name" IS '任务名称';
COMMENT ON COLUMN "ccsp_common"."sys_task"."task_group" IS '任务组名;执行任务串行';
COMMENT ON COLUMN "ccsp_common"."sys_task"."server_id" IS '服务模块';
COMMENT ON COLUMN "ccsp_common"."sys_task"."method_url" IS '调用接口';
COMMENT ON COLUMN "ccsp_common"."sys_task"."json_param" IS 'json格式参数';
COMMENT ON COLUMN "ccsp_common"."sys_task"."task_status" IS '状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时)';
COMMENT ON COLUMN "ccsp_common"."sys_task"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."sys_task"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_common"."sys_task"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."sys_task"."timeout" IS '超时时间;单位秒';
COMMENT ON COLUMN "ccsp_common"."sys_task"."start_time" IS '开始时间';
COMMENT ON COLUMN "ccsp_common"."sys_task"."end_time" IS '结束时间';
COMMENT ON COLUMN "ccsp_common"."sys_task"."policy" IS '是否允许重复执行;0-不允许，1允许';
COMMENT ON TABLE "ccsp_common"."sys_task" IS '异步任务表';

-- ----------------------------
-- Records of sys_task
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task_log
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."sys_task_log";
CREATE TABLE "ccsp_common"."sys_task_log" (
  "task_log_id" int8 NOT NULL,
  "task_id" int8 NOT NULL,
  "task_message" varchar(3000) COLLATE "pg_catalog"."default",
  "status" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_info" varchar(6000) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "trigger_time" int8
)
;
COMMENT ON COLUMN "ccsp_common"."sys_task_log"."task_log_id" IS '任务日志ID';
COMMENT ON COLUMN "ccsp_common"."sys_task_log"."task_id" IS '任务ID';
COMMENT ON COLUMN "ccsp_common"."sys_task_log"."task_message" IS '日志信息';
COMMENT ON COLUMN "ccsp_common"."sys_task_log"."status" IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN "ccsp_common"."sys_task_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "ccsp_common"."sys_task_log"."create_time" IS '创建时间;单位毫秒';
COMMENT ON COLUMN "ccsp_common"."sys_task_log"."trigger_time" IS '触发时间;任务服务上送';
COMMENT ON TABLE "ccsp_common"."sys_task_log" IS '异步任务执行日志表';

-- ----------------------------
-- Records of sys_task_log
-- ----------------------------

-- ----------------------------
-- Table structure for user_auth_code
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."user_auth_code";
CREATE TABLE "ccsp_common"."user_auth_code" (
  "id" int8 NOT NULL,
  "user_id" int8 NOT NULL,
  "auth_code" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "create_type" int4 NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."user_auth_code"."user_id" IS '账号ID';
COMMENT ON COLUMN "ccsp_common"."user_auth_code"."auth_code" IS '口令;加密存储';
COMMENT ON COLUMN "ccsp_common"."user_auth_code"."create_type" IS '来源 1初始化 2修改口令 3重置口令';
COMMENT ON COLUMN "ccsp_common"."user_auth_code"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_common"."user_auth_code"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."user_auth_code"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."user_auth_code"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."user_auth_code"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."user_auth_code"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."user_auth_code" IS '用户历史口令';

-- ----------------------------
-- Records of user_auth_code
-- ----------------------------

-- ----------------------------
-- Table structure for user_cert
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."user_cert";
CREATE TABLE "ccsp_common"."user_cert" (
  "id" int8 NOT NULL,
  "user_id" int8 NOT NULL,
  "cert" varchar(3000) COLLATE "pg_catalog"."default" NOT NULL,
  "serial" varchar(300) COLLATE "pg_catalog"."default",
  "cert_type" int4,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."user_cert"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_common"."user_cert"."cert" IS '证书;加密存储';
COMMENT ON COLUMN "ccsp_common"."user_cert"."serial" IS '证书;加密存储';
COMMENT ON COLUMN "ccsp_common"."user_cert"."cert_type" IS '证书类型;1UKEY证书';
COMMENT ON COLUMN "ccsp_common"."user_cert"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_common"."user_cert"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."user_cert"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."user_cert"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."user_cert"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."user_cert"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."user_cert" IS '用户认证证书表';

-- ----------------------------
-- Records of user_cert
-- ----------------------------

-- ----------------------------
-- Table structure for user_info
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."user_info";
CREATE TABLE "ccsp_common"."user_info" (
  "user_id" int8 NOT NULL,
  "user_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "user_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "user_status" int4 NOT NULL,
  "disable_reason_id" int4 NOT NULL DEFAULT 0,
  "phone_num" varchar(255) COLLATE "pg_catalog"."default",
  "email_address" varchar(255) COLLATE "pg_catalog"."default",
  "tenant_id" int8 NOT NULL,
  "tenant_code" varchar(255) COLLATE "pg_catalog"."default",
  "organization_id" int8,
  "role_id" int8 NOT NULL,
  "salt" varchar(270) COLLATE "pg_catalog"."default" NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "app_id" int8
)
;
COMMENT ON COLUMN "ccsp_common"."user_info"."user_id" IS '账户ID';
COMMENT ON COLUMN "ccsp_common"."user_info"."user_code" IS '登录账号';
COMMENT ON COLUMN "ccsp_common"."user_info"."user_name" IS '账号名称';
COMMENT ON COLUMN "ccsp_common"."user_info"."user_status" IS '用户状态;1启用 2禁用 3锁定';
COMMENT ON COLUMN "ccsp_common"."user_info"."disable_reason_id" IS '禁用原因;0非禁用状态 1手动禁用 2长期未登录禁用';
COMMENT ON COLUMN "ccsp_common"."user_info"."phone_num" IS '手机号码;加密存储';
COMMENT ON COLUMN "ccsp_common"."user_info"."email_address" IS '邮箱;加密存储';
COMMENT ON COLUMN "ccsp_common"."user_info"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ccsp_common"."user_info"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "ccsp_common"."user_info"."organization_id" IS '组织机构ID';
COMMENT ON COLUMN "ccsp_common"."user_info"."role_id" IS '角色ID';
COMMENT ON COLUMN "ccsp_common"."user_info"."salt" IS '用户盐值;加密存储';
COMMENT ON COLUMN "ccsp_common"."user_info"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_common"."user_info"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."user_info"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."user_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."user_info"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."user_info"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."user_info" IS '用户信息表';

-- ----------------------------
-- Records of user_info
-- ----------------------------

-- ----------------------------
-- Table structure for user_register
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."user_register";
CREATE TABLE "ccsp_common"."user_register" (
  "user_register_id" int8 NOT NULL,
  "user_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "user_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "phone_num" varchar(255) COLLATE "pg_catalog"."default",
  "email_address" varchar(100) COLLATE "pg_catalog"."default",
  "tenant_id" int8 NOT NULL,
  "tenant_code" varchar(255) COLLATE "pg_catalog"."default",
  "organization_id" int8,
  "role_id" int8 NOT NULL,
  "ukey_cert" varchar(2000) COLLATE "pg_catalog"."default",
  "ukey_serial" varchar(100) COLLATE "pg_catalog"."default",
  "auth_code" varchar(64) COLLATE "pg_catalog"."default",
  "user_type" int4,
  "audit_status" int4 NOT NULL,
  "audit_time" varchar(30) COLLATE "pg_catalog"."default",
  "audit_by" int8,
  "audit_suggestion" varchar(1000) COLLATE "pg_catalog"."default",
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "hmac" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."user_register"."user_register_id" IS '账户注册ID';
COMMENT ON COLUMN "ccsp_common"."user_register"."user_code" IS '登录账号';
COMMENT ON COLUMN "ccsp_common"."user_register"."user_name" IS '账号名称';
COMMENT ON COLUMN "ccsp_common"."user_register"."phone_num" IS '手机号码';
COMMENT ON COLUMN "ccsp_common"."user_register"."email_address" IS '邮箱';
COMMENT ON COLUMN "ccsp_common"."user_register"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ccsp_common"."user_register"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "ccsp_common"."user_register"."organization_id" IS '部门ID';
COMMENT ON COLUMN "ccsp_common"."user_register"."role_id" IS '角色ID';
COMMENT ON COLUMN "ccsp_common"."user_register"."ukey_cert" IS 'UKEY证书';
COMMENT ON COLUMN "ccsp_common"."user_register"."ukey_serial" IS 'UKEY序列号';
COMMENT ON COLUMN "ccsp_common"."user_register"."auth_code" IS '用户口令';
COMMENT ON COLUMN "ccsp_common"."user_register"."user_type" IS '1平台管理员 2租户用户';
COMMENT ON COLUMN "ccsp_common"."user_register"."audit_status" IS '0 待审核 1 审核通过 2 拒绝';
COMMENT ON COLUMN "ccsp_common"."user_register"."audit_time" IS '审核时间';
COMMENT ON COLUMN "ccsp_common"."user_register"."audit_by" IS '审核人';
COMMENT ON COLUMN "ccsp_common"."user_register"."audit_suggestion" IS '审核意见';
COMMENT ON COLUMN "ccsp_common"."user_register"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_common"."user_register"."hmac" IS '完整性';
COMMENT ON COLUMN "ccsp_common"."user_register"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."user_register"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."user_register"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."user_register"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."user_register"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."user_register" IS '用户注册信息表';

-- ----------------------------
-- Records of user_register
-- ----------------------------

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."user_role";
CREATE TABLE "ccsp_common"."user_role" (
  "role_id" int8 NOT NULL,
  "role_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "sord_num" int4 NOT NULL,
  "tenant_id" int8,
  "role_type" int4,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."user_role"."role_id" IS '角色ID';
COMMENT ON COLUMN "ccsp_common"."user_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "ccsp_common"."user_role"."sord_num" IS '排序序号';
COMMENT ON COLUMN "ccsp_common"."user_role"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ccsp_common"."user_role"."role_type" IS '角色类别;1平台角色 2租户角色';
COMMENT ON COLUMN "ccsp_common"."user_role"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_common"."user_role"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."user_role"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."user_role"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."user_role"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."user_role"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."user_role" IS '用户角色表';

-- ----------------------------
-- Records of user_role
-- ----------------------------
INSERT INTO "ccsp_common"."user_role" VALUES (1, '操作员', 1, NULL, 1, 0, '初始化管理员', 1, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO "ccsp_common"."user_role" VALUES (2, '管理员', 2, NULL, 1, 0, '初始化安全员', 1, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO "ccsp_common"."user_role" VALUES (3, '审计员', 3, NULL, 1, 0, '初始化审计员', 1, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO "ccsp_common"."user_role" VALUES (4, '租户操作员', 1, NULL, 2, 0, NULL, NULL, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO "ccsp_common"."user_role" VALUES (5, '租户管理员', 2, NULL, 2, 0, NULL, NULL, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO "ccsp_common"."user_role" VALUES (6, '租户审计员', 3, NULL, 2, 0, NULL, NULL, '2023-02-19 20:41:51', NULL, NULL);
INSERT INTO "ccsp_common"."user_role" VALUES (12, '单位审计员', 3, NULL, 4, 0, NULL, 1, '2023-02-19 20:41:51', NULL, NULL);

-- ----------------------------
-- Table structure for user_root_ca
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."user_root_ca";
CREATE TABLE "ccsp_common"."user_root_ca" (
  "id" int8 NOT NULL,
  "cert_type" varchar(255) COLLATE "pg_catalog"."default",
  "cert_format" varchar(255) COLLATE "pg_catalog"."default",
  "start_time" varchar(255) COLLATE "pg_catalog"."default",
  "end_time" varchar(255) COLLATE "pg_catalog"."default",
  "issuer" varchar(1000) COLLATE "pg_catalog"."default",
  "public_key" varchar(255) COLLATE "pg_catalog"."default",
  "verify_cert" varchar(1000) COLLATE "pg_catalog"."default",
  "version" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."cert_type" IS '证书类型;1 ukey证书';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."cert_format" IS '证书格式;证书类型 x509';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."start_time" IS '证书起始时间;证书起始时间';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."end_time" IS '证书过期时间;证书过期时间';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."issuer" IS '颁发者;颁发者';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."public_key" IS '证书公钥;证书公钥';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."verify_cert" IS '验签证书（BASE64编码）;验签证书（BASE64编码）';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."version" IS '版本;版本';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."user_root_ca"."update_time" IS '更新时间';

-- ----------------------------
-- Records of user_root_ca
-- ----------------------------
INSERT INTO "ccsp_common"."user_root_ca" VALUES (1, '1', 'X.509', 'Wed Sep 04 14:47:14 CST 2019', 'Fri Aug 23 14:47:14 CST 2069', 'C=CN,ST=北京,L=北京,O=三未信安,CN=SANSEC PD ROOT,E=<EMAIL>', 'SM2 Public Key      AffineX: f2a8cc6de14910861549e0f537e639670b40d881aeae1adc3e2af1233a703415      AffineY: b2596e160f798e384376afbca6c855b64ff629fcb35f7556e84b99745c4376e3', 'MIICOzCCAd6gAwIBAgIIbAbZnrBMKUgwDAYIKoEcz1UBg3UFADCBhTELMAkGA1UEBhMCQ04xDzANBgNVBAgMBuWMl+S6rDEPMA0GA1UEBwwG5YyX5LqsMRUwEwYDVQQKDAzkuInmnKrkv6HlrokxFzAVBgNVBAMMDlNBTlNFQyBQRCBST09UMSQwIgYJKoZIhvcNAQkBFhVzdXBwb3J0QHNhbnNlYy5jb20uY24wIBcNMTkwOTA0MDY0NzE0WhgPMjA2OTA4MjMwNjQ3MTRaMIGFMQswCQYDVQQGEwJDTjEPMA0GA1UECAwG5YyX5LqsMQ8wDQYDVQQHDAbljJfkuqwxFTATBgNVBAoMDOS4ieacquS/oeWuiTEXMBUGA1UEAwwOU0FOU0VDIFBEIFJPT1QxJDAiBgkqhkiG9w0BCQEWFXN1cHBvcnRAc2Fuc2VjLmNvbS5jbjBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABPKozG3hSRCGFUng9TfmOWcLQNiBrq4a3D4q8SM6cDQVslluFg95jjhDdq+8pshVtk/2KfyzX3VW6EuZdFxDduOjMjAwMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFELufEtM6TYgi+igpD6H062y1dOKMAwGCCqBHM9VAYN1BQADSQAwRgIhAJVvgnYYtxib6Og51spY0EcWmEZttRh2e+l207ygPicyAiEAu8a2kdv0H5YjWFAQtp7WI4inQdpLpssBJu0w19iVaLQ=', '3', '三未信安PD根证', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for user_security_extend
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_common"."user_security_extend";
CREATE TABLE "ccsp_common"."user_security_extend" (
  "id" int8 NOT NULL,
  "user_id" int8 NOT NULL,
  "hmac" varchar(255) COLLATE "pg_catalog"."default",
  "last_active_time" varchar(30) COLLATE "pg_catalog"."default",
  "last_update_pwd_time" varchar(30) COLLATE "pg_catalog"."default",
  "account_start_time" varchar(30) COLLATE "pg_catalog"."default",
  "account_end_time" varchar(30) COLLATE "pg_catalog"."default",
  "login_error_times" int4,
  "unlock_time" int8,
  "password_change_flag" int4 NOT NULL DEFAULT 1,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."user_id" IS '账户ID';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."hmac" IS '完整性校验值';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."last_active_time" IS '最后一次活跃时间';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."last_update_pwd_time" IS '最后一次修改密码时间';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."account_start_time" IS '账户有效期开始时间';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."account_end_time" IS '账户有效期结束时间';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."login_error_times" IS '登录失败次数';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."unlock_time" IS '解锁时间（时间戳）';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."password_change_flag" IS '口令是否需要修改';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_common"."user_security_extend"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_common"."user_security_extend" IS '用户安全信息扩展表';

-- ----------------------------
-- Records of user_security_extend
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table auth_code_blacklist
-- ----------------------------
ALTER TABLE "ccsp_common"."auth_code_blacklist" ADD CONSTRAINT "auth_code_blacklist_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table ccsp_message
-- ----------------------------
ALTER TABLE "ccsp_common"."ccsp_message" ADD CONSTRAINT "ccsp_message_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table config
-- ----------------------------
ALTER TABLE "ccsp_common"."config" ADD CONSTRAINT "config_pkey" PRIMARY KEY ("config_id");

-- ----------------------------
-- Primary Key structure for table config_regular
-- ----------------------------
ALTER TABLE "ccsp_common"."config_regular" ADD CONSTRAINT "config_regular_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_busi_service_type
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_busi_service_type" ADD CONSTRAINT "dic_busi_service_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_busi_type
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_busi_type" ADD CONSTRAINT "dic_busi_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_device_type
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_device_type" ADD CONSTRAINT "dic_device_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_gentype
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_gentype" ADD CONSTRAINT "dic_gentype_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_keyalgo
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_keyalgo" ADD CONSTRAINT "dic_keyalgo_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_keylen
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_keylen" ADD CONSTRAINT "dic_keylen_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_keylifeattribute
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_keylifeattribute" ADD CONSTRAINT "dic_keylifeattribute_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_keytype
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_keytype" ADD CONSTRAINT "dic_keytype_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_keyuse
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_keyuse" ADD CONSTRAINT "dic_keyuse_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_script_path
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_script_path" ADD CONSTRAINT "dic_script_path_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_share_group_type
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_share_group_type" ADD CONSTRAINT "dic_share_group_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_statistic
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_statistic" ADD CONSTRAINT "dic_statistic_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_sys_data
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_sys_data" ADD CONSTRAINT "dic_sys_data_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_tenant_status
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_tenant_status" ADD CONSTRAINT "dic_tenant_status_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_user_type
-- ----------------------------
ALTER TABLE "ccsp_common"."dic_user_type" ADD CONSTRAINT "dic_user_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table organization_info
-- ----------------------------
ALTER TABLE "ccsp_common"."organization_info" ADD CONSTRAINT "organization_info_pkey" PRIMARY KEY ("organization_id");

-- ----------------------------
-- Primary Key structure for table sys_job
-- ----------------------------
ALTER TABLE "ccsp_common"."sys_job" ADD CONSTRAINT "sys_job_pkey" PRIMARY KEY ("job_id");

-- ----------------------------
-- Primary Key structure for table sys_job_log
-- ----------------------------
ALTER TABLE "ccsp_common"."sys_job_log" ADD CONSTRAINT "sys_job_log_pkey" PRIMARY KEY ("job_log_id");

-- ----------------------------
-- Primary Key structure for table sys_menu
-- ----------------------------
ALTER TABLE "ccsp_common"."sys_menu" ADD CONSTRAINT "sys_menu_pkey" PRIMARY KEY ("menu_id");

-- ----------------------------
-- Primary Key structure for table sys_role_menu
-- ----------------------------
ALTER TABLE "ccsp_common"."sys_role_menu" ADD CONSTRAINT "sys_role_menu_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_task
-- ----------------------------
ALTER TABLE "ccsp_common"."sys_task" ADD CONSTRAINT "sys_task_pkey" PRIMARY KEY ("task_id");

-- ----------------------------
-- Primary Key structure for table sys_task_log
-- ----------------------------
ALTER TABLE "ccsp_common"."sys_task_log" ADD CONSTRAINT "sys_task_log_pkey" PRIMARY KEY ("task_log_id");

-- ----------------------------
-- Primary Key structure for table user_auth_code
-- ----------------------------
ALTER TABLE "ccsp_common"."user_auth_code" ADD CONSTRAINT "user_auth_code_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table user_cert
-- ----------------------------
ALTER TABLE "ccsp_common"."user_cert" ADD CONSTRAINT "user_cert_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table user_info
-- ----------------------------
ALTER TABLE "ccsp_common"."user_info" ADD CONSTRAINT "user_info_pkey" PRIMARY KEY ("user_id");

-- ----------------------------
-- Primary Key structure for table user_register
-- ----------------------------
ALTER TABLE "ccsp_common"."user_register" ADD CONSTRAINT "user_register_pkey" PRIMARY KEY ("user_register_id");

-- ----------------------------
-- Primary Key structure for table user_role
-- ----------------------------
ALTER TABLE "ccsp_common"."user_role" ADD CONSTRAINT "user_role_pkey" PRIMARY KEY ("role_id");

-- ----------------------------
-- Primary Key structure for table user_root_ca
-- ----------------------------
ALTER TABLE "ccsp_common"."user_root_ca" ADD CONSTRAINT "user_root_ca_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table user_security_extend
-- ----------------------------
ALTER TABLE "ccsp_common"."user_security_extend" ADD CONSTRAINT "user_security_extend_pkey" PRIMARY KEY ("id");
