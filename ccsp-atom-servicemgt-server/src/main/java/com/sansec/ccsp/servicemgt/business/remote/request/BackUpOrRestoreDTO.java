package com.sansec.ccsp.servicemgt.business.remote.request;

import lombok.Data;

/**
 * @类描述:数据库参数
 * @author: x<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/7/5 14:05
 */
@Data
public class BackUpOrRestoreDTO {
    /**
     * 备份还原类型 1备份 2还原
     */
    private Integer type;
    /**
     * 数据库类型
     */
    private String dbType;
    /**
     * 数据库实例库
     */
    private String dbCase;
    /**
     * 数据库模式 mysql传1
     */
    private String dbSchema;
    /**
     * 数据库IP
     */
    private String dbIp;
    /**
     * 数据库端口
     */
    private Integer dbPort;
    /**
     * 数据库用户名
     */
    private String dbUser;
    /**
     * 数据库密码
     */
    private String dbAuthCode;
    /**
     * 执行脚本路径
     */
    private String runScript;
    /**
     * 备份文件名称
     */
    private String fileName;
    /**
     * 备份文件路径 不包含文件名
     */
    private String filePath;
    private Long regionId;
    private String remoteIp;
    private Integer remotePort;

}
