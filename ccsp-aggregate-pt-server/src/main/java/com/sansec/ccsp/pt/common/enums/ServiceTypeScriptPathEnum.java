package com.sansec.ccsp.pt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;

/**
 * <AUTHOR>
 * @Description: 服务类型脚本路径枚举
 * @Date: 2022/2/6 10:43
 */
@Getter
@AllArgsConstructor
public enum ServiceTypeScriptPathEnum {
    PKI(1L, "pki"),
    SVS(2L, "svs"),
    DIGIST(3L, "digest"),
    KMS(4L, "kms"),
    TSA(5L, "tsa"),
    SMS(6L, "sms"),
    OTP(7L, "secauth"),
    SECDB(8L, "secdbhsm"),
    SECSTORAGE(9L, "storage"),
    TSC(10L, "electronicSeal"),
    VPN(11L, "vpn"),
    CA(12L, "ca"),
    ;
    /**
     * id
     */
    private final Long id;

    /**
     * code
     */
    private final String scriptPath;


    public static ServiceTypeScriptPathEnum byId(Long id) {
        return Arrays.stream(values()).filter(item -> item.getId().equals(id)).findAny().orElse(null);
    }

}
