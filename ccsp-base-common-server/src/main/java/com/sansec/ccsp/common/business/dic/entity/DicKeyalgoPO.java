package com.sansec.ccsp.common.business.dic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @Description: 密钥算法字典表;
 * <AUTHOR> http://www.chiner.pro
 * @Date: 2023-2-18
 */
@TableName("DIC_KEYALGO")
@Data
public class DicKeyalgoPO extends BasePO{
    /**
     * 
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 名称
     */
    private String dName;
    /**
     * 1 对称 0 非对称
     */
    private Integer dValue;
    /**
     * 长度id 多个，分割
     */
    private String dLengthId;
    /**
     * 生成方式 0 分量 1 随机数
     */
    private String dGentypeId;
    /**
     * 密钥用途 多个,分割
     */
    private String dKeyuseId;
    /**
     * KMS字典值
     */
    private Integer kmsValue;
    /**
     * 是否默认 1默认 0不默认
     */
    private Integer defaultFlag;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}