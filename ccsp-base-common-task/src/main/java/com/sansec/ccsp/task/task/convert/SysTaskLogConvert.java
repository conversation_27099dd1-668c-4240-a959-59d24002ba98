package com.sansec.ccsp.task.task.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.task.task.entity.SysTaskLogPO;
import com.sansec.ccsp.task.task.request.SysTaskLogDTO;
import com.sansec.ccsp.task.task.response.SysTaskLogVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR> xiaojiawei
 * @Description: 异步任务执行日志表;(SYS_TASK_LOG)实体类转换接口
 * @Date: 2023-3-10
 */
@Mapper(componentModel = "spring")
public interface SysTaskLogConvert {
    /**
     * dtoToPo
     *
     * @param sysTaskLogDTO
     * @return
     */
    @Mappings({})
    SysTaskLogPO dtoToPo(SysTaskLogDTO sysTaskLogDTO);

    /**
     * poToDto
     *
     * @param sysTaskLogPO
     * @return
     */
    SysTaskLogDTO poToDto(SysTaskLogPO sysTaskLogPO);

    /**
     * poToDto-list
     *
     * @param list
     * @return
     */
    List<SysTaskLogDTO> poToDto(List<SysTaskLogPO> list);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<SysTaskLogVO> pagePOToSecPageVOPage(IPage<SysTaskLogPO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<SysTaskLogVO> convert(List<SysTaskLogPO> list);

    @Mappings({})
    SysTaskLogVO convertVo(SysTaskLogPO request);
}