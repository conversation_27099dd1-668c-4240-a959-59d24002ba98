package com.sansec.ccsp.pt.business.app.tenant.request;

import lombok.Data;

 /**
 * @Description: 应用和租户关联表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>aw<PERSON>
 * @Date: 2023-2-18
 */
@Data
public class AppToTenantDTO{
    /**
     * ID
     */
    private Long id;
    /**
     * 应用ID
     */
    private Long appId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
}