package com.sansec.ccsp.pt.business.product.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.pt.business.product.entiry.TenantProductRecordPO;
import com.sansec.ccsp.pt.business.product.reponse.TenantProductRecordVO;
import com.sansec.ccsp.pt.business.product.request.TenantProductRecordAddDTO;
import com.sansec.ccsp.pt.business.product.request.TenantProductRecordDTO;
import com.sansec.ccsp.pt.business.product.request.TenantProductRecordEditDTO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @description : 租户开通密码产品记录表;(TENANT_PRODUCT_RECORD)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @date : 2024-4-25
 */
@Mapper(componentModel = "spring")
public interface TenantProductRecordConvert{
    /**
     * dtoToPo
     * @param tenantProductRecordDTO
     * @return
     */
    @Mappings({})
    TenantProductRecordPO dtoToPo(TenantProductRecordAddDTO tenantProductRecordAddDTO);
    @Mappings({})
    TenantProductRecordPO dtoToPo(TenantProductRecordEditDTO tenantProductRecordEditDTODTO);
    
    /**
     * poToDto
     * @param tenantProductRecordPO
     * @return
     */
    TenantProductRecordDTO poToDto(TenantProductRecordPO tenantProductRecordPO);
    
    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<TenantProductRecordDTO> poToDto(List<TenantProductRecordPO> list);
     
    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<TenantProductRecordVO> pagePOToSecPageVOPage(IPage<TenantProductRecordPO> iPage);
    
    @InheritConfiguration(name = "convertVo")
    List<TenantProductRecordVO> convert(List<TenantProductRecordPO> list);
    
    @Mappings({})
    TenantProductRecordVO convertVo(TenantProductRecordPO request);
}