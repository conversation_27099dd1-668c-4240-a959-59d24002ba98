package com.sansec.ccsp.pt.business.statistic.swm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdsDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoPageDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.DeviceInfoListDTO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoVO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceStatusVO;
import com.sansec.ccsp.pt.business.common.config.service.CommonConfigService;
import com.sansec.ccsp.pt.business.device.device.service.DeviceInfoService;
import com.sansec.ccsp.pt.business.device.deviceInfo.service.VsmDeviceInfoService;
import com.sansec.ccsp.pt.business.license.service.LicenseUseTimeService;
import com.sansec.ccsp.pt.business.product.service.ProductExpireService;
import com.sansec.ccsp.pt.business.region.service.RegionService;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.service.ServiceInfoService;
import com.sansec.ccsp.pt.business.servicemgt.servicetype.service.ServiceTypeService;
import com.sansec.ccsp.pt.business.statistic.alarm.constant.AlarmConstant;
import com.sansec.ccsp.pt.business.statistic.swm.entity.AlarmItem;
import com.sansec.ccsp.pt.business.statistic.swm.response.SwmIndexVo;
import com.sansec.ccsp.pt.business.statistic.swm.service.SwmIndexService;
import com.sansec.ccsp.pt.common.constant.CommonConstant;
import com.sansec.ccsp.servicemgt.serviceinfo.api.ServiceInfoServiceApi;
import com.sansec.ccsp.servicemgt.serviceinfo.request.FindServiceRunStatusParamDTO;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceStatusVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SwmIndexServiceImpl implements SwmIndexService {

    @Resource
    private ServiceInfoService serviceInfoService;
    @Resource
    private VsmDeviceInfoService vsmDeviceInfoService;
    @Resource
    private LicenseUseTimeService licenseUseTimeService;
    @Resource
    private CommonConfigService commonConfigService;
    @Resource
    private DeviceInfoService deviceInfoService;

    /**
     * 告警指标查询
     */
    @Override
    public SecRestResponse<List<SwmIndexVo<AlarmItem>>> searchAlarmItem(String metricsCode) {


        // 指标查询结果
        List<SwmIndexVo<AlarmItem>> metricsList = new ArrayList<>();
        // 服务状态
        if (StringUtils.equals(AlarmConstant.SERVICE_STATUS, metricsCode)) {
            // 获取所有服务id
            List<Long> serviceIdList = serviceInfoService.getServiceIdList();
            if (CollectionUtils.isNotEmpty(serviceIdList)) {
                // 查询所有服务状态
                FindServiceRunStatusParamDTO findServiceRunStatusParamDTO = new FindServiceRunStatusParamDTO();
                findServiceRunStatusParamDTO.setServiceIdList(serviceIdList);
                SecRestResponse<Map<Long, ServiceStatusVO>> response = serviceInfoService.findServiceRunAndOperStatus(findServiceRunStatusParamDTO);
                Map<Long, ServiceStatusVO> serviceRunStatusMap = response.getResult();
                if (!response.isSuccess() || CollectionUtils.isEmpty(serviceRunStatusMap)) {
                    return ResultUtil.ok(metricsList);
                }
                // 填充指标集结构
                serviceRunStatusMap.forEach((serviceId, serviceStatusVO) -> {
                    SwmIndexVo<AlarmItem> swmIndexVo = new SwmIndexVo<>();
                    swmIndexVo.setCode(metricsCode);
                    swmIndexVo.setAttribute(new AlarmItem(serviceId, getServiceStatus(serviceStatusVO)));
                    metricsList.add(swmIndexVo);
                });
            }
        } // 设备状态
        else if (StringUtils.equals(AlarmConstant.DEVICE_STATUS, metricsCode)) {
            // 查询所有设备id
            List<DeviceInfoVO> deviceInfoList = vsmDeviceInfoService.findDeviceUnUsed(new DeviceInfoPageDTO());
            List<Long> deviceIdList = deviceInfoList.stream().map(DeviceInfoVO::getDeviceId).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(deviceIdList)) {
                // 查询所有设备状态
                List<DeviceStatusVO> vsmStatusVOList = vsmDeviceInfoService.status(new DeviceIdsDTO(deviceIdList));
                // 填充指标集结构
                for (DeviceStatusVO deviceStatusVO : vsmStatusVOList) {
                    SwmIndexVo<AlarmItem> swmIndexVo = new SwmIndexVo<>();
                    swmIndexVo.setCode(metricsCode);
                    swmIndexVo.setAttribute(new AlarmItem(deviceStatusVO.getDeviceId(), deviceStatusVO.getStatus()));
                    metricsList.add(swmIndexVo);
                }
            }
        } // 许可有效期
        else if (StringUtils.equals(AlarmConstant.PERMISSION, metricsCode)) {
            // 下级平台
            // 查询所有服务许可有效期
            Map<Long, Long> serverIdTimeMap = licenseUseTimeService.getServerIdTimeList();
            for (Long serverId : serverIdTimeMap.keySet()) {
                serverIdTimeMap.put(serverId, convertMillisToDay(serverIdTimeMap.get(serverId)));
            }
            // 填充指标集结构
            if (CollectionUtils.isNotEmpty(serverIdTimeMap)) {
                buildMetricsData(metricsCode, serverIdTimeMap, metricsList);
            }
            // 平台许可有效期
            String endTime = commonConfigService.getConfigValueByConfigCode(CommonConstant.PLATFORM_END_TIME);
            if (!StringUtils.equals("-1", endTime) && !StringUtils.equals("0", endTime)) {
                LocalDateTime endDateTime = LocalDate.parse(endTime, DateTimeFormatter.ISO_DATE).atStartOfDay();
                Long ptEndMillis = LocalDateTime.now().until(endDateTime, ChronoUnit.DAYS);
                // 填充指标集结构
                SwmIndexVo<AlarmItem> swmIndexVo = new SwmIndexVo<>();
                swmIndexVo.setCode(metricsCode);
                swmIndexVo.setAttribute(new AlarmItem(1, ptEndMillis));
                metricsList.add(swmIndexVo);
            }
        }

        return ResultUtil.ok(metricsList);
    }

    private static void buildMetricsData(String metricsCode, Map<Long, Long> statisticMonitorIdDiskMap, List<SwmIndexVo<AlarmItem>> metricsList) {
        //todo 增加非空判断
        statisticMonitorIdDiskMap.forEach((id, value) -> {
            SwmIndexVo<AlarmItem> swmIndexVo = new SwmIndexVo<>();
            swmIndexVo.setCode(metricsCode);
            swmIndexVo.setAttribute(new AlarmItem(id, value));
            metricsList.add(swmIndexVo);
        });
    }

    /**
     * 判断服务状态
     *
     * @param serviceStatusVO
     * @return
     */
    private Integer getServiceStatus(ServiceStatusVO serviceStatusVO) {
        // 未运行
        Integer serviceStatus = 0;
        if (serviceStatusVO.getOperStatus() != null && serviceStatusVO.getOperStatus() == 1) {
            if (serviceStatusVO.getRunStatus() != null && serviceStatusVO.getRunStatus() == 1) {
                // 运行中
                serviceStatus = 1;
            } else if (serviceStatusVO.getRunStatus() != null && serviceStatusVO.getRunStatus() == 2) {
                // 异常
                serviceStatus = 2;
            }
        }
        return serviceStatus;
    }


    private Long convertMillisToDay(Long millis) {
        return TimeUnit.MILLISECONDS.toDays(millis);
    }
}
