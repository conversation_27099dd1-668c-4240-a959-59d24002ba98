package com.sansec.ccsp.pt.business.sim.request;

import com.sansec.ccsp.pt.business.sim.entity.BaseSim;
import lombok.Data;

/**
 * @ClassName: SignAuth
 * @Description:
 * @Author: xupingyong
 * @Date: 2022/10/19 16:06
 */
@Data
public class SimSignAuth extends BaseSim {

    /**
     * 校验SIM盾密码
     * 0：校验
     * 1：不校验
     * 默认为0
     */
    private String operType;

    /**
     * 签名类型:
     * 00：无模板签名数据为明文的签名操作
     * 01：无模板签名数据为密文的不带头签名操作
     * 02：模板签名数据为明文的签名操作
     * 03：模板签名数据为密文的不带头签名操作
     * 备注：
     * 1.signType与dataToBeDisplay有关，代表dataToBeDisplay的数据形式。
     * 2.使用模板，encodetype字段必须使用中文模式。
     * 3.密文模式下需解析证书获取公钥后对dataToBeDisplay加密
     */
    private String signType;

    /**
     * 3.0、4.0支持
     * 待签数据的哈希值，使用与hashType类型相对应算法加密，作为预留字段：待签数据先进行hash (sha1后是20字节，sha256后是32字节，sm3后是32字节)，对原文取值时中文用utf-16BE编码，英文用ascii编码，再进行BASE64加密处理 ，最后作为dataToBeSignedR的值。当dataToBeDisplay为空时，该字段必填。
     * 如果同时传提示语和哈希值，卡应用会默认对哈希值进行签名
     */
    private String dataToBeSignedR;

    /**
     * 签名算法类型，和申请证书时的证书密钥对算法对应
     * 01：rsa1024
     * 02：rsa2048
     * 03：SM2
     * 默认值为01
     */
    private String signAlgType;

    /**
     * Hash值算法类型，和证书密钥对算法对应
     * 01：SHA1--RSA1024
     * 02：SHA256--RSA2048
     * 03：SM3--SM2
     * 默认值01 ，若是要对hash值签名则必填
     */
    private String hashType;

    /**
     * 签名请求时间(yyyyMMddHHmmss格式)
     */
    private String signReqDate;

    /**
     * 渠道标识，用于区分签名认证请求是由用户的手机银行还是网银发起
     * 0：未知渠道
     * 1：网银
     * 2：手机银行
     */
    private String channelType;
}
