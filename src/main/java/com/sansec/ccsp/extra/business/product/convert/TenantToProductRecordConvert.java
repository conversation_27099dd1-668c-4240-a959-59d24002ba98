package com.sansec.ccsp.extra.business.product.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.extra.business.product.entity.TenantToProductRecordPO;
import com.sansec.ccsp.extra.business.product.request.TenantToProductRecordDTO;
import com.sansec.ccsp.extra.business.product.response.TenantToProductRecordVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

 /**
 * @description : 租户的密码产品修改记录;(TENANT_TO_PRODUCT_RECORD)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @date : 2023-11-21
 */
@Mapper(componentModel = "spring")
public interface TenantToProductRecordConvert{
    /**
     * dtoToPo
     * @param tenantToProductRecordDTO
     * @return
     */
    @Mappings({})
    TenantToProductRecordPO dtoToPo(TenantToProductRecordDTO tenantToProductRecordDTO);
    
    /**
     * poToDto
     * @param tenantToProductRecordPO
     * @return
     */
    TenantToProductRecordDTO poToDto(TenantToProductRecordPO tenantToProductRecordPO);
    
    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<TenantToProductRecordDTO> poToDto(List<TenantToProductRecordPO> list);

     @Mapping(source = "size", target = "pageSize")
     @Mapping(source = "current", target = "pageNum")
     @Mapping(source = "records", target = "list")
    SecPageVO<TenantToProductRecordVO> pagePOToSecPageVOPage(IPage<TenantToProductRecordPO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<TenantToProductRecordVO> convert(List<TenantToProductRecordPO> list);

    @Mappings({})
    TenantToProductRecordVO convertVo(TenantToProductRecordPO request);
}