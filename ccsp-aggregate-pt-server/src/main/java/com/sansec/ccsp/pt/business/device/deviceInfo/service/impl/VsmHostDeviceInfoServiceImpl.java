package com.sansec.ccsp.pt.business.device.deviceInfo.service.impl;

import com.sansec.ccsp.device.deviceinfo.api.DeviceInfoServiceApi;
import com.sansec.ccsp.device.deviceinfo.api.VsmHostDeviceInfoServiceApi;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdPageDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdsDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoPageDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.VsmHostDeviceInfoAddDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.VsmHostDeviceInfoEditDTO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoVO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceStatusVO;
import com.sansec.ccsp.device.deviceinfo.response.vsmhost.VsmHostDeviceInfoVO;
import com.sansec.ccsp.pt.business.common.config.service.CommonConfigService;
import com.sansec.ccsp.pt.business.device.device.service.DeviceInfoService;
import com.sansec.ccsp.pt.business.device.deviceInfo.service.VsmHostDeviceInfoService;
import com.sansec.ccsp.pt.business.region.service.RegionService;
import com.sansec.ccsp.pt.business.servicemgt.gateway.response.ServiceGatewayVO;
import com.sansec.ccsp.pt.business.tenant.request.TenantCodeDTO;
import com.sansec.ccsp.pt.business.tenant.request.TenantIdDTO;
import com.sansec.ccsp.pt.business.tenant.service.TenantService;
import com.sansec.ccsp.pt.common.error.SecErrorCodeConstant;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class VsmHostDeviceInfoServiceImpl implements VsmHostDeviceInfoService {

    @Resource
    VsmHostDeviceInfoServiceApi serviceApi;

    @Resource
    TenantService tenantService;

    @Resource
    RegionService regionService;
    @Resource
    private CommonConfigService commonConfigService;

    @Resource
    private DeviceInfoService deviceInfoService;

    @Resource
    private DeviceInfoServiceApi deviceInfoServiceApi;

    @Override
    public SecRestResponse<SecPageVO<DeviceInfoVO>> find(DeviceInfoPageDTO deviceInfoPageDTO) {
        // 将前端租户下拉列表传的code转为id
        String tenantCode = deviceInfoPageDTO.getTenantCode();
        if (StringUtils.isNotEmpty(tenantCode)) {
            TenantIdDTO tenantIdDTO = tenantService.tenantCodeToId(new TenantCodeDTO(tenantCode));
            deviceInfoPageDTO.setTenantId(tenantIdDTO.getTenantId());
        }
        SecRestResponse<SecPageVO<DeviceInfoVO>> response = serviceApi.find(deviceInfoPageDTO);
        List<DeviceInfoVO> deviceInfoVOS = response.getResult().getList();
        if(deviceInfoVOS.size() > 0){
            Map<Long, String> regionMap = regionService.getRegionIdNameMap();
            for (DeviceInfoVO deviceInfoVO : deviceInfoVOS) {
                if(deviceInfoVO.getRegionId() != null){
                    deviceInfoVO.setRegionName(regionMap.get(deviceInfoVO.getRegionId()));
                }
            }
        }
        response.getResult().setList(deviceInfoVOS);
        return response;
    }

    @Override
    public SecRestResponse<SecPageVO<DeviceInfoVO>> findVsmByHostId(DeviceIdPageDTO deviceIdDTO) {
        SecRestResponse<SecPageVO<DeviceInfoVO>> result=serviceApi.findVsmByHostId(deviceIdDTO);

        //租户信息
        Map<Long, String> tenantMap = tenantService.getTenantIdNameMap();
        Map<Long, String> regionMap = regionService.getRegionIdNameMap();

        List<DeviceInfoVO> list = result.getResult().getList();
        for(int i=0;i<list.size();i++){
            DeviceInfoVO deviceInfoVO = list.get(i);

            if(deviceInfoVO.getDeviceGroupType() != null && deviceInfoVO.getDeviceGroupType()==1){
                list.get(i).setDeviceGroupName(null);
            }

            Long tenantId = deviceInfoVO.getTenantId();
            if(tenantId != null){
                String tenantName= tenantMap.get(tenantId);
                if(tenantName != null){
                    list.get(i).setTenantName(tenantName);
                }
            }

            if(deviceInfoVO.getRegionId() != null){
                list.get(i).setRegionName(regionMap.get(deviceInfoVO.getRegionId()));
            }
        }

        result.getResult().setList(list);

        return result;
    }
    @Override
    public SecRestResponse<Long> add(VsmHostDeviceInfoAddDTO vsmHostDeviceInfoAddDTO) {
        //获取区域网关
        ServiceGatewayVO serviceGatewayVO=deviceInfoService.getGateWay(vsmHostDeviceInfoAddDTO.getRegionId());
        if(serviceGatewayVO!=null){
            vsmHostDeviceInfoAddDTO.setProxyRouteIs(true);
            vsmHostDeviceInfoAddDTO.setGatewayIp(serviceGatewayVO.getIp());
            vsmHostDeviceInfoAddDTO.setGatewayPort(serviceGatewayVO.getPort());
        }else{
            vsmHostDeviceInfoAddDTO.setProxyRouteIs(false);
        }
        //添加业务路由
        if(vsmHostDeviceInfoAddDTO.isProxyRouteIs()){
            deviceInfoService.editDeviceProxyRoute(vsmHostDeviceInfoAddDTO.getMgtIp(),vsmHostDeviceInfoAddDTO.getMgtPort(),
                    vsmHostDeviceInfoAddDTO.getRegionId(),"add");
        }
        try{
            SecRestResponse<Long> addDeviceResponse= serviceApi.add(vsmHostDeviceInfoAddDTO);
            //添加失败删除路由
            if(!addDeviceResponse.isSuccess()){
                deviceInfoService.editDeviceProxyRoute(vsmHostDeviceInfoAddDTO.getMgtIp(),vsmHostDeviceInfoAddDTO.getMgtPort(),
                        vsmHostDeviceInfoAddDTO.getRegionId(),"delete");
            }
            return addDeviceResponse;
        }catch (Exception e){
            log.error("HostDeviceInfoServiceImpl add error",e);
            deviceInfoService.editDeviceProxyRoute(vsmHostDeviceInfoAddDTO.getMgtIp(),vsmHostDeviceInfoAddDTO.getMgtPort(),
                    vsmHostDeviceInfoAddDTO.getRegionId(),"delete");
            throw  e;
        }
    }

    @Override
    public SecRestResponse<Long> delete(DeviceIdDTO deviceIdDTO) {
        SecRestResponse<DeviceInfoVO> deviceInfoVOSecRestResponse=deviceInfoServiceApi.info(deviceIdDTO);
        if(!deviceInfoVOSecRestResponse.isSuccess()){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NOT_EXIST);
        }
        DeviceInfoVO deviceInfoVO=deviceInfoVOSecRestResponse.getResult();
        ServiceGatewayVO serviceGatewayVO=deviceInfoService.getGateWay(deviceInfoVO.getRegionId());
        if(serviceGatewayVO!=null){
            deviceIdDTO.setProxyRouteIs(true);
            deviceIdDTO.setGatewayIp(serviceGatewayVO.getIp());
            deviceIdDTO.setGatewayPort(serviceGatewayVO.getPort());
        }else{
            deviceIdDTO.setProxyRouteIs(false);
        }
        SecRestResponse<Long> deleteResponse= serviceApi.delete(deviceIdDTO);
        //删除代理路由
        if(deviceIdDTO.isProxyRouteIs()&&deleteResponse.isSuccess()){
            deviceInfoService.editDeviceProxyRoute(deviceInfoVO.getMgtIp(), deviceInfoVO.getMgtPort(),
                    deviceInfoVO.getRegionId(),"delete");
        }
        return deleteResponse;
    }

    @Override
    public SecRestResponse<List<DeviceStatusVO>> status(DeviceIdsDTO deviceIdsDTO) {
        return serviceApi.status(deviceIdsDTO);
    }

    @Override
    public SecRestResponse<VsmHostDeviceInfoVO> hccsinfo(DeviceIdDTO deviceIdDTO) {
        ServiceGatewayVO serviceGatewayVO=deviceInfoService.getGateWayByDeviceId(deviceIdDTO.getDeviceId());
        if(serviceGatewayVO!=null){
            deviceIdDTO.setProxyRouteIs(true);
            deviceIdDTO.setGatewayIp(serviceGatewayVO.getIp());
            deviceIdDTO.setGatewayPort(serviceGatewayVO.getPort());
        }else{
            deviceIdDTO.setProxyRouteIs(false);
        }
        return serviceApi.hccsinfo(deviceIdDTO);
    }

    @Override
    public SecRestResponse<DeviceInfoVO> info(DeviceIdDTO deviceIdDTO) {
        SecRestResponse<DeviceInfoVO> response = serviceApi.info(deviceIdDTO);
        DeviceInfoVO deviceInfoVO = response.getResult();
        if(deviceInfoVO != null && deviceInfoVO.getRegionId() != null){
            Map<Long, String> regionMap = regionService.getRegionIdNameMap();
            deviceInfoVO.setRegionName(regionMap.get(deviceInfoVO.getRegionId()));
        }
        response.setResult(deviceInfoVO);
        return response;
    }

    @Override
    public SecRestResponse<Long> edit(VsmHostDeviceInfoEditDTO vsmHostDeviceInfoEditDTO) {
        return serviceApi.edit(vsmHostDeviceInfoEditDTO);
    }
}
