package com.sansec.ccsp.device.business.deviceinfo.handler.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sansec.ccsp.device.business.deviceapi.entity.DeviceApiPathPo;
import com.sansec.ccsp.device.business.devicedic.entity.DicDeviceImageTypePO;
import com.sansec.ccsp.device.business.deviceinfo.entity.DeviceInfoPO;
import com.sansec.ccsp.device.business.deviceinfo.handler.dic.DeviceInfoDic;
import com.sansec.ccsp.device.business.deviceinfo.handler.request.common.MgtRequest;
import com.sansec.ccsp.device.business.deviceinfo.handler.request.vsm.VsmCreateRequest;
import com.sansec.ccsp.device.business.deviceinfo.handler.request.vsm.VsmNetworkRequest;
import com.sansec.ccsp.device.business.devicenet.entity.DeviceNetDetailPO;
import com.sansec.ccsp.device.business.devicenet.entity.DeviceVsmNetConfigPO;
import com.sansec.ccsp.device.business.devicetype.entity.DeviceTypePO;
import com.sansec.common.id.IdGenerator;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

import static com.sansec.ccsp.device.business.deviceinfo.handler.dic.DeviceInfoDic.*;

/**
 * @author: zhandaojian
 * @Date: 2023/2/27 10:23
 * @Description: 批量创建虚机任务
 * @deprecated
 */
@Deprecated
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class CreateVsmTask extends AbstractVsmTask {

    private List<DeviceInfoPO> vsmList;

    @Override
    public void run() {
        for (DeviceInfoPO vsm : vsmList) {
            DeviceTypePO deviceType = deviceTypeMapper.selectById(vsm.getDeviceTypeId());
            // 获取镜像类型
            DicDeviceImageTypePO dicDeviceImageType = dicDeviceImageTypeMapper.selectById(deviceType.getHccsImageId());
            String imageValue = dicDeviceImageType.getImageValue();
            // 构建宿主机管理请求
            Long vsmHostId = vsm.getHccsDeviceId();
            DeviceInfoPO vsmHost = deviceInfoMapper.selectById(vsmHostId);
            MgtRequest mgtRequest = deviceInfoConvert.poToMgtRequest(vsmHost);

            //查找device_api表获取apiPath
            String createVsmApiPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_CREATECHSMVSM, vsmHost.getDeviceTypeId());
            String getVsmStatusApiPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_GETVSMSTATUS, vsmHost.getDeviceTypeId());
            String configVsmNetworkApiPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_CONFIGVSMNETWORK , vsmHost.getDeviceTypeId());

            // 虚拟机创建请求
            VsmCreateRequest vsmCreateRequest = VsmCreateRequest.builder().requestId(String.valueOf(vsm.getDeviceId()))
                    .oprType(DeviceInfoDic.CREATE).vsmType(imageValue).flavor(1).vsmFunction("hostservice")
                    .callbackUrl(getCallBackUrl(DeviceInfoDic.CREATE)).build();
            try {
                String vsmId = vsmHandler.create(mgtRequest, vsmCreateRequest,createVsmApiPath);
                vsm.setDeviceSerialnum(vsmId);
                if (loopGetVsmStatus(mgtRequest, vsm, DeviceInfoDic.CREATE,getVsmStatusApiPath)) {
                    createSuccessDeal(vsm, mgtRequest,configVsmNetworkApiPath);
                } else {
                    createFailDeal(vsm);
                }
            } catch (Exception e) {
                // 处理异常失败
                createFailDeal(vsm);
                throw e;
            }
        }
    }

    private void createSuccessDeal(DeviceInfoPO vsm, MgtRequest mgtRequest,String apiPath) {
        DeviceNetDetailPO deviceNetDetail = deviceNetDetailMapper.selectOne(new LambdaQueryWrapper<DeviceNetDetailPO>()
                .eq(DeviceNetDetailPO::getMgtIp, vsm.getMgtIp()));
        DeviceVsmNetConfigPO vsmNetConfig = deviceVsmNetConfigMapper.selectOne(new LambdaQueryWrapper<DeviceVsmNetConfigPO>()
                .eq(DeviceVsmNetConfigPO::getGateway, deviceNetDetail.getGateway()));

        // 配置虚拟机网络
        VsmNetworkRequest vsmNetworkRequest = VsmNetworkRequest.builder().requestId(IdGenerator.ins().generatorString())
                .vsmId(vsm.getDeviceSerialnum()).ip(vsm.getMgtIp()).gateway(vsmNetConfig.getGateway())
                .mask(vsmNetConfig.getMask()).build();
        vsmHandler.network(mgtRequest, vsmNetworkRequest,apiPath);

        // 将IP标记为使用状态
        deviceNetDetailMapper.update(null, new LambdaUpdateWrapper<DeviceNetDetailPO>()
                .eq(DeviceNetDetailPO::getIpId, deviceNetDetail.getIpId())
                .set(DeviceNetDetailPO::getStatus, DeviceInfoDic.NET_USED));

        // 更新虚机操作状态为运行中、填入虚机序列号
        deviceInfoMapper.update(null, new LambdaUpdateWrapper<DeviceInfoPO>()
                .eq(DeviceInfoPO::getDeviceId, vsm.getDeviceId())
                .set(DeviceInfoPO::getOperStatus, DeviceInfoDic.RUNNING_STATE)
                .set(DeviceInfoPO::getDeviceSerialnum, vsm.getDeviceSerialnum()));
    }

    private void createFailDeal(DeviceInfoPO vsm) {
        log.error("createFailDeal虚拟机创建异常,vsmId = {}", vsm.getDeviceId());
        // 更新操作状态为创建失败、设备组为空、IP为空
        deviceInfoMapper.update(null, new LambdaUpdateWrapper<DeviceInfoPO>()
                .eq(DeviceInfoPO::getDeviceId, vsm.getDeviceId())
                .set(DeviceInfoPO::getOperStatus, DeviceInfoDic.CREATE_FAIL_STATE)
                .set(DeviceInfoPO::getDeviceGroupId, null)
                .set(DeviceInfoPO::getMgtIp, "none").set(DeviceInfoPO::getBusiIp, "none"));

        // 更新IP未使用状态
        deviceNetDetailMapper.update(null, new LambdaUpdateWrapper<DeviceNetDetailPO>()
                .eq(DeviceNetDetailPO::getMgtIp, vsm.getMgtIp())
                .set(DeviceNetDetailPO::getStatus, DeviceInfoDic.NET_NOT_USE));
    }
}
