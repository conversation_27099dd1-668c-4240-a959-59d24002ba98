package com.sansec.ai.extra.dify.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 模板工具类
 *
 * <AUTHOR>
 * @since 2025/5/20 11:13
 */
@Slf4j
public class TemplateUtils {

	private static final Pattern PATTERN = Pattern.compile("\\$\\{([^}]+)}");


	/**
	 * 替换文件中的占位符<br>占位符匹配格式：${XXX}
	 *
	 * @param absoluteFilePath 文件绝对路径
	 * @param params           替换的键值对
	 * @throws IOException
	 */
	public static void composeFile(String absoluteFilePath, Map<String, Object> params) throws IOException {
		composeFile(absoluteFilePath, params, PATTERN);
	}

	/**
	 * 替换文件中的占位符，自定义占位符匹配格式
	 *
	 * @param absoluteFilePath 文件绝对路径
	 * @param params           替换的键值对
	 * @param pattern
	 * @throws IOException
	 */
	public static void composeFile(String absoluteFilePath, Map<String, Object> params, Pattern pattern) throws IOException {
		Path path = Paths.get(absoluteFilePath);
		if (!Files.exists(path)) {
			return;
		}
		String content = Files.readString(path);
		content = compose(content, params, pattern);
		Files.writeString(path, content);
	}

	/**
	 * 替换内容中的占位符<br>占位符匹配格式：${XXX} <br>
	 * 如${aa} cc ${bb} 其中 ${aa}, ${bb} 为占位符. 可用相关变量进行替换
	 *
	 * @param content 内容
	 * @param params  替换的变量值
	 * @return
	 */
	public static String compose(String content, Map<String, Object> params) {
		return compose(content, params, PATTERN);
	}

	/**
	 * 替换内容中的占位符，自定义占位符匹配格式
	 *
	 * @param content
	 * @param params
	 * @param pattern
	 * @return
	 */
	public static String compose(String content, Map<String, Object> params, Pattern pattern) {
		if (StringUtils.isBlank(content)) {
			return content;
		}
		if (params == null || params.isEmpty()) {
			return content;
		}
		StringBuilder result = new StringBuilder(content.length());
		Matcher matcher = pattern.matcher(content);
		while (matcher.find()) {
			String key = matcher.group(1);
			//不包含的key，不进行替换
			if (!params.containsKey(key)) {
				continue;
			}
			Object r = params.getOrDefault(key, "");
			matcher.appendReplacement(result, r.toString());
		}
		matcher.appendTail(result);
		return result.toString();
	}

}
