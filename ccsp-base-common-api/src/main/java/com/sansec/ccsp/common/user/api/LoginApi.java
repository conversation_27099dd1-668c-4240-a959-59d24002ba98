package com.sansec.ccsp.common.user.api;

import com.sansec.ccsp.common.config.api.fallback.CommonConfigServiceFallBack;
import com.sansec.ccsp.common.user.request.*;
import com.sansec.ccsp.common.user.response.CaptchaCheckVO;
import com.sansec.ccsp.common.user.response.GetRandomVO;
import com.sansec.ccsp.common.user.response.UserInfoVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = "${spring.protocol.prefix}${spring.application.common.name}", path = "/ccsp/common", fallbackFactory = CommonConfigServiceFallBack.class)
public interface LoginApi {
    /**
     * 口令登录
     *
     * @param authCodeLoginDTO 口令登录信息
     * @return token
     */
    @PostMapping(value = "/api/user/v1/authCode/login")
    SecRestResponse<String> authCodeLogin(@RequestBody AuthCodeLoginDTO authCodeLoginDTO);

    /**
     * 临时口令登录
     *
     * @param authCodeLoginDTO 口令登录信息
     * @return token
     */
    @PostMapping(value = "/api/user/v1/authCode/getLoginToken")
    SecRestResponse<String> getLoginToken(@RequestBody AuthCodeLoginDTO authCodeLoginDTO);

    /**
     * 获取验证码
     *
     * @return
     */
    @PostMapping(value = "/api/user/v1/getCaptcha")
    SecRestResponse<Object> getCaptcha();

    /**
     * 校验码检测
	 * @return 校验结果
	 */
	@PostMapping(value = "/api/user/v1/captcha/check")
	SecRestResponse<CaptchaCheckVO> captchaCheck(@RequestBody CaptchaCheckDTO captchaCheckDTO);

	/**
	 * 获取验证码
	 * @return 校验结果
	 */
	@PostMapping(value = "/api/user/v1/ukey/getRandom")
	SecRestResponse<GetRandomVO> getRandom(@RequestBody GetLoginRandomDTO getLoginRandomDTO);

	/**
	 * Ukey证书验签登录
	 *
	 * @return token
	 */
	@PostMapping(value = "/api/user/v1/ukey/login")
	SecRestResponse<String> uKeyLogin(@RequestBody UKeyCertLoginDTO uKeyCertLoginDTO);

	/**
	 * 根据UKEY查询对应用户信息
	 *
	 * @param uKeyCertLoginDTO
	 * @return
	 */
	@PostMapping(value = "/api/user/v1/ukey/getUkeyTenantId")
	SecRestResponse<UserInfoVO> getUkeyTenantId(@RequestBody UKeyCertLoginDTO uKeyCertLoginDTO);

	/**
	 * 退出登录
	 */
	@PostMapping(value = "/api/user/v1/logout")
	SecRestResponse<Object> logout();

	/**
	 * 获取SIM 登录token，带有登录状态检测
	 * @param userInfoDTO userInfoDTO
	 * @return 实例对象
	 */
	@PostMapping("/api/user/v1/getSimLoginTokenWithCheck")
	SecRestResponse<String> getSimLoginTokenWithCheck(@Validated @RequestBody UserInfoDTO userInfoDTO);



}
