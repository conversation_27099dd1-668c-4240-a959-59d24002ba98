package com.sansec.ccsp.pt.business.statistic.monitor.controller;

import com.sansec.ccsp.pt.business.statistic.monitor.request.*;
import com.sansec.ccsp.security.annotation.HasAnyPermissions;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sansec.ccsp.pt.business.statistic.monitor.response.ServiceMonitorVO;
import com.sansec.ccsp.pt.business.statistic.monitor.service.ServiceMonitorService;
import javax.annotation.Resource;
import java.util.List;

/**
 * @description : 监控服务表;(service_swmonitor)表控制层
 * <AUTHOR> xiaojiawei
 * @date : 2024-3-4
 */
@RestController
@RequestMapping("/serviceSwmonitor")
public class ServiceMonitorController{
    @Resource
    private ServiceMonitorService serviceMonitorService;
    

     /**
      * 不分页查询
      *
      * @param serviceMonitorPageDTO 筛选条件
      * @return 查询结果
      */
     @PostMapping("/find")
     @HasAnyPermissions("region:monitorServer:list")
     public SecRestResponse<List<ServiceMonitorVO>> find(@RequestBody @Validated ServiceMonitorDTO serviceMonitorPageDTO){
         return serviceMonitorService.find(serviceMonitorPageDTO);
     }
    
    /** 
     * 新增数据
     *
     * @param serviceMonitorAddDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    @HasAnyPermissions("region:monitorServer:add")
    public SecRestResponse<Object> add(@RequestBody @Validated ServiceMonitorAddDTO serviceMonitorAddDTO){
        return serviceMonitorService.add(serviceMonitorAddDTO);
    }
    
    /** 
     * 更新数据
     *
     * @param serviceMonitorEditDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/edit")
    @HasAnyPermissions("region:monitorServer:edit")
    public SecRestResponse<Object> edit(@RequestBody @Validated ServiceMonitorEditDTO serviceMonitorEditDTO){
        return serviceMonitorService.edit(serviceMonitorEditDTO);
    }
    
    /** 
     * 通过主键删除数据
     *
     * @param serviceMonitorIdDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/delete")
    @HasAnyPermissions("region:monitorServer:delete")
    public SecRestResponse<Object> deleteById(@RequestBody @Validated ServiceMonitorIdDTO serviceMonitorIdDTO){
        return serviceMonitorService.deleteById(serviceMonitorIdDTO);
    }
}