package com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.sansec.ccsp.common.dic.response.DicStatisticVO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request.*;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request.monitor.GetQuotaInfoDTO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request.monitor.QuotaInfoDTO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request.monitor.SetTenantQuotaDTO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request.token.WebTokenExpireDTO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.response.ServiceMenuVO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.service.AbstractInvoke;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.service.InterInvokeService;
import com.sansec.ccsp.pt.business.tenant.entity.TenantPO;
import com.sansec.ccsp.pt.business.tenant.service.TenantToServiceGroupService;
import com.sansec.ccsp.pt.common.enums.ServiceTypeEnum;
import com.sansec.ccsp.pt.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.pt.common.util.HttpUtils;
import com.sansec.ccsp.servicemgt.serviceinfo.request.ServiceInfoDTO;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceInfoVO;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import com.sansec.redis.utils.RedisUtill;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: SSL加密服务
 * @Date: 2023/2/19 16:05
 * @copyright sansec
 */
@Component
@Slf4j
public class VpnInvokeServiceImpl extends AbstractInvoke implements InterInvokeService {

    @Resource
    private RedisUtill redisUtill;

    @Resource
    TenantToServiceGroupService tenantToServiceGroupService;

    @Override
    public ServiceTypeEnum type() {
        return ServiceTypeEnum.VPN;
    }


    @Override
    public SecRestResponse<Object> initTenantUser(ServiceInfoDTO serviceInfoDTO, TenantInvokeDTO tenantInvokeDTO) {
        log.debug("{}InvokeServiceImpl-initTenantUser", type().getPathWeb());
        String serUrl = String.format("/%s/v1/initTenantUser", type().getPathWeb());
        String realIpPort = String.format("%s:%s", serviceInfoDTO.getMgtIp(), serviceInfoDTO.getMgtPort());
        String url = getServiceInvokeUrl(serviceInfoDTO , serUrl) ;
        try {
            String json = JSON.toJSONString(tenantInvokeDTO);
            initTenantUser(url, json, realIpPort);
        } catch (Exception e) {
            log.error("{}初始化租户返回结果异常,url={},message={}", type().getName(), url, e.getMessage());
            return ResultUtil.error(SecErrorCodeConstant.INIT_TENANT_ERROR);
        }
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Object> deleteTenant(ServiceInfoDTO serviceInfoDTO, TenantInvokeDTO tenantInvokeDTO) {
        log.debug("{}InvokeServiceImpl-deleteTenant", type().getPathWeb());
        String serUrl = String.format("/%s/v1/deleteTenant", type().getPathWeb());
        String realIpPort = String.format("%s:%s", serviceInfoDTO.getMgtIp(), serviceInfoDTO.getMgtPort());
        String url = getServiceInvokeUrl(serviceInfoDTO , serUrl) ;
        try {
            String json = JSON.toJSONString(tenantInvokeDTO);
            deleteTenant(url, json, realIpPort);
        } catch (Exception e) {
            log.error("{}删除租户返回结果异常,url={},message={}", type().getName(), url, e.getMessage());
            return ResultUtil.error(SecErrorCodeConstant.DELETE_TENANT_ERROR);
        }
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Object> receiveDeviceInfo(List<ServiceInfoVO> ServiceInfoVOList, DeviceInvokeBeanDTO deviceInvokeBeanDTO) {
        log.debug("{}InvokeServiceImpl-receiveDeviceInfo", type().getPathWeb());
        String serUrl = String.format("/%s/v1/receiveDeviceInfo", type().getPathWeb());
        for (ServiceInfoVO ServiceInfoVO : ServiceInfoVOList) {
            String realIpPort = String.format("%s:%s", ServiceInfoVO.getMgtIp(), ServiceInfoVO.getMgtPort());
            String url = getServiceInvokeUrl(ServiceInfoVO , serUrl) ;
            try {
                String json = JSON.toJSONString(deviceInvokeBeanDTO);
                receiveDeviceInfo(url, json, realIpPort);
            } catch (Exception e) {
                log.error("{}接收设备信息接口返回结果异常,serviceId={},url={},message={}", type().getName(), ServiceInfoVO.getId(), url, e.getMessage());
                return ResultUtil.error(SecErrorCodeConstant.RECEIVE_DEVICE_ERROR);
            }
        }
        return ResultUtil.ok();
    }

    /**
     * 配置公钥
     *
     * @param serviceInfoDTO
     * @param configMap
     * @return
     */
    @Override
    public SecRestResponse<Object> configAuthPk(ServiceInfoDTO serviceInfoDTO, Map<String, String> configMap) {
        log.debug("{}InvokeServiceImpl-configAuthPk", type().getPathWeb());
        String realIpPort = String.format("%s:%s", serviceInfoDTO.getMgtIp(), serviceInfoDTO.getMgtPort());
        String url = getServiceInvokeUrl(serviceInfoDTO , String.format("/%s", type().getPathWeb())) ;
        //String url = HttpUtils.httpsUrl(ip, port, String.format("/%s", type().getPathWeb()));
        try {
            configPublicKey(url,configMap, realIpPort);
        } catch (Exception e) {
            log.error("{}配置公钥返回结果异常,url={},message={}", type().getName(), url, e.getMessage());
            return ResultUtil.error(SecErrorCodeConstant.CONFIG_PUBLIC_KEY_FUN_ERROR);
        }
        return ResultUtil.ok();
    }

//    @Override
//    public SecRestResponse<Object> configAuthPk(String ip, Integer port, Map<String,String> configMap) {
//        log.debug("{}InvokeServiceImpl-configAuthPk", type().getPathWeb());
//        String realIpPort = String.format("%s:%s", serviceInfoDTO.getMgtIp(), serviceInfoDTO.getMgtPort());
//        String url = getServiceInvokeUrl(serviceInfoDTO , serUrl) ;
//        try {
//            configPublicKey(url,configMap);
//        } catch (Exception e) {
//            log.error("{}配置公钥返回结果异常,url={},message={}", type().getName(), url, e.getMessage());
//            return ResultUtil.error(SecErrorCodeConstant.CONFIG_PUBLIC_KEY_FUN_ERROR);
//        }
//        return ResultUtil.ok();
//    }

    @Override
    public SecRestResponse<Object> receiveConfig(List<ServiceInfoVO> ServiceInfoVOList, Map<String, String> configMap) {
        log.debug("{}InvokeServiceImpl-receiveConfig", type().getPathWeb());
        String serUrl = String.format("/%s/v1/receiveConfig", type().getPathWeb());
        for (ServiceInfoVO ServiceInfoVO : ServiceInfoVOList) {
            String realIpPort = String.format("%s:%s", ServiceInfoVO.getMgtIp(), ServiceInfoVO.getMgtPort());
            String url = getServiceInvokeUrl(ServiceInfoVO , serUrl) ;
            try {
                receiveConfig(url, configMap, realIpPort);
            } catch (Exception e) {
                log.error("{}接收配置信息接口返回结果异常,serviceId={},url={},message={}", type().getName(), ServiceInfoVO.getId(), url, e.getMessage());
                return ResultUtil.error(SecErrorCodeConstant.RECEIVE_CONFIG_ERROR);
            }
        }
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Object> startBusiService(InvokeStartBusiDTO dto) {
        log.debug("{}InvokeServiceImpl-startBusiService", type().getPathWeb());
        String serUrl = String.format("/%s/v1/startBusiService", type().getPathWeb());

        dto.getData().stream().forEach(infoVO -> {
            String realIpPort = String.format("%s:%s", infoVO.getMgtIp(), infoVO.getMgtPort());
            String url = getServiceInvokeUrl(infoVO , serUrl) ;
            try {
                
                startBusiService(url, StartReqDTO.builder().serviceCode(type().getCode()).build(), realIpPort);
            } catch (Exception e) {
                log.error("{}业务服务接口返回结果异常,serviceId={},url={},message={}", type().getName(), infoVO.getId(), url, e.getMessage());
                throw new BusinessException(SecErrorCodeConstant.START_BUSI_SERVICE_ERROR);
            }
        });

        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Object> stopBusiService(InvokeEndBusiDTO dto) {
        log.debug("{}InvokeServiceImpl-stopBusiService", type().getPathWeb());
        String serUrl = String.format("/%s/v1/stopBusiService", type().getPathWeb());

        dto.getData().stream().forEach(infoVO -> {
            String realIpPort = String.format("%s:%s", infoVO.getMgtIp(), infoVO.getMgtPort());
            String url = getServiceInvokeUrl(infoVO , serUrl) ;
            try {
                stopBusiService(url, EndReqDTO.builder().serviceCode(type().getCode()).build(), realIpPort);
            } catch (Exception e) {
                log.error("{}停止业务服务接口返回结果异常,serviceId={},url={},message={}", type().getName(), infoVO.getId(), url, e.getMessage());
                throw new BusinessException(SecErrorCodeConstant.STOP_BUSI_SERVICE_ERROR);
            }
        });
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Map<Long, Integer>> getServiceState(InvokeServiceStateDTO dto) {
        log.debug("{}InvokeServiceImpl-getServiceState", type().getPathWeb());

        Map<Long, Integer> map = new HashMap<>();

        String serUrl = String.format("/%s/v1/getServiceState", type().getPathWeb());
        dto.getData().stream().forEach(infoVO -> {
            String realIpPort = String.format("%s:%s", infoVO.getMgtIp(), infoVO.getMgtPort());
            String url = getServiceInvokeUrl(infoVO , serUrl) ;
            Integer runStatus = getServiceState(url, infoVO.getId(), realIpPort);
            map.put(infoVO.getId(), runStatus);
        });
        return SecRestResponse.success(map);
    }

    @Override
    public void getServiceStateAll() {
        List<ServiceInfoVO> serviceListByType = getServiceListByType(type().getId());
        String serUrl = String.format("/%s/v1/getServiceState", type().getPathWeb());
        if(CollectionUtils.isEmpty(serviceListByType)){
            return;
        }
        serviceListByType.forEach(item -> {
            String realIpPort = String.format("%s:%s", item.getMgtIp(), item.getMgtPort());
            String url = getServiceInvokeUrl(item , serUrl) ;
            try {
                getServiceState(url, item.getId(), realIpPort);
            } catch (Exception e) {
                operServiceState(item.getId(), 2);
                log.error("{}-InvokeServiceImpl-getServiceStateAll获取服务状态失败,item={}",type().getPathWeb(),item,e);
            }
        });
    }

    @Override
    public SecRestResponse<List<ServiceMenuVO>> getMenuList(ServiceInfoVO serviceInfoVO, int roleCode) {
        log.debug("{}InvokeServiceImpl-getMenuList", type().getPathWeb());
        String serUrl = String.format("/%s/v1/getMenuList", type().getPathWeb());
        String realIpPort = String.format("%s:%s", serviceInfoVO.getMgtIp(), serviceInfoVO.getMgtPort());
        String url = getServiceInvokeUrl(serviceInfoVO , serUrl) ;

        List<ServiceMenuVO> list = new ArrayList<>();
        try {
            list = getMenuList(url, ServiceMenuDTO.builder().roleCode(roleCode).build(), realIpPort);
        } catch (Exception e) {
            log.error("{}获取服务菜单接口返回结果异常,serviceId={},url={},message={}", type().getName(), serviceInfoVO.getId(), url, e.getMessage());
        }
        return ResultUtil.ok(list);
    }

    @Override
    public void getServiceOperationLog(Map<Long, String> idCodeMap) {
        // 获取服务实例
        List<ServiceInfoVO> serviceListByType = getServiceOfTenantByType(type().getId());
        if (CollectionUtils.isEmpty(serviceListByType)) {
            return;
        }
        String startTime;
        String tenantCode;
        for (ServiceInfoVO serviceInfoVO : serviceListByType) {
            String serUrl = String.format("/%s/v1/operationlog", type().getPathWeb());
            String realIpPort = String.format("%s:%s", serviceInfoVO.getMgtIp(), serviceInfoVO.getMgtPort());
            String url = getServiceInvokeUrl(serviceInfoVO , serUrl) ;
            try {
                Long serviceGroupId = serviceInfoVO.getServiceGroupId();
                List<Long> tenantIdList = tenantToServiceGroupService.getTenantIdByGroupId(serviceGroupId);
                if(tenantIdList != null && ! tenantIdList.isEmpty() ){
                    for (Long tenantId : tenantIdList) {
                        // 获取子服务操作日志
                        ServiceOperLogDTO serviceOperLogDTO = new ServiceOperLogDTO();
                        tenantCode = idCodeMap.get(tenantId);
                        String startTimeKey =  tenantCode + "_operlog:" + tenantCode + "_" + type().getCode() + "_LogStartTime";
                        startTime = redisUtill.strGet(startTimeKey);
                        if(StringUtils.isBlank(startTime)){
                            serviceOperLogDTO.setStartTime(null);
                        }
                        else{
                            serviceOperLogDTO.setStartTime(Long.valueOf(startTime));
                        }
                        if (ServiceTypeEnum.initTenantService.containsKey(type().getId())) {
                            serviceOperLogDTO.setTenantCode(tenantCode);
                        }
                        // 本次请求的最后一条日志时间
                        startTime = getServiceOperationLog(url, serviceOperLogDTO, serviceInfoVO, realIpPort,tenantId);
                        if (startTime != null) {
                            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            redisUtill.strSet(startTimeKey, format.parse(startTime).getTime());
                        }

                    }
                }

            } catch (Exception e) {
                log.error("{}-InvokeServiceImpl-getServiceOperationLog记录服务操作日志失败,item={}", type().getPathWeb(), serviceInfoVO, e);
            }
        }
    }

    @Override
    public void setServiceTokenExpire(String tokenExpire) {
        // 获取服务实例
        List<ServiceInfoVO> serviceListByType = getServiceOfTenantByType(type().getId());
        if (CollectionUtils.isEmpty(serviceListByType)) {
            return;
        }
        for (ServiceInfoVO serviceInfoVO : serviceListByType) {
            String serUrl = String.format("/%s/v1/setTokenExpire", type().getPathWeb());
            String realIpPort = String.format("%s:%s", serviceInfoVO.getMgtIp(), serviceInfoVO.getMgtPort());
            String url = getServiceInvokeUrl(serviceInfoVO , serUrl) ;
            try {
                setServiceTokenExpire(url, WebTokenExpireDTO.builder().expireTime(tokenExpire).build(), realIpPort);
            } catch (Exception e) {
                log.error("{}-InvokeServiceImpl-setServiceTokenExpire,item={}", type().getPathWeb(), serviceInfoVO, e);
            }
        }
    }

    @Override
    public void setTenantQuota(List<QuotaInfoDTO> quotaInfoList) {
        log.debug("{}InvokeServiceImpl-setTenantQuota", type().getPathWeb());
        for (QuotaInfoDTO quotaInfoDTO : quotaInfoList) {
            ServiceInfoVO serviceInfoVO = getServiceInfoByGroupId(type().getId(),quotaInfoDTO.getServiceGroupId());
            if (serviceInfoVO == null) {
                continue;
            }
            String serUrl = String.format("/%s/v1/setTenantQuota", type().getPathWeb());
            String realIpPort = String.format("%s:%s", serviceInfoVO.getMgtIp(), serviceInfoVO.getMgtPort());
            String url = getServiceInvokeUrl(serviceInfoVO , serUrl) ;
            try {
                setTenantQuota(url, SetTenantQuotaDTO.builder().quotaInfoList(Lists.newArrayList(quotaInfoDTO)).build(), realIpPort);
            } catch (Exception e) {
                log.error("{}-InvokeServiceImpl-setTenantQuota,item={}", type().getPathWeb(), serviceInfoVO, e);
                throw new BusinessException(SecErrorCodeConstant.SET_TENANT_QUOTA_ERROR);
            }
        }
    }

    @Override
    public void getTenantQuota() {
        List<ServiceInfoVO> serviceListByType = getServiceOfTenantByType(type().getId());
        if (CollectionUtils.isEmpty(serviceListByType)) {
            return;
        }

        //获取租户标识
        //getServiceAddTenantCode(serviceListByType);
        for (ServiceInfoVO serviceInfoVO : serviceListByType) {
            String serUrl = String.format("/%s/v1/getQuotaInfo", type().getPathWeb());
            String realIpPort = String.format("%s:%s", serviceInfoVO.getMgtIp(), serviceInfoVO.getMgtPort());
            String url = getServiceInvokeUrl(serviceInfoVO , serUrl) ;
            try {
                List<TenantPO> tenantList = getTenantByServiceGroupId(serviceInfoVO.getServiceGroupId());
                for(TenantPO tenant: tenantList){
                    String tenantCode = tenant.getTenantCode();
                    if (StringUtils.isNotBlank(tenantCode)){
                        getTenantQuota(url, serviceInfoVO, GetQuotaInfoDTO.builder().tenantCode(tenantCode).build(),realIpPort,tenant.getTenantId());
                    }
                }
            } catch (Exception e) {
                log.error("{}-InvokeServiceImpl-getQuotaInfo,item={}", type().getPathWeb(), serviceInfoVO, e);
            }
        }
    }

    @Override
    public void getTenantQuotaOfTenantByCode(Long serviceGroupId, String tenantCodeParam,Long tenantId) {
        ServiceInfoVO serviceInfoVO = getServiceInfoByGroupId(type().getId(), serviceGroupId);
        if (serviceInfoVO == null) {
            return;
        }
        serviceInfoVO.setTenantCode(tenantCodeParam);

        String serUrl = String.format("/%s/v1/getQuotaInfo", type().getPathWeb());
        String realIpPort = String.format("%s:%s", serviceInfoVO.getMgtIp(), serviceInfoVO.getMgtPort());
        String url = getServiceInvokeUrl(serviceInfoVO, serUrl);
        try {
            String tenantCode = serviceInfoVO.getTenantCode();
            if (StringUtils.isNotBlank(tenantCode)) {
                getTenantQuota(url, serviceInfoVO, GetQuotaInfoDTO.builder().tenantCode(tenantCode).build(), realIpPort,tenantId);
            }
        } catch (Exception e) {
            log.error("{}-InvokeServiceImpl-getQuotaInfo,item={}", type().getPathWeb(), serviceInfoVO, e);
        }
    }

    @Override
    public void getStatisticIncreInfo(List<DicStatisticVO> dicStatisticList) {
        //密码机服务/vpn查询服务类型下所有服务调用,其他服务查询服务类型下其中一个服务查询调用
        //判断是否同服务类型全服务调用 1是 2否
        Map<Integer, List<DicStatisticVO>> map = dicStatisticList.stream().collect(Collectors.groupingBy(DicStatisticVO::getIsInvokeMultiService));

        for (Map.Entry<Integer, List<DicStatisticVO>> entry : map.entrySet()) {
            Integer key = entry.getKey();
            List<DicStatisticVO> list = entry.getValue();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }

            if (1 == key) {
                //查询服务类型下所有服务调用
                List<ServiceInfoVO> serviceListByTypeAll = getServiceListByType(type().getId());
                getStatisticInfo(serviceListByTypeAll, list);
            } else {
                //查询服务类型下其中一个服务查询调用
                List<ServiceInfoVO> serviceListByType = getServiceOfTenantByType(type().getId());
                getStatisticInfo(serviceListByType,list);
            }
        }
    }

    @Override
    public void getStatisticUnIncreInfo(List<DicStatisticVO> unIncreDicStatisticList) {
        //密码机服务/vpn查询服务类型下所有服务调用,其他服务查询服务类型下其中一个服务查询调用
        //判断是否同服务类型全服务调用 1是 2否
        Map<Integer, List<DicStatisticVO>> map = unIncreDicStatisticList.stream().collect(Collectors.groupingBy(DicStatisticVO::getIsInvokeMultiService));

        for (Map.Entry<Integer, List<DicStatisticVO>> entry : map.entrySet()) {
            Integer key = entry.getKey();
            List<DicStatisticVO> unIncreList = entry.getValue();
            if (CollectionUtils.isEmpty(unIncreList)) {
                continue;
            }

            if (1 == key) {
                //查询服务类型下所有服务调用
                List<ServiceInfoVO> serviceListByTypeAll = getServiceListByType(type().getId());
                getStatisticInfo(serviceListByTypeAll, unIncreList);
            } else {
                //查询服务类型下其中一个服务查询调用
                List<ServiceInfoVO> serviceListByType = getServiceOfTenantByType(type().getId());
                getStatisticInfo(serviceListByType, unIncreList);
            }
        }
    }

    private void getStatisticInfo(List<ServiceInfoVO> serviceList,List<DicStatisticVO> list){
        String serUrl = String.format("/%s/swmonitor/rest/v1/walk", type().getPathWeb());
        if (CollectionUtils.isEmpty(serviceList)) {
            return;
        }
        //获取租户标识
        getServiceAddTenantCode(serviceList);

        List<String> oidList = list.stream().map(DicStatisticVO::getOid).distinct().collect(Collectors.toList());

        serviceList.forEach(item -> {
            String realIpPort = String.format("%s:%s", item.getMgtIp(), item.getMgtPort());
            String url = getServiceInvokeUrl(item , serUrl) ;
            try {
                if (item.getTenantId() != null ) {
                    getStatisticInfo(url, oidList, item, realIpPort);
                }
            } catch (Exception e) {
                log.error("{}-InvokeServiceImpl-getStatisticInfo获取服务统计信息失败,item={}", type().getPathWeb(), item, e);
            }
        });
    }
}
