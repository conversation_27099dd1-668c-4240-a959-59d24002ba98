package com.sansec.ccsp.device.business.monitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @description : 设备snmp配置表(1030);
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-9-20
 */
@TableName("DEVICE_SNMP_OID_CONFIG")
@Data
public class DeviceSnmpOidConfigPO extends BasePO{
    /**
     * id
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 设备类型id
     */
    private Long deviceTypeId;
    /**
     * oid
     */
    private String oid;
    /**
     * 名称
     */
    private String name;
    /**
     * oid数据类型 String Integer Long
     */
    private String valueType;
    /**
     * 请求类型 get Walk
     */
    private String requestType;
    /**
     * 计算方式 0:普通、1：聚合
     */
    private Integer collectorType;
    /**
     * 父节点id
     */
    private Long parentId;
    /**
     * 排序序号
     */
    private Integer subOidKey;
    /**
     * 子oid
     */
    private String subOidValue;
    /**
     * 计算方式
     */
    private String computingType;
    /**
     * 计算模板
     */
    private String calculateTemplate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}