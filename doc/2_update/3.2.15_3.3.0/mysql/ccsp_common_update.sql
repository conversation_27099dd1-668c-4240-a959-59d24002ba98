ALTER TABLE `config`
    ADD COLUMN `TENANT_ID` bigint(20) NULL DEFAULT NULL AFTER `UPDATE_TIME`;

ALTER TABLE `config`
    ADD COLUMN `APP_ID` bigint(20) NULL DEFAULT NULL AFTER `TENANT_ID`;

-- ----------------------------
-- Table structure for dic_share_group_type
-- ----------------------------
DROP TABLE IF EXISTS `dic_share_group_type`;
CREATE TABLE `dic_share_group_type`
(
    `ID`           bigint(20) NOT NULL,
    `BUSI_TYPE_ID` bigint(20) NOT NULL,
    `IS_NEED_KMS`  int(11) NOT NULL COMMENT ''0:不需要；1：需要 '',
    `IS_SHARE`     int(11) NOT NULL COMMENT ''0:不支持共享；1：支持共享 '',
    PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = ''支持创建共享服务类型（330）'' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_share_group_type
-- ----------------------------
INSERT INTO `dic_share_group_type`
VALUES (1, 1, 1, 1);
INSERT INTO `dic_share_group_type`
VALUES (2, 2, 1, 1);
INSERT INTO `dic_share_group_type`
VALUES (3, 4, 0, 1);
INSERT INTO `dic_share_group_type`
VALUES (4, 5, 0, 1);
INSERT INTO `dic_share_group_type`
VALUES (5, 7, 0, 1);


-- ----------------------------
-- Table structure for dic_user_type
-- ----------------------------
DROP TABLE IF EXISTS `dic_user_type`;
CREATE TABLE `dic_user_type`
(
    `ID`           bigint(20) NOT NULL COMMENT '' ID '',
    `USER_TYPE_ID` int(255) NULL DEFAULT NULL COMMENT '' 用户类型id;
1平台用户
2租户用户 3应用用户 4单位用户'',
  `USER_TYPE_NAME` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT ''用户类型名称'',
  `REMARK` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT ''备注'',
  `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT ''创建人'',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT ''创建时间'',
  `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT ''更新人'',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT ''更新时间'',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = ''用户类型（330）'' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dic_user_type
-- ----------------------------
INSERT INTO `dic_user_type`
VALUES (1, 1, ''平台用户'', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_user_type`
VALUES (2, 2, ''租户用户'', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dic_user_type`
VALUES (4, 4, ''单位用户'', NULL, NULL, NULL, NULL, NULL);



INSERT INTO `config`
VALUES (5492748794601407029, 0, ''is_service_share'', ''是否共享服务'', ''1'', 0, 0, 1, 74, ''0：密码服务不支持共享；1：密码服务支持共享'', NULL,
        NULL,
        NULL, NULL, NULL, NULL);
INSERT INTO `config`
VALUES (5492748794601407030, 0, ''is_state_mode'', ''是否政务模式'', ''1'', 0, 0, 1, 75, ''0：非政务模式，应用名称保持不变；1：政务模式，应用改为授权账号'',
        NULL, NULL, NULL, NULL, NULL, NULL);



UPDATE `config`
SET `CONFIG_VALUE` = ''1''
WHERE `CONFIG_ID` = 5467049693950640604;

UPDATE `config`
SET `CONFIG_VALUE` = ''false''
WHERE `CONFIG_ID` = 5492748794601406988;

UPDATE `config`
SET `CONFIG_VALUE` = ''2''
WHERE `CONFIG_ID` = 5492748794601407003;

UPDATE `config`
SET `CONFIG_VALUE` = ''1''
WHERE `CONFIG_ID` = 5492748794601407007;

UPDATE `config`
SET `CONFIG_VALUE` = ''true''
WHERE `CONFIG_ID` = 5492748794601407018;



INSERT INTO `dic_statistic`
VALUES (25, ''kms'', 2, ''pki密钥数量'', ''kmsToPkiEncKeyNumStatistic'', 1, 0, ''.1.3.6.1.4.1.746366.17.1.0'', ''个'');
INSERT INTO `dic_statistic`
VALUES (26, ''kms'', 2, ''svs应用证书数量'', ''kmsToSvsSignAppCertStatistic'', 1, 0, ''.1.3.6.1.4.1.746366.17.1.1'', ''个'');
INSERT INTO `dic_statistic`
VALUES (27, ''kms'', 2, ''svs用户证书数量'', ''kmsToSvsSignUserCertStatistic'', 1, 0, ''.1.3.6.1.4.1.746366.17.1.2'', ''个'');
INSERT INTO `dic_statistic`
VALUES (28, 'secauth', 2, '用户数量', 'secauthUserNumStatistic', 1, 0, '.1.3.6.1.4.1.746371.5.1.1', '个');



INSERT INTO `sys_menu`
VALUES (101100101, ''单位首页'', 0, '' / organizeindex '', '''', 0, ''M'', 1, 0, ''organization:index'', '''', '''', 2,
        '''', '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (104104107, ''同步数据库密码'', 104104100, '''', '''', 0, ''F'', 0, 0, ''servicemgt:serviceinfo:resetDbAuthCode'', '''',
        '''', 7, '''', '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (104109108, ''修改密码'', 104109100, '''', '''', 0, ''F'', 0, 0, ''servicemgt:dbinfo:updateAuthCode'', '''', '''', 8,
        '''', '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (120101110, ''绑定SIM盾'', 120101100, '''', '''', 0, ''F'', 0, 0, ''common:user:bindSimShield'', '''', '''', 4,
        '''', '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (120101111, ''绑定SIMKEY'', 120101100, '''', '''', 0, ''F'', 0, 0, ''common:user:bindSimKey'', '''', '''', 4, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (120103100, ''单位管理'', 120100100, '' / system / organize / index '', '' / system / organize / index.vue '', 0,
        ''C'', 0, 0, ''common:organization:list'', '''', '''', 3, '''', '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (120103101, ''新增'', 120103100, '''', '''', 0, ''F'', 0, 0, ''common:organization:add'', '''', '''', 2, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (120103102, ''编辑'', 120103100, '''', '''', 0, ''F'', 0, 0, ''common:organization:edit'', '''', '''', 3, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (120103103, ''删除'', 120103100, '''', '''', 0, ''F'', 0, 0, ''common:organization:delete'', '''', '''', 4, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124102100, ''认证服务管理'', 124100100, '' / subarea / subarea / serviceOauth '',
        '' / subarea / subarea / serviceOauth.vue '', 0, ''F'', 1, 0, ''region:authCenter:list'', '''',
        ''subareaManage'', 1, '''', '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124102101, ''新增'', 124102100, '''', '''', 0, ''F'', 0, 0, ''region:authCenter:add'', '''', '''', 2, '''', '''',
        0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124102102, ''编辑'', 124102100, '''', '''', 0, ''F'', 1, 0, ''region:authCenter:edit'', '''', '''', 3, '''', '''',
        0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124102103, ''删除'', 124102100, '''', '''', 0, ''F'', 0, 0, ''region:authCenter:delete'', '''', '''', 4, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124103100, ''管控服务管理'', 124100100, '' / subarea / subarea / serviceControl '',
        '' / subarea / subarea / serviceControl.vue '', 0, ''F'', 1, 0, ''region:remoteServer:list'', '''',
        ''subareaManage'', 1, '''', '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124103101, ''新增'', 124103100, '''', '''', 0, ''F'', 0, 0, ''region:remoteServer:add'', '''', '''', 2, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124103102, ''编辑'', 124103100, '''', '''', 0, ''F'', 1, 0, ''region:remoteServer:edit'', '''', '''', 3, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124103103, ''删除'', 124103100, '''', '''', 0, ''F'', 0, 0, ''region:remoteServer:delete'', '''', '''', 4, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124104100, ''监控服务管理'', 124100100, '' / subarea / subarea / serviceMonitor '',
        '' / subarea / subarea / serviceMonitor.vue '', 0, ''F'', 1, 0, ''region:monitorServer:list'', '''',
        ''subareaManage'', 1, '''', '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124104101, ''新增'', 124104100, '''', '''', 0, ''F'', 0, 0, ''region:monitorServer:add'', '''', '''', 2, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124104102, ''编辑'', 124104100, '''', '''', 0, ''F'', 1, 0, ''region:monitorServer:edit'', '''', '''', 3, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124104103, ''删除'', 124104100, '''', '''', 0, ''F'', 0, 0, ''region:monitorServer:delete'', '''', '''', 4, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124105100, ''网关统计组件管理'', 124100100, '' / subarea / subarea / countModule '',
        '' / subarea / subarea / countModule.vue '', 0, ''F'', 1, 0, ''region:gatewayStatic:list'', '''',
        ''subareaManage'', 1, '''', '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124105101, ''新增'', 124105100, '''', '''', 0, ''F'', 0, 0, ''region:gatewayStatic:add'', '''', '''', 2, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124105102, ''编辑'', 124105100, '''', '''', 0, ''F'', 1, 0, ''region:gatewayStatic:edit'', '''', '''', 3, '''',
        '''', 0, '''', NULL, '''', NULL, '''');

INSERT INTO `sys_menu`
VALUES (124105103, ''删除'', 124105100, '''', '''', 0, ''F'', 0, 0, ''region:gatewayStatic:delete'', '''', '''', 4, '''',
        '''', 0, '''', NULL, '''', NULL, '''');



UPDATE `sys_menu`
SET `STATUS` = 0
WHERE `MENU_ID` = 102102100;

UPDATE `sys_menu`
SET `STATUS` = 0
WHERE `MENU_ID` = 102103100;

UPDATE `sys_menu`
SET `MENU_NAME` = ''授权管理''
WHERE `MENU_ID` = 106100100;

UPDATE `sys_menu`
SET `MENU_NAME` = ''授权账号''
WHERE `MENU_ID` = 106102100;

UPDATE `sys_menu`
SET `VISIBLE` = 0
WHERE `MENU_ID` = 123101100;

UPDATE `sys_menu`
SET `IFRAME_URL` = '' / swmonitor / front / viewPage?viewName=panel-multiple&isShowScreen=1''
WHERE `MENU_ID` = 123102100;

UPDATE `sys_menu`
SET `PERMS` = ''simkey:cert:histpry''
WHERE `MENU_ID` = 128102101;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1102101101;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1102101102;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1102102101;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1102102102;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1102103101;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1123101100;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1123101101;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1123101102;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1123101103;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1123102100;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 1123106100;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4104102100;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4104102101;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4104102102;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4104102103;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4104102104;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4104102105;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4104102106;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4104102107;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4105105100;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4105105101;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4105105102;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4105105103;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4105105104;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4105105105;

DELETE
FROM `sys_role_menu`
WHERE `ID` = 4105105106;

INSERT INTO `sys_role_menu`
VALUES (1104102100, 104102100, 1, ''服务组管'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1104102101, 104102101, 1, ''新建'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1104102102, 104102102, 1, ''编辑'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1104102103, 104102103, 1, ''服务管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1104102104, 104102104, 1, ''删除'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1104102107, 104102107, 1, ''释放服务'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1104104107, 104104107, 1, ''同步数据库密码'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1104109108, 104109108, 1, ''修改密码'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1105105100, 105105100, 1, ''设备组管'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1105105101, 105105101, 1, ''新建'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1105105102, 105105102, 1, ''编辑'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1105105103, 105105103, 1, ''删除'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1105105104, 105105104, 1, ''设备管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1105105105, 105105105, 1, ''绑定设备'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1105105106, 105105106, 1, ''释放设备'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1120103100, 120103100, 1, ''单位管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1120103101, 120103101, 1, ''新增'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1120103102, 120103102, 1, ''编辑'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1120103103, 120103103, 1, ''删除'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1121106100, 121106100, 1, ''主密钥管'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1121106101, 121106101, 1, ''备份'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1121106102, 121106102, 1, ''恢复'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1121109100, 121109100, 1, ''内部密钥管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1121109101, 121109101, 1, ''备份'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1121109102, 121109102, 1, ''恢复'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124100100, 124100100, 1, ''区域管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124101100, 124101100, 1, ''区域管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124101101, 124101101, 1, ''新建'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124101102, 124101102, 1, ''详情'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124101103, 124101103, 1, ''编辑'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124101104, 124101104, 1, ''删除'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124102100, 124102100, 1, ''认证服务管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124102101, 124102101, 1, ''新增'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124102102, 124102102, 1, ''编辑'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124102103, 124102103, 1, ''删除'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124103100, 124103100, 1, ''管控服务管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124103101, 124103101, 1, ''新增'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124103102, 124103102, 1, ''编辑'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124103103, 124103103, 1, ''删除'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124104100, 124104100, 1, ''监控服务管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124104101, 124104101, 1, ''新增'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124104102, 124104102, 1, ''编辑'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124104103, 124104103, 1, ''删除'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124105100, 124105100, 1, ''网关统计组件管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124105101, 124105101, 1, ''新增'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124105102, 124105102, 1, ''编辑'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (1124105103, 124105103, 1, ''删除'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (5121100100, 121100100, 5, ''系统管理'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (5121104100, 121104100, 5, ''人员管理配置'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (5121104101, 121104101, 5, ''修改配置'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (5121105100, 121105100, 5, ''系统配置'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (5121105101, 121105101, 5, ''修改配置'', NULL, '''', NULL, '''');

INSERT INTO `sys_role_menu`
VALUES (12101100101, 101100101, 12, ''单位首页'', NULL, '''', NULL, '''');


INSERT INTO `user_role`
VALUES (12, ''单位审计员'', 3, NULL, 4, 0, NULL, 1, ''2023-02-19 20:41:51'', NULL, NULL);

