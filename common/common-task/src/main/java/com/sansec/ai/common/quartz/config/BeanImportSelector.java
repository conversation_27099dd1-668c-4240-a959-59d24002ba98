package com.sansec.ai.common.quartz.config;

import com.sansec.ai.common.config.EnableQuartz;
import org.springframework.context.annotation.ImportSelector;
import org.springframework.core.type.AnnotationMetadata;

import java.util.Map;

/**
 * 定时任务模块加载。
 * <AUTHOR>
 * @since 2025-2-24
 **/
public class BeanImportSelector implements ImportSelector {
	@Override
	public String[] selectImports(AnnotationMetadata annotationMetadata) {
		Map<String, Object> annotationAttributes = annotationMetadata.getAnnotationAttributes(EnableQuartz.class.getName());
		if (null == annotationAttributes) {
			return new String[]{};
		}
		boolean flag = (boolean) annotationAttributes.get("value");
		if (!flag) {
			return new String[]{};
		}
		return (String[]) annotationAttributes.get("classes");
	}
}
