package com.sansec.ccsp.device.business.devicetype.entity;

import lombok.Data;

@Data
public class DeviceTypePagePo {

    /**
     * 设备类型ID
     */
    private Long deviceTypeId;
    /**
     * 设备类型名称
     */
    private String deviceTypeName;
    /**
     * 是否默认 1默认 0不默认;默认0
     */
    private Integer defaultFlag = 0;
    /**
     * 所属厂商ID
     */
    private Long vendorId;

    /**
     * 厂商名称
     */
    private String vendorName;

    /**
     * 设备类型(1：云密码机:2：物理机:3：虚拟机)
     */
    private Integer familyType;
    /**
     * 密码机服务类型ID
     */
    private Long machineTypeId;
    /**
     * 上级设备类型ID（虚拟机独有）;默认0
     */
    private Long parentId;
    /**
     * 超融合虚拟机镜像类型（虚拟机独有）
     */
    private String hccsImage;
    /**
     * 管理规范（交互类型编号）
     */
    private String interactionSerialNumber;

    /**
     * 云密码机支持的虚机数量
     */
    private Integer cloudVsmTotal;

    /**
     * 管理管理接口协议（1：HTTPS；2：HTTP）
     */
    private Integer mgtMethod;
    /**
     * 管理端口
     */
    private Integer mgtPort;
    /**
     * 服务端口
     */
    private Integer busiPort;
    /**
     * 是否支持生成主密钥0不支持 1支持
     */
    private Integer supportMainKeyFlag;
    /**
     * 是否支持安全管理，0不支持 1支持
     */
    private Integer supportSecManageFlag;
    /**
     * 是否支持生成密钥
     */
    private Integer supportGenKeyFlag;
    /**
     * 是否支持SNMP监控0不支持 1支持
     */
    private Integer supportSnmpFlag;
    /**
     * 是否支持token回调
     */
    private Integer tokenCallBackFlag;
    /**
     * 是否支持连接密码或token;
     */
    private Integer needPasswordFlag;
    /**
     * 读取设备信息 1需要 0不需要
     */
    private Integer readInfoFlag;
    /**
     * 是否支持管理接口公钥验签（0：否；1：是）
     */
    private Integer mgtPublickeyFlag;
    /**
     * 该类型设备支持的密钥模板id，多个以逗号分隔
     */
    private String keyTempletIds;
    /**
     * 默认自动生成密钥时支持的密钥模板id，多个以逗号分隔
     */
    private String defaultKeyTempletIds;
    /**
     * 无效标识 0可用 1无效
     */
    private Integer invalidFlag;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 备注
     */
    private String remark;
}
