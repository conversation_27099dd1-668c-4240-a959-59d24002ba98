package com.sansec.ccsp.pt.common.util;

import cn.hutool.extra.spring.SpringUtil;
import com.sansec.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.util.CollectionUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.security.GeneralSecurityException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;

/**
 * <AUTHOR>
 * @Description: http https  httpclient工具类
 * @Date: 2022/4/18 19:57
 * @copyright sansec
 */
@Slf4j
public class HttpsClientUtil {

    public static final String ENCODING = "UTF-8";
    public static final String CONTEXT_TYPE = "application/json";
    public static final String CLOSE_ERROR = "Close error:";
    public static final String EXCEPTION_MESSAGE = "Exception message: ";

    private RequestConfig requestConfig;


    public HttpsClientUtil(int connectTimeout, int socketTimeout, int connectionRequestTimeout) {
        RequestConfig.Builder configBuilder = RequestConfig.custom();
        // 设置连接超时
        configBuilder.setConnectTimeout(connectTimeout);
        // 设置读取超时
        configBuilder.setSocketTimeout(socketTimeout);
        // 设置从连接池获取连接实例的超时
        configBuilder.setConnectionRequestTimeout(connectionRequestTimeout);
        requestConfig = configBuilder.build();
    }

    public static HttpsClientUtil getDefault() {
        ApplicationContext applicationContext = SpringUtil.getApplicationContext();
        Environment environment = applicationContext.getEnvironment();
        Integer connectTimeout = environment.getProperty("http.connectTimeout", Integer.TYPE, 30000);
        Integer socketTimeout = environment.getProperty("http.socketTimeout", Integer.TYPE, 30000 );
        Integer connectionRequestTimeout = environment.getProperty("http.connectionRequestTimeout", Integer.TYPE, 30000);
        return new HttpsClientUtil(connectTimeout, socketTimeout, connectionRequestTimeout);
    }


//    static {
//        RequestConfig.Builder configBuilder = RequestConfig.custom();
//        // 设置连接超时
//        configBuilder.setConnectTimeout(MAX_TIMEOUT);
//        // 设置读取超时
//        configBuilder.setSocketTimeout(MAX_TIMEOUT);
//        // 设置从连接池获取连接实例的超时
//        configBuilder.setConnectionRequestTimeout(MAX_TIMEOUT);
//        requestConfig = configBuilder.build();
//    }

    /**
     * 发送 GET 请求（HTTP），不带输入数据
     *
     * @param url
     * @return
     */
    public String doGet(String url) {
        return doGet(url, new HashMap<String, Object>(), new HashMap<String, String>());
    }

    /**
     * 发送 GET 请求（HTTP），K-V形式
     *
     * @param url
     * @param params
     * @param headers
     * @return
     */
    public String doGet(String url, Map<String, Object> params, Map<String, String> headers) {
        String apiUrl = url;
        StringBuilder param = getParam(params);
        apiUrl += param;
        String result = null;
        CloseableHttpResponse response = null;
        CloseableHttpClient httpclient = null;
        HttpGet httpGet = null;
        try {
            httpclient = HttpClients.createDefault();
            httpGet = new HttpGet(apiUrl);
            packageHeader(headers, httpGet);
            response = httpclient.execute(httpGet);

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                InputStream instream = entity.getContent();
                result = IOUtils.toString(instream, ENCODING);
            }
        } catch (IOException e) {
            log.error("调用http-get接口异常,url={},params={},errorMsg={}", url, params, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            try {
                if (null != httpclient) {
                    // 此处就只关闭Response等底层流，而httpclient不用关闭，保持socket放入缓存池中
                    httpclient.close();
                }
                if (null != response) {
                    // 此处就只关闭Response等底层流，而httpclient不用关闭，保持socket放入缓存池中
                    response.close();
                }
                if (null != httpGet) {
                    httpGet.releaseConnection();
                }
            } catch (IOException e) {
                log.error(CLOSE_ERROR, e);
            }
        }
        return result;
    }

    /**
     * 发送 GET 请求（HTTP），K-V形式
     *
     * @param url
     * @param params
     * @return
     */
    public String doGetSsl(String url, Map<String, Object> params, Map<String, String> headers) {
        String apiUrl = url;
        StringBuilder param = getParam(params);
        apiUrl += param;
        String result = null;
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        HttpGet httpGet = null;
        try {
            httpGet = new HttpGet(apiUrl);
            httpClient = HttpClients.custom().setSSLSocketFactory(createSSLConnSocketFactory()).setDefaultRequestConfig(requestConfig).build();
            packageHeader(headers, httpGet);
            response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                InputStream instream = entity.getContent();
                result = IOUtils.toString(instream, ENCODING);
            }
        } catch (IOException e) {
            log.error("调用https-get接口异常,url={},params={},errorMsg={}", url, params, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (null != response) {
                    // 此处就只关闭Response等底层流，而httpclient不用关闭，保持socket放入缓存池中
                    response.close();
                }
                if (null != httpGet) {
                    httpGet.releaseConnection();
                }
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e.getMessage());
            }
        }
        return result;
    }

    /**
     * 发送 DELETE 请求（HTTP），K-V形式
     *
     * @param url
     * @param params
     * @param headers
     * @return
     */
    public String doDelete(String url, Map<String, Object> params, Map<String, String> headers) {
        String apiUrl = url;
        StringBuilder param = getParam(params);
        apiUrl += param;
        String result = null;
        CloseableHttpResponse response = null;
        CloseableHttpClient httpclient = null;
        HttpDelete httpDelete = null;
        try {
            httpclient = HttpClients.createDefault();
            httpDelete = new HttpDelete(apiUrl);
            packageHeader(headers, httpDelete);
            response = httpclient.execute(httpDelete);

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                InputStream instream = entity.getContent();
                result = IOUtils.toString(instream, ENCODING);
            }
        } catch (IOException e) {
            log.error("调用http-delete接口异常,url={},params={},errorMsg={}", url, params, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            try {
                if (httpclient != null) {
                    httpclient.close();
                }
                if (null != response) {
                    // 此处就只关闭Response等底层流，而httpclient不用关闭，保持socket放入缓存池中
                    response.close();
                }
                if (null != httpDelete) {
                    httpDelete.releaseConnection();
                }
            } catch (IOException e) {
                log.error(CLOSE_ERROR, e);
            }
        }
        return result;
    }

    /**
     * 发送 GET 请求（HTTP），K-V形式
     *
     * @param url
     * @param params
     * @return
     */
    public String doDeleteSsl(String url, Map<String, Object> params, Map<String, String> headers) {
        String apiUrl = url;
        StringBuilder param = getParam(params);
        apiUrl += param;
        String result = null;
        CloseableHttpClient httpClient = null;
        HttpDelete httpDelete = null;
        CloseableHttpResponse response = null;
        try {
            httpClient = HttpClients.custom().setSSLSocketFactory(createSSLConnSocketFactory()).setDefaultRequestConfig(requestConfig).build();
            httpDelete = new HttpDelete(apiUrl);
            packageHeader(headers, httpDelete);
            response = httpClient.execute(httpDelete);

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                InputStream instream = entity.getContent();
                result = IOUtils.toString(instream, ENCODING);
            }
        } catch (IOException e) {
            log.error("调用https-delete接口异常,url={},params={},errorMsg={}", url, params, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (null != response) {
                    // 此处就只关闭Response等底层流，而httpclient不用关闭，保持socket放入缓存池中
                    response.close();
                }
                if (null != httpDelete) {
                    httpDelete.releaseConnection();
                }
            } catch (IOException e) {
                log.error(CLOSE_ERROR, e);
            }
        }
        return result;
    }


    /**
     * 发送 POST 请求（HTTP），不带输入数据
     *
     * @param apiUrl
     * @return
     */
    public String doPost(String apiUrl) {
        return doPost(apiUrl, new HashMap<String, Object>(), new HashMap<String, String>());
    }

    /**
     * 发送 POST 请求（HTTP），K-V形式
     *
     * @param apiUrl API接口URL
     * @param params 参数map
     * @return
     */
    public String doPost(String apiUrl, Map<String, Object> params, Map<String, String> headers) {
        CloseableHttpClient httpClient = null;
        String httpStr = null;
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;

        try {
            httpClient = HttpClients.createDefault();
            httpPost = new HttpPost(apiUrl);

            packageHeader(headers, httpPost);
            httpPost.setConfig(requestConfig);
            List<NameValuePair> pairList = new ArrayList<>(params.size());
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                NameValuePair pair = new BasicNameValuePair(entry.getKey(), entry
                        .getValue().toString());
                pairList.add(pair);
            }
            httpPost.setEntity(new UrlEncodedFormEntity(pairList, Charset.forName(ENCODING)));
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            httpStr = EntityUtils.toString(entity, ENCODING);
        } catch (IOException e) {
            log.error("调用http-post接口异常,url={},params={},errorMsg={}", apiUrl, params, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
                if (httpPost != null) {
                    httpPost.releaseConnection();
                }
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e.getMessage());
            }
        }
        return httpStr;
    }

    /**
     * 发送 POST 请求（HTTP），JSON形式
     *
     * @param apiUrl
     * @param json   json对象
     * @param ,      Map<String, String> headers
     * @return
     */
    public String doPost(String apiUrl, String json, Map<String, String> headers) {
        CloseableHttpClient httpClient = null;
        String httpStr = null;
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;

        try {
            httpClient = HttpClients.createDefault();
            httpPost = new HttpPost(apiUrl);
            httpPost.setConfig(requestConfig);
            packageHeader(headers, httpPost);

            StringEntity stringEntity = new StringEntity(json, ENCODING);//解决中文乱码问题
            stringEntity.setContentEncoding(ENCODING);
            stringEntity.setContentType(CONTEXT_TYPE);
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            httpStr = EntityUtils.toString(entity, ENCODING);
        } catch (IOException e) {
            log.error("调用http-post接口异常,url={},json={},errorMsg={}", apiUrl, json, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
                if (httpPost != null) {
                    httpPost.releaseConnection();
                }
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e.getMessage());
            }
        }
        return httpStr;
    }

    /**
     * 发送 SSL POST 请求（HTTPS），K-V形式
     *
     * @param apiUrl API接口URL
     * @param params 参数map
     * @return
     */
    public String doPostSSL(String apiUrl, Map<String, Object> params, Map<String, String> headers) {
        CloseableHttpClient httpClient = null;
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;
        String httpStr = null;
        try {
            httpClient = HttpClients.custom().setSSLSocketFactory(createSSLConnSocketFactory()).setDefaultRequestConfig(requestConfig).build();
            httpPost = new HttpPost(apiUrl);
            httpPost.setConfig(requestConfig);
            packageHeader(headers, httpPost);

            List<NameValuePair> pairList = new ArrayList<NameValuePair>(params.size());
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                NameValuePair pair = new BasicNameValuePair(entry.getKey(), entry
                        .getValue().toString());
                pairList.add(pair);
            }
            httpPost.setEntity(new UrlEncodedFormEntity(pairList, ENCODING));
            response = httpClient.execute(httpPost);
            /*int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                return null;
            }*/
            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }
            httpStr = EntityUtils.toString(entity, ENCODING);
        } catch (Exception e) {
            log.error("调用https-post接口异常,url={},params={},headers={},errorMsg={}", apiUrl, params, headers, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
                if (httpPost != null) {
                    httpPost.releaseConnection();
                }
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e.getMessage());
            }
        }
        return httpStr;
    }

    /**
     * 发送 SSL POST 请求（HTTPS），JSON形式
     *
     * @param apiUrl API接口URL
     * @param json   JSON对象
     * @return
     */
    public String doPostSSL(String apiUrl, Object json, Map<String, String> headers) {
        CloseableHttpClient httpClient = null;
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;
        String httpStr = null;

        try {
            httpClient = HttpClients.custom().setSSLSocketFactory(createSSLConnSocketFactory()).setDefaultRequestConfig(requestConfig).build();
            httpPost = new HttpPost(apiUrl);
            httpPost.setConfig(requestConfig);
            packageHeader(headers, httpPost);

            StringEntity stringEntity = new StringEntity(json.toString(), ENCODING);
            stringEntity.setContentEncoding(ENCODING);
            stringEntity.setContentType(CONTEXT_TYPE);
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            /*int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                if(response.getEntity() != null) {
                    log.warn("调用https-post请求结果{}", EntityUtils.toString(response.getEntity(), ENCODING));
                }
                return null;
            }*/
            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }
            httpStr = EntityUtils.toString(entity, ENCODING);
        } catch (Exception e) {
            log.error("调用https-post接口异常,url={},json={},headers={},errorMsg={}", apiUrl, json, headers, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
                if (httpPost != null) {
                    httpPost.releaseConnection();
                }
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e.getMessage());
            }
        }
        return httpStr;
    }

    /**
     * 发送 SSL POST 请求（HTTPS），multipart/form-data;形式
     */
    public String doPostSSL(String apiUrl, Map<String, Object> formMap, Map<String, String> headers, File file) {
        CloseableHttpClient httpClient = null;
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;
        String httpStr = null;

        try {
            httpClient = HttpClients.custom().setSSLSocketFactory(createSSLConnSocketFactory()).setDefaultRequestConfig(requestConfig).build();
            httpPost = new HttpPost(apiUrl);
            httpPost.setConfig(requestConfig);
            packageHeader(headers, httpPost);

            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
            multipartEntityBuilder.addBinaryBody("file",file);

            formMap.keySet().forEach(item->{
                multipartEntityBuilder.addTextBody(item,formMap.get(item).toString());
            });



            httpPost.setEntity(multipartEntityBuilder.build());
            response = httpClient.execute(httpPost);

            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }
            httpStr = EntityUtils.toString(entity, ENCODING);
        } catch (Exception e) {
            log.error("调用https-post接口异常,url={},formMap={},headers={},errorMsg={}", apiUrl, formMap, headers, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
                if (httpPost != null) {
                    httpPost.releaseConnection();
                }
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e.getMessage());
            }
        }
        return httpStr;
    }

    /**
     * @param @param  apiUrl   API接口URL
     * @param @param  json     传
     * @param @param  headers  请求头附加信息
     * @param @return 参数
     * @return String 返回类型
     * @throws
     * @Title: doPatch
     * @Description: PATCH    SSL请求
     */
    public String doPatch(String apiUrl, String json, Map<String, String> headers) {
        CloseableHttpClient httpClient = null;
        HttpPatch httpPatch = null;
        CloseableHttpResponse response = null;
        //返回数据字符串
        String responseStr = null;
        try {
            httpClient = HttpClients.createDefault();
            httpPatch = new HttpPatch(apiUrl);
            httpPatch.setConfig(requestConfig);
            packageHeader(headers, httpPatch);

            StringEntity stringEntity = new StringEntity(json, ENCODING);
            stringEntity.setContentEncoding(ENCODING);
            stringEntity.setContentType(CONTEXT_TYPE);
            httpPatch.setEntity(stringEntity);
            response = httpClient.execute(httpPatch);

            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }
            responseStr = EntityUtils.toString(entity, ENCODING);
        } catch (Exception e) {
            log.error("调用https-pathc接口异常,apiUrl={},json={},headers={},errorMsg={}", apiUrl, json, headers, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
                if (httpPatch != null) {
                    httpPatch.releaseConnection();
                }
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e.getMessage());
            }
        }
        return responseStr;
    }

    /**
     * @param @param  apiUrl   API接口URL
     * @param @param  json     传
     * @param @param  headers  请求头附加信息
     * @param @return 参数
     * @return String 返回类型
     * @throws
     * @Title: doPatchSSL
     * @Description: PATCH    SSL请求
     */
    public String doPatchSSL(String apiUrl, String json, Map<String, String> headers) {
        CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(createSSLConnSocketFactory()).setDefaultRequestConfig(requestConfig).build();
        HttpPatch httpPatch = new HttpPatch(apiUrl);
        CloseableHttpResponse response = null;
        //返回数据字符串
        String responseStr = null;
        try {
            httpPatch.setConfig(requestConfig);
            packageHeader(headers, httpPatch);

            StringEntity stringEntity = new StringEntity(json, ENCODING);
            stringEntity.setContentEncoding(ENCODING);
            stringEntity.setContentType(CONTEXT_TYPE);
            httpPatch.setEntity(stringEntity);
            response = httpClient.execute(httpPatch);

            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }
            responseStr = EntityUtils.toString(entity, ENCODING);
        } catch (Exception e) {
            log.error("调用https-pathc接口异常,apiUrl={},json={},headers={},errorMsg={}", apiUrl, json, headers, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
                httpPatch.releaseConnection();
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e.getMessage());
            }
        }
        return responseStr;
    }

    /**
     * 发送 PUT 请求（HTTP），JSON形式
     *
     * @param apiUrl
     * @param json   json对象
     * @param ,      Map<String, String> headers
     * @return
     */
    public String doPut(String apiUrl, String json, Map<String, String> headers) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String httpStr = null;
        HttpPut httpPut = new HttpPut(apiUrl);
        CloseableHttpResponse response = null;

        try {
            httpPut.setConfig(requestConfig);
            packageHeader(headers, httpPut);

            StringEntity stringEntity = new StringEntity(json, ENCODING);//解决中文乱码问题
            stringEntity.setContentEncoding(ENCODING);
            stringEntity.setContentType(CONTEXT_TYPE);
            httpPut.setEntity(stringEntity);
            response = httpClient.execute(httpPut);
            HttpEntity entity = response.getEntity();
            httpStr = EntityUtils.toString(entity, ENCODING);
        } catch (IOException e) {
            log.error("调用http-put接口异常,url={},json={},errorMsg={}", apiUrl, json, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
                httpPut.releaseConnection();
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e);
            }
        }
        return httpStr;
    }


    /**
     * 发送 PUT 请求（HTTP），JSON形式
     *
     * @param apiUrl
     * @param json   json对象
     * @param ,      Map<String, String> headers
     * @return
     */
    public String doPutSSL(String apiUrl, String json, Map<String, String> headers) {
        CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(createSSLConnSocketFactory()).setDefaultRequestConfig(requestConfig).build();
        String httpStr = null;
        HttpPut httpPut = new HttpPut(apiUrl);
        CloseableHttpResponse response = null;

        try {
            httpPut.setConfig(requestConfig);
            packageHeader(headers, httpPut);

            StringEntity stringEntity = new StringEntity(json, ENCODING);//解决中文乱码问题
            stringEntity.setContentEncoding(ENCODING);
            stringEntity.setContentType(CONTEXT_TYPE);
            httpPut.setEntity(stringEntity);
            response = httpClient.execute(httpPut);
            HttpEntity entity = response.getEntity();
            httpStr = EntityUtils.toString(entity, ENCODING);
        } catch (IOException e) {
            log.error("调用http-putSsl接口异常,url={},json={},errorMsg={}", apiUrl, json, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
                httpPut.releaseConnection();
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e);
            }
        }
        return httpStr;
    }

/*    *//**
     * 发送 SSL PUT 请求（HTTPS），K-V形式
     *
     * @param apiUrl API接口URL
     * @param params 参数map
     * @return
     *//*
    public String doPutSSL(String apiUrl, Map<String, Object> params, Map<String, String> headers) {
        CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(createSSLConnSocketFactory()).setDefaultRequestConfig(requestConfig).build();
        HttpPut httpPut = new HttpPut(apiUrl);
        CloseableHttpResponse response = null;
        String httpStr = null;
        try {
            httpPut.setConfig(requestConfig);

            packageHeader(headers, httpPut);

            List<NameValuePair> pairList = new ArrayList<NameValuePair>(params.size());
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                NameValuePair pair = new BasicNameValuePair(entry.getKey(), entry
                        .getValue().toString());
                pairList.add(pair);
            }
            httpPut.setEntity(new UrlEncodedFormEntity(pairList, ENCODING));
            response = httpClient.execute(httpPut);
            *//*int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                return null;
            }*//*
            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }
            httpStr = EntityUtils.toString(entity, ENCODING);
        } catch (Exception e) {
            log.error("调用https-put接口异常,url={},params={},headers={},errorMsg={}", apiUrl, params, headers, e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放资源
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
                httpPut.releaseConnection();
            } catch (Exception e) {
                log.error(EXCEPTION_MESSAGE + e.getMessage());
            }
        }
        return httpStr;
    }*/


    /**
     * 创建SSL安全连接
     *
     * @return
     */
    private static SSLConnectionSocketFactory createSSLConnSocketFactory() {
        SSLConnectionSocketFactory sslsf = null;
        try {
            SSLContext ctx = SSLContext.getInstance("TLSv1.2");
            X509TrustManager tm = new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }

                @Override
                public void checkClientTrusted(X509Certificate[] arg0,
                                               String arg1) throws CertificateException {
                    if(arg0.length==0){
                        throw new CertificateException("证书异常");
                    }
                }

                @Override
                public void checkServerTrusted(X509Certificate[] arg0,
                                               String arg1) throws CertificateException {
                    if(arg0.length==0){
                        throw new CertificateException("证书异常");
                    }
                }
            };
            ctx.init(null, new TrustManager[]{tm}, new java.security.SecureRandom());
            sslsf = new SSLConnectionSocketFactory(
                    ctx, NoopHostnameVerifier.INSTANCE);
        } catch (GeneralSecurityException e) {
            log.error(EXCEPTION_MESSAGE + e.getMessage());
        }
        return sslsf;
    }

    /**
     * Description: 封装请求头
     *
     * @param params
     * @param httpMethod
     */
    public static void packageHeader(Map<String, String> params, HttpRequestBase httpMethod) {
        if (!CollectionUtils.isEmpty(params)) {
            Set<Map.Entry<String, String>> entrySet = params.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                // 设置到请求头到HttpRequestBase对象中
                httpMethod.setHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    public static StringBuilder getParam(Map<String, Object> params) {
        StringBuilder param = new StringBuilder();
        int i = 0;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (i == 0) {
                param.append("?");
            } else {
                param.append("&");
            }
            param.append(entry.getKey()).append("=").append(entry.getValue());
            i++;
        }
        return param;
    }
}