package com.sansec.ccsp.device.business.metrics.service;

import com.sansec.ccsp.device.business.metrics.entity.MetricsAggregateConfigPO;
import com.sansec.ccsp.device.metrics.request.MetricsAggregateConfigAddDTO;
import com.sansec.ccsp.device.metrics.request.MetricsAggregateConfigQueryDTO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * @description : 指标聚合配置;(METRICS_AGGREGATE_CONFIG)表服务接口
 * <AUTHOR> xiaojiawei
 * @date : 2024-6-22
 */
public interface MetricsAggregateConfigService{
    /**
     * 新增数据
     *
     * @param metricsAggregateConfigAddDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(MetricsAggregateConfigAddDTO metricsAggregateConfigAddDTO);

    List<MetricsAggregateConfigPO> query(MetricsAggregateConfigQueryDTO queryDTO);

    SecRestResponse<Object> syncAggregateConfig(String targetMetricsSetCode, boolean proxyRouteIs, List<String> gatewayAddresses);

    SecRestResponse<Object> syncAggregateConfigByDeviceTypeId(Long deviceTypeId, boolean proxyRouteIs, List<String> gatewayAddresses);

    SecRestResponse<Object> addAggregateConfigByModel(String targetMetricsSetCode, String sourceMetricsSetCode, String group);

    void deleteByTargetMetricsSetCode(String targetMetricsSetCode);
}