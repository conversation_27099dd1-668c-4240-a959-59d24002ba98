package com.sansec.ccsp.datastatic.business.statistic.realtime.convert;

import com.sansec.ccsp.datastatic.business.statistic.realtime.request.StatisticRealTimeDataAddDTO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.request.StatisticRealTimeDataDTO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.entity.StatisticRealTimeDataPO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticRealTimeDataVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.common.param.response.SecPageVO;
import java.util.List;

 /**
 * @description : 调用次数实时数据（330）;(STATISTIC_REAL_TIME_DATA)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @date : 2024-3-6
 */
@Mapper(componentModel = "spring")
public interface StatisticRealTimeDataConvert{
    /**
     * dtoToPo
     * @param statisticRealTimeDataDTO
     * @return
     */
    @Mappings({})
    StatisticRealTimeDataPO dtoToPo(StatisticRealTimeDataDTO statisticRealTimeDataDTO);


     @Mappings({})
     StatisticRealTimeDataPO dtoToPo(StatisticRealTimeDataAddDTO statisticRealTimeDataDTO);
    
    /**
     * poToDto
     * @param statisticRealTimeDataPO
     * @return
     */
    StatisticRealTimeDataDTO poToDto(StatisticRealTimeDataPO statisticRealTimeDataPO);
    
    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<StatisticRealTimeDataDTO> poToDto(List<StatisticRealTimeDataPO> list);


     //List<StatisticRealTimeDataVO> poToVO(List<StatisticRealTimeDataPO> list);
     
    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<StatisticRealTimeDataVO> pagePOToSecPageVOPage(IPage<StatisticRealTimeDataPO> iPage);
    
    @InheritConfiguration(name = "convertVo")
    List<StatisticRealTimeDataVO> convert(List<StatisticRealTimeDataPO> list);
    
    @Mappings({})
    StatisticRealTimeDataVO convertVo(StatisticRealTimeDataPO request);
}