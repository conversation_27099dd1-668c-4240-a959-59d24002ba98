package com.sansec.ccsp.pt.business.sim.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 申请证书响应
 * @CreateTime: 2023-10-23
 * @Author: wang<PERSON><PERSON>e
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimShieldApplyCertVO {
    /**
     * 账号名
     */
    private String requestId;
    /**
     * 重复申请
     * 1:重复申请 0：未申请
     */
    private Integer repeatApply;
}
