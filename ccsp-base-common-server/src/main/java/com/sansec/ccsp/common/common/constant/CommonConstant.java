package com.sansec.ccsp.common.common.constant;

/**
 * @Description: 通用常量类
 * <AUTHOR>
 * @Date: 2021/9/6 10:43
 */
public class CommonConstant {
    public static final String ZERO = "0";
    public static final String ONE = "1";
    public static final String ETCD_ERROR = "1";
    public static final String ALG_SM4 = "SM4";
    public static final String ALG_SM2 = "SM2";
    public static final String ALG_RSA = "RSA";
    public static final String KMS_PUBLIC_KEY_PREFIX = "pubKey_";
    public static final String KMS_PRIVATE_KEY_PREFIX = "priKey_";
    public static final String TRUE = "TRUE";
    public static final String FALSE = "false";

    public static final String CCSP_INIT_TIME = "ccsp_init_time";
    /**
     * 是否唯一
     */
    public static final String NOT_UNIQUE = "1";
    public static final String UNIQUE = "0";
    /**
     * 是否菜单外链（是）
     */
    public static final String YES_FRAME = "0";

    /**
     * 是否菜单外链（否）
     */
    public static final String NO_FRAME = "1";
    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 作废的
     */
    public static final Integer INVALID = 1;
    /**
     * 未作废的
     */
    public static final Integer NOT_INVALID = 0;


    /**
     * 可编辑的
     */
    public static final Integer EDITABLE = 1;
    /**
     * 不可编辑的
     */
    public static final Integer NOT_EDITABLE = 0;


    /** 菜单类型（目录） */
    public static final String TYPE_DIR = "M";

    /** 菜单类型（菜单） */
    public static final String TYPE_MENU = "C";

    /** 菜单类型（按钮） */
    public static final String TYPE_BUTTON = "F";
    /** Layout组件标识 */
    public final static String LAYOUT = "Layout";

    /** ParentView组件标识 */
    public final static String PARENT_VIEW = "ParentView";

    /** InnerLink组件标识 */
    public final static String INNER_LINK = "InnerLink";

    /** 顶层租户租户标识 */
    public final static String TOP_TENANT_CODE = "ccsp_tenant";

    /** 顶层租户租户ID */
    public final static Long TOP_TENANT_ID = 1L;

    /**
     * 消息未读
     */
    public static final Integer UNREAD_MESSAGE = 1;

    /**
     * 消息已读
     */
    public static final Integer READ_MESSAGE = 2;

    /**
     * 消息接收者类型（平台）
     */
    public static final Integer TOP_TENANT_RECEIVER = 1;

    /**
     * 消息接收者类型（租户）
     */
    public static final Integer TENANT_RECEIVER = 2;

    /**
     * 默认消息条数
     */
    public static final Integer DEFAULT_ROWS_OF_MESSAGE = 100;
    /**
     * 默认未读消息天数
     */
    public static final Integer DEFAULT_DAYS_OF_UNREAD_MESSAGE = 3;
    /**
     * 默认未读消息条数
     */
    public static final Integer DEFAULT_ROWS_OF_UNREAD_MESSAGE = 10;
    /**
     * 是否为区域模式
     */
    public final static String REGION_MODE = "region_mode";
    /**
     * 区域模式开启
     */
    public final static String REGION_MODE_ENABLED = "1";
    /**
     * 是否共享服务
     */
    public final static String IS_SERVICE_SHARE = "is_service_share";
    /**
     * 共享服务
     */
    public final static String SERVICE_SHARE_ENABLED = "1";
    /**
     * 是否政务模式
     */
    public final static String IS_STATE_MODE = "is_state_mode";
    /**
     * 政务模式
     */
    public final static String STATE_MODE_ENABLED = "1";

    public static final String API_TOKEN_EXPIRE_CONFIG = "api_token_expire";

    // 是否支持组织管理 0不支持 1支持
    public static final String HAS_ORGANIZATION = "has_organization";
}
