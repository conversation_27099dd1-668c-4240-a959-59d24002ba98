<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ccsp.common.business.config.mapper.ConfigRegularMapper">
    <sql id="Base_Column_List" >
        ID,CONFIG_CODE,REGULAR,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>

    <select id="getRegularByCode" resultType="java.lang.String">
        SELECT REGULAR FROM CONFIG_REGULAR WHERE CONFIG_CODE = #{code} AND INVALID_FLAG = 0
    </select>


</mapper>