package com.sansec.ccsp.common.business.dic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.common.business.dic.convert.DicBusiTypeConvert;
import com.sansec.ccsp.common.business.dic.entity.DicBusiTypePO;
import com.sansec.ccsp.common.business.dic.mapper.DicBusiTypeMapper;
import com.sansec.ccsp.common.business.dic.service.DicBusiTypeService;
import com.sansec.ccsp.common.dic.request.DicBusiTypeDTO;
import com.sansec.ccsp.common.dic.request.DicBusiTypePageDTO;
import com.sansec.ccsp.common.dic.response.DeviceInnerKeyConfigVO;
import com.sansec.ccsp.common.dic.response.DicBusiTypeVO;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> xiaojiawei
 * @Description: 密钥类型字典表;(DIC_KEYTYPE)表服务实现类
 * @Date: 2023-2-18
 */
@Service
@Slf4j
public class DicBusiTypeServiceImpl extends ServiceImpl<DicBusiTypeMapper, DicBusiTypePO> implements DicBusiTypeService {
    @Resource
    private DicBusiTypeConvert dicBusiTypeConvert;

    @Override
    public SecRestResponse<SecPageVO<DicBusiTypeVO>> find(DicBusiTypePageDTO dicBusiTypePageDTO) {
        QueryWrapper<DicBusiTypePO> queryWrapper = Wrappers.query();
        IPage<DicBusiTypePO> page = new Page<>(dicBusiTypePageDTO.getPageNum(), dicBusiTypePageDTO.getPageSize());
        IPage<DicBusiTypePO> dicBusiTypePOPage = baseMapper.selectPage(page, queryWrapper);
        SecPageVO<DicBusiTypeVO> dicBusiTypePageVO = dicBusiTypeConvert.pagePOToSecPageVOPage(dicBusiTypePOPage);
        return ResultUtil.ok(dicBusiTypePageVO);
    }

    @Override
    public SecRestResponse<List<DicBusiTypeVO>> getBusiTypeList() {
        QueryWrapper<DicBusiTypePO> queryWrapper = Wrappers.query();
        List<DicBusiTypePO> busiTypePOList = baseMapper.selectList(queryWrapper);
        List<DicBusiTypeVO> dicBusiTypeVOList = dicBusiTypeConvert.convert(busiTypePOList);
        return ResultUtil.ok(dicBusiTypeVOList);
    }

    @Override
    public SecRestResponse<Object> add(DicBusiTypeDTO dicBusiTypeDTO) {
        DicBusiTypePO dicBusiTypePO = dicBusiTypeConvert.dtoToPo(dicBusiTypeDTO);
        dicBusiTypePO.setId(IdGenerator.ins().generator());
        dicBusiTypePO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.insert(dicBusiTypePO);
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Object> edit(DicBusiTypeDTO dicBusiTypeDTO) {
        DicBusiTypePO dicBusiTypePO = dicBusiTypeConvert.dtoToPo(dicBusiTypeDTO);
        dicBusiTypePO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.updateById(dicBusiTypePO);
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Object> deleteById(DicBusiTypeDTO dicBusiTypeDTO) {
        baseMapper.deleteById(dicBusiTypeDTO.getId());
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<List<DeviceInnerKeyConfigVO>> getDeviceInnerKeyConfigByIdList(List<Long> idList) {

        List<DicBusiTypePO> dicBusiTypePOList = baseMapper.selectBatchIds(idList);
        List<DeviceInnerKeyConfigVO> deviceInnerKeyConfigVOList = dicBusiTypeConvert.convertVoList(dicBusiTypePOList);

        return ResultUtil.ok(deviceInnerKeyConfigVOList);
    }
}
