package com.sansec.ccsp.datastatic.business.statistic.realtime.controller;

import com.sansec.ccsp.datastatic.business.statistic.realtime.request.StatisticRealTimeDataAddDTO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.request.StatisticRealTimeDataDTO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.request.StatisticRealTimeDataPageDTO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticRealTimeDataVO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.service.StatisticRealTimeDataService;
import com.sansec.ccsp.staticapi.realtime.request.StatisticRealTimeCollectDTO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> xia<PERSON><PERSON><PERSON><PERSON>
 * @description : 调用次数实时数据（330）;(STATISTIC_REAL_TIME_DATA)表控制层
 * @date : 2024-3-6
 */
@RestController
@RequestMapping("/real/time/")
@Validated
public class StatisticRealTimeDataController {
    @Resource
    private StatisticRealTimeDataService statisticRealTimeDataService;

    /**
     * 分页查询
     *
     * @param statisticRealTimeDataPageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    public SecRestResponse<SecPageVO<StatisticRealTimeDataVO>> find(@RequestBody StatisticRealTimeDataPageDTO statisticRealTimeDataPageDTO) {
        return statisticRealTimeDataService.find(statisticRealTimeDataPageDTO);
    }

    /**
     * 新增数据
     *
     * @param statisticRealTimeDataDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    public SecRestResponse<Object> add(@RequestBody StatisticRealTimeDataAddDTO statisticRealTimeDataAddDTO) {
        return statisticRealTimeDataService.add(statisticRealTimeDataAddDTO);
    }

    /**
     * 更新数据
     *
     * @param statisticRealTimeDataDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/edit")
    public SecRestResponse<Object> edit(@RequestBody StatisticRealTimeDataDTO statisticRealTimeDataDTO) {
        return statisticRealTimeDataService.edit(statisticRealTimeDataDTO);
    }

    /**
     * 通过主键删除数据
     *
     * @param statisticRealTimeDataDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/deleteById")
    public SecRestResponse<Object> deleteById(@RequestBody StatisticRealTimeDataDTO statisticRealTimeDataDTO) {
        return statisticRealTimeDataService.deleteById(statisticRealTimeDataDTO);
    }
}