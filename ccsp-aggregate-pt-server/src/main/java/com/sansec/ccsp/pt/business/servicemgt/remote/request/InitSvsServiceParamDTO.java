package com.sansec.ccsp.pt.business.servicemgt.remote.request;

import lombok.Data;
import lombok.ToString;

@Data
@ToString(exclude="dbAuthCode")
public class InitSvsServiceParamDTO {
    /**
     * 数据库类型
     */
    private String dbType;
    /**
     * 数据库实例库
     */
    private String dbCase;
    /**
     * 数据库模式 高斯必填
     */
    private String dbSchema;
    /**
     * 数据库IP
     */
    private String dbIp;
    /**
     * 数据库端口
     */
    private Integer dbPort;
    /**
     * 数据库用户名
     */
    private String dbUser;
    /**
     * 数据库密码
     */
    private String dbAuthCode;
    /**
     * 业务网关IP
     */
    private String busiGatewayIp;
    /**
     * 业务网关端口
     */
    private String busiGatewayPort;
    /**
     * 密码机类型
     */
    private Integer deviceType;
    /**
     * 脚本路径
     */
    private String runScript;
    /**
     * 数据库IP端口
     */
    private String dbIpPort;
}
