package com.sansec.ccsp.common.dic.api;

import com.sansec.ccsp.common.dic.api.fallback.DicServiceApiFallBack;
import com.sansec.ccsp.common.dic.response.DeviceInnerKeyConfigVO;
import com.sansec.ccsp.common.dic.response.DicBusiTypeVO;
import com.sansec.ccsp.common.dic.response.DicSysDataVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Describe
 * @Author:  d.s
 * @create 2023/2/20 9:09
 */
@FeignClient(name = "${spring.protocol.prefix}${spring.application.common.name}" , path="/ccsp/common", fallbackFactory = DicServiceApiFallBack.class)
public interface DicServiceApi {
    @PostMapping(value = "/api/dicBusiType/v1/getBusiTypeList")
    SecRestResponse<List<DicBusiTypeVO>> getBusiTypeList();

    /**
     * 获取系统字典
     * @param dicType
     * @return
     */
    @PostMapping(value = "/api/dic/v1/getSysDictByType/{dicType}")
    SecRestResponse<List<DicSysDataVO>> getSysDictByType(@PathVariable(value = "dicType") @NotEmpty String dicType);

    @PostMapping(value = "/api/dic/v1/getDeviceInnerKeyConfigByIdList")
    SecRestResponse<List<DeviceInnerKeyConfigVO>> getDeviceInnerKeyConfigByIdList(@RequestBody @Validated List<Long> idList);
}
