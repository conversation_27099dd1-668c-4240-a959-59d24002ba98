package com.sansec.ccsp.common.organization.request;

import lombok.Data;

 /**
 * @Description: 组织字典表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>aw<PERSON>
 * @Date: 2023-2-18
 */
@Data
public class OrganizationInfoDTO{
    /**
     * 组织机构ID
     */

    private Long organizationId;
    /**
     * 组织机构代码
     */
    private String organizationCode;
    /**
     * 组织机构名称
     */
    private String organizationName;
    /**
     * 父ID
     */
    private Long parentId;
    /**
     * 排序序号
     */
    private Integer sordNum;
    /**
     * 是否作废;默认为0
     */
    private Integer invalidFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
}
