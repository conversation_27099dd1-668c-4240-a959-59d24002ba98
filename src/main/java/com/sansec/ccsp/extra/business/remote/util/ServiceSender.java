package com.sansec.ccsp.extra.business.remote.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.sansec.ccsp.extra.business.remote.RemoteConst;
import com.sansec.ccsp.extra.business.utils.HttpsServiceUtil;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import static com.sansec.ccsp.extra.business.constant.SecErrorCodeConstant.HTTP_EMPTY_RESULT_ERROR;
import static com.sansec.ccsp.extra.business.remote.RemoteConst.RES_CODE_SUCCESS;

/**
 * 服务请求相关封装类
 * <AUTHOR>
 * @Date 2024/3/11 15:17
 */
@Slf4j
@Component
public class ServiceSender {
    @Resource
    private HttpsServiceUtil httpsServiceUtil;


    public <T> T postResult(String url, Object params, TypeReference<T> typeReference) {
        return postResult(url, params, null, typeReference);
    }

    public void postResult(String url, Object params) {
        postResult(url, params, null, null);
    }

    public <T> T postResult(String url, Object params, Map<String, String> headers, TypeReference<T> typeReference) {
        String resultMsg = httpsServiceUtil.sendHttpsToService(url, JSON.toJSONString(params), headers);
        if (StringUtils.isBlank(resultMsg)) {
            log.error("httpRequest fail, result is blank, url: {}", url);
            throw new BusinessException(HTTP_EMPTY_RESULT_ERROR);
        }
        JSONObject jsonResult = JSONObject.parseObject(resultMsg);
        if(!isSuccess(jsonResult)) {
            log.error("http request invoke fail, url: {}, param: {}, result: {}", url, JSON.toJSONString(params), JSON.toJSONString(jsonResult));
            SecRestResponse response = jsonResult.toJavaObject(SecRestResponse.class);
            throw new BusinessException(response.getCode(),response.getMessage());
        }
        if(typeReference == null) {
            return null;
        }
        return jsonResult.getObject(RemoteConst.RES_RESULT, typeReference);
    }

    private boolean isSuccess(JSONObject jsonResult) {
        return jsonResult.get(RemoteConst.RES_CODE).equals(RES_CODE_SUCCESS);
    }


}
