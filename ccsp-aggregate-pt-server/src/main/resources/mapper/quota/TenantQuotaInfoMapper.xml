<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ccsp.pt.business.quota.mapper.TenantQuotaInfoMapper">
    <sql id="Base_Column_List">
        ID
        ,TENANT_ID,SERVICE_GROUP_ID,SERVICE_CODE,QUOTA_NAME,QUOTA_VALUE,START_TIME,END_TIME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>

    <select id="findPage" resultType="com.sansec.ccsp.pt.business.quota.entity.TenantQuotaInfoPagePO">
        select
            info.*,
            dic.SHOW_NAME,dic.VALUE_UNIT,dic.SERVICE_TYPE_ID,
            useinfo.USED_QUOTA,useinfo.RESIDUE_QUOTA

        from tenant_quota_info info
                 inner join dic_service_quota dic on info.SERVICE_CODE=dic.SERVICE_CODE and info.QUOTA_NAME=dic.QUOTA_NAME
                 left join tenant_quota_use_info useinfo on info.TENANT_ID=useinfo.TENANT_ID and info.SERVICE_CODE=useinfo.SERVICE_CODE and info.QUOTA_NAME=useinfo.QUOTA_NAME

        where info.TENANT_ID=#{dto.tenantId}

        order by info.CREATE_TIME desc,info.SERVICE_CODE desc,info.QUOTA_NAME desc
    </select>
</mapper>