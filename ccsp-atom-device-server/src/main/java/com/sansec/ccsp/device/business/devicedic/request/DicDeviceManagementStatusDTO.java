package com.sansec.ccsp.device.business.devicedic.request;

import lombok.Data;

 /**
 * @Description: 密码机使用状态字典表;
 * <AUTHOR> x<PERSON><PERSON><PERSON>aw<PERSON>
 * @Date: 2023-2-18
 */
@Data
public class DicDeviceManagementStatusDTO{
    /**
     * 操作状态ID
     */
    private Long id;
    /**
     * 状态名
     */
    private String statusName;
    /**
     * 状态值
     */
    private String statusCode;
    /**
     * 是否作废;默认为0
     */
    private Integer invalidFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
}