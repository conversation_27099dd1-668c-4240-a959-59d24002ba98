package com.sansec.ai.user.dto;

import com.sansec.common.param.request.SecPageDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WanJun
 * @Date: 2025/5/12 08:46
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PortalUserPageDTO extends SecPageDTO {

    /**
     * 用户名
     */
    private String userName;


    /**
     * 门户id
     */
//    private Long portalId;

    /**
     * 绑定状态
     */
    private Boolean bindStatus;
}