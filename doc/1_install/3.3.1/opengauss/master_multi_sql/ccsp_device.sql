/*
 Navicat Premium Data Transfer

 Source Server         : ************<PERSON><PERSON>uss
 Source Server Type    : PostgreSQL
 Source Server Version : 90204
 Source Host           : ************:5432
 Source Catalog        : ykytest
 Source Schema         : ccsp_device

 Target Server Type    : PostgreSQL
 Target Server Version : 90204
 File Encoding         : 65001

 Date: 26/04/2024 11:03:01
*/


-- ----------------------------
-- Table structure for device_api
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_api";
CREATE TABLE "ccsp_device"."device_api" (
  "api_id" int8 NOT NULL,
  "api_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "device_type_id" int8 NOT NULL,
  "interaction_serial_number" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "api_template_id" int8 NOT NULL,
  "api_serial_number" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "context_path" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "api_path" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "default_flag" int4 NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_api"."api_id" IS '设备接口ID';
COMMENT ON COLUMN "ccsp_device"."device_api"."api_name" IS '接口名称';
COMMENT ON COLUMN "ccsp_device"."device_api"."device_type_id" IS '所属设备类型ID';
COMMENT ON COLUMN "ccsp_device"."device_api"."interaction_serial_number" IS '设备交互类型编号';
COMMENT ON COLUMN "ccsp_device"."device_api"."api_template_id" IS '所属接口模板ID';
COMMENT ON COLUMN "ccsp_device"."device_api"."api_serial_number" IS '所属接口编号';
COMMENT ON COLUMN "ccsp_device"."device_api"."context_path" IS '环境路径';
COMMENT ON COLUMN "ccsp_device"."device_api"."api_path" IS '接口路径';
COMMENT ON COLUMN "ccsp_device"."device_api"."default_flag" IS '是否默认（0：否；1：是）';
COMMENT ON COLUMN "ccsp_device"."device_api"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."device_api"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_api"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_api"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_api"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_api"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_api" IS '设备接口信息';

-- ----------------------------
-- Records of device_api
-- ----------------------------
INSERT INTO "ccsp_device"."device_api" VALUES (6075363783263125507, 'getVsmInfo', 1, 'chsm_sansec_old_0088', 201, '1', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075363783464452104, 'getVsmStatus', 1, 'chsm_sansec_old_0088', 202, '2', '/', '/api/1.0/vsm/status', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075363783665778694, 'configVsmNetwork', 1, 'chsm_sansec_old_0088', 203, '3', '/', '/api/1.0/vsm/network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075363783867105280, 'createVsm', 1, 'chsm_sansec_old_0088', 204, '4', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075363783967768585, 'vsmOperate', 1, 'chsm_sansec_old_0088', 205, '5', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075363784101986313, 'getChsmInfo', 1, 'chsm_sansec_old_0088', 206, '6', '/', '/api/1.0/chsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075363784202649609, 'getChsmStatus', 1, 'chsm_sansec_old_0088', 207, '7', '/', '/api/1.0/chsm/status', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075363784303312897, 'configChsmPublicKey', 1, 'chsm_sansec_old_0088', 208, '8', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075363784437530627, 'clearChsmPublicKey', 1, 'chsm_sansec_old_0088', 209, '9', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075363784571748355, 'getChsmPublicKeyFinger', 1, 'chsm_sansec_old_0088', 210, '10', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075363784705966086, 'configVsm', 1, 'chsm_sansec_old_0088', 211, '30', '/', '/api/1.0/vsm/config', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:25:50', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183023638280, 'getHsmInfo', 6, 'vsm_sansec_unified_1001', 2001, '11', '/pkiweb/sansecplat', '/api/1.0/chsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183090747140, 'getHsmStatus', 6, 'vsm_sansec_unified_1001', 2002, '12', '/pkiweb/sansecplat', '/api/1.0/chsm/status', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183124301577, 'configHsmNetwork', 6, 'vsm_sansec_unified_1001', 2003, '13', '/pkiweb/sansecplat', '/api/1.0/chsm/network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183157856009, 'restartHsm', 6, 'vsm_sansec_unified_1001', 2004, '14', '/pkiweb/sansecplat', '/api/1.0/chsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183191410441, 'configHsmPublicKey', 6, 'vsm_sansec_unified_1001', 2005, '15', '/pkiweb/sansecplat', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183224964870, 'clearHsmPublicKey', 6, 'vsm_sansec_unified_1001', 2006, '16', '/pkiweb/sansecplat', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183258519297, 'getHsmPublicKeyFinger', 6, 'vsm_sansec_unified_1001', 2007, '17', '/pkiweb/sansecplat', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183292073728, 'createHsmLmk', 6, 'vsm_sansec_unified_1001', 2008, '18', '/pkiweb/sansecplat/hsmm', '/key/masterKey/generateLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183325628169, 'generateHsmLMKWithComponent', 6, 'vsm_sansec_unified_1001', 2009, '19', '/pkiweb/sansecplat/hsmm', '/key/masterKey/importLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183359182600, 'getHsmDeviceKeyPair', 6, 'vsm_sansec_unified_1001', 2010, '20', '/pkiweb/sansecplat/hsmm', '/platform/key/gennerAsymmTempKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183359182601, 'exportHsmMasterKey', 6, 'vsm_sansec_unified_1001', 2011, '21', '/pkiweb/sansecplat/hsmm', '/platform/key/exportLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183392737031, 'importHsmMasterKey', 6, 'vsm_sansec_unified_1001', 2012, '22', '/pkiweb/sansecplat/hsmm', '/platform/key/impoerLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183426291459, 'getDeviceInitState', 6, 'vsm_sansec_unified_1001', 2013, '23', '/pkiweb/sansecplat', '/api/1.0/chsm/init/status', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183459845895, 'deviceInit', 6, 'vsm_sansec_unified_1001', 2014, '24', '/pkiweb/sansecplat', '/api/1.0/chsm/init', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183493400325, 'importAsyncKeyPair', 6, 'vsm_sansec_unified_1001', 2015, '25', '/pkiweb/sansecplat/hsmm', '/platform/key/importAsyncKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183526954761, 'importSyncKeyPair', 6, 'vsm_sansec_unified_1001', 2016, '26', '/pkiweb/sansecplat/hsmm', '/platform/key/importSyncKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183560509189, 'deleteSymmetricKey', 6, 'vsm_sansec_unified_1001', 2017, '27', '/pkiweb/sansecplat/hsmm', '/key/symmetricKey/deleteSymmetricKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183594063623, 'deleteSM2Key', 6, 'vsm_sansec_unified_1001', 2018, '28', '/pkiweb/sansecplat/hsmm', '/key/sm2Key/deleteSM2Key', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075391183627618055, 'getHsmServiceStatus', 6, 'vsm_sansec_unified_1001', 2019, '29', '/PlatformServlet', '?method=getServiceState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399467713562372, 'getHsmInfo', 11, 'hsm_sansec_1002', 3101, '101', '/PlatformServlet', '?method=getHsmInfo', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399467747116809, 'getHsmStatus', 11, 'hsm_sansec_1002', 3102, '102', '/PlatformServlet', '?method=getState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399467780671241, 'configHsmNetwork', 11, 'hsm_sansec_1002', 3103, '103', '/PlatformServlet', '?method=network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399467814225672, 'restartHsm', 11, 'hsm_sansec_1002', 3104, '104', '/PlatformServlet', '?method=restartHsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399467847780104, 'configHsmPublicKey', 11, 'hsm_sansec_1002', 3105, '105', '/AuthServlet', '?method=authPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399467881334535, 'clearHsmPublicKey', 11, 'hsm_sansec_1002', 3106, '106', '/AuthServlet', '?method=cleanPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399467914888968, 'getHsmPublicKeyFinger', 11, 'hsm_sansec_1002', 3107, '107', '/AuthServlet', '?method=getAuthPKFingerprints', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399467948443392, 'createHsmLmk', 11, 'hsm_sansec_1002', 3108, '108', '/PlatformServlet', '?method=generateLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399467981997832, 'generateHsmLMKWithComponent', 11, 'hsm_sansec_1002', 3109, '109', '/PlatformServlet', '?method=importLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399468015552261, 'getHsmDeviceKeyPair', 11, 'hsm_sansec_1002', 3110, '110', '/PlatformServlet', '?method=gennerAsymmTempKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399468015552262, 'exportHsmMasterKey', 11, 'hsm_sansec_1002', 3111, '111', '/PlatformServlet', '?method=exportLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399468049106689, 'importHsmMasterKey', 11, 'hsm_sansec_1002', 3112, '112', '/PlatformServlet', '?method=importLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399468082661126, 'getDeviceInitState', 11, 'hsm_sansec_1002', 3113, '113', '/PlatformServlet', '?method=initStatus', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399468116215557, 'deviceInit', 11, 'hsm_sansec_1002', 3114, '114', '/PlatformServlet', '?method=doHsmInit', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399468149769985, 'importAsyncKeyPair', 11, 'hsm_sansec_1002', 3115, '115', '/PlatformServlet', '?method=importAsyncKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399468183324417, 'importSyncKeyPair', 11, 'hsm_sansec_1002', 3116, '116', '/PlatformServlet', '?method=importSymmKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399468216878857, 'deleteSymmetricKey', 11, 'hsm_sansec_1002', 3117, '117', '/PlatformServlet', '?method=delHsmSymmetricKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399468216878858, 'deleteSM2Key', 11, 'hsm_sansec_1002', 3118, '118', '/PlatformServlet', '?method=delHsmKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075399468250433286, 'getHsmServiceStatus', 11, 'hsm_sansec_1002', 3119, '119', '/PlatformServlet', '?method=getServiceState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:43:33', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568489441028, 'getHsmInfo', 12, 'hsm_sansec_1002', 3101, '101', '/PlatformServlet', '?method=getHsmInfo', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568522995460, 'getHsmStatus', 12, 'hsm_sansec_1002', 3102, '102', '/PlatformServlet', '?method=getState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568556549894, 'configHsmNetwork', 12, 'hsm_sansec_1002', 3103, '103', '/PlatformServlet', '?method=network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568590104321, 'restartHsm', 12, 'hsm_sansec_1002', 3104, '104', '/PlatformServlet', '?method=restartHsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568590104322, 'configHsmPublicKey', 12, 'hsm_sansec_1002', 3105, '105', '/AuthServlet', '?method=authPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568623658756, 'clearHsmPublicKey', 12, 'hsm_sansec_1002', 3106, '106', '/AuthServlet', '?method=cleanPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568657213191, 'getHsmPublicKeyFinger', 12, 'hsm_sansec_1002', 3107, '107', '/AuthServlet', '?method=getAuthPKFingerprints', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568690767622, 'createHsmLmk', 12, 'hsm_sansec_1002', 3108, '108', '/PlatformServlet', '?method=generateLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568690767623, 'generateHsmLMKWithComponent', 12, 'hsm_sansec_1002', 3109, '109', '/PlatformServlet', '?method=importLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568724322055, 'getHsmDeviceKeyPair', 12, 'hsm_sansec_1002', 3110, '110', '/PlatformServlet', '?method=gennerAsymmTempKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568757876483, 'exportHsmMasterKey', 12, 'hsm_sansec_1002', 3111, '111', '/PlatformServlet', '?method=exportLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568757876484, 'importHsmMasterKey', 12, 'hsm_sansec_1002', 3112, '112', '/PlatformServlet', '?method=importLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568791430921, 'getDeviceInitState', 12, 'hsm_sansec_1002', 3113, '113', '/PlatformServlet', '?method=initStatus', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568824985345, 'deviceInit', 12, 'hsm_sansec_1002', 3114, '114', '/PlatformServlet', '?method=doHsmInit', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568858539782, 'importAsyncKeyPair', 12, 'hsm_sansec_1002', 3115, '115', '/PlatformServlet', '?method=importAsyncKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568858539783, 'importSyncKeyPair', 12, 'hsm_sansec_1002', 3116, '116', '/PlatformServlet', '?method=importSymmKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568892094214, 'deleteSymmetricKey', 12, 'hsm_sansec_1002', 3117, '117', '/PlatformServlet', '?method=delHsmSymmetricKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568925648642, 'deleteSM2Key', 12, 'hsm_sansec_1002', 3118, '118', '/PlatformServlet', '?method=delHsmKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075401568959203081, 'getHsmServiceStatus', 12, 'hsm_sansec_1002', 3119, '119', '/PlatformServlet', '?method=getServiceState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:44:36', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333318346500, 'getHsmInfo', 21, 'hsm_sansec_1002', 3101, '101', '/PlatformServlet', '?method=getHsmInfo', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333385455369, 'getHsmStatus', 21, 'hsm_sansec_1002', 3102, '102', '/PlatformServlet', '?method=getState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333419009797, 'configHsmNetwork', 21, 'hsm_sansec_1002', 3103, '103', '/PlatformServlet', '?method=network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333452564230, 'restartHsm', 21, 'hsm_sansec_1002', 3104, '104', '/PlatformServlet', '?method=restartHsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333486118659, 'configHsmPublicKey', 21, 'hsm_sansec_1002', 3105, '105', '/AuthServlet', '?method=authPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333519673090, 'clearHsmPublicKey', 21, 'hsm_sansec_1002', 3106, '106', '/AuthServlet', '?method=cleanPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333553227524, 'getHsmPublicKeyFinger', 21, 'hsm_sansec_1002', 3107, '107', '/AuthServlet', '?method=getAuthPKFingerprints', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333586781960, 'createHsmLmk', 21, 'hsm_sansec_1002', 3108, '108', '/PlatformServlet', '?method=generateLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333620336389, 'generateHsmLMKWithComponent', 21, 'hsm_sansec_1002', 3109, '109', '/PlatformServlet', '?method=importLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333653890820, 'getHsmDeviceKeyPair', 21, 'hsm_sansec_1002', 3110, '110', '/PlatformServlet', '?method=gennerAsymmTempKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333687445251, 'exportHsmMasterKey', 21, 'hsm_sansec_1002', 3111, '111', '/PlatformServlet', '?method=exportLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333687445252, 'importHsmMasterKey', 21, 'hsm_sansec_1002', 3112, '112', '/PlatformServlet', '?method=importLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333720999686, 'getDeviceInitState', 21, 'hsm_sansec_1002', 3113, '113', '/PlatformServlet', '?method=initStatus', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403333754554112, 'deviceInit', 21, 'hsm_sansec_1002', 3114, '114', '/PlatformServlet', '?method=doHsmInit', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403334022989570, 'importAsyncKeyPair', 21, 'hsm_sansec_1002', 3115, '115', '/PlatformServlet', '?method=importAsyncKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403334056544000, 'importSyncKeyPair', 21, 'hsm_sansec_1002', 3116, '116', '/PlatformServlet', '?method=importSymmKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403334090098433, 'deleteSymmetricKey', 21, 'hsm_sansec_1002', 3117, '117', '/PlatformServlet', '?method=delHsmSymmetricKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403334090098434, 'deleteSM2Key', 21, 'hsm_sansec_1002', 3118, '118', '/PlatformServlet', '?method=delHsmKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075403334123652868, 'getHsmServiceStatus', 21, 'hsm_sansec_1002', 3119, '119', '/PlatformServlet', '?method=getServiceState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:45:28', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405747073318918, 'getVsmInfo', 31, 'chsm_sansec_standard_0088', 101, '1', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405747241091077, 'getVsmStatus', 31, 'chsm_sansec_standard_0088', 102, '2', '/', '/api/1.0/vsm/status', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405747375308803, 'configVsmNetwork', 31, 'chsm_sansec_standard_0088', 103, '3', '/', '/api/1.0/vsm/network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405747543080962, 'createVsm', 31, 'chsm_sansec_standard_0088', 104, '4', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405747677298689, 'vsmOperate', 31, 'chsm_sansec_standard_0088', 105, '5', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405747811516420, 'getChsmInfo', 31, 'chsm_sansec_standard_0088', 106, '6', '/', '/api/1.0/chsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405747945734153, 'getChsmStatus', 31, 'chsm_sansec_standard_0088', 107, '7', '/', '/api/1.0/chsm/status', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405748113506310, 'configChsmPublicKey', 31, 'chsm_sansec_standard_0088', 108, '8', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405748247724036, 'clearChsmPublicKey', 31, 'chsm_sansec_standard_0088', 109, '9', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405748381941763, 'getChsmPublicKeyFinger', 31, 'chsm_sansec_standard_0088', 110, '10', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075405748482605062, 'configVsm', 31, 'chsm_sansec_standard_0088', 111, '30', '/', '/api/1.0/vsm/config', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214002091781, 'getHsmInfo', 32, 'vsm_sansec_1002', 2101, '101', '/PlatformServlet', '?method=getHsmInfo', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214035646209, 'getHsmStatus', 32, 'vsm_sansec_1002', 2102, '102', '/PlatformServlet', '?method=getState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214069200643, 'configHsmNetwork', 32, 'vsm_sansec_1002', 2103, '103', '/PlatformServlet', '?method=network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214102755081, 'restartHsm', 32, 'vsm_sansec_1002', 2104, '104', '/PlatformServlet', '?method=restartHsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214136309512, 'configHsmPublicKey', 32, 'vsm_sansec_1002', 2105, '105', '/AuthServlet', '?method=authPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214169863940, 'clearHsmPublicKey', 32, 'vsm_sansec_1002', 2106, '106', '/AuthServlet', '?method=cleanPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214203418368, 'getHsmPublicKeyFinger', 32, 'vsm_sansec_1002', 2107, '107', '/AuthServlet', '?method=getAuthPKFingerprints', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214236972808, 'createHsmLmk', 32, 'vsm_sansec_1002', 2108, '108', '/PlatformServlet', '?method=generateLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214270527234, 'generateHsmLMKWithComponent', 32, 'vsm_sansec_1002', 2109, '109', '/PlatformServlet', '?method=importLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214304081666, 'getHsmDeviceKeyPair', 32, 'vsm_sansec_1002', 2110, '110', '/PlatformServlet', '?method=gennerAsymmTempKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214337636097, 'exportHsmMasterKey', 32, 'vsm_sansec_1002', 2111, '111', '/PlatformServlet', '?method=exportLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214337636098, 'importHsmMasterKey', 32, 'vsm_sansec_1002', 2112, '112', '/PlatformServlet', '?method=importLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214371190537, 'getDeviceInitState', 32, 'vsm_sansec_1002', 2113, '113', '/PlatformServlet', '?method=initStatus', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214404744961, 'deviceInit', 32, 'vsm_sansec_1002', 2114, '114', '/PlatformServlet', '?method=doHsmInit', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214438299392, 'importAsyncKeyPair', 32, 'vsm_sansec_1002', 2115, '115', '/PlatformServlet', '?method=importAsyncKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214471853830, 'importSyncKeyPair', 32, 'vsm_sansec_1002', 2116, '116', '/PlatformServlet', '?method=importSymmKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214505408260, 'deleteSymmetricKey', 32, 'vsm_sansec_1002', 2117, '117', '/PlatformServlet', '?method=delHsmSymmetricKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214538962693, 'deleteSM2Key', 32, 'vsm_sansec_1002', 2118, '118', '/PlatformServlet', '?method=delHsmKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_api" VALUES (6075412214538962694, 'getHsmServiceStatus', 32, 'vsm_sansec_1002', 2119, '119', '/PlatformServlet', '?method=getServiceState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968601, 'getVsmInfo', 41, 'chsm_sansec_standard_0088', 101, '1', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968602, 'getVsmStatus', 41, 'chsm_sansec_standard_0088', 102, '2', '/', '/api/1.0/vsm/status', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968603, 'configVsmNetwork', 41, 'chsm_sansec_standard_0088', 103, '3', '/', '/api/1.0/vsm/network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968604, 'createVsm', 41, 'chsm_sansec_standard_0088', 104, '4', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968605, 'vsmOperate', 41, 'chsm_sansec_standard_0088', 105, '5', '/', '/api/1.0/vsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968606, 'getChsmInfo', 41, 'chsm_sansec_standard_0088', 106, '6', '/', '/api/1.0/chsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968607, 'getChsmStatus', 41, 'chsm_sansec_standard_0088', 107, '7', '/', '/api/1.0/chsm/status', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968608, 'configChsmPublicKey', 41, 'chsm_sansec_standard_0088', 108, '8', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968609, 'clearChsmPublicKey', 41, 'chsm_sansec_standard_0088', 109, '9', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968610, 'getChsmPublicKeyFinger', 41, 'chsm_sansec_standard_0088', 110, '10', '/', '/api/1.0/chsm/authpk', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968611, 'configVsm', 41, 'chsm_sansec_standard_0088', 111, '30', '/', '/api/1.0/vsm/config', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:46:40', NULL, NULL);

INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968631, 'getHsmInfo', 42, 'vsm_sansec_1002', 2101, '101', '/PlatformServlet', '?method=getHsmInfo', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968612, 'getHsmStatus', 42, 'vsm_sansec_1002', 2102, '102', '/PlatformServlet', '?method=getState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968613, 'configHsmNetwork', 42, 'vsm_sansec_1002', 2103, '103', '/PlatformServlet', '?method=network', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968614, 'restartHsm', 42, 'vsm_sansec_1002', 2104, '104', '/PlatformServlet', '?method=restartHsm', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968615, 'configHsmPublicKey', 42, 'vsm_sansec_1002', 2105, '105', '/AuthServlet', '?method=authPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968616, 'clearHsmPublicKey', 42, 'vsm_sansec_1002', 2106, '106', '/AuthServlet', '?method=cleanPK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968617, 'getHsmPublicKeyFinger', 42, 'vsm_sansec_1002', 2107, '107', '/AuthServlet', '?method=getAuthPKFingerprints', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968618, 'createHsmLmk', 42, 'vsm_sansec_1002', 2108, '108', '/PlatformServlet', '?method=generateLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968619, 'generateHsmLMKWithComponent', 42, 'vsm_sansec_1002', 2109, '109', '/PlatformServlet', '?method=importLMK', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968621, 'getHsmDeviceKeyPair', 42, 'vsm_sansec_1002', 2110, '110', '/PlatformServlet', '?method=gennerAsymmTempKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968622, 'exportHsmMasterKey', 42, 'vsm_sansec_1002', 2111, '111', '/PlatformServlet', '?method=exportLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968623, 'importHsmMasterKey', 42, 'vsm_sansec_1002', 2112, '112', '/PlatformServlet', '?method=importLMKByCipher', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968624, 'getDeviceInitState', 42, 'vsm_sansec_1002', 2113, '113', '/PlatformServlet', '?method=initStatus', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968625, 'deviceInit', 42, 'vsm_sansec_1002', 2114, '114', '/PlatformServlet', '?method=doHsmInit', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968626, 'importAsyncKeyPair', 42, 'vsm_sansec_1002', 2115, '115', '/PlatformServlet', '?method=importAsyncKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968627, 'importSyncKeyPair', 42, 'vsm_sansec_1002', 2116, '116', '/PlatformServlet', '?method=importSymmKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968628, 'deleteSymmetricKey', 42, 'vsm_sansec_1002', 2117, '117', '/PlatformServlet', '?method=delHsmSymmetricKey', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968629, 'deleteSM2Key', 42, 'vsm_sansec_1002', 2118, '118', '/PlatformServlet', '?method=delHsmKeyPair', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_api (API_ID, API_NAME, DEVICE_TYPE_ID, INTERACTION_SERIAL_NUMBER, API_TEMPLATE_ID, API_SERIAL_NUMBER, CONTEXT_PATH, API_PATH, DEFAULT_FLAG, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412214538968630, 'getHsmServiceStatus', 42, 'vsm_sansec_1002', 2119, '119', '/PlatformServlet', '?method=getServiceState', 0, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);



-- ----------------------------
-- Table structure for device_api_record
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_api_record";
CREATE TABLE "ccsp_device"."device_api_record" (
  "id" int8 NOT NULL,
  "deviice_id" int8 NOT NULL,
  "api_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "requeset_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "api_id" varchar(255) COLLATE "pg_catalog"."default",
  "method_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "interface_url" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "notify_status" varchar(50) COLLATE "pg_catalog"."default",
  "notify_info" varchar(100) COLLATE "pg_catalog"."default",
  "oper_status" varchar(50) COLLATE "pg_catalog"."default",
  "oper_info" varchar(100) COLLATE "pg_catalog"."default",
  "reqinfo" varchar(1000) COLLATE "pg_catalog"."default",
  "resinfo" varchar(1000) COLLATE "pg_catalog"."default",
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_api_record"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."deviice_id" IS '所属设备ID';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."api_name" IS '接口名称';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."requeset_id" IS '接口请求流水号';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."api_id" IS '设备接口ID';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."method_name" IS '方法名';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."interface_url" IS '接口地址';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."status" IS '状态';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."notify_status" IS '回调状态';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."notify_info" IS '回调结果';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."oper_status" IS '操作状态';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."oper_info" IS '操作结果';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."reqinfo" IS '请求信息';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."resinfo" IS '响应信息';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_api_record"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_api_record" IS '设备接口信息';

-- ----------------------------
-- Records of device_api_record
-- ----------------------------

-- ----------------------------
-- Table structure for device_busitype
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_busitype";
CREATE TABLE "ccsp_device"."device_busitype" (
  "id" int8 NOT NULL,
  "device_type_id" int8 NOT NULL,
  "busi_type_id" int8 NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_busitype"."id" IS '主键标识';
COMMENT ON COLUMN "ccsp_device"."device_busitype"."device_type_id" IS '设备类型ID';
COMMENT ON COLUMN "ccsp_device"."device_busitype"."busi_type_id" IS '业务类型ID';
COMMENT ON COLUMN "ccsp_device"."device_busitype"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."device_busitype"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_busitype"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_busitype"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_busitype"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_busitype"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_busitype" IS '设备类型与业务类型关系表';

-- ----------------------------
-- Records of device_busitype
-- ----------------------------
INSERT INTO "ccsp_device"."device_busitype" VALUES (1, 2, 1, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (2, 2, 2, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (4, 2, 4, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5, 2, 5, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (6, 2, 6, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (7, 2, 7, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (8, 2, 8, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (9, 2, 9, 0, NULL, 1, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (10, 2, 10, 0, NULL, 1, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (11, 2, 11, 0, NULL, 1, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (12, 6, 1, 0, NULL, 1, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (13, 6, 2, 0, NULL, 1, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (15, 6, 4, 0, NULL, 1, '2023-03-09 14:26:07', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (16, 6, 5, 0, NULL, 1, '2023-03-09 01:40:56', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (17, 6, 6, 0, NULL, 1, '2023-03-09 01:40:56', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (18, 6, 7, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (19, 6, 8, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (20, 6, 9, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (21, 6, 10, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (22, 6, 11, 0, NULL, 1, '2023-03-09 16:48:11', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (23, 10, 1, 0, NULL, 1, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (24, 10, 2, 0, NULL, 1, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (26, 10, 4, 0, NULL, 1, '2023-03-09 14:26:07', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (27, 10, 5, 0, NULL, 1, '2023-03-09 01:40:56', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (28, 10, 6, 0, NULL, 1, '2023-03-09 01:40:56', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (29, 10, 7, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (30, 10, 8, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (31, 10, 9, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (32, 10, 10, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (33, 10, 11, 0, NULL, 1, '2023-03-09 16:48:11', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (34, 11, 2, 0, NULL, 1, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (36, 11, 4, 0, NULL, 1, '2023-03-09 14:26:07', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (37, 11, 5, 0, NULL, 1, '2023-03-09 01:40:56', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (38, 11, 6, 0, NULL, 1, '2023-03-09 01:40:56', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (39, 11, 7, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (40, 11, 8, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (41, 11, 9, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (42, 11, 10, 0, NULL, 1, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (43, 11, 11, 0, NULL, 1, '2023-03-09 16:48:11', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (44, 11, 1, 0, NULL, 1, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (45, 12, 1, 0, NULL, NULL, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (46, 12, 2, 0, NULL, NULL, '2023-03-08 08:12:44', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (47, 12, 4, 0, NULL, NULL, '2023-03-09 14:26:07', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (48, 12, 5, 0, NULL, NULL, '2023-03-09 01:40:56', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (49, 12, 6, 0, NULL, NULL, '2023-03-09 01:40:56', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (50, 12, 7, 0, NULL, NULL, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (51, 12, 8, 0, NULL, NULL, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (52, 12, 9, 0, NULL, NULL, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (53, 12, 10, 0, NULL, NULL, '2023-03-09 01:59:09', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (54, 12, 11, 0, NULL, NULL, '2023-03-09 16:48:11', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (601, 6, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (1101, 11, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (1201, 12, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (2101, 21, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (3201, 32, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875303145879242119, 21, 1, 0, NULL, 5872045327240398210, '2023-07-20 14:14:38', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875303145912796553, 21, 2, 0, NULL, 5872045327240398210, '2023-07-20 14:14:38', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875303145979905415, 21, 4, 0, NULL, 5872045327240398210, '2023-07-20 14:14:38', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875303146013459845, 21, 5, 0, NULL, 5872045327240398210, '2023-07-20 14:14:38', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875303146080568706, 21, 6, 0, NULL, 5872045327240398210, '2023-07-20 14:14:38', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875303146114123142, 21, 7, 0, NULL, 5872045327240398210, '2023-07-20 14:14:38', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875303146181232005, 21, 8, 0, NULL, 5872045327240398210, '2023-07-20 14:14:38', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875303146248340871, 21, 9, 0, NULL, 5872045327240398210, '2023-07-20 14:14:38', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875303146281895299, 21, 10, 0, NULL, 5872045327240398210, '2023-07-20 14:14:38', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875303146349004161, 21, 11, 0, NULL, 5872045327240398210, '2023-07-20 14:14:38', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875336200780252550, 32, 1, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875336200847361409, 32, 2, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875336200880915840, 32, 4, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875336200914470272, 32, 5, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875336200914470273, 32, 9, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875336200948024711, 32, 8, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875336201015133568, 32, 7, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875336201048688000, 32, 11, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875336201115796864, 32, 10, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO "ccsp_device"."device_busitype" VALUES (5875336201149351300, 32, 6, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4201, 42, 1, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4202, 42, 2, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4203, 42, 4, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4204, 42, 5, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4205, 42, 6, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4206, 42, 7, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4207, 42, 8, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4208, 42, 9, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4209, 42, 10, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4210, 42, 11, 0, NULL, 5872045327240398210, '2023-07-20 14:31:03', NULL, NULL);
INSERT INTO ccsp_device.device_busitype (ID, DEVICE_TYPE_ID, BUSI_TYPE_ID, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (4211, 42, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);

-- ----------------------------
-- Table structure for device_group
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_group";
CREATE TABLE "ccsp_device"."device_group" (
  "device_group_id" int8 NOT NULL,
  "device_group_name" varchar(270) COLLATE "pg_catalog"."default" NOT NULL,
  "device_group_type" int4 NOT NULL,
  "region_id" int8,
  "tenant_id" int8,
  "is_rest" varchar(255) COLLATE "pg_catalog"."default",
  "net_pro" int4,
  "device_type_id" int8,
  "is_share" int4,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_group"."device_group_id" IS '设备组ID';
COMMENT ON COLUMN "ccsp_device"."device_group"."device_group_name" IS '设备组名称';
COMMENT ON COLUMN "ccsp_device"."device_group"."device_group_type" IS '设备组类型';
COMMENT ON COLUMN "ccsp_device"."device_group"."region_id" IS '区域ID';
COMMENT ON COLUMN "ccsp_device"."device_group"."tenant_id" IS '所属租户ID';
COMMENT ON COLUMN "ccsp_device"."device_group"."is_rest" IS '是否支持直接调用;0：不支持；1：支持';
COMMENT ON COLUMN "ccsp_device"."device_group"."net_pro" IS '网络协议;1：TCP；2：HTTPS';
COMMENT ON COLUMN "ccsp_device"."device_group"."device_type_id" IS '设备类型ID';
COMMENT ON COLUMN "ccsp_device"."device_group"."is_share" IS '是否支持共享';
COMMENT ON COLUMN "ccsp_device"."device_group"."invalid_flag" IS '是否作废';
COMMENT ON COLUMN "ccsp_device"."device_group"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_group"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_group"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_group"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_group"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_group" IS '设备组';

-- ----------------------------
-- Records of device_group
-- ----------------------------

-- ----------------------------
-- Table structure for device_group_to_busi_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_group_to_busi_type";
CREATE TABLE "ccsp_device"."device_group_to_busi_type" (
  "id" int8 NOT NULL,
  "device_group_id" int8 NOT NULL,
  "busi_type_id" int8 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_group_to_busi_type"."id" IS '主键';
COMMENT ON COLUMN "ccsp_device"."device_group_to_busi_type"."device_group_id" IS '设备组ID';
COMMENT ON COLUMN "ccsp_device"."device_group_to_busi_type"."busi_type_id" IS '业务类型ID';
COMMENT ON COLUMN "ccsp_device"."device_group_to_busi_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_group_to_busi_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_group_to_busi_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_group_to_busi_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_group_to_busi_type"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_group_to_busi_type" IS '设备组和业务类型关联表';

-- ----------------------------
-- Records of device_group_to_busi_type
-- ----------------------------

-- ----------------------------
-- Table structure for device_info
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_info";
CREATE TABLE "ccsp_device"."device_info" (
  "device_id" int8 NOT NULL,
  "device_self_id" varchar(100) COLLATE "pg_catalog"."default",
  "device_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "vendor_id" int8 NOT NULL,
  "device_type_id" int8 NOT NULL,
  "family_type" int4,
  "hccs_device_id" int8,
  "hccs_vsm_total" int4,
  "hccs_vsm_usable" int4,
  "hccs_vsm_used" int4,
  "device_group_id" int8,
  "mgt_ip" varchar(40) COLLATE "pg_catalog"."default",
  "mgt_port" int4,
  "busi_ip" varchar(40) COLLATE "pg_catalog"."default",
  "busi_port" int4,
  "connect_password" varchar(200) COLLATE "pg_catalog"."default",
  "connect_protocol" varchar(40) COLLATE "pg_catalog"."default",
  "device_version" varchar(100) COLLATE "pg_catalog"."default",
  "device_serialnum" varchar(100) COLLATE "pg_catalog"."default",
  "region_id" int8,
  "web_url" varchar(255) COLLATE "pg_catalog"."default",
  "public_key" text COLLATE "pg_catalog"."default",
  "public_key_finger" text COLLATE "pg_catalog"."default",
  "device_weight" numeric(24,6),
  "auth_code" varchar(200) COLLATE "pg_catalog"."default",
  "vsm_resource" int8,
  "hmac" varchar(200) COLLATE "pg_catalog"."default",
  "cloud_token" varchar(255) COLLATE "pg_catalog"."default",
  "management_status_id" int8,
  "oper_status" int8,
  "master_key_flag" int4 DEFAULT 0,
  "connect_auth_code" varchar(200) COLLATE "pg_catalog"."default",
  "run_status" int4,
  "busi_port_extend" int4 NOT NULL DEFAULT 8008,
  "invalid_flag" text COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_info"."device_id" IS '设备ID';
COMMENT ON COLUMN "ccsp_device"."device_info"."device_self_id" IS '设备内部ID';
COMMENT ON COLUMN "ccsp_device"."device_info"."device_name" IS '设备名称';
COMMENT ON COLUMN "ccsp_device"."device_info"."vendor_id" IS '厂商ID';
COMMENT ON COLUMN "ccsp_device"."device_info"."device_type_id" IS '设备类型ID';
COMMENT ON COLUMN "ccsp_device"."device_info"."family_type" IS '设备物理类型：1：云密码机；2：物理机；3：虚拟机';
COMMENT ON COLUMN "ccsp_device"."device_info"."hccs_device_id" IS '所属宿主机ID（虚拟机使用）';
COMMENT ON COLUMN "ccsp_device"."device_info"."hccs_vsm_total" IS '可创建虚拟机总数';
COMMENT ON COLUMN "ccsp_device"."device_info"."hccs_vsm_usable" IS '可创建虚拟机数';
COMMENT ON COLUMN "ccsp_device"."device_info"."hccs_vsm_used" IS '已创建数量';
COMMENT ON COLUMN "ccsp_device"."device_info"."device_group_id" IS '设备组ID';
COMMENT ON COLUMN "ccsp_device"."device_info"."mgt_ip" IS '管理IP';
COMMENT ON COLUMN "ccsp_device"."device_info"."mgt_port" IS '管理端口';
COMMENT ON COLUMN "ccsp_device"."device_info"."busi_ip" IS '服务IP';
COMMENT ON COLUMN "ccsp_device"."device_info"."busi_port" IS '服务端口';
COMMENT ON COLUMN "ccsp_device"."device_info"."connect_password" IS '服务连接密码';
COMMENT ON COLUMN "ccsp_device"."device_info"."connect_protocol" IS '协议（HTTP、HTTPS）';
COMMENT ON COLUMN "ccsp_device"."device_info"."device_version" IS '设备版本号';
COMMENT ON COLUMN "ccsp_device"."device_info"."device_serialnum" IS '设备序列号';
COMMENT ON COLUMN "ccsp_device"."device_info"."region_id" IS '区域ID';
COMMENT ON COLUMN "ccsp_device"."device_info"."web_url" IS '设备管理平台URL';
COMMENT ON COLUMN "ccsp_device"."device_info"."public_key" IS '公钥';
COMMENT ON COLUMN "ccsp_device"."device_info"."public_key_finger" IS '公钥指纹';
COMMENT ON COLUMN "ccsp_device"."device_info"."device_weight" IS '设备权重';
COMMENT ON COLUMN "ccsp_device"."device_info"."auth_code" IS '授权码';
COMMENT ON COLUMN "ccsp_device"."device_info"."vsm_resource" IS '资源分配;虚机资源分配';
COMMENT ON COLUMN "ccsp_device"."device_info"."hmac" IS 'HMAC';
COMMENT ON COLUMN "ccsp_device"."device_info"."cloud_token" IS '云机回调token';
COMMENT ON COLUMN "ccsp_device"."device_info"."management_status_id" IS '使用状态ID';
COMMENT ON COLUMN "ccsp_device"."device_info"."oper_status" IS '操作状态ID';
COMMENT ON COLUMN "ccsp_device"."device_info"."master_key_flag" IS '是否生成主密钥(1是，0否);默认0';
COMMENT ON COLUMN "ccsp_device"."device_info"."connect_auth_code" IS '服务连接密码';
COMMENT ON COLUMN "ccsp_device"."device_info"."run_status" IS '运行状态：0：未运行，1：运行中，2：获取出错';
COMMENT ON COLUMN "ccsp_device"."device_info"."busi_port_extend" IS '扩展端口';
COMMENT ON COLUMN "ccsp_device"."device_info"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."device_info"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_info"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_info"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_info"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_info" IS '设备信息(1030)';

-- ----------------------------
-- Records of device_info
-- ----------------------------

-- ----------------------------
-- Table structure for device_master_cover_record
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_master_cover_record";
CREATE TABLE "ccsp_device"."device_master_cover_record" (
  "id" int8 NOT NULL,
  "device_id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "cover_status" int4,
  "cover_type" int4 NOT NULL,
  "cover_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."id" IS '主键';
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."device_id" IS '设备ID';
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."cover_status" IS '恢复状态0-恢复成功，1-恢复中，2-恢复失败';
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."cover_type" IS '恢复记录类型 1-主密钥，2-内部密钥';
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."cover_time" IS '恢复时间';
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_master_cover_record"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_master_cover_record" IS '密钥恢复记录表（1030）';

-- ----------------------------
-- Records of device_master_cover_record
-- ----------------------------

-- ----------------------------
-- Table structure for device_monitor_config
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_monitor_config";
CREATE TABLE "ccsp_device"."device_monitor_config" (
  "id" int8 NOT NULL,
  "device_type_id" int8,
  "monitor_type" int4,
  "url" varchar(255) COLLATE "pg_catalog"."default",
  "snmp_version" varchar(30) COLLATE "pg_catalog"."default",
  "snmp_proto" varchar(30) COLLATE "pg_catalog"."default",
  "snmp_port" int4,
  "safe_level" int4,
  "security_name" varchar(150) COLLATE "pg_catalog"."default",
  "authentication_protocol" int4,
  "authentication_auth_code" varchar(100) COLLATE "pg_catalog"."default",
  "privacy_protocol" int4,
  "privacy_auth_code" varchar(100) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."id" IS 'id';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."device_type_id" IS '设备类型id';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."monitor_type" IS '监控类型;1 监控组件 2snmp 3监控组件http 4 自定义http';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."url" IS 'url地址;http方式预留字段';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."snmp_version" IS 'SNMP版本;snmp版本备用';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."snmp_proto" IS 'snmp监控协议;snmp监控协议  TCP/UDP,默认UDP，备用';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."snmp_port" IS 'snmp端口;snmp端口';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."safe_level" IS '安全等级;安全等级 1 不认证 2认证不加密 3认证+加密';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."security_name" IS 'SNMP用户名';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."authentication_protocol" IS '认证算法id';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."authentication_auth_code" IS '认证密码';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."privacy_protocol" IS '加密算法id';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."privacy_auth_code" IS '加密密码';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_monitor_config"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_monitor_config" IS '设备类型监控配置(1030)';

-- ----------------------------
-- Records of device_monitor_config
-- ----------------------------
INSERT INTO "ccsp_device"."device_monitor_config" VALUES (6118851783558825989, 6, 1, NULL, NULL, 'https', 19443, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 6075546759500924936, '2023-10-12 14:26:33', NULL, '2023-10-12 14:26:33');
INSERT INTO "ccsp_device"."device_monitor_config" VALUES (6118878410611230722, 21, 2, NULL, 'v3', 'UDP', 161, 1, 'sansec', 0, 'A/YC4vF0axVbZJsskjNGhQ==', 2, 'A/YC4vF0axVbZJsskjNGhQ==', NULL, 6075546759500924936, '2023-10-12 14:39:47', NULL, '2023-10-12 14:39:47');
INSERT INTO "ccsp_device"."device_monitor_config" VALUES (6118880293417519113, 32, 2, NULL, 'v3', 'UDP', 161, 1, 'snmpuser', 5, 'DXTNvsZh9NyBEcy74FgtnQ==', 2, 'DXTNvsZh9NyBEcy74FgtnQ==', NULL, NULL, '2023-10-19 09:22:59', NULL, '2023-10-19 09:22:59');
INSERT INTO ccsp_device.device_monitor_config (ID, DEVICE_TYPE_ID, MONITOR_TYPE, URL, SNMP_VERSION, SNMP_PROTO, SNMP_PORT, SAFE_LEVEL, SECURITY_NAME, AUTHENTICATION_PROTOCOL, AUTHENTICATION_AUTH_CODE, PRIVACY_PROTOCOL, PRIVACY_AUTH_CODE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118880293417519114, 42, 2, NULL, 'v3', 'UDP', 161, 1, 'snmpuser', 5, 'DXTNvsZh9NyBEcy74FgtnQ==', 2, 'DXTNvsZh9NyBEcy74FgtnQ==', NULL, NULL, '2023-10-19 09:22:59', NULL, '2023-10-19 09:22:59');

-- ----------------------------
-- Table structure for device_net_detail
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_net_detail";
CREATE TABLE "ccsp_device"."device_net_detail" (
  "ip_id" int8 NOT NULL,
  "mgt_ip" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
  "busi_ip" varchar(60) COLLATE "pg_catalog"."default",
  "mgt_ip_num" int8 NOT NULL,
  "busi_ip_num" int8,
  "gateway" varchar(60) COLLATE "pg_catalog"."default" NOT NULL,
  "status" int4 NOT NULL DEFAULT 1,
  "device_id" int8,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."ip_id" IS 'IP地址ID';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."mgt_ip" IS '管理IP';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."busi_ip" IS '业务IP';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."mgt_ip_num" IS '管理IP数字';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."busi_ip_num" IS '业务IP数字';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."gateway" IS '管理IP网关';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."status" IS '状态（1未使用 2锁定 3被使用）';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."device_id" IS '绑定云机的ID';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_net_detail"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_net_detail" IS 'IP资源详情（1030）';

-- ----------------------------
-- Records of device_net_detail
-- ----------------------------

-- ----------------------------
-- Table structure for device_snmp_oid_config
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_snmp_oid_config";
CREATE TABLE "ccsp_device"."device_snmp_oid_config" (
  "id" int8 NOT NULL,
  "device_type_id" int8,
  "oid" varchar(150) COLLATE "pg_catalog"."default",
  "name" varchar(150) COLLATE "pg_catalog"."default",
  "value_type" varchar(50) COLLATE "pg_catalog"."default",
  "request_type" varchar(50) COLLATE "pg_catalog"."default",
  "collector_type" int4,
  "parent_id" int8,
  "sub_oid_key" int4,
  "sub_oid_value" varchar(150) COLLATE "pg_catalog"."default",
  "computing_type" varchar(50) COLLATE "pg_catalog"."default",
  "calculate_template" varchar(100) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."id" IS 'id';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."device_type_id" IS '设备类型id';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."oid" IS 'oid';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."name" IS '名称';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."value_type" IS 'oid数据类型';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."request_type" IS '请求类型';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."collector_type" IS '计算方式';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."parent_id" IS '父节点id';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."sub_oid_key" IS '排序序号';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."sub_oid_value" IS '子oid';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."computing_type" IS '计算方式';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."calculate_template" IS '计算模板';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_snmp_oid_config"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_snmp_oid_config" IS '设备snmp配置表(1030)';

-- ----------------------------
-- Records of device_snmp_oid_config
-- ----------------------------
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585568600257202049, 32, '.1.3.6.1.4.1.2021.11.9.0', 'sansec.cpu.userCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:07:18', 6585233652468156297, '2024-03-21 14:07:18');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585568600257202102, 21, '.1.3.6.1.4.1.2021.11.9.0.1', 'sansec.cpu.userCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:07:18', 6585233652468156297, '2024-03-21 14:07:18');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585568928352438111, 21, '.1.3.6.1.4.1.2021.11.10.0.1', 'sansec.cpu.sysCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:07:28', 6585233652468156297, '2024-03-21 14:07:28');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585568928352438149, 32, '.1.3.6.1.4.1.2021.11.10.0', 'sansec.cpu.sysCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:07:28', 6585233652468156297, '2024-03-21 14:07:28');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585570273583171116, 21, '.1.2.3.4.111.1.1', 'sansec.cpu.id', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '100-${0}-${1}', NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585570273583171120, 21, NULL, NULL, NULL, NULL, NULL, 6585570273583171116, 1, '.1.3.6.1.4.1.2021.11.9.0.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585570273583171123, 21, NULL, NULL, NULL, NULL, NULL, 6585570273583171116, 2, '.1.3.6.1.4.1.2021.11.10.0.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585570273583171463, 32, '.1.2.3.4.111.1', 'sansec.cpu.id', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '100-${0}-${1}', NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585570273583171464, 32, NULL, NULL, NULL, NULL, NULL, 6585570273583171463, 1, '.1.3.6.1.4.1.2021.11.9.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585570273583171465, 32, NULL, NULL, NULL, NULL, NULL, 6585570273583171463, 2, '.1.3.6.1.4.1.2021.11.10.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585571687667599113, 21, '.1.2.3.4.111.4.1', 'sansec.cpu.cpuUsedPercent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '${0}+${1}', NULL, 6585233652468156297, '2024-03-21 14:08:50', 6585233652468156297, '2024-03-21 14:08:50');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585571687667599114, 21, NULL, NULL, NULL, NULL, NULL, 6585571687667599113, 1, '.1.3.6.1.4.1.2021.11.9.0.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:08:50', 6585233652468156297, '2024-03-21 14:08:50');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585571687667599117, 21, NULL, NULL, NULL, NULL, NULL, 6585571687667599113, 2, '.1.3.6.1.4.1.2021.11.10.0.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:08:50', 6585233652468156297, '2024-03-21 14:08:50');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585572422308311101, 21, '.1.3.6.1.2.1.25.2.2.0.1', 'sansec.mem.total', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:12', 6585233652468156297, '2024-03-21 14:09:12');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585572422308333440, 32, '.1.3.6.1.2.1.25.2.2.0', 'sansec.mem.total', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:12', 6585233652468156297, '2024-03-21 14:09:12');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585572745739503121, 21, '.1.3.6.1.4.1.2021.4.6.0.1', 'sansec.mem.free', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:22', 6585233652468156297, '2024-03-21 14:09:22');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585572745739503497, 32, '.1.3.6.1.4.1.2021.4.6.0', 'sansec.mem.free', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:22', 6585233652468156297, '2024-03-21 14:09:22');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585573670801639108, 21, '.1.2.3.4.111.2.1', 'sansec.mem.percent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '((${0}-${1})/${0})*100', NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585573670801639300, 32, '.1.2.3.4.111.2', 'sansec.mem.percent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '((${0}-${1})/${0})*100', NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585573670835193109, 21, NULL, NULL, NULL, NULL, NULL, 6585573670801639108, 1, '.1.3.6.1.2.1.25.2.2.0.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585573670835193112, 21, NULL, NULL, NULL, NULL, NULL, 6585573670801639108, 2, '.1.3.6.1.4.1.2021.4.6.0.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585573670835193732, 32, NULL, NULL, NULL, NULL, NULL, 6585573670801639300, 1, '.1.3.6.1.2.1.25.2.2.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585573670835193733, 32, NULL, NULL, NULL, NULL, NULL, 6585573670801639300, 2, '.1.3.6.1.4.1.2021.4.6.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585575224103405115, 21, '.1.2.3.4.111.3.1', 'sansec.mem.used', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '${0}-${1}', NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585575224103405119, 21, NULL, NULL, NULL, NULL, NULL, 6585575224103405115, 1, '.1.3.6.1.2.1.25.2.2.0.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585575224103405122, 21, NULL, NULL, NULL, NULL, NULL, 6585575224103405115, 2, '.1.3.6.1.4.1.2021.4.6.0.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585575224103405447, 32, '.1.2.3.4.111.3', 'sansec.mem.used', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '${0}-${1}', NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585575224103405448, 32, NULL, NULL, NULL, NULL, NULL, 6585575224103405447, 1, '.1.3.6.1.2.1.25.2.2.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585575224103405449, 32, NULL, NULL, NULL, NULL, NULL, 6585575224103405447, 2, '.1.3.6.1.4.1.2021.4.6.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585576558294730118, 21, '.1.3.6.1.2.1.25.2.3.1.5.1', 'sansec.file.osFileStores.totalSpace', 'Integer', 'Walk', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:11:15', 6585233652468156297, '2024-03-21 14:11:15');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585576558294730631, 32, '.1.3.6.1.2.1.25.2.3.1.5', 'sansec.file.osFileStores.totalSpace', 'Integer', 'Walk', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:11:15', 6585233652468156297, '2024-03-21 14:11:15');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585577584926132105, 32, '.1.2.3.4.111.6', 'sansec.file.wholeTotalSpace', 'Integer', 'Walk', 1, NULL, NULL, NULL, 'SUM', NULL, NULL, 6585233652468156297, '2024-03-21 14:11:46', 6585233652468156297, '2024-03-21 14:11:46');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585577584926132106, 32, NULL, NULL, NULL, NULL, NULL, 6585577584926132105, 1, '.1.3.6.1.2.1.25.2.3.1.5', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:11:46', 6585233652468156297, '2024-03-21 14:11:46');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585577584926132124, 21, '.1.2.3.4.111.6.1', 'sansec.file.wholeTotalSpace', 'Integer', 'Walk', 1, NULL, NULL, NULL, 'SUM', NULL, NULL, 6585233652468156297, '2024-03-21 14:11:46', 6585233652468156297, '2024-03-21 14:11:46');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585577584926132125, 21, NULL, NULL, NULL, NULL, NULL, 6585577584926132124, 1, '.1.3.6.1.2.1.25.2.3.1.5.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:11:46', 6585233652468156297, '2024-03-21 14:11:46');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585578412848187110, 21, '.1.3.6.1.2.1.25.2.3.1.6.1', 'sansec.file.osFileStores.usedSpace', 'Integer', 'Walk', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:12:10', 6585233652468156297, '2024-03-21 14:12:10');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585578412848187268, 32, '.1.3.6.1.2.1.25.2.3.1.6', 'sansec.file.osFileStores.usedSpace', 'Integer', 'Walk', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:12:10', 6585233652468156297, '2024-03-21 14:12:10');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585579113330509104, 21, '.1.2.3.4.111.7.1', 'sansec.file.wholeUsedSpace', 'Integer', 'Walk', 1, NULL, NULL, NULL, 'SUM', NULL, NULL, 6585233652468156297, '2024-03-21 14:12:31', 6585233652468156297, '2024-03-21 14:12:31');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585579113330509106, 21, NULL, NULL, NULL, NULL, NULL, 6585579113330509104, 1, '.1.3.6.1.2.1.25.2.3.1.6.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:12:31', 6585233652468156297, '2024-03-21 14:12:31');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585579113330509697, 32, '.1.2.3.4.111.7', 'sansec.file.wholeUsedSpace', 'Integer', 'Walk', 1, NULL, NULL, NULL, 'SUM', NULL, NULL, 6585233652468156297, '2024-03-21 14:12:31', 6585233652468156297, '2024-03-21 14:12:31');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585579113330509698, 32, NULL, NULL, NULL, NULL, NULL, 6585579113330509697, 1, '.1.3.6.1.2.1.25.2.3.1.6', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:12:31', 6585233652468156297, '2024-03-21 14:12:31');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585580774576883103, 21, '.1.2.3.4.111.8.1', 'sansec.file.wholeFileSystemPercent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '(${1}/${0})*100', NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585580774576883105, 21, NULL, NULL, NULL, NULL, NULL, 6585580774576883103, 1, '.1.2.3.4.111.6.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585580774576883107, 21, NULL, NULL, NULL, NULL, NULL, 6585580774576883103, 2, '.1.2.3.4.111.7.1', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585580774576883585, 32, '.1.2.3.4.111.8', 'sansec.file.wholeFileSystemPercent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '(${1}/${0})*100', NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585580774576883586, 32, NULL, NULL, NULL, NULL, NULL, 6585580774576883585, 1, '.1.2.3.4.111.6', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585580774576883587, 32, NULL, NULL, NULL, NULL, NULL, 6585580774576883585, 2, '.1.2.3.4.111.7', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585738084377560965, 32, '.1.2.3.4.111.4', 'sansec.cpu.cpuUsedPercent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '${0}+${1}', NULL, 6585233652468156297, '2024-03-21 15:31:29', 6585233652468156297, '2024-03-21 15:31:29');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585738084377560966, 32, NULL, NULL, NULL, NULL, NULL, 6585738084377560965, 1, '.1.3.6.1.4.1.2021.11.9.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 15:31:29', 6585233652468156297, '2024-03-21 15:31:29');
INSERT INTO "ccsp_device"."device_snmp_oid_config" VALUES (6585738084377560967, 32, NULL, NULL, NULL, NULL, NULL, 6585738084377560965, 2, '.1.3.6.1.4.1.2021.11.10.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 15:31:29', 6585233652468156297, '2024-03-21 15:31:29');

INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560901, 42, '.1.3.6.1.4.1.2021.11.9.0', 'sansec.cpu.userCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:07:18', 6585233652468156297, '2024-03-21 14:07:18');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560902, 42, '.1.3.6.1.4.1.2021.11.10.0', 'sansec.cpu.sysCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:07:28', 6585233652468156297, '2024-03-21 14:07:28');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560903, 42, '.1.2.3.4.111.1.42', 'sansec.cpu.id', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '100-${0}-${1}', NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560904, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560903, 1, '.1.3.6.1.4.1.2021.11.9.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560905, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560903, 2, '.1.3.6.1.4.1.2021.11.10.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:08:08', 6585233652468156297, '2024-03-21 14:08:08');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560906, 42, '.1.3.6.1.2.1.25.2.2.0', 'sansec.mem.total', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:12', 6585233652468156297, '2024-03-21 14:09:12');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560907, 42, '.1.3.6.1.4.1.2021.4.6.0', 'sansec.mem.free', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:22', 6585233652468156297, '2024-03-21 14:09:22');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560908, 42, '.1.2.3.4.111.2.42', 'sansec.mem.percent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '((${0}-${1})/${0})*100', NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560909, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560908, 1, '.1.3.6.1.2.1.25.2.2.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560910, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560908, 2, '.1.3.6.1.4.1.2021.4.6.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:09:49', 6585233652468156297, '2024-03-21 14:09:49');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560911, 42, '.1.2.3.4.111.3.42', 'sansec.mem.used', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '${0}-${1}', NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560912, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560911, 1, '.1.3.6.1.2.1.25.2.2.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560913, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560911, 2, '.1.3.6.1.4.1.2021.4.6.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:10:35', 6585233652468156297, '2024-03-21 14:10:35');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560914, 42, '.1.3.6.1.2.1.25.2.3.1.5', 'sansec.file.osFileStores.totalSpace', 'Integer', 'Walk', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:11:15', 6585233652468156297, '2024-03-21 14:11:15');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560915, 42, '.1.2.3.4.111.6.42', 'sansec.file.wholeTotalSpace', 'Integer', 'Walk', 1, NULL, NULL, NULL, 'SUM', '', NULL, 6585233652468156297, '2024-03-21 14:11:46', 6585233652468156297, '2024-03-21 14:11:46');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560916, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560915, 1, '.1.3.6.1.2.1.25.2.3.1.5', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:11:46', 6585233652468156297, '2024-03-21 14:11:46');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560917, 42, '.1.3.6.1.2.1.25.2.3.1.6', 'sansec.file.osFileStores.usedSpace', 'Integer', 'Walk', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:12:10', 6585233652468156297, '2024-03-21 14:12:10');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560918, 42, '.1.2.3.4.111.7.42', 'sansec.file.wholeUsedSpace', 'Integer', 'Walk', 1, NULL, NULL, NULL, 'SUM', '', NULL, 6585233652468156297, '2024-03-21 14:12:31', 6585233652468156297, '2024-03-21 14:12:31');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560919, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560918, 1, '.1.3.6.1.2.1.25.2.3.1.6', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:12:31', 6585233652468156297, '2024-03-21 14:12:31');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560920, 42, '.1.2.3.4.111.8.42', 'sansec.file.wholeFileSystemPercent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '(${1}/${0})*100', NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560921, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560920, 1, '.1.2.3.4.111.6', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560922, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560920, 2, '.1.2.3.4.111.7', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 14:13:21', 6585233652468156297, '2024-03-21 14:13:21');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560923, 42, '.1.2.3.4.111.4.42', 'sansec.cpu.cpuUsedPercent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '${0}+${1}', NULL, 6585233652468156297, '2024-03-21 15:31:29', 6585233652468156297, '2024-03-21 15:31:29');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560924, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560923, 1, '.1.3.6.1.4.1.2021.11.9.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 15:31:29', 6585233652468156297, '2024-03-21 15:31:29');
INSERT INTO ccsp_device.device_snmp_oid_config (ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6585738084377560925, 42, NULL, NULL, NULL, NULL, NULL, 6585738084377560923, 2, '.1.3.6.1.4.1.2021.11.10.0', NULL, NULL, NULL, 6585233652468156297, '2024-03-21 15:31:29', 6585233652468156297, '2024-03-21 15:31:29');


-- ----------------------------
-- Table structure for device_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_type";
CREATE TABLE "ccsp_device"."device_type" (
  "device_type_id" int8 NOT NULL,
  "device_type_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "default_flag" int4 NOT NULL DEFAULT 0,
  "vendor_id" int8 NOT NULL,
  "family_type" int4 NOT NULL,
  "machine_type_id" int8,
  "parent_id" int8 DEFAULT 0,
  "hccs_image_id" int8,
  "interaction_serial_number" varchar(100) COLLATE "pg_catalog"."default",
  "mgt_method" int4,
  "mgt_port" int4 NOT NULL,
  "busi_port" int4,
  "cloud_vsm_total" int4,
  "support_main_key_flag" int4 NOT NULL DEFAULT 0,
  "support_sec_manage_flag" int4 NOT NULL DEFAULT 0,
  "support_gen_key_flag" int4 NOT NULL DEFAULT 0,
  "support_snmp_flag" int4 NOT NULL DEFAULT 0,
  "token_call_back_flag" int4 DEFAULT 0,
  "need_password_flag" int4 NOT NULL DEFAULT 0,
  "read_info_flag" int4 NOT NULL DEFAULT 0,
  "mgt_publickey_flag" int4 NOT NULL DEFAULT 0,
  "key_templet_ids" text COLLATE "pg_catalog"."default",
  "default_key_templet_ids" varchar(500) COLLATE "pg_catalog"."default",
  "support_external_service" int4 NOT NULL DEFAULT 0,
  "support_manage_pt" int4 NOT NULL DEFAULT 0,
  "connect_auth_code" varchar(255) COLLATE "pg_catalog"."default",
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_type"."device_type_id" IS '设备类型ID';
COMMENT ON COLUMN "ccsp_device"."device_type"."device_type_name" IS '设备类型名称';
COMMENT ON COLUMN "ccsp_device"."device_type"."default_flag" IS '是否默认 1默认 0不默认;默认0';
COMMENT ON COLUMN "ccsp_device"."device_type"."vendor_id" IS '所属厂商ID';
COMMENT ON COLUMN "ccsp_device"."device_type"."family_type" IS '设备类型(1：云密码机:2：物理机:3：虚拟机)';
COMMENT ON COLUMN "ccsp_device"."device_type"."machine_type_id" IS '密码机服务类型ID';
COMMENT ON COLUMN "ccsp_device"."device_type"."parent_id" IS '上级设备类型ID（虚拟机独有）;默认0';
COMMENT ON COLUMN "ccsp_device"."device_type"."hccs_image_id" IS '超融合虚拟机镜像类型（虚拟机独有）';
COMMENT ON COLUMN "ccsp_device"."device_type"."interaction_serial_number" IS '管理规范（交互类型编号）';
COMMENT ON COLUMN "ccsp_device"."device_type"."mgt_method" IS '管理管理接口协议（1：HTTPS；2：HTTP）';
COMMENT ON COLUMN "ccsp_device"."device_type"."mgt_port" IS '管理端口';
COMMENT ON COLUMN "ccsp_device"."device_type"."busi_port" IS '服务端口';
COMMENT ON COLUMN "ccsp_device"."device_type"."cloud_vsm_total" IS '云密码机可创建虚机数量';
COMMENT ON COLUMN "ccsp_device"."device_type"."support_main_key_flag" IS '是否支持生成主密钥0不支持 1支持';
COMMENT ON COLUMN "ccsp_device"."device_type"."support_sec_manage_flag" IS '是否支持安全管理，0不支持 1支持';
COMMENT ON COLUMN "ccsp_device"."device_type"."support_gen_key_flag" IS '是否支持生成密钥';
COMMENT ON COLUMN "ccsp_device"."device_type"."support_snmp_flag" IS '是否支持SNMP监控0不支持 1支持';
COMMENT ON COLUMN "ccsp_device"."device_type"."token_call_back_flag" IS '是否支持token回调';
COMMENT ON COLUMN "ccsp_device"."device_type"."need_password_flag" IS '是否支持连接密码或token;';
COMMENT ON COLUMN "ccsp_device"."device_type"."read_info_flag" IS '读取设备信息 1需要 0不需要';
COMMENT ON COLUMN "ccsp_device"."device_type"."mgt_publickey_flag" IS '是否支持管理接口公钥验签（0：否；1：是）';
COMMENT ON COLUMN "ccsp_device"."device_type"."key_templet_ids" IS '该类型设备支持的密钥模板id，多个以逗号分隔';
COMMENT ON COLUMN "ccsp_device"."device_type"."default_key_templet_ids" IS '默认自动生成密钥时支持的密钥模板id，多个以逗号分隔';
COMMENT ON COLUMN "ccsp_device"."device_type"."support_external_service" IS '是否支持对外服务配置，0不支持 1支持';
COMMENT ON COLUMN "ccsp_device"."device_type"."support_manage_pt" IS '是否支持跳转设备管理界面，0不支持 1支持';
COMMENT ON COLUMN "ccsp_device"."device_type"."connect_auth_code" IS '默认连接密码';
COMMENT ON COLUMN "ccsp_device"."device_type"."invalid_flag" IS '无效标识 0可用 1无效';
COMMENT ON COLUMN "ccsp_device"."device_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_type"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_device"."device_type"."remark" IS '备注';
COMMENT ON TABLE "ccsp_device"."device_type" IS '设备类型';

-- ----------------------------
-- Records of device_type
-- ----------------------------
INSERT INTO "ccsp_device"."device_type" VALUES (11, '华为服务器密码机', 1, 5466516874462629769, 2, 2, 0, NULL, 'hsm_sansec_1002', 1, 9443, 8008, NULL, 1, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 0, 0, 'W1AOdzYqNEGmEskVh5fSZE0He4pDJv+7rY9Phv41EM5EPaHE8+6U4gkzWEE2UnguPvChwM6xWVXDePuS6QjCjb47DWZVVB78p2yVB1dllXc=', 0, NULL, '2023-05-05 14:14:06', 6071976419609675523, '2023-09-27 14:43:33', NULL);
INSERT INTO "ccsp_device"."device_type" VALUES (12, '华为租户服务器密码机', 1, 5466516874462629769, 2, 2, 0, NULL, 'hsm_sansec_1002', 1, 9443, 8008, NULL, 1, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 0, 0, 'W1AOdzYqNEGmEskVh5fSZE0He4pDJv+7rY9Phv41EM5EPaHE8+6U4gkzWEE2UnguPvChwM6xWVXDePuS6QjCjb47DWZVVB78p2yVB1dllXc=', 0, NULL, '2023-05-05 14:14:06', 6071976419609675523, '2023-09-27 14:44:36', NULL);
INSERT INTO "ccsp_device"."device_type" VALUES (21, '服务器密码机_V5.2.7', 1, 5466516874462629769, 2, 2, 0, NULL, 'hsm_sansec_1002', 1, 443, 8008, NULL, 1, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 0, 0, 'zM7MYRiXXu7pgCPspRhR70oMk0sJQgTU7wXIhD0e4NNz8GGRvSvd+Qw/X2NQBq2p', 0, 5808835364836477569, '2023-07-15 14:58:43', 6071976419609675523, '2023-09-27 14:45:28', NULL);
INSERT INTO "ccsp_device"."device_type" VALUES (31, '云密码机_V4.1.1', 1, 5466516874462629769, 1, NULL, 0, NULL, 'chsm_sansec_standard_0088', 1, 8083, NULL, 32, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 0, 0, NULL, 0, 5808835364836477569, '2023-07-10 10:32:34', 6071976419609675523, '2023-09-27 14:46:38', NULL);
INSERT INTO "ccsp_device"."device_type" VALUES (32, '云服务器密码机_V5.2.7', 1, 5466516874462629769, 3, 2, 31, 2, 'vsm_sansec_1002', 1, 443, 8008, NULL, 1, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 0, 0, 'zM7MYRiXXu7pgCPspRhR70oMk0sJQgTU7wXIhD0e4NNz8GGRvSvd+Qw/X2NQBq2p', 0, 5808835364836477569, '2023-07-10 10:33:37', 6071976419609675523, '2023-09-27 14:49:53', NULL);
INSERT INTO ccsp_device.device_type VALUES (41, '云密码机_V4.1.2', 1, 5466516874462629769, 1, NULL, 0, NULL, 'chsm_sansec_standard_0088', 1, 8083, NULL, 32, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 0, 0, NULL, 0, 5808835364836477569, '2023-07-10 10:32:34', 6071976419609675523, '2023-09-27 14:46:38', '');
INSERT INTO ccsp_device.device_type VALUES (42, '云服务器密码机_V5.2.7.1', 1, 5466516874462629769, 3, 2, 41, 2, 'vsm_sansec_1002', 1, 443, 8008, NULL, 1, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 0, 0, 'zM7MYRiXXu7pgCPspRhR70oMk0sJQgTU7wXIhD0e4NNz8GGRvSvd+Qw/X2NQBq2p', 0, 5808835364836477569, '2023-07-10 10:33:37', 6071976419609675523, '2023-09-27 14:49:53', '');

-- ----------------------------
-- Table structure for device_type_manage_path
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_type_manage_path";
CREATE TABLE "ccsp_device"."device_type_manage_path" (
  "id" int8 NOT NULL,
  "device_type_id" int8 NOT NULL,
  "device_pt_method" int4 NOT NULL,
  "device_pt_port" int4,
  "device_pt_path" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_type_manage_path"."id" IS '主键';
COMMENT ON COLUMN "ccsp_device"."device_type_manage_path"."device_type_id" IS '设备类型id';
COMMENT ON COLUMN "ccsp_device"."device_type_manage_path"."device_pt_method" IS '管理管理接口协议（1：HTTPS；2：HTTP）';
COMMENT ON COLUMN "ccsp_device"."device_type_manage_path"."device_pt_port" IS '管理端口';
COMMENT ON COLUMN "ccsp_device"."device_type_manage_path"."device_pt_path" IS '管理平台路径';
COMMENT ON COLUMN "ccsp_device"."device_type_manage_path"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_type_manage_path"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_type_manage_path"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_type_manage_path"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_type_manage_path"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_type_manage_path" IS '设备类型与设备管理平台路径参数关联表（1030）';

-- ----------------------------
-- Records of device_type_manage_path
-- ----------------------------

-- ----------------------------
-- Table structure for device_type_rela_value
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_type_rela_value";
CREATE TABLE "ccsp_device"."device_type_rela_value" (
  "id" int8 NOT NULL,
  "device_type_id" int8 NOT NULL,
  "value" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "value_type" int4 NOT NULL,
  "default_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_type_rela_value"."id" IS '主键';
COMMENT ON COLUMN "ccsp_device"."device_type_rela_value"."device_type_id" IS '设备类型id';
COMMENT ON COLUMN "ccsp_device"."device_type_rela_value"."value" IS '关联值';
COMMENT ON COLUMN "ccsp_device"."device_type_rela_value"."value_type" IS '关联值类型;1：云宿主机关联的虚机镜像id，2：虚拟机关联的资源分配类型code';
COMMENT ON COLUMN "ccsp_device"."device_type_rela_value"."default_flag" IS '是否默认 1默认 0不默认；默认0';
COMMENT ON COLUMN "ccsp_device"."device_type_rela_value"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_type_rela_value"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_type_rela_value"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_type_rela_value"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_type_rela_value"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_type_rela_value" IS '设备类型关联值表（1030）';

-- ----------------------------
-- Records of device_type_rela_value
-- ----------------------------
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (1, 1, '1', 1, 0, NULL, 6071976419609675523, '2023-09-27 11:29:37', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (2, 1, '2', 1, 0, NULL, 6071976419609675523, '2023-09-27 11:29:37', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (3, 1, '3', 1, 0, NULL, 6071976419609675523, '2023-09-27 11:29:37', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (4, 1, '4', 1, 0, NULL, 6071976419609675523, '2023-09-27 11:29:37', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (5, 1, '5', 1, 0, NULL, 6071976419609675523, '2023-09-27 11:29:37', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6, 1, '6', 1, 0, NULL, 6071976419609675523, '2023-09-27 11:29:37', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075391182620985096, 6, '1', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075391182620985097, 6, '2', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075391182620985098, 6, '3', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075391182620985099, 6, '4', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075391182620985100, 6, '5', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:39:26', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075405145760990985, 31, '1', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075405145760990986, 31, '2', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075405145760990987, 31, '3', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075405145760990988, 31, '4', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075405145760990989, 31, '5', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075405145760990990, 31, '6', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075412213599438601, 32, '1', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075412213599438602, 32, '2', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075412213599438603, 32, '3', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075412213599438604, 32, '4', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO "ccsp_device"."device_type_rela_value" VALUES (6075412213599438605, 32, '5', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439001, 41, '1', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439002, 41, '2', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439003, 41, '3', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439004, 41, '4', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439005, 41, '5', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439006, 41, '6', 1, 0, NULL, 6071976419609675523, '2023-09-27 14:46:22', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439007, 42, '1', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439008, 42, '2', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439009, 42, '3', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439010, 42, '4', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);
INSERT INTO ccsp_device.device_type_rela_value (ID, DEVICE_TYPE_ID, VALUE, VALUE_TYPE, DEFAULT_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6075412213599439011, 42, '5', 2, 0, NULL, 6071976419609675523, '2023-09-27 14:49:53', NULL, NULL);

-- ----------------------------
-- Table structure for device_type_route_config
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_type_route_config";
CREATE TABLE "ccsp_device"."device_type_route_config" (
  "id" int8 NOT NULL,
  "device_type_id" int8 NOT NULL,
  "net_pro" int4 NOT NULL,
  "url_start" varchar(1000) COLLATE "pg_catalog"."default",
  "support_allocate_app" int4 NOT NULL,
  "has_manage_route" int4 NOT NULL,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_type_route_config"."id" IS '主键';
COMMENT ON COLUMN "ccsp_device"."device_type_route_config"."device_type_id" IS '设备类型ID';
COMMENT ON COLUMN "ccsp_device"."device_type_route_config"."net_pro" IS '请求协议;1：TCP，2：HTTPS，3：HTTP';
COMMENT ON COLUMN "ccsp_device"."device_type_route_config"."url_start" IS 'URL前缀';
COMMENT ON COLUMN "ccsp_device"."device_type_route_config"."support_allocate_app" IS '是否分配应用;0：否，1：是';
COMMENT ON COLUMN "ccsp_device"."device_type_route_config"."has_manage_route" IS '是否有管理路由;0：否，1：是';
COMMENT ON COLUMN "ccsp_device"."device_type_route_config"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_type_route_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_type_route_config"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_type_route_config"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_type_route_config" IS '设备类型路由配置（1030）';

-- ----------------------------
-- Records of device_type_route_config
-- ----------------------------

-- ----------------------------
-- Table structure for device_vendor
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_vendor";
CREATE TABLE "ccsp_device"."device_vendor" (
  "vendor_id" int8 NOT NULL,
  "vendor_name" varchar(240) COLLATE "pg_catalog"."default" NOT NULL,
  "vendor_short_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "link_man" varchar(120) COLLATE "pg_catalog"."default",
  "link_man_phone" varchar(120) COLLATE "pg_catalog"."default",
  "default_flag" int4 NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_vendor"."vendor_id" IS '厂商ID';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."vendor_name" IS '厂商名称';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."vendor_short_name" IS '厂商简称';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."link_man" IS '联系人;加密存储';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."link_man_phone" IS '联系方式;加密存储';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."default_flag" IS '是否默认（1默认 0自定义）;1默认 0自定义 。默认的不可删除，可修改基础信息';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_vendor"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_vendor" IS '厂商信息表';

-- ----------------------------
-- Records of device_vendor
-- ----------------------------
INSERT INTO "ccsp_device"."device_vendor" VALUES (5466516874462629769, '三未信安科技股份有限公司', 'sansec', NULL, NULL, 1, 0, '默认厂商', 1234, '2023-03-01 14:08:18', 5469505260282187143, '2023-03-17 11:25:37');

-- ----------------------------
-- Table structure for device_vsm_net_config
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."device_vsm_net_config";
CREATE TABLE "ccsp_device"."device_vsm_net_config" (
  "id" int8 NOT NULL,
  "net_name" varchar(150) COLLATE "pg_catalog"."default",
  "net_card_name" varchar(150) COLLATE "pg_catalog"."default",
  "host_serialnum" varchar(80) COLLATE "pg_catalog"."default",
  "host_id" int8,
  "subdomain" varchar(80) COLLATE "pg_catalog"."default",
  "gateway" varchar(80) COLLATE "pg_catalog"."default" NOT NULL,
  "hccs_id" int8,
  "mask" varchar(80) COLLATE "pg_catalog"."default" NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."id" IS '网络配置ID';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."net_name" IS '网络名称';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."net_card_name" IS '网卡名称';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."host_serialnum" IS '关联宿主机序列号';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."host_id" IS '关联宿主机ID';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."subdomain" IS '网域';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."gateway" IS '网关';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."hccs_id" IS '超融合网络ID';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."mask" IS '子网掩码';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."device_vsm_net_config"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."device_vsm_net_config" IS '虚拟机网络配置（1030）';

-- ----------------------------
-- Records of device_vsm_net_config
-- ----------------------------

-- ----------------------------
-- Table structure for dic_device_api_template
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."dic_device_api_template";
CREATE TABLE "ccsp_device"."dic_device_api_template" (
  "id" int8 NOT NULL,
  "interaction_api_id" int8 NOT NULL,
  "sord_num" int4 NOT NULL,
  "api_serial_num" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "api_name" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "context_path" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "api_path" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."id" IS '设备接口模板ID';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."interaction_api_id" IS '所属设备交互类型ID';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."sord_num" IS '接口序号（展示顺序）';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."api_serial_num" IS '接口编号(同交互类型下唯一)';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."api_name" IS '接口名称';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."context_path" IS '环境路径';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."api_path" IS '接口路径';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."dic_device_api_template"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."dic_device_api_template" IS '设备接口模板字典表';

-- ----------------------------
-- Records of dic_device_api_template
-- ----------------------------
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (101, 1, 1, '1', 'getVsmInfo', '/', '/api/1.0/vsm', 0, '获取虚拟机信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (102, 1, 2, '2', 'getVsmStatus', '/', '/api/1.0/vsm/status', 0, '获取虚拟机运行状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (103, 1, 3, '3', 'configVsmNetwork', '/', '/api/1.0/vsm/network', 0, '配置虚拟机网络信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (104, 1, 4, '4', 'createVsm', '/', '/api/1.0/vsm', 0, '创建虚拟机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (105, 1, 5, '5', 'vsmOperate', '/', '/api/1.0/vsm', 0, '虚拟机启动、停止、重启、重置、删除请求', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (106, 1, 6, '6', 'getChsmInfo', '/', '/api/1.0/chsm', 0, '获取宿主机信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (107, 1, 7, '7', 'getChsmStatus', '/', '/api/1.0/chsm/status', 0, '获取宿主机设备运行状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (108, 1, 8, '8', 'configChsmPublicKey', '/', '/api/1.0/chsm/authpk', 0, '配置宿主机设备公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (109, 1, 9, '9', 'clearChsmPublicKey', '/', '/api/1.0/chsm/authpk', 0, '清楚宿主机设备公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (110, 1, 10, '10', 'getChsmPublicKeyFinger', '/', '/api/1.0/chsm/authpk', 0, '获取宿主机公钥指纹', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (111, 1, 30, '30', 'configVsm', '/', '/api/1.0/vsm/config', 0, '配置虚拟机信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (201, 2, 1, '1', 'getVsmInfo', '/', '/api/1.0/vsm', 0, '获取虚拟机信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (202, 2, 2, '2', 'getVsmStatus', '/', '/api/1.0/vsm/status', 0, '获取虚拟机运行状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (203, 2, 3, '3', 'configVsmNetwork', '/', '/api/1.0/vsm/network', 0, '配置虚拟机网络信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (204, 2, 4, '4', 'createVsm', '/', '/api/1.0/vsm', 0, '创建虚拟机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (205, 2, 5, '5', 'vsmOperate', '/', '/api/1.0/vsm', 0, '虚拟机启动、停止、重启、重置、删除请求', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (206, 2, 6, '6', 'getChsmInfo', '/', '/api/1.0/chsm', 0, '获取宿主机信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (207, 2, 7, '7', 'getChsmStatus', '/', '/api/1.0/chsm/status', 0, '获取宿主机设备运行状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (208, 2, 8, '8', 'configChsmPublicKey', '/', '/api/1.0/chsm/authpk', 0, '配置宿主机设备公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (209, 2, 9, '9', 'clearChsmPublicKey', '/', '/api/1.0/chsm/authpk', 0, '清楚宿主机设备公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (210, 2, 10, '10', 'getChsmPublicKeyFinger', '/', '/api/1.0/chsm/authpk', 0, '获取宿主机公钥指纹', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (211, 2, 30, '30', 'configVsm', '/', '/api/1.0/vsm/config', 0, '配置虚拟机信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2001, 20, 11, '11', 'getHsmInfo', '/pkiweb/sansecplat', '/api/1.0/chsm', 0, '获取物理机信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2002, 20, 12, '12', 'getHsmStatus', '/pkiweb/sansecplat', '/api/1.0/chsm/status', 0, '获取物理机设备状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2003, 20, 13, '13', 'configHsmNetwork', '/pkiweb/sansecplat', '/api/1.0/chsm/network', 0, '配置物理机网络', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2004, 20, 14, '14', 'restartHsm', '/pkiweb/sansecplat', '/api/1.0/chsm', 0, '重启物理机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2005, 20, 15, '15', 'configHsmPublicKey', '/pkiweb/sansecplat', '/api/1.0/chsm/authpk', 0, '配置物理机公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2006, 20, 16, '16', 'clearHsmPublicKey', '/pkiweb/sansecplat', '/api/1.0/chsm/authpk', 0, '清楚物理机公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2007, 20, 17, '17', 'getHsmPublicKeyFinger', '/pkiweb/sansecplat', '/api/1.0/chsm/authpk', 0, '获取物理机公钥指纹', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2008, 20, 18, '18', 'createHsmLmk', '/pkiweb/sansecplat/hsmm', '/key/masterKey/generateLMK', 0, '创建物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2009, 20, 19, '19', 'generateHsmLMKWithComponent', '/pkiweb/sansecplat/hsmm', '/key/masterKey/importLMK', 0, '分量合成物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2010, 20, 20, '20', 'getHsmDeviceKeyPair', '/pkiweb/sansecplat/hsmm', '/platform/key/gennerAsymmTempKey', 0, '获取物理机设备公私钥对', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2011, 20, 21, '21', 'exportHsmMasterKey', '/pkiweb/sansecplat/hsmm', '/platform/key/exportLMKByCipher', 0, '导出物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2012, 20, 22, '22', 'importHsmMasterKey', '/pkiweb/sansecplat/hsmm', '/platform/key/impoerLMKByCipher', 0, '导入物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2013, 20, 23, '23', 'getDeviceInitState', '/pkiweb/sansecplat', '/api/1.0/chsm/init/status', 0, '获取设备初始化状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2014, 20, 24, '24', 'deviceInit', '/pkiweb/sansecplat', '/api/1.0/chsm/init', 0, '设备初始化', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2015, 20, 25, '25', 'importAsyncKeyPair', '/pkiweb/sansecplat/hsmm', '/platform/key/importAsyncKeyPair', 0, '导入非对称密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2016, 20, 26, '26', 'importSyncKeyPair', '/pkiweb/sansecplat/hsmm', '/platform/key/importSyncKeyPair', 0, '导入对称密钥分量', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2017, 20, 27, '27', 'deleteSymmetricKey', '/pkiweb/sansecplat/hsmm', '/key/symmetricKey/deleteSymmetricKey', 0, '删除对称密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2018, 20, 28, '28', 'deleteSM2Key', '/pkiweb/sansecplat/hsmm', '/key/sm2Key/deleteSM2Key', 0, '删除sm2密钥对', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2019, 20, 29, '29', 'getHsmServiceStatus', '/PlatformServlet', '?method=getServiceState', 0, '获取物理机服务状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2101, 21, 101, '101', 'getHsmInfo', '/PlatformServlet', '?method=getHsmInfo', 0, '获取物理机信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2102, 21, 102, '102', 'getHsmStatus', '/PlatformServlet', '?method=getState', 0, '获取物理机设备状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2103, 21, 103, '103', 'configHsmNetwork', '/PlatformServlet', '?method=network', 0, '配置物理机网络', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2104, 21, 104, '104', 'restartHsm', '/PlatformServlet', '?method=restartHsm', 0, '重启物理机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2105, 21, 105, '105', 'configHsmPublicKey', '/AuthServlet', '?method=authPK', 0, '配置物理机公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2106, 21, 106, '106', 'clearHsmPublicKey', '/AuthServlet', '?method=cleanPK', 0, '清楚物理机公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2107, 21, 107, '107', 'getHsmPublicKeyFinger', '/AuthServlet', '?method=getAuthPKFingerprints', 0, '获取物理机公钥指纹', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2108, 21, 108, '108', 'createHsmLmk', '/PlatformServlet', '?method=generateLMK', 0, '创建物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2109, 21, 109, '109', 'generateHsmLMKWithComponent', '/PlatformServlet', '?method=importLMK', 0, '分量合成物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2110, 21, 110, '110', 'getHsmDeviceKeyPair', '/PlatformServlet', '?method=gennerAsymmTempKey', 0, '获取物理机设备公私钥对', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2111, 21, 111, '111', 'exportHsmMasterKey', '/PlatformServlet', '?method=exportLMKByCipher', 0, '导出物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2112, 21, 112, '112', 'importHsmMasterKey', '/PlatformServlet', '?method=importLMKByCipher', 0, '导入物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2113, 21, 113, '113', 'getDeviceInitState', '/PlatformServlet', '?method=initStatus', 0, '获取设备初始化状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2114, 21, 114, '114', 'deviceInit', '/PlatformServlet', '?method=doHsmInit', 0, '设备初始化', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2115, 21, 115, '115', 'importAsyncKeyPair', '/PlatformServlet', '?method=importAsyncKeyPair', 0, '导入非对称密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2116, 21, 116, '116', 'importSyncKeyPair', '/PlatformServlet', '?method=importSymmKey', 0, '导入对称密钥分量', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2117, 21, 117, '117', 'deleteSymmetricKey', '/PlatformServlet', '?method=delHsmSymmetricKey', 0, '删除对称密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2118, 21, 118, '118', 'deleteSM2Key', '/PlatformServlet', '?method=delHsmKeyPair', 0, '删除sm2密钥对', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (2119, 21, 119, '119', 'getHsmServiceStatus', '/PlatformServlet', '?method=getServiceState', 0, '获取物理机服务状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3001, 30, 11, '11', 'getHsmInfo', '/pkiweb/sansecplat', '/api/1.0/chsm', 0, '获取物理机信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3002, 30, 12, '12', 'getHsmStatus', '/pkiweb/sansecplat', '/api/1.0/chsm/status', 0, '获取物理机设备状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3003, 30, 13, '13', 'configHsmNetwork', '/pkiweb/sansecplat', '/api/1.0/chsm/network', 0, '配置物理机网络', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3004, 30, 14, '14', 'restartHsm', '/pkiweb/sansecplat', '/api/1.0/chsm', 0, '重启物理机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3005, 30, 15, '15', 'configHsmPublicKey', '/pkiweb/sansecplat', '/api/1.0/chsm/authpk', 0, '配置物理机公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3006, 30, 16, '16', 'clearHsmPublicKey', '/pkiweb/sansecplat', '/api/1.0/chsm/authpk', 0, '清楚物理机公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3007, 30, 17, '17', 'getHsmPublicKeyFinger', '/pkiweb/sansecplat', '/api/1.0/chsm/authpk', 0, '获取物理机公钥指纹', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3008, 30, 18, '18', 'createHsmLmk', '/pkiweb/sansecplat/hsmm', '/key/masterKey/generateLMK', 0, '创建物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3009, 30, 19, '19', 'generateHsmLMKWithComponent', '/pkiweb/sansecplat/hsmm', '/key/masterKey/importLMK', 0, '分量合成物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3010, 30, 20, '20', 'getHsmDeviceKeyPair', '/pkiweb/sansecplat/hsmm', '/platform/key/gennerAsymmTempKey', 0, '获取物理机设备公私钥对', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3011, 30, 21, '21', 'exportHsmMasterKey', '/pkiweb/sansecplat/hsmm', '/platform/key/exportLMKByCipher', 0, '导出物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3012, 30, 22, '22', 'importHsmMasterKey', '/pkiweb/sansecplat/hsmm', '/platform/key/impoerLMKByCipher', 0, '导入物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3013, 30, 23, '23', 'getDeviceInitState', '/pkiweb/sansecplat', '/api/1.0/chsm/init/status', 0, '获取设备初始化状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3014, 30, 24, '24', 'deviceInit', '/pkiweb/sansecplat', '/api/1.0/chsm/init', 0, '设备初始化', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3015, 30, 25, '25', 'importAsyncKeyPair', '/pkiweb/sansecplat/hsmm', '/platform/key/importAsyncKeyPair', 0, '导入非对称密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3016, 30, 26, '26', 'importSyncKeyPair', '/pkiweb/sansecplat/hsmm', '/platform/key/importSyncKeyPair', 0, '导入对称密钥分量', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3017, 30, 27, '27', 'deleteSymmetricKey', '/pkiweb/sansecplat/hsmm', '/key/symmetricKey/deleteSymmetricKey', 0, '删除对称密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3018, 30, 28, '28', 'deleteSM2Key', '/pkiweb/sansecplat/hsmm', '/key/sm2Key/deleteSM2Key', 0, '删除sm2密钥对', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3019, 30, 29, '29', 'getHsmServiceStatus', '/PlatformServlet', '?method=getServiceState', 0, '获取物理机服务状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3101, 31, 101, '101', 'getHsmInfo', '/PlatformServlet', '?method=getHsmInfo', 0, '获取物理机信息', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3102, 31, 102, '102', 'getHsmStatus', '/PlatformServlet', '?method=getState', 0, '获取物理机设备状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3103, 31, 103, '103', 'configHsmNetwork', '/PlatformServlet', '?method=network', 0, '配置物理机网络', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3104, 31, 104, '104', 'restartHsm', '/PlatformServlet', '?method=restartHsm', 0, '重启物理机', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3105, 31, 105, '105', 'configHsmPublicKey', '/AuthServlet', '?method=authPK', 0, '配置物理机公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3106, 31, 106, '106', 'clearHsmPublicKey', '/AuthServlet', '?method=cleanPK', 0, '清楚物理机公钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3107, 31, 107, '107', 'getHsmPublicKeyFinger', '/AuthServlet', '?method=getAuthPKFingerprints', 0, '获取物理机公钥指纹', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3108, 31, 108, '108', 'createHsmLmk', '/PlatformServlet', '?method=generateLMK', 0, '创建物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3109, 31, 109, '109', 'generateHsmLMKWithComponent', '/PlatformServlet', '?method=importLMK', 0, '分量合成物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3110, 31, 110, '110', 'getHsmDeviceKeyPair', '/PlatformServlet', '?method=gennerAsymmTempKey', 0, '获取物理机设备公私钥对', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3111, 31, 111, '111', 'exportHsmMasterKey', '/PlatformServlet', '?method=exportLMKByCipher', 0, '导出物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3112, 31, 112, '112', 'importHsmMasterKey', '/PlatformServlet', '?method=importLMKByCipher', 0, '导入物理机设备主密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3113, 31, 113, '113', 'getDeviceInitState', '/PlatformServlet', '?method=initStatus', 0, '获取设备初始化状态', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3114, 31, 114, '114', 'deviceInit', '/PlatformServlet', '?method=doHsmInit', 0, '设备初始化', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3115, 31, 115, '115', 'importAsyncKeyPair', '/PlatformServlet', '?method=importAsyncKeyPair', 0, '导入非对称密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3116, 31, 116, '116', 'importSyncKeyPair', '/PlatformServlet', '?method=importSymmKey', 0, '导入对称密钥分量', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3117, 31, 117, '117', 'deleteSymmetricKey', '/PlatformServlet', '?method=delHsmSymmetricKey', 0, '删除对称密钥', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3118, 31, 118, '118', 'deleteSM2Key', '/PlatformServlet', '?method=delHsmKeyPair', 0, '删除sm2密钥对', NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_api_template" VALUES (3119, 31, 119, '119', 'getHsmServiceStatus', '/PlatformServlet', '?method=getServiceState', 0, '获取物理机服务状态', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_device_image_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."dic_device_image_type";
CREATE TABLE "ccsp_device"."dic_device_image_type" (
  "id" int8 NOT NULL,
  "image_name" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "image_value" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "image_version" varchar(100) COLLATE "pg_catalog"."default",
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "vendor_id" int8,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."id" IS '镜像类型ID';
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."image_name" IS '镜像名称';
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."image_value" IS '镜像值';
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."image_version" IS '镜像版本';
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."vendor_id" IS '厂商id';
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."dic_device_image_type"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."dic_device_image_type" IS '虚拟机镜像类型字典表';

-- ----------------------------
-- Records of dic_device_image_type
-- ----------------------------
INSERT INTO "ccsp_device"."dic_device_image_type" VALUES (1, '密钥管理服务器', 'kms', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_image_type" VALUES (2, '服务器密码机', 'hsm', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_image_type" VALUES (3, '签名验签服务器', 'svs', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_image_type" VALUES (4, '金融密码机', 'phsm', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_image_type" VALUES (5, '动态令牌服务器', 'sms', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_image_type" VALUES (6, '数字证书认证服务器', 'sca', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_device_interaction_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."dic_device_interaction_type";
CREATE TABLE "ccsp_device"."dic_device_interaction_type" (
  "id" int8 NOT NULL,
  "interaction_name" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "interaction_serial_number" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "family_type" int4 NOT NULL
)
;
COMMENT ON COLUMN "ccsp_device"."dic_device_interaction_type"."id" IS '设备交互类型ID';
COMMENT ON COLUMN "ccsp_device"."dic_device_interaction_type"."interaction_name" IS '接口交互类型名称';
COMMENT ON COLUMN "ccsp_device"."dic_device_interaction_type"."interaction_serial_number" IS '接口交互类型序编号';
COMMENT ON COLUMN "ccsp_device"."dic_device_interaction_type"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."dic_device_interaction_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."dic_device_interaction_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."dic_device_interaction_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."dic_device_interaction_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."dic_device_interaction_type"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_device"."dic_device_interaction_type"."family_type" IS '所属设备类型（设备类型(1：云密码机:0：物理机和虚拟机)）';
COMMENT ON TABLE "ccsp_device"."dic_device_interaction_type" IS '设备交互类型字典表';

-- ----------------------------
-- Records of dic_device_interaction_type
-- ----------------------------
INSERT INTO "ccsp_device"."dic_device_interaction_type" VALUES (1, '三未标准云密码机0088管理规范', 'chsm_sansec_standard_0088', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 1);
INSERT INTO "ccsp_device"."dic_device_interaction_type" VALUES (2, '三未旧云密码机0088管理规范', 'chsm_sansec_old_0088', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 1);
INSERT INTO "ccsp_device"."dic_device_interaction_type" VALUES (20, '三未虚拟机统一web平台管理规范', 'vsm_sansec_unified_1001', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 3);
INSERT INTO "ccsp_device"."dic_device_interaction_type" VALUES (21, '三未虚拟机V5密码机管理规范', 'vsm_sansec_1002', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 3);
INSERT INTO "ccsp_device"."dic_device_interaction_type" VALUES (22, '密服一体虚拟机管理规范', 'vsm_extra_standard_1003', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 3);
INSERT INTO "ccsp_device"."dic_device_interaction_type" VALUES (30, '三未物理机统一web平台管理规范', 'hsm_sansec_unified_1001', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 2);
INSERT INTO "ccsp_device"."dic_device_interaction_type" VALUES (31, '三未物理密码机管理规范', 'hsm_sansec_1002', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 2);
INSERT INTO "ccsp_device"."dic_device_interaction_type" VALUES (32, '密服一体物理机管理规范', 'hsm_extra_standard_1003', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 2);

-- ----------------------------
-- Table structure for dic_device_machine_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."dic_device_machine_type";
CREATE TABLE "ccsp_device"."dic_device_machine_type" (
  "id" int8 NOT NULL,
  "server_name" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "server_code" int4 NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "family_type" int4 NOT NULL
)
;
COMMENT ON COLUMN "ccsp_device"."dic_device_machine_type"."id" IS '设备类型ID';
COMMENT ON COLUMN "ccsp_device"."dic_device_machine_type"."server_name" IS '设备类型名称';
COMMENT ON COLUMN "ccsp_device"."dic_device_machine_type"."server_code" IS '设备类型值';
COMMENT ON COLUMN "ccsp_device"."dic_device_machine_type"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."dic_device_machine_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."dic_device_machine_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."dic_device_machine_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."dic_device_machine_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."dic_device_machine_type"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_device"."dic_device_machine_type"."family_type" IS '所属设备类型（设备类型(1：云密码机:0：物理机和虚拟机)）';
COMMENT ON TABLE "ccsp_device"."dic_device_machine_type" IS '密码机服务类型字典表';

-- ----------------------------
-- Records of dic_device_machine_type
-- ----------------------------
INSERT INTO "ccsp_device"."dic_device_machine_type" VALUES (1, '云密码机', 1, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 1);
INSERT INTO "ccsp_device"."dic_device_machine_type" VALUES (2, '服务器密码机', 2, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 0);
INSERT INTO "ccsp_device"."dic_device_machine_type" VALUES (3, '签名验签服务器', 3, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 0);
INSERT INTO "ccsp_device"."dic_device_machine_type" VALUES (4, '时间戳密码机', 4, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 0);
INSERT INTO "ccsp_device"."dic_device_machine_type" VALUES (5, '金融密码机', 5, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 0);
INSERT INTO "ccsp_device"."dic_device_machine_type" VALUES (6, '网关服务器', 6, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 0);

-- ----------------------------
-- Table structure for dic_device_management_status
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."dic_device_management_status";
CREATE TABLE "ccsp_device"."dic_device_management_status" (
  "id" int8 NOT NULL,
  "status_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "status_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."dic_device_management_status"."id" IS '操作状态ID';
COMMENT ON COLUMN "ccsp_device"."dic_device_management_status"."status_name" IS '状态名';
COMMENT ON COLUMN "ccsp_device"."dic_device_management_status"."status_code" IS '状态值';
COMMENT ON COLUMN "ccsp_device"."dic_device_management_status"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."dic_device_management_status"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."dic_device_management_status"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."dic_device_management_status"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."dic_device_management_status"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."dic_device_management_status"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."dic_device_management_status" IS '密码机使用状态字典表';

-- ----------------------------
-- Records of dic_device_management_status
-- ----------------------------
INSERT INTO "ccsp_device"."dic_device_management_status" VALUES (1, '标记使用', '1', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_management_status" VALUES (2, '未使用', '2', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_management_status" VALUES (3, '平台使用', '3', 0, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_device_operation_status
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."dic_device_operation_status";
CREATE TABLE "ccsp_device"."dic_device_operation_status" (
  "id" int8 NOT NULL,
  "oper_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "oper_code" int4 NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."dic_device_operation_status"."id" IS '操作状态ID';
COMMENT ON COLUMN "ccsp_device"."dic_device_operation_status"."oper_name" IS '状态名称';
COMMENT ON COLUMN "ccsp_device"."dic_device_operation_status"."oper_code" IS '状态值';
COMMENT ON COLUMN "ccsp_device"."dic_device_operation_status"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_device"."dic_device_operation_status"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."dic_device_operation_status"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."dic_device_operation_status"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."dic_device_operation_status"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."dic_device_operation_status"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."dic_device_operation_status" IS '密码机操作状态字典表';

-- ----------------------------
-- Records of dic_device_operation_status
-- ----------------------------
INSERT INTO "ccsp_device"."dic_device_operation_status" VALUES (0, '运行中', 0, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_operation_status" VALUES (1, '创建中', 1, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_operation_status" VALUES (2, '创建失败', 2, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_operation_status" VALUES (3, '启动中', 3, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_operation_status" VALUES (4, '重启中', 4, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_operation_status" VALUES (5, '停止中', 5, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_operation_status" VALUES (6, '删除中', 6, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_operation_status" VALUES (7, '删除失败', 7, 0, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_device_vsm_resource
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."dic_device_vsm_resource";
CREATE TABLE "ccsp_device"."dic_device_vsm_resource" (
  "id" int8 NOT NULL,
  "resource_value" varchar(50) COLLATE "pg_catalog"."default",
  "resource_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."dic_device_vsm_resource"."id" IS '主键';
COMMENT ON COLUMN "ccsp_device"."dic_device_vsm_resource"."resource_value" IS '虚拟机资源配置值';
COMMENT ON COLUMN "ccsp_device"."dic_device_vsm_resource"."resource_name" IS '虚拟机资源配置名称';
COMMENT ON COLUMN "ccsp_device"."dic_device_vsm_resource"."invalid_flag" IS '是否有效，0有效，1无效，默认0';
COMMENT ON COLUMN "ccsp_device"."dic_device_vsm_resource"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."dic_device_vsm_resource"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."dic_device_vsm_resource"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."dic_device_vsm_resource"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."dic_device_vsm_resource"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."dic_device_vsm_resource" IS '虚拟机资源配置字典表（1030）';

-- ----------------------------
-- Records of dic_device_vsm_resource
-- ----------------------------
INSERT INTO "ccsp_device"."dic_device_vsm_resource" VALUES (1, '1', '一倍虚机资源', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_vsm_resource" VALUES (2, '2', '二倍虚机资源', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_vsm_resource" VALUES (3, '4', '四倍虚机资源', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_vsm_resource" VALUES (4, '8', '八倍虚机资源', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_device_vsm_resource" VALUES (5, '0', '动态分配', 0, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_snmp_authentication
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."dic_snmp_authentication";
CREATE TABLE "ccsp_device"."dic_snmp_authentication" (
  "id" int8,
  "snmp_authentication_id" int4,
  "snmp_authentication_name" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."dic_snmp_authentication"."id" IS 'id';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_authentication"."snmp_authentication_id" IS '认证方式id;与监控组件保持同步';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_authentication"."snmp_authentication_name" IS '认证方式名称;与监控组件同步';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_authentication"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_authentication"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_authentication"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_authentication"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_authentication"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."dic_snmp_authentication" IS 'snmp认证方式字典表(1030)';

-- ----------------------------
-- Records of dic_snmp_authentication
-- ----------------------------
INSERT INTO "ccsp_device"."dic_snmp_authentication" VALUES (0, 0, 'MD5', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_snmp_authentication" VALUES (1, 1, 'SHA', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_snmp_authentication" VALUES (2, 2, 'SHA224', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_snmp_authentication" VALUES (3, 3, 'SHA256', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_snmp_authentication" VALUES (4, 4, 'SHA384', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_snmp_authentication" VALUES (5, 5, 'SHA512', NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for dic_snmp_privacy
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."dic_snmp_privacy";
CREATE TABLE "ccsp_device"."dic_snmp_privacy" (
  "id" int8,
  "snmp_privacy_id" int4,
  "snmp_privacy_name" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."dic_snmp_privacy"."id" IS 'id';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_privacy"."snmp_privacy_id" IS '认证方式id;与监控组件保持同步';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_privacy"."snmp_privacy_name" IS '认证方式名称;与监控组件同步';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_privacy"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_privacy"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_privacy"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_privacy"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."dic_snmp_privacy"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_device"."dic_snmp_privacy" IS 'snmp加密方式字典表(1030)';

-- ----------------------------
-- Records of dic_snmp_privacy
-- ----------------------------
INSERT INTO "ccsp_device"."dic_snmp_privacy" VALUES (0, 0, 'DES', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_snmp_privacy" VALUES (1, 1, 'TRIPLE_DES', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_snmp_privacy" VALUES (2, 2, 'AES', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_snmp_privacy" VALUES (3, 3, 'AES192', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_device"."dic_snmp_privacy" VALUES (4, 4, 'AES256', NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."sys_job";
CREATE TABLE "ccsp_device"."sys_job" (
  "job_id" int8 NOT NULL,
  "job_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "job_group" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "server_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "method_url" varchar(1500) COLLATE "pg_catalog"."default",
  "json_param" text COLLATE "pg_catalog"."default" NOT NULL,
  "cron_expression" varchar(255) COLLATE "pg_catalog"."default",
  "misfire_policy" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 3,
  "concurrent" varchar(1) COLLATE "pg_catalog"."default" DEFAULT 1,
  "job_status" varchar(1) COLLATE "pg_catalog"."default" DEFAULT 0,
  "created_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "updated_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(1500) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."sys_job"."job_id" IS '任务号';
COMMENT ON COLUMN "ccsp_device"."sys_job"."job_name" IS '任务名称';
COMMENT ON COLUMN "ccsp_device"."sys_job"."job_group" IS '任务组名';
COMMENT ON COLUMN "ccsp_device"."sys_job"."server_id" IS '服务模块';
COMMENT ON COLUMN "ccsp_device"."sys_job"."method_url" IS '调用接口';
COMMENT ON COLUMN "ccsp_device"."sys_job"."json_param" IS 'json格式参数';
COMMENT ON COLUMN "ccsp_device"."sys_job"."cron_expression" IS 'CRON执行表达式';
COMMENT ON COLUMN "ccsp_device"."sys_job"."misfire_policy" IS '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）';
COMMENT ON COLUMN "ccsp_device"."sys_job"."concurrent" IS '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）';
COMMENT ON COLUMN "ccsp_device"."sys_job"."job_status" IS '状态（0正常 1暂停）';
COMMENT ON COLUMN "ccsp_device"."sys_job"."created_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."sys_job"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."sys_job"."updated_by" IS '更新人';
COMMENT ON COLUMN "ccsp_device"."sys_job"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_device"."sys_job"."remark" IS '备注';
COMMENT ON TABLE "ccsp_device"."sys_job" IS '定时任务表';

-- ----------------------------
-- Records of sys_job
-- ----------------------------

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."sys_job_log";
CREATE TABLE "ccsp_device"."sys_job_log" (
  "job_log_id" int8 NOT NULL,
  "job_id" int8 NOT NULL,
  "job_message" varchar(1500) COLLATE "pg_catalog"."default",
  "status" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_info" varchar(6000) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "trigger_time" int8
)
;
COMMENT ON COLUMN "ccsp_device"."sys_job_log"."job_log_id" IS '任务日志ID';
COMMENT ON COLUMN "ccsp_device"."sys_job_log"."job_id" IS '任务ID';
COMMENT ON COLUMN "ccsp_device"."sys_job_log"."job_message" IS '日志信息';
COMMENT ON COLUMN "ccsp_device"."sys_job_log"."status" IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN "ccsp_device"."sys_job_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "ccsp_device"."sys_job_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."sys_job_log"."trigger_time" IS '触发时间';
COMMENT ON TABLE "ccsp_device"."sys_job_log" IS '定时任务执行日志表';

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."sys_task";
CREATE TABLE "ccsp_device"."sys_task" (
  "task_id" int8 NOT NULL,
  "task_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "task_group" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "server_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "method_url" varchar(1000) COLLATE "pg_catalog"."default",
  "json_param" text COLLATE "pg_catalog"."default" NOT NULL,
  "task_status" int4 NOT NULL DEFAULT 0,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(300) COLLATE "pg_catalog"."default",
  "timeout" int4,
  "start_time" varchar(255) COLLATE "pg_catalog"."default",
  "end_time" varchar(255) COLLATE "pg_catalog"."default",
  "policy" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "ccsp_device"."sys_task"."task_id" IS '任务号';
COMMENT ON COLUMN "ccsp_device"."sys_task"."task_name" IS '任务名称';
COMMENT ON COLUMN "ccsp_device"."sys_task"."task_group" IS '任务组名;执行任务串行';
COMMENT ON COLUMN "ccsp_device"."sys_task"."server_id" IS '服务模块';
COMMENT ON COLUMN "ccsp_device"."sys_task"."method_url" IS '调用接口';
COMMENT ON COLUMN "ccsp_device"."sys_task"."json_param" IS 'json格式参数';
COMMENT ON COLUMN "ccsp_device"."sys_task"."task_status" IS '状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时)';
COMMENT ON COLUMN "ccsp_device"."sys_task"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_device"."sys_task"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_device"."sys_task"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_device"."sys_task"."timeout" IS '超时时间;单位秒';
COMMENT ON COLUMN "ccsp_device"."sys_task"."start_time" IS '开始时间';
COMMENT ON COLUMN "ccsp_device"."sys_task"."end_time" IS '结束时间';
COMMENT ON COLUMN "ccsp_device"."sys_task"."policy" IS '是否允许重复执行;0-不允许，1允许';
COMMENT ON TABLE "ccsp_device"."sys_task" IS '异步任务表';

-- ----------------------------
-- Records of sys_task
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task_log
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."sys_task_log";
CREATE TABLE "ccsp_device"."sys_task_log" (
  "task_log_id" int8 NOT NULL,
  "task_id" int8 NOT NULL,
  "task_message" varchar(3000) COLLATE "pg_catalog"."default",
  "status" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_info" varchar(6000) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "trigger_time" int8
)
;
COMMENT ON COLUMN "ccsp_device"."sys_task_log"."task_log_id" IS '任务日志ID';
COMMENT ON COLUMN "ccsp_device"."sys_task_log"."task_id" IS '任务ID';
COMMENT ON COLUMN "ccsp_device"."sys_task_log"."task_message" IS '日志信息';
COMMENT ON COLUMN "ccsp_device"."sys_task_log"."status" IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN "ccsp_device"."sys_task_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "ccsp_device"."sys_task_log"."create_time" IS '创建时间;单位毫秒';
COMMENT ON COLUMN "ccsp_device"."sys_task_log"."trigger_time" IS '触发时间;任务服务上送';
COMMENT ON TABLE "ccsp_device"."sys_task_log" IS '异步任务执行日志表';

-- ----------------------------
-- Records of sys_task_log
-- ----------------------------

-- ----------------------------
-- Table structure for tenant_key
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_device"."tenant_key";
CREATE TABLE "ccsp_device"."tenant_key" (
  "id" int8 NOT NULL,
  "tenant_id" int8 NOT NULL,
  "key_type" int4 NOT NULL,
  "content" varchar(1000) COLLATE "pg_catalog"."default" NOT NULL,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_device"."tenant_key"."id" IS '主键';
COMMENT ON COLUMN "ccsp_device"."tenant_key"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ccsp_device"."tenant_key"."key_type" IS '密钥类型：1-2号非对称公钥，2-2号非对称私钥，3-2号对称密钥分量';
COMMENT ON COLUMN "ccsp_device"."tenant_key"."content" IS '密钥内容';
COMMENT ON COLUMN "ccsp_device"."tenant_key"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_device"."tenant_key"."create_time" IS '创建时间';
COMMENT ON TABLE "ccsp_device"."tenant_key" IS '租户密钥关系表';

-- ----------------------------
-- Records of tenant_key
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table device_api
-- ----------------------------
ALTER TABLE "ccsp_device"."device_api" ADD CONSTRAINT "device_api_pkey" PRIMARY KEY ("api_id");

-- ----------------------------
-- Primary Key structure for table device_api_record
-- ----------------------------
ALTER TABLE "ccsp_device"."device_api_record" ADD CONSTRAINT "device_api_record_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_busitype
-- ----------------------------
ALTER TABLE "ccsp_device"."device_busitype" ADD CONSTRAINT "device_busitype_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_group
-- ----------------------------
ALTER TABLE "ccsp_device"."device_group" ADD CONSTRAINT "device_group_pkey" PRIMARY KEY ("device_group_id");

-- ----------------------------
-- Primary Key structure for table device_group_to_busi_type
-- ----------------------------
ALTER TABLE "ccsp_device"."device_group_to_busi_type" ADD CONSTRAINT "device_group_to_busi_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_info
-- ----------------------------
ALTER TABLE "ccsp_device"."device_info" ADD CONSTRAINT "device_info_pkey" PRIMARY KEY ("device_id");

-- ----------------------------
-- Primary Key structure for table device_master_cover_record
-- ----------------------------
ALTER TABLE "ccsp_device"."device_master_cover_record" ADD CONSTRAINT "device_master_cover_record_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_monitor_config
-- ----------------------------
ALTER TABLE "ccsp_device"."device_monitor_config" ADD CONSTRAINT "device_monitor_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_net_detail
-- ----------------------------
ALTER TABLE "ccsp_device"."device_net_detail" ADD CONSTRAINT "device_net_detail_pkey" PRIMARY KEY ("ip_id");

-- ----------------------------
-- Primary Key structure for table device_snmp_oid_config
-- ----------------------------
ALTER TABLE "ccsp_device"."device_snmp_oid_config" ADD CONSTRAINT "device_snmp_oid_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_type
-- ----------------------------
ALTER TABLE "ccsp_device"."device_type" ADD CONSTRAINT "device_type_pkey" PRIMARY KEY ("device_type_id");

-- ----------------------------
-- Primary Key structure for table device_type_manage_path
-- ----------------------------
ALTER TABLE "ccsp_device"."device_type_manage_path" ADD CONSTRAINT "device_type_manage_path_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_type_rela_value
-- ----------------------------
ALTER TABLE "ccsp_device"."device_type_rela_value" ADD CONSTRAINT "device_type_rela_value_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_type_route_config
-- ----------------------------
ALTER TABLE "ccsp_device"."device_type_route_config" ADD CONSTRAINT "device_type_route_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_vendor
-- ----------------------------
ALTER TABLE "ccsp_device"."device_vendor" ADD CONSTRAINT "device_vendor_pkey" PRIMARY KEY ("vendor_id");

-- ----------------------------
-- Primary Key structure for table device_vsm_net_config
-- ----------------------------
ALTER TABLE "ccsp_device"."device_vsm_net_config" ADD CONSTRAINT "device_vsm_net_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_device_api_template
-- ----------------------------
ALTER TABLE "ccsp_device"."dic_device_api_template" ADD CONSTRAINT "dic_device_api_template_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_device_image_type
-- ----------------------------
ALTER TABLE "ccsp_device"."dic_device_image_type" ADD CONSTRAINT "dic_device_image_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_device_interaction_type
-- ----------------------------
ALTER TABLE "ccsp_device"."dic_device_interaction_type" ADD CONSTRAINT "dic_device_interaction_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_device_machine_type
-- ----------------------------
ALTER TABLE "ccsp_device"."dic_device_machine_type" ADD CONSTRAINT "dic_device_machine_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_device_management_status
-- ----------------------------
ALTER TABLE "ccsp_device"."dic_device_management_status" ADD CONSTRAINT "dic_device_management_status_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_device_operation_status
-- ----------------------------
ALTER TABLE "ccsp_device"."dic_device_operation_status" ADD CONSTRAINT "dic_device_operation_status_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_device_vsm_resource
-- ----------------------------
ALTER TABLE "ccsp_device"."dic_device_vsm_resource" ADD CONSTRAINT "dic_device_vsm_resource_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_job
-- ----------------------------
ALTER TABLE "ccsp_device"."sys_job" ADD CONSTRAINT "sys_job_pkey" PRIMARY KEY ("job_id");

-- ----------------------------
-- Primary Key structure for table sys_job_log
-- ----------------------------
ALTER TABLE "ccsp_device"."sys_job_log" ADD CONSTRAINT "sys_job_log_pkey" PRIMARY KEY ("job_log_id");

-- ----------------------------
-- Primary Key structure for table sys_task
-- ----------------------------
ALTER TABLE "ccsp_device"."sys_task" ADD CONSTRAINT "sys_task_pkey" PRIMARY KEY ("task_id");

-- ----------------------------
-- Primary Key structure for table sys_task_log
-- ----------------------------
ALTER TABLE "ccsp_device"."sys_task_log" ADD CONSTRAINT "sys_task_log_pkey" PRIMARY KEY ("task_log_id");

-- ----------------------------
-- Primary Key structure for table tenant_key
-- ----------------------------
ALTER TABLE "ccsp_device"."tenant_key" ADD CONSTRAINT "tenant_key_pkey" PRIMARY KEY ("id");
