package com.sansec.ccsp.pt.upgrade.v3211_v331.service;

import com.sansec.common.param.response.SecRestResponse;

/**
 * <AUTHOR>
 */
public interface V3211V331Service {
    /**
     * 可以反复执行的洗数据升级处理
     */
    SecRestResponse<Object> handle1();
    /**
     * 不可以反复执行的洗数据升级处理
     */
    SecRestResponse<Object> handle2();

    /**
     * 不可以反复执行的洗数据升级处理
     */
    SecRestResponse<Object> handle3();
}
