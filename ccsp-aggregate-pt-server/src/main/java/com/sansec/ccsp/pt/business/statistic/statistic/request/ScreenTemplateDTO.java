package com.sansec.ccsp.pt.business.statistic.statistic.request;

import com.sansec.ccsp.pt.common.error.SecErrorCodeConstant;
import com.sansec.common.exception.BusinessException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Describe 大屏通用组件查询
 * <AUTHOR>
 * @Date 2023/7/7 17:35
 **/
@Data
public class ScreenTemplateDTO {

    private String indexType;

    private String tenantCode;

    private String templateCode;

    public void check() {
        if (StringUtils.isBlank(indexType)) {
            throw new BusinessException(SecErrorCodeConstant.FIELD_NOT_ALLOW_NULL, true,"indexType");
        }
    }
}
