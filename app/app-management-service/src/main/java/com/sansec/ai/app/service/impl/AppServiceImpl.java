package com.sansec.ai.app.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.sansec.ai.app.request.*;
import com.sansec.ai.app.service.AppService;
import com.sansec.ai.common.base.enums.TokenTypeEnum;
import com.sansec.ai.common.base.utils.TokenUtil;
import com.sansec.ai.common.service.SysConfigService;
import com.sansec.ai.extra.dify.constant.DifyConfigEnum;
import com.sansec.ai.extra.dify.entity.AppEntity;
import com.sansec.ai.extra.dify.entity.ConversationEntity;
import com.sansec.ai.extra.dify.request.DifyInsInfo;
import com.sansec.ai.extra.dify.service.DifyApiService;
import com.sansec.ai.portal.constant.PortalErrorCode;
import com.sansec.ai.portal.entity.vo.DifyInstanceInfoVo;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;
import com.sansec.ai.portal.service.IPortalService;
import com.sansec.ai.user.service.PortalUserService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecPageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.zip.GZIPOutputStream;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Service
@Slf4j
public class AppServiceImpl implements AppService, InitializingBean {

    @Autowired
    private DifyApiService difyApiService;
    @Autowired
    private IPortalService portalService;
    @Autowired
    private PortalUserService portalUserService;
	@Autowired
	private SysConfigService sysConfigService;

	/**
	 * 默认应用ID
	 */
	private String defaultAppId;

	/**
	 * 在Bean初始化完成后执行，从系统配置中获取业务数据库和插件数据库模板名称
	 *
	 * @throws Exception
	 */
	@Override
	public void afterPropertiesSet() throws Exception {
		defaultAppId = sysConfigService.getConfigValue(DifyConfigEnum.DEFAULT_APP_ID);
	}

    @Override
    public SecPageVO<AppEntity> getAppList(AppListDTO appListDTO) {
        log.info("Fetching app list for user with ID: {}", 1);
        long userId = TokenUtil.getUserId(TokenTypeEnum.AI_PORTAL_TOKEN);
        Long portalId = portalUserService.get(userId).getPortalId();
        DifyInstanceInfoVo difyInstanceInfoVo = portalService.getDifyInstanceInfoByPortalId(portalId);
        DifyInsInfo difyInsInfo = new DifyInsInfo(difyInstanceInfoVo.getInstanceIp(), difyInstanceInfoVo.getInstancePort());

        if (Objects.isNull(appListDTO.getPageNum()) || appListDTO.getPageNum() == 0) {
            appListDTO.setPageNum(1);
        }
        if (Objects.isNull(appListDTO.getPageSize()) || appListDTO.getPageSize() == 0) {
            appListDTO.setPageSize(10);
        }
        SecPageVO<AppEntity> appList = difyApiService.getAppList(difyInsInfo, appListDTO.getAppType(), appListDTO.getPageNum(), appListDTO.getPageSize());
        return appList;
    }

    @Override
    public JSONObject getAppDetail(AppDetailDTO appDetailDTO) {
        log.info("Fetching app list for user with ID: {}", 1);
        long userId = TokenUtil.getUserId(TokenTypeEnum.AI_PORTAL_TOKEN);
        Long portalId = portalUserService.get(userId).getPortalId();
        DifyInstanceInfoVo difyInstanceInfoVo = portalService.getDifyInstanceInfoByPortalId(portalId);
        DifyInsInfo difyInsInfo = new DifyInsInfo(difyInstanceInfoVo.getInstanceIp(), difyInstanceInfoVo.getInstancePort());

        return difyApiService.getAppDetail(difyInsInfo, appDetailDTO.getAppId());
    }

    @Override
    public String getIframeUrl(IframeUrlDTO iframeUrlDTO) {
        // appId为空则代表默认应用
        if (StringUtils.isBlank(iframeUrlDTO.getAppId())) {
            iframeUrlDTO.setAppId(defaultAppId);
        }
        log.info("Generating iframe URL for app with ID: {}", iframeUrlDTO.getAppId());
        long userId = TokenUtil.getUserId(TokenTypeEnum.AI_PORTAL_TOKEN);
        Long portalId = portalUserService.get(userId).getPortalId();
        DifyInstanceInfoVo difyInstanceInfoVo = portalService.getDifyInstanceInfoByPortalId(portalId);
        DifyInsInfo difyInsInfo = new DifyInsInfo(difyInstanceInfoVo.getInstanceIp(), difyInstanceInfoVo.getInstancePort());

        String iframeUrl = difyApiService.getIframeUrl(difyInsInfo, iframeUrlDTO.getAppId());
        if (StringUtils.isBlank(iframeUrl)) {
            return "";
        }
        //将userId进行compressAndEncodeBase64操作
        String encUserId = this.compressAndEncodeBase64(String.valueOf(userId));
        iframeUrl += "?sys.user_id=" + encUserId;
        log.info("Generated iframe URL: {}", iframeUrl);

        return iframeUrl;
    }

    private String compressAndEncodeBase64(String input) {
        log.info("Compressing and encoding string: {}", input);
        // 创建一个 ByteArrayOutputStream 来保存压缩后的数据
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        // 使用 GZIPOutputStream 进行压缩
        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
            gzipOutputStream.write(input.getBytes("UTF-8"));
        } catch (IOException e) {
            log.error("Error during compression and encoding", e);
            e.printStackTrace();
        }

        // 获取压缩后的字节数组
        byte[] compressedBytes = byteArrayOutputStream.toByteArray();

        // 使用 Base64 编码器进行编码
        String encodedString = Base64.getEncoder().encodeToString(compressedBytes);
        log.info("Compressed and encoded string: {}", encodedString);
        return encodedString;
    }

    @Override
    public List<ConversationEntity> getConvserationList(ConversationDTO conversationDTO) {
        if (Objects.isNull(conversationDTO.getLimit()) || conversationDTO.getLimit() == 0) {
            conversationDTO.setLimit(20);
        }
        long userId = TokenUtil.getUserId(TokenTypeEnum.AI_PORTAL_TOKEN);
        log.info("Fetching conversation list for user with ID: {}", userId);

        Long portalId = portalUserService.get(userId).getPortalId();
        DifyInstanceInfoVo difyInstanceInfoVo = portalService.getDifyInstanceInfoByPortalId(portalId);

        List<ConversationEntity> conversationList = difyApiService.getConvserationList(difyInstanceInfoVo.getPortalCode(), String.valueOf(userId), conversationDTO.getLimit());
        log.info("Fetched {} conversations", conversationList.size());

        return conversationList;
    }

    @Override
    public void removeConversation(DelConversationDTO delConversationDTO) {
        log.info("Removing conversation with ID: {} for app with ID: {}", delConversationDTO.getConversationId(), delConversationDTO.getAppId());
        long userId = TokenUtil.getUserId(TokenTypeEnum.AI_PORTAL_TOKEN);
        Long portalId = portalUserService.get(userId).getPortalId();
        String portalCode = portalService.getDifyInstanceInfoByPortalId(portalId).getPortalCode();

        difyApiService.removeConversation(portalCode, String.valueOf(userId), delConversationDTO.getConversationId());
        log.info("Conversation removed successfully");
    }

    @Override
    public void renameConversation(RenameConversationDTO renameConversationDTO) {
        log.info("Renaming conversation with ID: {} for app with ID: {} to new name: {}", renameConversationDTO.getConversationId(), renameConversationDTO.getAppId(), renameConversationDTO.getNewName());
        long userId = TokenUtil.getUserId(TokenTypeEnum.AI_PORTAL_TOKEN);
        Long portalId = portalUserService.get(userId).getPortalId();
        String portalCode = portalService.getDifyInstanceInfoByPortalId(portalId).getPortalCode();

        difyApiService.renameConversation(portalCode, String.valueOf(userId), renameConversationDTO.getConversationId(), renameConversationDTO.getNewName());
        log.info("Conversation renamed successfully");
    }

    @Override
    public PortalSiteInfoVo getSiteInfo() {
        //根据cookie中的difyCookie获取门户编码
        String portalCode = null;
        
        // 从当前请求中获取Cookie
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Cookie[] cookies = request.getCookies();
        
        // 遍历cookies查找difyCookie
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("portal_code".equals(cookie.getName())) {
                    portalCode = cookie.getValue();
                    break;
                }
            }
        }
        
        if (StringUtils.isEmpty(portalCode)) {
            log.error("未能从cookie中获取门户编码");
            throw new BusinessException(PortalErrorCode.PORTAL_NOT_EXIST);
        }

        PortalSiteInfoVo siteInfo = portalService.getPortalSiteInfo(portalCode);
        if (siteInfo == null) {
            log.error("门户不存在或网站配置为空，code: {}", portalCode);
            throw new BusinessException(PortalErrorCode.PORTAL_NOT_EXIST);
        }
        return siteInfo;
    }
}
