package com.sansec.ccsp.app.business.servermanage.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.app.business.servermanage.entity.AppAuthAkskPO;
import com.sansec.ccsp.app.info.request.*;
import com.sansec.ccsp.app.info.response.AppAuthAkskAddVO;
import com.sansec.ccsp.app.info.response.AppAuthAkskVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;


/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface AppAuthAkskConvert{
    @Mappings({})
    AppAuthAkskVO convertVo(AppAuthAkskPO request);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<AppAuthAkskVO> pagePOToSecPageVOPage(IPage<AppAuthAkskPO> appAuthAkskPOPage);

    AppAuthAkskPO addKeyDtoToPo(AppAddAkKeyDTO appAddAkKeyDTO);

    AppAuthAkskPO editKeyDtoToPo(AppEditAkKeyDTO appEditAkKeyDTO);

    AppAuthAkskPO deleteDtoToPo(AppDeleteAkskDTO appDeleteAkskDTO);

    AppAuthAkskPO startDtoToPo(AppStartAkskDTO appStartAkskDTO);

    AppAuthAkskPO stopDtoToPo(AppStopAkskDTO appStopAkskDTO);

    AppAuthAkskAddVO poToAddVo(AppAuthAkskPO appAuthAkskPO);
}