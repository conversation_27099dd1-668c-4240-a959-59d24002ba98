package com.sansec.ccsp.common.business.dic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.common.business.dic.convert.DicBusiServiceTypeConvert;
import com.sansec.ccsp.common.business.dic.entity.DicBusiServiceTypePO;
import com.sansec.ccsp.common.business.dic.mapper.DicBusiServiceTypeMapper;
import com.sansec.ccsp.common.business.dic.service.DicBusiServiceTypeService;
import com.sansec.ccsp.common.dic.request.DicBusiServiceTypeDTO;
import com.sansec.ccsp.common.dic.request.DicBusiServiceTypePageDTO;
import com.sansec.ccsp.common.dic.response.DicBusiServiceTypeVO;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
 /**
 * @Description: 业务服务类型字典表;(DIC_BUSI_SERVICE_TYPE)表服务实现类
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Service
@Slf4j
public class DicBusiServiceTypeServiceImpl extends ServiceImpl<DicBusiServiceTypeMapper, DicBusiServiceTypePO> implements DicBusiServiceTypeService{
    @Resource
    private DicBusiServiceTypeConvert dicBusiServiceTypeConvert;

    /**
     * 分页查询
     *
     * @param dicBusiServiceTypePageDTO 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<DicBusiServiceTypeVO>> find(DicBusiServiceTypePageDTO dicBusiServiceTypePageDTO){
        QueryWrapper<DicBusiServiceTypePO> queryWrapper = Wrappers.query();
        IPage<DicBusiServiceTypePO> page = new Page<>(dicBusiServiceTypePageDTO.getPageNum(),dicBusiServiceTypePageDTO.getPageSize());
        IPage<DicBusiServiceTypePO> dicBusiServiceTypePOPage = baseMapper.selectPage(page, queryWrapper);
        SecPageVO<DicBusiServiceTypeVO> dicBusiServiceTypePageVO = dicBusiServiceTypeConvert.pagePOToSecPageVOPage(dicBusiServiceTypePOPage);
        return ResultUtil.ok(dicBusiServiceTypePageVO);
    }

    /**
     * 新增数据
     *
     * @param dicBusiServiceTypeDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> add(DicBusiServiceTypeDTO dicBusiServiceTypeDTO){
        DicBusiServiceTypePO dicBusiServiceTypePO = dicBusiServiceTypeConvert.dtoToPo(dicBusiServiceTypeDTO);
        dicBusiServiceTypePO.setId(IdGenerator.ins().generator());
        dicBusiServiceTypePO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.insert(dicBusiServiceTypePO);
        return ResultUtil.ok();
    }

    /**
     * 更新数据
     *
     * @param dicBusiServiceTypeDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(DicBusiServiceTypeDTO dicBusiServiceTypeDTO){
        DicBusiServiceTypePO dicBusiServiceTypePO = dicBusiServiceTypeConvert.dtoToPo(dicBusiServiceTypeDTO);
        dicBusiServiceTypePO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.updateById(dicBusiServiceTypePO);
        return ResultUtil.ok();
    }

    /**
     * 通过主键删除数据
     *
     * @param dicBusiServiceTypeDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> deleteById(DicBusiServiceTypeDTO dicBusiServiceTypeDTO){
        baseMapper.deleteById(dicBusiServiceTypeDTO.getId());
        return ResultUtil.ok();
    }
}
