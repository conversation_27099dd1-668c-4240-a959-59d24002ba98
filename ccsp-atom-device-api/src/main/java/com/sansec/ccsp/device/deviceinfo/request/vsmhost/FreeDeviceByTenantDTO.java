package com.sansec.ccsp.device.deviceinfo.request.vsmhost;

import lombok.Data;

import java.util.List;

/**
 * @Description: 根据租户获取空闲设备
 * @CreateTime: 2023-09-22
 * @Author: wangjunjie
 */
@Data
public class FreeDeviceByTenantDTO {
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 区域Id
     */
    private Long regionId;
    /**
     * 设备类型
     */
    private Long deviceTypeId;

    /**
     * 设备组ID
     */
    private Long deviceGroupId;

    /**
     * 是否支持对外服务，不支持需排除对外服务设备类型的设备
     */
    private Integer isRest;

    private List<Long> deviceGroupIdList;
}
