<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ccsp.datastatic.business.screen.mapper.ScreenTemplateRelMapper">
    <sql id="Base_Column_List" >
    ID,TEMPLATE_NAME,TEMPLATE_CODE,INDEX_ID,INDEX_TYPE,INVALID_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK
    </sql>

    <select id="getDistinctScreenTemplateRelList" resultType="com.sansec.ccsp.datastatic.business.screen.entity.ScreenTemplateRelPO">
        select
            DISTINCT rel.TEMPLATE_CODE,rel.TEMPLATE_NAME
        from screen_template_rel rel where rel.INVALID_FLAG=0
    </select>
    
</mapper>