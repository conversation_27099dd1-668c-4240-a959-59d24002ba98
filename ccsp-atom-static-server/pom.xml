<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sansec.ccsp.atom</groupId>
        <artifactId>ccsp-atom-static</artifactId>
        <version>3.3.1.dcqc-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ccsp-atom-static-server</artifactId>
    <version>3.3.1.dcqc-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>8</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.sansec.ccsp.atom</groupId>
            <artifactId>ccsp-atom-static-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sansec.ccsp.base</groupId>
            <artifactId>ccsp-base-common-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sansec.springcloud</groupId>
            <artifactId>sansec-springcloud-dependencies</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sansec.db</groupId>
            <artifactId>sansec-db</artifactId>
        </dependency>
        <!--分量合成加解密算法-->
        <dependency>
            <groupId>com.sansec.component.algorithm</groupId>
            <artifactId>sansec-component-algorithm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sansec.ccsp.base</groupId>
            <artifactId>ccsp-base-common-task</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sansec.ccsp.base</groupId>
            <artifactId>ccsp-base-common-security</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20230227</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- kingbase8 支持 -->
        <dependency>
            <groupId>com.kingbase8</groupId>
            <artifactId>kingbase8</artifactId>
            <version>8.2.0</version>
        </dependency>

        <!--DB2-->
        <dependency>
            <groupId>com.ibm.db2</groupId>
            <artifactId>jcc</artifactId>
            <version>11.5.0.0</version>
        </dependency>

        <!--达梦-->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.2.192</version>
        </dependency>

        <!--oracle-->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>21.9.0.0</version>
        </dependency>

        <!--Gbase-->
        <dependency>
            <groupId>com.gbase</groupId>
            <artifactId>gbasedbtjdbc</artifactId>
            <version>3.3.0_3</version>
        </dependency>
        <!-- opengauss 支持 -->
        <dependency>
            <groupId>com.gauss.opengauss</groupId>
            <artifactId>jdbc</artifactId>
            <version>5.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.vastbase</groupId>
            <artifactId>jdbc</artifactId>
            <version>2.6</version>
        </dependency>
        <!-- OpenFeign 支持 -->
        <dependency>
            <groupId>com.sansec.tracer.adapter</groupId>
            <artifactId>sansec-loggin-tracer-feign</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <finalName>${project.artifactId}</finalName>
                </configuration>
                <executions>  <!--执行器 mvn assembly:assembly-->
                    <execution>
                        <id>release</id><!--名字任意 -->
                        <phase>package</phase><!-- 绑定到package生命周期阶段上 -->
                        <goals>
                            <goal>single</goal><!-- 只运行一次 -->
                        </goals>
                        <configuration>
                            <descriptors> <!--描述文件路径-->
                                <descriptor>../assembly/assembly.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.sansec.common.secure.plugin</groupId>
                <artifactId>sansec-secure-plugin</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>sonar</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <sonar.jdbc.username>admin</sonar.jdbc.username>
                <sonar.jdbc.password>sansec@2021</sonar.jdbc.password>
                <sonar.host.url>http://**********:9000</sonar.host.url>
                <sonar.projectKey>ccsp-atom-servicemgt</sonar.projectKey>
                <sonar.projectName>ccsp-atom-servicemgt</sonar.projectName>
            </properties>
        </profile>
    </profiles>
</project>