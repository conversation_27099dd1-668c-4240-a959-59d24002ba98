package com.sansec.ccsp.device.devicevendor.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @Description: 厂商信息表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-2-18
 */
@Data
public class DeviceVendorDTO{
    /**
     * 厂商ID
     */
    private Long vendorId;
    /**
     * 厂商名称
     */
    @Size(max = 50,message = "厂商名称长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "厂商名称只能包含中文、英文、数字或特殊字符 - _")
    @NotEmpty(message = "厂商名称不能为空")
    private String vendorName;
    /**
     * 厂商简称
     */
    @Size(max = 50,message = "厂商简称长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "厂商名称只能包含中文、英文、数字或特殊字符 - _")
    @NotEmpty(message = "厂商简称不能为空")
    private String vendorShortName;
    /**
     * 联系人;加密存储
     */
    private String linkMan;
    /**
     * 联系方式;加密存储
     */
    private String linkManPhone;
    /**
     * 备注
     */
    @Size(max = 100,message = "备注长度不能大于100")
    @Pattern(regexp = CommonPattern.COMMON_DESC,
            message = "备注请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
    private String remark;


}