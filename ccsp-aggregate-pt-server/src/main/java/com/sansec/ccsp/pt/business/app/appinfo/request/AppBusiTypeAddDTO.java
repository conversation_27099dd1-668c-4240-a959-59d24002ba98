package com.sansec.ccsp.pt.business.app.appinfo.request;


import com.sansec.ccsp.pt.common.error.SecErrorCodeConstant;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class AppBusiTypeAddDTO extends SecPageDTO {
    /**
     * 应用id
     */
    @NotNull(message = "应用ID不可为空")
    private Long appId;
    /**
     * 应用业务类型
     */
    @NotNull(message = "业务类型不可为空")
    private Long busiTypeId;
    /**
     * 组类型 1:设备组 2:服务组
     */

    @Min(value = 1, message = "组类型参数错误，设备组: 1,服务组: 2")
    @Max(value = 2, message = "组类型参数错误，设备组: 1,服务组: 2")
    private Integer groupType;
    /**
     * 组id
     */

    private Long groupId;

    /**
     * 组标识
     */
    private String groupCode;

    public void check() {
        if (ObjectUtils.isEmpty(groupType)) {
            throw new BusinessException(SecErrorCodeConstant.GROUP_TYPE_IS_BLANK);
        }
        if (ObjectUtils.isEmpty(groupId)) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_ID_IS_BLANK);
        }
    }
}
