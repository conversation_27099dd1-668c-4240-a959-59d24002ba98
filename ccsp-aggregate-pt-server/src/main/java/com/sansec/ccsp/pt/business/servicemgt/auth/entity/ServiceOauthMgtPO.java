package com.sansec.ccsp.pt.business.servicemgt.auth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 认证服务管理类
 * <AUTHOR>
 * @Date 2024/3/4 19:42
 */
@EqualsAndHashCode(callSuper = true)
@TableName("SERVICE_OAUTH_MGT")
@Data
public class ServiceOauthMgtPO  extends BasePO {

    private Long id;

    /**
     * 区域id
     */
    private Long regionId;

    /**
     * 服务名称
     */
    private String name;

    /**
     * ip
     */
    private String ip;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 服务状态
     */
    private Integer status;

    /**
     * 是否作废, 暂未使用
     */
    private Integer invalidFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}
