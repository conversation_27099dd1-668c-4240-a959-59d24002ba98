package com.sansec.ccsp.pt.business.servicemgt.remoteServer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.common.enums.ProxyRouteTypeEnum;
import com.sansec.ccsp.pt.business.region.response.RegionVO;
import com.sansec.ccsp.pt.business.region.service.RegionService;
import com.sansec.ccsp.pt.business.servicemgt.remote.service.RemoteService;
import com.sansec.ccsp.pt.business.servicemgt.remoteServer.convert.ServiceRemoteMgtConvert;
import com.sansec.ccsp.pt.business.servicemgt.remoteServer.entity.ServiceRemoteMgtPO;
import com.sansec.ccsp.pt.business.servicemgt.remoteServer.mapper.ServiceRemoteMgtMapper;
import com.sansec.ccsp.pt.business.servicemgt.remoteServer.request.*;
import com.sansec.ccsp.pt.business.servicemgt.remoteServer.response.ServiceRemoteMgtVO;
import com.sansec.ccsp.pt.business.servicemgt.remoteServer.service.ServiceRemoteMgtService;
import com.sansec.ccsp.pt.business.servicemgt.gateway.response.ServiceGatewayVO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.service.ServiceGatewayService;
import com.sansec.ccsp.pt.business.servicemgt.proxyroute.request.ProxyRouteDTO;
import com.sansec.ccsp.pt.business.servicemgt.proxyroute.service.ProxyRouteService;
import com.sansec.ccsp.pt.common.constant.CommonConstant;
import com.sansec.ccsp.pt.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.pt.common.util.ApisixIpUtil;
import com.sansec.ccsp.pt.common.util.HttpsClientUtil;
import com.sansec.ccsp.security.util.LoginUserUtil;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;

import java.time.LocalDateTime;

import com.sansec.common.id.IdGenerator;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.sansec.ccsp.pt.business.servicemgt.remote.constant.RemoteConstant.HEADER_SWAK;

/**
 * <AUTHOR> xiaojiawei
 * @description : 管控服务管理表（330）;(SERVICE_REMOTE_MGT)表服务实现类
 * @date : 2024-3-4
 */
@Service
@Slf4j
public class ServiceRemoteMgtServiceImpl extends ServiceImpl<ServiceRemoteMgtMapper, ServiceRemoteMgtPO> implements ServiceRemoteMgtService {
    @Value("${http.connectTimeout}")
    private int CONNECT_TIME_OUT;
    @Value("${http.socketTimeout}")
    private int SOCKET_TIME_OUT;
    @Value("${http.connectionRequestTimeout}")
    private int CONNECTION_REQUEST_TIMEOUT;
    @Resource
    private ServiceRemoteMgtConvert serviceRemoteMgtConvert;
    @Autowired
    private ServiceGatewayService serviceGatewayService;
    @Autowired
    private ProxyRouteService proxyRouteService;
    @Autowired
    private RegionService regionService;
    @Resource
    private RemoteService remoteService;

    /**
     * 分页查询
     *
     * @param dto 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<ServiceRemoteMgtVO>> find(ServiceRemoteMgtPageDTO dto) {
        //设置分页查询条件
        Page<ServiceRemoteMgtPO> page = this.lambdaQuery()
                .eq(ServiceRemoteMgtPO::getRegionId, dto.getRegionId())
                .like(StringUtils.isNotBlank(dto.getName()), ServiceRemoteMgtPO::getName, dto.getName())
                .like(StringUtils.isNotBlank(dto.getIp()), ServiceRemoteMgtPO::getIp, dto.getIp())
                .page(dto.toMpPage());
        //返回
        SecPageVO<ServiceRemoteMgtVO> serviceRemoteMgtPageVO = serviceRemoteMgtConvert.pagePOToSecPageVOPage(page);
        return ResultUtil.ok(serviceRemoteMgtPageVO);
    }

    /**
     * 新增管控服务
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> add(ServiceRemoteMgtAddDTO dto) {
        //0.检验区域是否存在
        Long regionId = dto.getRegionId();
        RegionVO region = regionService.findRegionById(regionId);
        if (ObjectUtils.isEmpty(region)) {
            log.error("The Current Region Is Not Exist");
            throw new BusinessException(SecErrorCodeConstant.REGION_NOT_EXIST);
        }
        //1. 校验相同区域下否重复添加相同的管控服务
        ServiceRemoteMgtPO remoteMgtPO = this.getOne(new LambdaQueryWrapper<ServiceRemoteMgtPO>()
                .eq(ServiceRemoteMgtPO::getIp, dto.getIp())
                .eq(ServiceRemoteMgtPO::getPort, dto.getPort())
                .eq(ServiceRemoteMgtPO::getRegionId, dto.getRegionId()));
        if (ObjectUtils.isNotEmpty(remoteMgtPO)) {
            log.error("Remote Server Is Exist");
            throw new BusinessException(SecErrorCodeConstant.REMOTE_SERVER_IS_EXIST);
        }

        //2. 添加或更新路由
        ProxyRouteDTO proxyRouteDTO = new ProxyRouteDTO();
        proxyRouteDTO.setRegionId(dto.getRegionId());
        proxyRouteDTO.setProxyRouteTypeEnum(ProxyRouteTypeEnum.PT_REMOTE);
        //管控服务IP
        proxyRouteDTO.setIp(dto.getIp());
        //管控服务Port
        proxyRouteDTO.setPort(dto.getPort().toString());
        proxyRouteDTO.setRemark("平台管控");
        proxyRouteService.addProxyRoute(proxyRouteDTO);
        //3.通过代理路由对管控服务进行探活
        ServiceGatewayVO aliveGateWay = serviceGatewayService.getAliveGateWay(dto.getRegionId(), 2);
        boolean remoteServerIsLive = this.checkRemoteServerLive(Collections.singletonList(aliveGateWay), dto.getIp(), dto.getPort());
        if (!remoteServerIsLive) {
            log.error("Remote Server Is Not Living,remoteServerIp:{},remoteServerPort:{}", dto.getIp(), dto.getPort());
            rollProxyRoute(dto);
            throw new BusinessException(SecErrorCodeConstant.REMOTE_SERVER_NOT_LIVE);
        }

        //4.入库
        ServiceRemoteMgtPO mgtPO = serviceRemoteMgtConvert.dtoToPo(dto);
        mgtPO.setId(IdGenerator.ins().generator());
        mgtPO.setCreateBy(LoginUserUtil.getUserId());
        mgtPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        try {
            this.save(mgtPO);
        } catch (Exception e) {
            log.error("Remote Server Insert Db Is Error");
            rollProxyRoute(dto);
            throw new BusinessException(SecErrorCodeConstant.INSERT_REMOTE_SERVER_DB_ERROR);
        }
        return ResultUtil.ok();

    }

    /**
     * 更新管控服务名称或备注
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(ServiceRemoteMgtEditDTO dto) {
        //1. 判断要更新的对象是否存在
        ServiceRemoteMgtPO remoteMgtPO = this.getOne(new LambdaQueryWrapper<ServiceRemoteMgtPO>()
                .eq(ServiceRemoteMgtPO::getId, dto.getId()));
        if (ObjectUtils.isEmpty(remoteMgtPO)) {
            throw new BusinessException(SecErrorCodeConstant.REMOTE_SERVER_IS_NOT_EXIST);
        }
        //2. 设置要更新的字段
        ServiceRemoteMgtPO mgtPO = new ServiceRemoteMgtPO();
        mgtPO.setId(remoteMgtPO.getId());
        if (StringUtils.isNotBlank(dto.getName())) {
            mgtPO.setName(dto.getName());
        }
        if (StringUtils.isNotBlank(dto.getRemark())) {
            mgtPO.setRemark(dto.getRemark());
        }
        mgtPO.setUpdateBy(LoginUserUtil.getUserId());
        mgtPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        //3. 入库
        boolean isSuccessUpdate = this.updateById(mgtPO);
        if (!isSuccessUpdate) {
            log.error("Update Remote Server Is Fail,remoteIp:{},remotePort{}", remoteMgtPO.getIp(), remoteMgtPO.getPort());
            throw new BusinessException(SecErrorCodeConstant.INSERT_REMOTE_SERVER_DB_ERROR);
        }
        return ResultUtil.ok();
    }

    /**
     * 通过主键删除数据
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecRestResponse<Object> deleteById(ServiceRemoteMgtDelDTO dto) {
        Long remoteServerId = dto.getId();
        //1. 校验要删除的管控服务是否存在
        ServiceRemoteMgtPO serviceRemoteMgtPO = this.lambdaQuery()
                .eq(ServiceRemoteMgtPO::getId, remoteServerId)
                .one();
        if (ObjectUtils.isEmpty(serviceRemoteMgtPO)) {
            throw new BusinessException(SecErrorCodeConstant.REMOTE_SERVER_IS_NOT_EXIST);
        }
        //2.区域模式下删除代理路由
        Long regionId = serviceRemoteMgtPO.getRegionId();
        //需要删除代理路由
        ProxyRouteDTO delRouteDTO = new ProxyRouteDTO();
        delRouteDTO.setIp(serviceRemoteMgtPO.getIp());
        delRouteDTO.setPort(serviceRemoteMgtPO.getPort().toString());
        delRouteDTO.setRegionId(regionId);
        delRouteDTO.setProxyRouteTypeEnum(ProxyRouteTypeEnum.PT_REMOTE);
        proxyRouteService.deleteProxyRoute(delRouteDTO);
        //3. 根据Id删除管控服务
        try {
            this.removeById(remoteServerId);
        } catch (Exception e) {
            //数据库删除失败，回滚apisix
            ProxyRouteDTO rollRouteDTO = new ProxyRouteDTO();
            rollRouteDTO.setIp(serviceRemoteMgtPO.getIp());
            rollRouteDTO.setPort(serviceRemoteMgtPO.getPort().toString());
            rollRouteDTO.setRegionId(regionId);
            rollRouteDTO.setProxyRouteTypeEnum(ProxyRouteTypeEnum.PT_REMOTE);
            proxyRouteService.addProxyRoute(rollRouteDTO);
            log.error("Remote Server Delete Failure,remoteIp:{},remotePort{}", serviceRemoteMgtPO.getIp(), serviceRemoteMgtPO.getPort());
            throw new BusinessException(SecErrorCodeConstant.REMOTE_SERVER_DELETE_FAILURE);
        }

        return ResultUtil.ok();
    }


    /**
     * 根据区域Id获取当前区域下任意一个可用的管控服务
     *
     * @param regionId
     * @return
     */
    @Override
    public ServiceRemoteMgtPO checkRemoteServerByRegionId(Long regionId) {
        if (regionId == null) {
            throw new BusinessException(SecErrorCodeConstant.REGION_ID_NOT_NULL);
        }
        LambdaQueryWrapper<ServiceRemoteMgtPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtils.isNotEmpty(regionId), ServiceRemoteMgtPO::getRegionId, regionId);
        List<ServiceRemoteMgtPO> list = this.list(wrapper);
        if (list.isEmpty()) {
            log.error("The Current Region Not Exist Remote Server");
            throw new BusinessException(SecErrorCodeConstant.THE_CURRENT_NOT_EXIST_REMOTE_SERVER);
        }
        return list.get(0);
    }

    /**
     * TODO Remote应该统一放到RemoteService里面
     */
    @Override
    public boolean checkRemoteServerLive(List<ServiceGatewayVO> gatewayVOList, String remoteIp, Integer remotePort) {
        String checkRemoteStatus = "/ccsp/remote/health/check";
        HttpsClientUtil httpsClientUtil = new HttpsClientUtil(CONNECT_TIME_OUT, SOCKET_TIME_OUT, CONNECTION_REQUEST_TIMEOUT);
        for (ServiceGatewayVO gatewayVO : gatewayVOList) {
            String gatewayApiUrl = String.format("%s%s:%d", CommonConstant.HTTPS_URL, gatewayVO.getIp(), gatewayVO.getPort());
            //curl -i -H  https://网关IP:网关Port/健康检查接口
            String url = gatewayApiUrl + checkRemoteStatus;
            try {
                Map<String, String> headers = new HashMap<>();
                headers.put(ProxyRouteTypeEnum.PT_REMOTE.getConditionKey(), "ptRemote");
                headers.put(HEADER_SWAK, remoteService.getSwak());//331 @weic 增加鉴权
                String resMsg = httpsClientUtil.doGetSsl(url, new HashMap<String, Object>(), headers);
                JSONObject object = JSON.parseObject(resMsg);
                if (object != null && "0".equals(object.getString("code"))) {
                    return true;
                }
            } catch (Exception e) {
                log.error("Remote Server Is Not Living,remoteServerIp:{},remoteServerPort:{}", remoteIp, remotePort, e);
            }
        }
        return false;
    }

    /**
     * 回滚路由
     * @param dto
     */
    private void rollProxyRoute(ServiceRemoteMgtAddDTO dto) {
        ProxyRouteDTO delProxyRouteDTO = new ProxyRouteDTO();
        delProxyRouteDTO.setIp(dto.getIp());
        delProxyRouteDTO.setPort(dto.getPort().toString());
        delProxyRouteDTO.setRegionId(dto.getRegionId());
        delProxyRouteDTO.setProxyRouteTypeEnum(ProxyRouteTypeEnum.PT_REMOTE);
        proxyRouteService.deleteProxyRoute(delProxyRouteDTO);
    }

}