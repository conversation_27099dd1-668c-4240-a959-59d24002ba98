package com.sansec.ccsp.pt.business.tenant.response;

import lombok.Data;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description : 租户绑定共享服务组（330）;
 * @date : 2024-3-12
 */
@Data
public class TenantToServiceGroupVO {
    /**
     * ID
     */
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 共享服务组ID
     */
    private Long serviceGroupId;
    /**
     * 业务类型ID
     */
    private Long busiTypeId;

    private Integer isShare;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}