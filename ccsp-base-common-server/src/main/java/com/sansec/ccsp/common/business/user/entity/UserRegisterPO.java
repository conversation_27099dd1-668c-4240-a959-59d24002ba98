package com.sansec.ccsp.common.business.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @Description: 用户注册信息表;
 * <AUTHOR> http://www.chiner.pro
 * @Date: 2023-2-18
 */
@TableName("USER_REGISTER")
@Data
public class UserRegisterPO extends BasePO{
    /**
     * 账户注册ID
     */
    @TableId(type = IdType.INPUT)
    private Long userRegisterId;
    /**
     * 登录账号
     */
    private String userCode;
    /**
     * 账号名称
     */
    private String userName;
    /**
     * 手机号码
     */
    private String phoneNum;
    /**
     * 邮箱
     */
    private String emailAddress;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 租户标识
     */
    private String tenantCode;
    /**
     * 部门ID
     */
    private Long organizationId;
    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * UKEY证书
     */
    private String ukeyCert;
    /**
     * UKEY序列号
     */
    private String ukeySerial;
    /**
     * 用户口令
     */
    private String authCode;
    /**
     * 1平台管理员 2租户用户
     */
    private Integer userType;
    /**
     * 0 待审核 1 审核通过 2 拒绝
     */
    private Integer auditStatus;
    /**
     * 审核时间
     */
    private String auditTime;
    /**
     * 审核人
     */
    private Long auditBy;
    /**
     * 审核意见
     */
    private String auditSuggestion;
    /**
     * 是否作废;默认为0
     */
    private Integer invalidFlag;
    /**
     * 完整性
     */
    private String hmac;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}
