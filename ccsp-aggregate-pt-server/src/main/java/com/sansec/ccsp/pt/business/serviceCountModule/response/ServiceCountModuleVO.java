package com.sansec.ccsp.pt.business.serviceCountModule.response;

import lombok.Data;

/**
 * @description : 网关统计组件表（330）;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>aw<PERSON>
 * @date : 2024-3-9
 */
@Data
public class ServiceCountModuleVO{
    /**
     * 管控服务ID
     */
    private Long id;
    /**
     * 区域ID
     */
    private Long regionId;
    /**
     * 网关ID
     */
    private Long gatewayId;
    /**
     * 网关统计组件标识
     */
    private String moduleCode;
    /**
     * 管控服务IP
     */
    private String ip;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}