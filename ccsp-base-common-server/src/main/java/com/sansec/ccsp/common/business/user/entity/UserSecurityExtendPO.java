package com.sansec.ccsp.common.business.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @Description: 用户安全信息扩展表;
 * <AUTHOR> http://www.chiner.pro
 * @Date: 2023-2-18
 */
@TableName("USER_SECURITY_EXTEND")
@Data
public class UserSecurityExtendPO extends BasePO{
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 账户ID
     */
    private Long userId;
    /**
     * 完整性校验值
     */
    private String hmac;
    /**
     * 最后一次活跃时间
     */
    private String lastActiveTime;
    /**
     * 最后一次修改密码时间
     */
    private String lastUpdatePwdTime;
    /**
     * 账户有效期开始时间
     */
    private String accountStartTime;
    /**
     * 账户有效期结束时间
     */
    private String accountEndTime;
    /**
     * 登录失败次数
     */
    private Integer loginErrorTimes;
    /**
     * 解锁时间（时间戳）
     */
    private Long unlockTime;
    /**
     * 口令是否需要修改
     */
    private Integer passwordChangeFlag;
    /**
     * 是否作废;默认为0
     */
    private Integer invalidFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}
