package com.sansec.ccsp.servicemgt.business.dbmgt.service;

import com.sansec.ccsp.servicemgt.dbmgt.request.DatabaseUnitUpgradeBackupRecodePageDTO;
import com.sansec.ccsp.servicemgt.dbmgt.request.DatabaseUnitUpgradeRecodePageDTO;
import com.sansec.ccsp.servicemgt.dbmgt.request.DbBackRecordIdDTO;
import com.sansec.ccsp.servicemgt.dbmgt.response.DatabaseUpgradeRecordVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;
import java.util.Map;

public interface DataBaseUnitUpgradeService {


    SecRestResponse<SecPageVO<DatabaseUpgradeRecordVO>> dbUpgradeRecordList(DatabaseUnitUpgradeRecodePageDTO dto);

    /**
     * 数据库实例状态查询
     * 数据库实例id -> 实例状态
     */
    Map<Long, Integer> getUnitStatusByIds(List<Long> unitIdList);

    /**
     * 数据库记录状态查询
     */
    Map<Long, Integer> getRecordStatusByIds(List<Long> recordIdList);

    SecRestResponse<SecPageVO<DatabaseUpgradeRecordVO>> dbUpgradeBackupRecordList(DatabaseUnitUpgradeBackupRecodePageDTO dto);

    SecRestResponse<DatabaseUpgradeRecordVO> getDbBackUpRecordById(DbBackRecordIdDTO id);


}
