package com.sansec.ccsp.servicemgt.business.remote.controller;

import com.sansec.ccsp.common.dic.response.DicBusiTypeVO;
import com.sansec.ccsp.servicemgt.business.dbmgt.service.DicBusiTypeToDatabaseService;
import com.sansec.ccsp.servicemgt.business.remote.request.BackUpOrRestoreDTO;
import com.sansec.ccsp.servicemgt.business.remote.request.DbInfoDTO;
import com.sansec.ccsp.servicemgt.business.remote.request.InitServiceParamDTO;
import com.sansec.ccsp.servicemgt.business.remote.service.RemoteService;
import com.sansec.ccsp.servicemgt.business.service.BusiTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description: 管控测试类
 * @Date: 2023年2月21日
 */
@RestController
@RequestMapping("/remote/v1")
@Validated
@Slf4j
public class RemoteController {
    @Resource
    private RemoteService remoteService;
    @Resource
    private BusiTypeService busiTypeService;
    @Resource
    private DicBusiTypeToDatabaseService dicBusiTypeToDatabaseService;

    /**
     * @description: 创建数据库实例
     * @param dbInfo
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/createDbCase/{remoteIp}/{remotePort}")
    public void createDbCase(@RequestBody DbInfoDTO dbInfo, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.createDbCase(dbInfo,remoteIp,remotePort);
    }

    /**
     * @description: 创建数据库模式
     * @param dbInfo
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/createDbSchema/{remoteIp}/{remotePort}")
    public void createDbSchema(@RequestBody DbInfoDTO dbInfo, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.createDbSchema(dbInfo,remoteIp,remotePort);
    }

    /**
     * @description: 检测数据库实例/模式状态
     * @param dbInfo
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/checkDbCaseOrSchema/{remoteIp}/{remotePort}")
    public void checkDbCaseOrSchema(@RequestBody DbInfoDTO dbInfo, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.checkDbCaseOrSchema(dbInfo,remoteIp,remotePort);
    }

    /**
     * @description: 执行数据库脚本
     * @param dbInfo
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/initDbData/{remoteIp}/{remotePort}")
    public void initDbData(@RequestBody DbInfoDTO dbInfo, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.initDbData(dbInfo,remoteIp,remotePort);
    }

    /**
     * @description: 删除数据库实例
     * @param dbInfo
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/deleteDbCase/{remoteIp}/{remotePort}")
    public void deleteDbCase(@RequestBody DbInfoDTO dbInfo, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.deleteDbCase(dbInfo,remoteIp,remotePort);
    }

    /**
     * @description: 删除数据库模式
     * @param dbInfo
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/deleteDbSchema/{remoteIp}/{remotePort}")
    public void deleteDbSchema(@RequestBody DbInfoDTO dbInfo, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.deleteDbSchema(dbInfo,remoteIp,remotePort);
    }

    /**
     * @description: 检查数据库脚本执行结果模式
     * @param dbInfo
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/checkInitDbData/{remoteIp}/{remotePort}")
    public void checkInitDbData(@RequestBody DbInfoDTO dbInfo, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.checkInitDbData(dbInfo,remoteIp,remotePort);
    }

    /**
     * @description: 数据库备份恢复
     * @param backUpOrRestoreDTO
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/dbBackUpOrRestore/{remoteIp}/{remotePort}")
    public void dbBackUpOrRestore(@RequestBody BackUpOrRestoreDTO backUpOrRestoreDTO, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.dbBackUpOrRestore(backUpOrRestoreDTO,remoteIp,remotePort);
    }

    /**
     * @description: 检测数据库备份恢复状态
     * @param backUpOrRestoreDTO
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/dbCheckBackUpOrRestore/{remoteIp}/{remotePort}")
    public void dbCheckBackUpOrRestore(@RequestBody BackUpOrRestoreDTO backUpOrRestoreDTO, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.dbCheckBackUpOrRestore(backUpOrRestoreDTO,remoteIp,remotePort);
    }

    /**
     * @description: 执行数据库脚本
     * @param dbInfo
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/initDbDataTest/{remoteIp}/{remotePort}")
    public void initDbDataTest(@RequestBody DbInfoDTO dbInfo, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) throws InterruptedException {
/*        List<DicBusiTypeVO> busiTypeList = busiTypeService.getBusiTypeList();
        Map<Long, String> dbNameByTypeIdMap = dicBusiTypeToDatabaseService.getDbNameByTypeIdMap();
        for (DicBusiTypeVO busiType : busiTypeList) {
//            if(busiType.getId() != 1){
//                continue;
//            }
            DbInfoDTO dbInfoDelete = new DbInfoDTO();
            dbInfoDelete.setDbType("dm");
            dbInfoDelete.setDbCase("1");
            String dbName = dbNameByTypeIdMap.get(busiType.getId()) + "_" + "tenantxjw";
            dbInfoDelete.setDbSchema(dbName);
            dbInfoDelete.setDbIp("************");
            dbInfoDelete.setDbPort(5236);
            dbInfoDelete.setDbUser("SANSEC");
            dbInfoDelete.setDbAuthCode("swxa1234.");
            dbInfoDelete.setRunScript("/opt/ccsp/ccspUtilRemote/shell/deleteDbSchema.sh");
            remoteService.deleteDbSchema(dbInfoDelete,remoteIp,remotePort);
        }

        for (DicBusiTypeVO busiType : busiTypeList) {
//            if(busiType.getId() != 1){
//                continue;
//            }
            DbInfoDTO dbInfoCreate = new DbInfoDTO();
            dbInfoCreate.setDbType("dm");
            dbInfoCreate.setDbCase("1");
            String dbName = dbNameByTypeIdMap.get(busiType.getId()) + "_" + "tenantxjw";
            dbInfoCreate.setDbSchema(dbName);
            dbInfoCreate.setDbIp("************");
            dbInfoCreate.setDbPort(5236);
            dbInfoCreate.setDbUser("SANSEC");
            dbInfoCreate.setDbAuthCode("swxa1234.");
            dbInfoCreate.setRunScript("/opt/ccsp/ccspUtilRemote/shell/createDbSchema.sh");
            remoteService.createDbSchema(dbInfoCreate,remoteIp,remotePort);
        }

        for (DicBusiTypeVO busiType : busiTypeList) {
//            if(busiType.getId() != 1){
//                continue;
//            }
            DbInfoDTO dbInfoInit = new DbInfoDTO();
            dbInfoInit.setDbType("dm");
            dbInfoInit.setDbCase("1");
            String dbName = dbNameByTypeIdMap.get(busiType.getId()) + "_" + "tenantxjw";
            dbInfoInit.setDbSchema(dbName);
            dbInfoInit.setDbIp("************");
            dbInfoInit.setDbPort(5236);
            dbInfoInit.setDbUser("SANSEC");
            dbInfoInit.setDbAuthCode("swxa1234.");
            dbInfoInit.setRunScript("/opt/ccsp/ccspUtilRemote/shell/initDbData.sh");
            if ("ccsp_secdb".equals( dbNameByTypeIdMap.get(busiType.getId()))){
                dbInfoInit.setSqlRunScript("/opt/ccsp/ccspUtilRemote/shell/sql/dm/secdbhsm-dm.sql");
            }else if("ccsp_electronic_seal".equals( dbNameByTypeIdMap.get(busiType.getId()))){
                dbInfoInit.setSqlRunScript("/opt/ccsp/ccspUtilRemote/shell/sql/dm/electronic_seal-dm.sql");
            }else {
                dbInfoInit.setSqlRunScript("/opt/ccsp/ccspUtilRemote/shell/sql/dm/"+ dbNameByTypeIdMap.get(busiType.getId()).split("_")[1] +"-dm.sql");
            }
            remoteService.initDbData(dbInfoInit,remoteIp,remotePort);
        }
        TimeUnit.SECONDS.sleep(10);
        for (DicBusiTypeVO busiType : busiTypeList) {
//            if(busiType.getId() != 1){
//                continue;
//            }
            DbInfoDTO dbInfoCheck = new DbInfoDTO();
            dbInfoCheck.setDbType("dm");
            dbInfoCheck.setDbCase("1");
            String dbName = dbNameByTypeIdMap.get(busiType.getId()) + "_" + "tenantxjw";
            dbInfoCheck.setDbSchema(dbName);
            dbInfoCheck.setDbIp("************");
            dbInfoCheck.setDbPort(5236);
            dbInfoCheck.setDbUser("SANSEC");
            dbInfoCheck.setDbAuthCode("swxa1234.");
            dbInfoCheck.setRunScript("/opt/ccsp/ccspUtilRemote/shell/checkDbInitData.sh");
            dbInfoCheck.setSqlRunScript("/opt/ccsp/ccspUtilRemote/shell/sql/dm/"+ dbNameByTypeIdMap.get(busiType.getId()).split("_")[1] +"-dm.sql");
            try {
                remoteService.checkInitDbData(dbInfoCheck, remoteIp,remotePort);
                log.info("检测数据库脚本成功,busiType={}",busiType.getBusiTypeName());
            } catch (Exception e) {
                log.error("检测数据库脚本异常,busiType={}",busiType.getBusiTypeName());
            }
        }*/
    }

}