package com.sansec.ccsp.datastatic.business.statistic.realtime.convert;

import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import com.sansec.ccsp.datastatic.business.statistic.realtime.request.StatisticConfigParamDTO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticConfigParamDataVO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.entity.StatisticConfigParamPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.common.param.response.SecPageVO;
import java.util.List;

 /**
 * @description : 统计配置参数（330）;(STATISTIC_CONFIG_PARAM)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @date : 2024-3-13
 */
@Mapper(componentModel = "spring")
public interface StatisticConfigParamDataConvert{
    /**
     * dtoToPo
     * @param statisticConfigParamDataDTO
     * @return
     */
    @Mappings({})
    StatisticConfigParamPO dtoToPo(StatisticConfigParamDTO statisticConfigParamDataDTO);
    
    /**
     * poToDto
     * @param statisticConfigParamDataPO
     * @return
     */
    StatisticConfigParamDTO poToDto(StatisticConfigParamPO statisticConfigParamDataPO);
    
    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<StatisticConfigParamDTO> poToDto(List<StatisticConfigParamPO> list);
     
    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<StatisticConfigParamDataVO> pagePOToSecPageVOPage(IPage<StatisticConfigParamPO> iPage);
    
    @InheritConfiguration(name = "convertVo")
    List<StatisticConfigParamDataVO> convert(List<StatisticConfigParamPO> list);
    
    @Mappings({})
    StatisticConfigParamDataVO convertVo(StatisticConfigParamPO request);
}