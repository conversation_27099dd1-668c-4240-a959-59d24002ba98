package com.sansec.ccsp.pt.common.config;

import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * @Description: SessionFactoryConfiguration
 * <AUTHOR>
 * @Date: 2022/2/17 18:01
 */
@Configuration
public class SessionFactoryConfiguration {
    @Autowired
    private DataSource dataSource;

    @Primary
	@Bean
    public DatabaseIdProvider getDatabaseIdProvider(){
        DatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties p = new Properties();
        p.setProperty("Oracle", "oracle");
        p.setProperty("KingbaseES", "oracle");
        p.setProperty("MySQL", "mysql");
		p.setProperty("MariaDB", "mysql");
        //p.setProperty("PostgreSQL", "hg");
        p.setProperty("DM DBMS", "dm");
        p.setProperty("PostgreSQL", "gauss");
        databaseIdProvider.setProperties(p);
        return databaseIdProvider;
    }
}
