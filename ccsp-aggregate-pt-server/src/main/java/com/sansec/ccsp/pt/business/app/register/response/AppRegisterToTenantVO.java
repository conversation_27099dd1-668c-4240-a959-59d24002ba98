package com.sansec.ccsp.pt.business.app.register.response;

import lombok.Data;

 /**
 * @Description: 应用申请和租户关联表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-2-18
 */
@Data
public class AppRegisterToTenantVO{
    /**
     * ID
     */
    private Long id;
    /**
     * 应用申请信息ID
     */
    private Long appRegisterId;
    /**
     * 应用ID
     */
    private Long appId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 是否作废;默认为0
     */
    private Integer invalidFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}