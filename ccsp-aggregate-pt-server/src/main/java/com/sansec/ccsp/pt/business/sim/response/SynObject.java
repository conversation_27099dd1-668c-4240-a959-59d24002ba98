package com.sansec.ccsp.pt.business.sim.response;

import lombok.Data;

/**
 * @Description: 异步响应体
 * @CreateTime: 2023-09-12
 * @Author: wang<PERSON><PERSON>e
 */
@Data
public class SynObject {
    // 状态  0处理成功 1待处理 2处理中 3处理失败
    private Integer status;
    // 请求时间戳  超时后删除该数据
    private Long timestamp;
    // 事务id
    private String requestId;
    // 请求数据
    private Object requestData;
    // 回调数据
    private Object callbackData;
    // 处理结果   签名值可在该字段内
    private String dealResult;
    // 信息
    private String message;
    // 手机号   sim盾使用，simkey无该数据
    private String phone;
}
