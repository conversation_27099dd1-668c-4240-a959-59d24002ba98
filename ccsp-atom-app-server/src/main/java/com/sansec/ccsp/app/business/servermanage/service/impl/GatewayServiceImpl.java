package com.sansec.ccsp.app.business.servermanage.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sansec.ccsp.app.business.servermanage.response.CheckGatewayLiveVO;
import com.sansec.ccsp.app.business.servermanage.service.GatewayService;
import com.sansec.ccsp.app.common.constant.CommonConstant;
import com.sansec.ccsp.app.common.constant.ErrorCodeConst;
import com.sansec.common.exception.BusinessException;
import com.sansec.redis.utils.RedisUtill;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @deprecated 获取区域下网关修改，作废原获取方法
 */
@Deprecated
@Service
@Slf4j
public class GatewayServiceImpl implements GatewayService {
    @Resource
    RedisUtill redisUtill;

    @Override
    public String getLiveGatewayReplaceAuth() {
        //从redis中获取网关状态
        String mgtGatewayStr = redisUtill.strGet(CommonConstant.MANAGE_GATEWAY_STATUS);
        List<CheckGatewayLiveVO> mgtGatewayLiveVOS = JSON.parseObject(mgtGatewayStr, new TypeReference<List<CheckGatewayLiveVO>>() {
        });
        for (CheckGatewayLiveVO gatewayLiveVO : mgtGatewayLiveVOS) {
            if(gatewayLiveVO.getStatus()==1){
                String gatewayApiUrl = String.format("%s%s:%d", CommonConstant.HTTPS_URL, gatewayLiveVO.getIp(), gatewayLiveVO.getPort());
                return gatewayApiUrl;
            }
        }
        String busiGatewayStr=redisUtill.strGet(CommonConstant.BUSINESS_GATEWAY_STATUS);
        List<CheckGatewayLiveVO> busiGatewayLiveVOS = JSON.parseObject(busiGatewayStr, new TypeReference<List<CheckGatewayLiveVO>>() {
        });
        for (CheckGatewayLiveVO gatewayLiveVO : busiGatewayLiveVOS) {
            if(gatewayLiveVO.getStatus()==1){
                String gatewayApiUrl = String.format("%s%s:%d", CommonConstant.HTTPS_URL, gatewayLiveVO.getIp(), gatewayLiveVO.getPort());
                return gatewayApiUrl;
            }
        }
        log.error("There is no available gateway in the system,errorcode={}", ErrorCodeConst.GATEWAY_LIVE_NOT_EXIST);
        throw new BusinessException(ErrorCodeConst.GATEWAY_LIVE_NOT_EXIST);
    }



}
