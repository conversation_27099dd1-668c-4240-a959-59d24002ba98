package com.sansec.ccsp.device.business.devicedic.request;

import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;

/**
 * @Description: 虚拟机镜像类型字典表;
 * <AUTHOR> x<PERSON><PERSON>jiaw<PERSON>
 * @Date: 2023-2-18
 */
@Data
public class DicDeviceImageTypePageDTO extends SecPageDTO{
   /**
     * 应用名
     */
    @Size(max = 50, message = "名称长度限制50")
    private String name;
}