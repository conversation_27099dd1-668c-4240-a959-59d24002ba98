package com.sansec.ccsp.pt.business.quota.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class TenantQuotaInfoSelectDTO {
    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    /**
     * 服务组ID
     */
    private Long serviceGroupId;

    /**
     * 服务标识;服务标识+配额信息key对应唯一配额信息
     */
    @NotBlank(message = "服务标识不能为空")
    private String serviceCode;
    /**
     * 配额信息key
     */
    @NotBlank(message = "配额信息不能为空")
    private String quotaName;
}
