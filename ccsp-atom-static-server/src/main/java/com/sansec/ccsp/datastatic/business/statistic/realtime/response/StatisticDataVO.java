package com.sansec.ccsp.datastatic.business.statistic.realtime.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 天、日、月  条件查询返回数据
 * @Author: zhangweicheng
 * @Date: 2024/3/8
 */

@Data
public class StatisticDataVO extends BasePO {


    /**
     * 拆分数据小时时间;采集时间：字符串格式
     */
    private String collectTime;
    /**
     * 调用次数
     */
    private Long callNum;
    /**
     * 区域标识
     */
    private String regionCode;
    /**
     * 租户标识
     */
    private String tenantCode;
    /**
     * 应用标识
     */
    private String appCode;
    /**
     * 服务IP
     */
    private String serviceIp;
    /**
     * 服务端口
     */
    private Integer servicePort;
    /**
     * 服务标识
     */
    private String serviceTypeCode;
    /**
     * 服务URL
     */
    private String serviceUrl;
    /**
     * 平均耗时
     */
    private Long avgCostTime;
    /**
     * 最大耗时
     */
    private Long maxCostTime;
    /**
     * 流量
     */
    private Long serviceFlowNum;
    

}