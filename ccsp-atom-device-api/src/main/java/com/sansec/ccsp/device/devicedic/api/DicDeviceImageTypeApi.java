package com.sansec.ccsp.device.devicedic.api;


import com.sansec.ccsp.device.devicedic.api.fallback.DicDeviceImageTypeApiFallBack;
import com.sansec.ccsp.device.devicedic.response.DicDeviceImageTypeApiVO;
import com.sansec.ccsp.device.devicetype.request.DeviceTypeIdDTO;
import com.sansec.ccsp.device.devicevendor.request.DeviceVendorIdDTO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "${spring.protocol.prefix}${spring.application.device.name}" , path="/ccsp/device",  fallbackFactory = DicDeviceImageTypeApiFallBack.class)
public interface DicDeviceImageTypeApi {

    /**
     * 虚拟机镜像类型字典表下拉列表，INVALID_FLAG=0
     * @return
     */
    @PostMapping("/api/dicDeviceImageType/v1/findVendorImageList")
    SecRestResponse<List<DicDeviceImageTypeApiVO>> findList(@Validated @RequestBody DeviceVendorIdDTO vendorDTO);


    /**
     * 根据设备类型id（云机）获取支持的镜像列表
     * @param typeIdDTO
     * @return
     */
    @PostMapping("/api/dicDeviceImageType/v1/findImageListByTypeId")
    SecRestResponse<List<DicDeviceImageTypeApiVO>> findImageListByTypeId(@Validated @RequestBody DeviceTypeIdDTO typeIdDTO);

}
