package com.sansec.ccsp.common.message.response;

import lombok.Data;

/**
 * @description : 消息表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2023-11-21
 */
@Data
public class CcspMessageVO {
	/**
	 * 主键
	 */
	private Long id;
	/**
	 * 消息来源;1工单 2告警
	 */
	private Integer source;
	/**
	 * 消息类型
	 */
	private Integer type;
	/**
	 * 消息状态;1未读 2已读
	 */
	private Integer status;
	/**
	 * 消息标题
	 */
	private String title;
	/**
	 * 消息内容
	 */
	private String content;
	/**
	 * 消息发送者ID;用户ID、租户ID、或者空
	 */
	private Long senderId;
	/**
	 * 消息发送者角色ID
	 */
	private Long senderRoleId;
	/**
	 * 消息接收者类型;1平台 2租户
	 */
	private Integer receiverType;
	/**
	 * 消息接收者ID;0L为通知所有租户、1L为通知平台、租户ID
	 */
	private Long receiverId;
	/**
	 * 消息接收者角色ID
	 */
	private Long receiverRoleId;
	/**
	 * 跳转路径
	 */
	private String jumpUrl;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 创建人
	 */
	private Long createBy;
	/**
	 * 创建时间
	 */
	private String createTime;
	/**
	 * 更新人
	 */
	private Long updateBy;
	/**
	 * 更新时间
	 */
	private String updateTime;

}