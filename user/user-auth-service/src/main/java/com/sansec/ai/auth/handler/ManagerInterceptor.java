package com.sansec.ai.auth.handler;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson.JSONObject;
import com.sansec.ai.auth.constant.RequestAttributeConst;
import com.sansec.ai.common.base.constant.ErrorCode;
import com.sansec.ai.common.base.handler.BaseInterceptorAdapter;
import com.sansec.ai.user.enums.UserTypeEnum;
import com.sansec.ai.user.service.PortalManagerService;
import com.sansec.ai.user.service.PortalUserService;
import com.sansec.ai.user.vo.PortalManagerVO;
import com.sansec.common.exception.BusinessException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 门户管理拦截器
 *
 * <AUTHOR>
 * @since 2025/5/12 11:55
 */
@Slf4j
@Component
public class ManagerInterceptor extends BaseInterceptorAdapter {

	@Value("${secplat.base.token}")
	private String tokenKey;

	//配置平台侧校验登陆接口
	@Value("${secplat.restUrl}")
	private String secPlatUrl;
	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	private PortalManagerService portalManagerService;
	@Autowired
	private PortalUserService portalUserService;

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		//获取token
		String token = request.getHeader("SecToken");
		if (StringUtils.isBlank(token)) {
			throw new BusinessException(ErrorCode.AUTH_VERIFY_FAILED);
		}
		//校验token
		{
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.add("SecToken", token);

			Map<String, String> body = new HashMap<>(2);
			body.put("uri", request.getRequestURI());

			String validateTokenUrl = secPlatUrl + "sec/user/validateToken";
			ResponseEntity<JSONObject> checkResult = restTemplate.exchange(validateTokenUrl, HttpMethod.POST, new HttpEntity<>(body, headers), new ParameterizedTypeReference<JSONObject>() {
			});
			log.debug("secRestResponse : " + checkResult);
			if (!HttpStatus.OK.equals(checkResult.getStatusCode())) {
				throw new BusinessException(ErrorCode.AUTH_VERIFY_FAILED);
			}
		}
		JWT jwt = JWTUtil.parseToken(token);
		//获取用户ID，填充到request中
		Long userId = NumberUtil.parseLong(String.valueOf(jwt.getPayload().getClaim("userId")));
		request.setAttribute(RequestAttributeConst.USER_ID, userId);
		//获取、填充用户信息和用户类型
		PortalManagerVO userInfo = portalManagerService.get(userId);
		if (Objects.isNull(userInfo.getPortalId())) {
			//未查询到门户信息，用户类型=系统管理员
			request.setAttribute(RequestAttributeConst.USER_TYPE, UserTypeEnum.SYSTEM_MANAGER);
		} else {
			//已查询到门户信息，用户类型=门户管理员
			request.setAttribute(RequestAttributeConst.USER_TYPE, UserTypeEnum.PORTAL_MANAGER);
			//门户ID
			request.setAttribute(RequestAttributeConst.PORTAL_ID, userInfo.getPortalId());
		}
		return true;
	}
}
