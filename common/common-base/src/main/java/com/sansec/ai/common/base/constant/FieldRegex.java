package com.sansec.ai.common.base.constant;

/**
 * 全局正则常量类
 *
 * <AUTHOR>
 * @since 2024/3/12 16:36
 */
public final class FieldRegex {

    // 长度1-32, 只能包含大、小写字母、中文、数字、特殊字符_-
    public static final String LETTER_CHINESE_NUM_2LINE_32 = "^([A-Za-z\\d\\u4e00-\\u9fa5]{1})([\\w\\u4e00-\\u9fa5-]{0,31})$";

    // 长度1-32, 只能包含大、小写字母、数字、特殊字符-_
    public static final String LETTER_NUM_2LINE_32 = "^[\\w-]{1,32}$";

	// 长度1-32, 以小写字母或数字开头，只能包含小写字母、数字、特殊字符-_
	public static final String LETTER_CHINESE_NUM = "^[a-z0-9][a-z0-9_-]{0,31}$";

    // 时间日期格式  yyyy-MM-dd hh:mm:ss
    public static final String COMMON_DATE = "^$|([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8])))([ ])([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])";

    // Java类全名正则，包含包名和类名 例如：java.lang.String
    public static final String JAVA_FULLY_QUALIFIED_CLASS_NAME = "^([a-zA-Z_$][a-zA-Z\\d_$]*\\.)*[a-zA-Z_$][a-zA-Z\\d_$]*$";

    // 通用备注描述，长度0-256，可包含中英文、数字、常用标点符号
    public static final String COMMON_DESCRIPTION = "^[a-zA-Z0-9\\u4e00-\\u9fa5\\s,.，。!！?？:：;；_\\-()（）【】\\[\\]]{0,256}$";

    // 通用长度限制描述，长度0-512，可包含中英文、数字、常用标点符号
    public static final String COMMON_ALG = "^.{1,256}$";

    // 通用描述，长度0-512，可包含中英文、数字、常用标点符号
    public static final String COMMON_SHA_512= "^.{1,512}$";
    // ipv4 正则
    public static final String IPV4_REGEX = "^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])$";

    // ipv4 netmask 正则
    public static final String IPV4_NETMASK = "^(255|254|252|248|240|224|192|128|0)\\.0\\.0\\.0$|^255\\.(255|254|252|248|240|224|192|128|0)\\.0\\.0$|^255\\.255\\.(254|252|248|240|224|192|128|0)\\.0$|^255\\.255\\.255\\.(255|254|252|248|240|224|192|128|0)$";

    private FieldRegex() {
    }
}
