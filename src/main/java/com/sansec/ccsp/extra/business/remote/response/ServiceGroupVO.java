package com.sansec.ccsp.extra.business.remote.response;

import com.sansec.ccsp.extra.business.constant.CommonConstant;
import lombok.Data;

import java.util.List;

/**
 * 服务组VO
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @date : 2024-03-11
 */
@Data
public class ServiceGroupVO {
    /**
     * 服务组ID
     */
    private Long serviceGroupId;
    /**
     * 服务组CODE
     */
    private String serviceGroupCode;
    /**
     * 服务组名称
     */
    private String serviceGroupName;
    /**
     * 服务组类型
     */
    private Integer serviceGroupType;
    /**
     * 服务组状态
     */
    private Integer serviceGroupStatus;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 服務组服务数量
     */
    private Integer serviceNum;
    /**
     * 是否共享
     */
    private Integer isShare;

    /**
     * 区域Id
     */
    private Long regionId;
    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 是否共享
     */
    public boolean isShare() {
        return CommonConstant.ONE.equals(isShare);
    }
}