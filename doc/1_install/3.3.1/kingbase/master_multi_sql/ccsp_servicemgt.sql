-- CCSP_SERVICEMGT.DATABASE_INFO definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.DATABASE_INFO;

CREATE TABLE CCSP_SERVICEMGT.DATABASE_INFO (
                                               ID INT8 NOT NULL,
                                               <PERSON><PERSON><PERSON><PERSON>E_TYPE_ID INT8 NOT NULL,
                                               DATABASE_NAME VARCHAR(300) NOT NULL,
                                               DATABASE_IP VARCHAR(300),
                                               DATABASE_PORT VARCHAR(60),
                                               DATABASE_MAP_IP VARCHAR(300),
                                               DATABASE_MAP_PORT VARCHAR(60),
                                               REGION_ID INT8,
                                               CASE_NAME VARCHAR(300),
                                               ADMIN_USER VARCHAR(200),
                                               ADMI<PERSON>_AUTH_CODE VARCHAR(200),
                                               AUTO_CREATED INT4 DEFAULT 0,
                                               HM<PERSON> VARCHAR(255),
                                               REMARK VARCHAR(1000),
                                               CREATE_BY INT8,
                                               CREATE_TIME VARCHAR(30),
                                               UPDATE_BY INT8,
                                               UPDATE_TIME VARCHAR(30),
                                               CONSTRAINT DATABASE_INFO_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.DATABASE_MINIMUM_UNIT definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.DATABASE_MINIMUM_UNIT;

CREATE TABLE CCSP_SERVICEMGT.DATABASE_MINIMUM_UNIT (
                                                       ID INT8 NOT NULL,
                                                       DATABASE_UNIT_NAME VARCHAR(300) NOT NULL,
                                                       SERVICE_TYPE_ID INT8 NOT NULL,
                                                       DATABASE_ID INT8 NOT NULL,
                                                       UNIT_USER VARCHAR(200),
                                                       AUTH_CODE VARCHAR(200),
                                                       HMAC VARCHAR(255),
                                                       REMARK VARCHAR(1000),
                                                       CREATE_BY INT8,
                                                       CREATE_TIME VARCHAR(30),
                                                       UPDATE_BY INT8,
                                                       UPDATE_TIME VARCHAR(30),
                                                       CONSTRAINT DATABASE_MINIMUM_UNIT_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.DATABASE_UNIT_TO_SERVICE_GROUP definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.DATABASE_UNIT_TO_SERVICE_GROUP;

CREATE TABLE CCSP_SERVICEMGT.DATABASE_UNIT_TO_SERVICE_GROUP (
                                                                ID INT8 NOT NULL,
                                                                DATABASE_UNIT_ID INT8 NOT NULL,
                                                                DATABASE_ID INT8 NOT NULL,
                                                                SERVICE_GROUP_ID INT8 NOT NULL,
                                                                REMARK VARCHAR(1000),
                                                                CREATE_BY INT8,
                                                                CREATE_TIME VARCHAR(30),
                                                                UPDATE_BY INT8,
                                                                UPDATE_TIME VARCHAR(30),
                                                                CONSTRAINT DATABASE_UNIT_TO_SERVICE_GROUP_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.DATABASE_UPGRADE_RECORD definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.DATABASE_UPGRADE_RECORD;

CREATE TABLE CCSP_SERVICEMGT.DATABASE_UPGRADE_RECORD (
                                                         ID INT8 NOT NULL,
                                                         DATABASE_MINIMUM_UNIT_ID INT8 NOT NULL,
                                                         DATABASE_TYPE_ID INT8,
                                                         SERVICE_TYPE_ID INT8,
                                                         REQUEST_DATA VARCHAR(2000),
                                                         RESPONSE_DATA VARCHAR(2000),
                                                         STATUS INT4,
                                                         "TYPE" INT4,
                                                         BACKUP_PATH VARCHAR(255),
                                                         BACKUP_NAME VARCHAR(255),
                                                         CREATE_TIME VARCHAR(30),
                                                         CONSTRAINT DATABASE_UPGRADE_RECORD_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE;

CREATE TABLE CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (
                                                           ID INT8 NOT NULL,
                                                           SERVICE_TYPE_ID INT8 NOT NULL,
                                                           DATABASE_UNIT_NAME VARCHAR(255) NOT NULL,
                                                           CONSTRAINT DIC_BUSI_TYPE_TO_DATABASE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.DIC_DATABASE_TYPE definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.DIC_DATABASE_TYPE;

CREATE TABLE CCSP_SERVICEMGT.DIC_DATABASE_TYPE (
                                                   ID INT8 NOT NULL,
                                                   DATABASE_TYPE_CODE VARCHAR(300),
                                                   VERISON VARCHAR(255),
                                                   REMARK VARCHAR(1000),
                                                   CREATE_BY INT8,
                                                   CREATE_TIME VARCHAR(30),
                                                   UPDATE_BY INT8,
                                                   UPDATE_TIME VARCHAR(30),
                                                   CONSTRAINT DIC_DATABASE_TYPE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.FILE_INFO definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.FILE_INFO;

CREATE TABLE CCSP_SERVICEMGT.FILE_INFO (
                                           ID VARCHAR(255),
                                           FILE_NAME VARCHAR(255),
                                           FILE_REMOTE_PATH VARCHAR(255),
                                           FILE_DIGEST VARCHAR(255),
                                           FILE_SIZE INT8,
                                           BUSINESS_TYPE VARCHAR(255),
                                           STATUS INT4,
                                           HMAC VARCHAR(255),
                                           REMARK VARCHAR(1000),
                                           CREATE_BY INT8,
                                           CREATE_TIME VARCHAR(30),
                                           UPDATE_BY INT8,
                                           UPDATE_TIME VARCHAR(30)
);


-- CCSP_SERVICEMGT.IMAGE_UPLOAD_RECORD definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.IMAGE_UPLOAD_RECORD;

CREATE TABLE CCSP_SERVICEMGT.IMAGE_UPLOAD_RECORD (
                                                     ID INT8,
                                                     IMAGE_ID INT8,
                                                     STATUS INT4,
                                                     MSG VARCHAR(255),
                                                     REMARK VARCHAR(1000),
                                                     CREATE_BY INT8,
                                                     CREATE_TIME VARCHAR(30),
                                                     UPDATE_BY INT8,
                                                     UPDATE_TIME VARCHAR(30)
);


-- CCSP_SERVICEMGT.SERVICE_DOCKER_PORT_MAPPING definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SERVICE_DOCKER_PORT_MAPPING;

CREATE TABLE CCSP_SERVICEMGT.SERVICE_DOCKER_PORT_MAPPING (
                                                             ID INT8 DEFAULT 2 NOT NULL,
                                                             SERVICE_INFO_ID INT8 NOT NULL,
                                                             MAPPING_CODE VARCHAR(50) NOT NULL,
                                                             OUTSIDE_PORT INT4,
                                                             INSIDE_PORT INT4,
                                                             REMARK VARCHAR(1000),
                                                             CREATE_BY INT8,
                                                             CREATE_TIME VARCHAR(30),
                                                             UPDATE_BY INT8,
                                                             UPDATE_TIME VARCHAR(30),
                                                             CONSTRAINT SERVICE_DOCKER_PORT_MAPPING_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.SERVICE_GROUP definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SERVICE_GROUP;

CREATE TABLE CCSP_SERVICEMGT.SERVICE_GROUP (
                                               SERVICE_GROUP_ID INT8 NOT NULL,
                                               SERVICE_GROUP_CODE VARCHAR(90) NOT NULL,
                                               SERVICE_GROUP_NAME VARCHAR(270) NOT NULL,
                                               SERVICE_GROUP_TYPE INT4,
                                               SERVICE_GROUP_STATUS INT4 NOT NULL,
                                               TENANT_ID INT8,
                                               REGION_ID INT8,
                                               IS_SHARE INT4 DEFAULT 0 NOT NULL,
                                               INVALID_FLAG INT4 DEFAULT 0,
                                               REMARK VARCHAR(1000),
                                               CREATE_BY INT8,
                                               CREATE_TIME VARCHAR(30),
                                               UPDATE_BY INT8,
                                               UPDATE_TIME VARCHAR(30),
                                               CONSTRAINT SERVICE_GROUP_PRIMARY PRIMARY KEY (SERVICE_GROUP_ID)
);


-- CCSP_SERVICEMGT.SERVICE_GROUP_TO_BUSI_TYPE definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SERVICE_GROUP_TO_BUSI_TYPE;

CREATE TABLE CCSP_SERVICEMGT.SERVICE_GROUP_TO_BUSI_TYPE (
                                                            ID INT8 NOT NULL,
                                                            SERVICE_GROUP_ID INT8 NOT NULL,
                                                            BUSI_TYPE_ID INT8 NOT NULL,
                                                            REMARK VARCHAR(1000),
                                                            CREATE_BY INT8,
                                                            CREATE_TIME VARCHAR(30),
                                                            UPDATE_BY INT8,
                                                            UPDATE_TIME VARCHAR(30),
                                                            CONSTRAINT SERVICE_GROUP_TO_BUSI_TYPE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.SERVICE_GROUP_TO_GROUP definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SERVICE_GROUP_TO_GROUP;

CREATE TABLE CCSP_SERVICEMGT.SERVICE_GROUP_TO_GROUP (
                                                        ID INT8 NOT NULL,
                                                        SERVICE_GROUP_ID INT8 NOT NULL,
                                                        KMS_GROUP_ID INT8 NOT NULL,
                                                        CREATE_BY INT8,
                                                        CREATE_TIME VARCHAR(30),
                                                        UPDATE_BY INT8,
                                                        UPDATE_TIME VARCHAR(30),
                                                        CONSTRAINT SERVICE_GROUP_TO_GROUP_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.SERVICE_INFO definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SERVICE_INFO;

CREATE TABLE CCSP_SERVICEMGT.SERVICE_INFO (
                                              ID INT8 NOT NULL,
                                              SERVICE_TYPE_ID INT8 NOT NULL,
                                              SERVICE_NAME VARCHAR(150) NOT NULL,
                                              DEVICE_GROUP_ID INT8,
                                              SERVICE_GROUP_ID INT8,
                                              TENANT_ID INT8 NOT NULL,
                                              REGION_ID INT8,
                                              OPER_STATUS INT4,
                                              RUN_STATUS INT8 DEFAULT 2 NOT NULL,
                                              MGT_IP VARCHAR(20),
                                              MGT_PORT INT4,
                                              MGT_GATEWAY_IP VARCHAR(20),
                                              MGT_GATEWAY_PORT INT4,
                                              BUSI_IP VARCHAR(20),
                                              BUSI_PORT INT4,
                                              BUSI_GATEWAY_IP VARCHAR(20),
                                              BUSI_GATEWAY_PORT INT4,
                                              REMOTE_IP VARCHAR(20),
                                              REMOTE_PORT INT4,
                                              EXPAND_PORT INT4,
                                              TCP_PORT INT4,
                                              MONITOR_PORT INT4,
                                              GATEWAY_ID INT8,
                                              ROUTE_ID INT8,
                                              IS_ACTIVE_STANDBY INT4 DEFAULT 1 NOT NULL,
                                              DB_CREATED INT4,
                                              INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
                                              HMAC VARCHAR(255),
                                              REMARK VARCHAR(1000),
                                              CREATE_BY INT8,
                                              CREATE_TIME VARCHAR(30),
                                              UPDATE_BY INT8,
                                              UPDATE_TIME VARCHAR(30),
                                              DEPLOY_MOD INT4 DEFAULT 1,
                                              IMAGE_ID VARCHAR(255),
                                              CONTAINER_NAME VARCHAR(255),
                                              CONSTRAINT SERVICE_INFO_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.SERVICE_TYPE definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SERVICE_TYPE;

CREATE TABLE CCSP_SERVICEMGT.SERVICE_TYPE (
                                              ID INT8 NOT NULL,
                                              SERVICE_TYPE_NAME VARCHAR(150) NOT NULL,
                                              SERVICE_CODE VARCHAR(50) NOT NULL,
                                              PARENT_ID INT8 NOT NULL,
                                              SERVICE_CREATE_TYPE INT4,
                                              SERVICE_USE_TYPE INT4,
                                              MGT_PORT INT4,
                                              MGT_GATEWAY_PORT INT4,
                                              BUSI_PORT INT4,
                                              BUSI_GATEWAY_PORT INT4,
                                              REMOTE_PORT INT8 DEFAULT 18086,
                                              EXPAND_PORT INT4,
                                              TCP_PORT INT4,
                                              MONITOR_PORT INT4,
                                              IS_RESTART INT4 DEFAULT 1,
                                              IS_CREATE_MGT_ROUTE INT4 DEFAULT 1,
                                              IS_CREATE_BUSI_ROUTE INT4 DEFAULT 1,
                                              IS_CREATE_LOG_PLUGIN INT4 DEFAULT 1,
                                              LOG_PLUGIN_FORMAT VARCHAR(500),
                                              IS_MONITOR_DEVICE INT4 DEFAULT 2,
                                              CONFIG_DEVICE_TYPE INT4 DEFAULT 2,
                                              AVAILABLE_DEVICE_TYPE VARCHAR(200),
                                              CONNECT_TIME INT4,
                                              SEND_TIME INT4,
                                              READ_TIME INT4,
                                              IMAGE_ID INT8,
                                              SERVICE_PATH VARCHAR(100),
                                              DB_UPDATE_CONFIG_FLAG INT4,
                                              DB_COMMON INT4,
                                              DB_TENANT_COMMON INT4,
                                              DB_NAME VARCHAR(150),
                                              SERVICE_CLASS INT4,
                                              REMARK VARCHAR(1000),
                                              CREATE_BY INT8,
                                              CREATE_TIME VARCHAR(30),
                                              UPDATE_BY INT8,
                                              UPDATE_TIME VARCHAR(30),
                                              CONSTRAINT SERVICE_TYPE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE;

CREATE TABLE CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (
                                                           ID INT8 NOT NULL,
                                                           SERVICE_TYPE_ID INT8 NOT NULL,
                                                           BUSI_TYPE_ID INT8 NOT NULL,
                                                           BUSI_TYPE_NAME VARCHAR(150),
                                                           REMARK VARCHAR(1000),
                                                           CREATE_BY INT8,
                                                           CREATE_TIME VARCHAR(30),
                                                           UPDATE_BY INT8,
                                                           UPDATE_TIME VARCHAR(30)
);


-- CCSP_SERVICEMGT.SYS_JOB definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SYS_JOB;

CREATE TABLE CCSP_SERVICEMGT.SYS_JOB (
                                         JOB_ID INT8 NOT NULL,
                                         JOB_NAME VARCHAR(255) NOT NULL,
                                         JOB_GROUP VARCHAR(255) NOT NULL,
                                         SERVER_ID VARCHAR(100) NOT NULL,
                                         METHOD_URL VARCHAR(1500),
                                         JSON_PARAM TEXT NOT NULL,
                                         CRON_EXPRESSION VARCHAR(255),
                                         MISFIRE_POLICY VARCHAR(20) DEFAULT '3'::CHARACTER VARYING,
                                         CONCURRENT VARCHAR(1) DEFAULT '1'::CHARACTER VARYING,
                                         JOB_STATUS VARCHAR(1) DEFAULT '0'::CHARACTER VARYING,
                                         CREATED_BY INT8,
                                         CREATE_TIME VARCHAR(30),
                                         UPDATED_BY INT8,
                                         UPDATE_TIME VARCHAR(30),
                                         REMARK VARCHAR(1500),
                                         CONSTRAINT SYS_JOB_PRIMARY PRIMARY KEY (JOB_ID)
);


-- CCSP_SERVICEMGT.SYS_JOB_LOG definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SYS_JOB_LOG;

CREATE TABLE CCSP_SERVICEMGT.SYS_JOB_LOG (
                                             JOB_LOG_ID INT8 NOT NULL,
                                             JOB_ID INT8 NOT NULL,
                                             JOB_MESSAGE VARCHAR(1500),
                                             STATUS VARCHAR(1) NOT NULL,
                                             EXCEPTION_INFO VARCHAR(6000),
                                             CREATE_TIME VARCHAR(30) NOT NULL,
                                             TRIGGER_TIME INT8,
                                             CONSTRAINT SYS_JOB_LOG_PRIMARY PRIMARY KEY (JOB_LOG_ID)
);


-- CCSP_SERVICEMGT.SYS_TASK definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SYS_TASK;

CREATE TABLE CCSP_SERVICEMGT.SYS_TASK (
                                          TASK_ID INT8 NOT NULL,
                                          TASK_NAME VARCHAR(255) NOT NULL,
                                          TASK_GROUP VARCHAR(255) NOT NULL,
                                          SERVER_ID VARCHAR(255) NOT NULL,
                                          METHOD_URL VARCHAR(1000),
                                          JSON_PARAM TEXT NOT NULL,
                                          TASK_STATUS INT4 DEFAULT 0 NOT NULL,
                                          CREATE_TIME VARCHAR(30),
                                          UPDATE_TIME VARCHAR(30),
                                          REMARK VARCHAR(300),
                                          TIMEOUT INT4,
                                          START_TIME VARCHAR(255),
                                          END_TIME VARCHAR(255),
                                          POLICY VARCHAR(1) DEFAULT '0'::CHARACTER VARYING NOT NULL,
                                          CONSTRAINT SYS_TASK_PRIMARY PRIMARY KEY (TASK_ID)
);


-- CCSP_SERVICEMGT.SYS_TASK_LOG definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.SYS_TASK_LOG;

CREATE TABLE CCSP_SERVICEMGT.SYS_TASK_LOG (
                                              TASK_LOG_ID INT8 NOT NULL,
                                              TASK_ID INT8 NOT NULL,
                                              TASK_MESSAGE VARCHAR(3000),
                                              STATUS VARCHAR(1) NOT NULL,
                                              EXCEPTION_INFO VARCHAR(6000),
                                              CREATE_TIME VARCHAR(30),
                                              TRIGGER_TIME INT8,
                                              CONSTRAINT SYS_TASK_LOG_PRIMARY PRIMARY KEY (TASK_LOG_ID)
);


-- CCSP_SERVICEMGT.UPLOAD_FILE definition

-- Drop table

-- DROP TABLE CCSP_SERVICEMGT.UPLOAD_FILE;

CREATE TABLE CCSP_SERVICEMGT.UPLOAD_FILE (
                                             ID VARCHAR(255),
                                             FILE_NAME VARCHAR(255),
                                             FILE_REMOTE_PATH VARCHAR(255),
                                             FILE_DIGEST VARCHAR(255),
                                             BUSINESS_TYPE VARCHAR(255),
                                             FILE_SIZE INT8,
                                             REMARK VARCHAR(1000),
                                             CREATE_BY INT8,
                                             CREATE_TIME VARCHAR(30),
                                             UPDATE_BY INT8,
                                             UPDATE_TIME VARCHAR(30)
);


INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (1,1,'ccsp_pki');
INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (2,2,'ccsp_svs');
INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (4,4,'ccsp_kms');
INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (5,5,'ccsp_tsa');
INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (6,6,'ccsp_sms');
INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (7,7,'ccsp_secauth');
INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (8,8,'ccsp_secdb');
INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (9,9,'ccsp_storage');
INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (10,10,'ccsp_electronic_seal');
INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (11,11,'ccsp_vpn');
INSERT INTO CCSP_SERVICEMGT.DIC_BUSI_TYPE_TO_DATABASE (ID,SERVICE_TYPE_ID,DATABASE_UNIT_NAME) VALUES (12,12,'ccsp_ca');
INSERT INTO CCSP_SERVICEMGT.DIC_DATABASE_TYPE (ID,DATABASE_TYPE_CODE,VERISON,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1,'mysql',NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.DIC_DATABASE_TYPE (ID,DATABASE_TYPE_CODE,VERISON,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2,'gauss',NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.DIC_DATABASE_TYPE (ID,DATABASE_TYPE_CODE,VERISON,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3,'opengauss',NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.DIC_DATABASE_TYPE (ID,DATABASE_TYPE_CODE,VERISON,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (4,'dm',NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.DIC_DATABASE_TYPE (ID,DATABASE_TYPE_CODE,VERISON,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5,'kingbase',NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1,'加解密服务','pki',1,1,1,20000,NULL,20004,NULL,18086,20005,NULL,20002,1,1,1,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'',NULL,NULL,NULL,'2023-07-04 09:45:51');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2,'签名验签服务','svs',1,1,1,20010,NULL,20014,NULL,18086,20015,NULL,20012,1,1,1,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-03-28 19:13:30');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (4,'密钥管理服务','kms',1,1,1,20100,NULL,20121,NULL,18086,20134,20122,20102,1,1,1,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-03-28 19:13:30');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5,'时间戳服务','tsa',1,1,1,20300,NULL,20304,NULL,18086,NULL,NULL,20306,2,1,1,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-03-28 18:58:41');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6,'协同签名服务','sms',1,1,1,20513,NULL,20524,NULL,18086,NULL,NULL,20512,1,1,1,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-03-28 19:13:30');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (7,'动态令牌服务','secauth',1,1,1,20613,NULL,20624,NULL,18086,NULL,NULL,20612,1,1,1,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-03-28 18:58:41');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (8,'数据库加密服务','secdb',1,1,1,20400,NULL,20400,NULL,18086,NULL,NULL,20402,2,1,1,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-04-21 11:14:43');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (9,'文件加密服务','secstorage',1,1,1,20200,NULL,20200,NULL,18086,NULL,20222,20202,2,1,1,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-03-28 19:13:30');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (10,'电子签章服务','tsc',1,1,1,8011,NULL,8011,NULL,18086,NULL,NULL,8099,1,1,1,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-03-28 18:58:41');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (11,'SSLVPN加密通道服务','vpn',1,1,1,20700,NULL,20700,NULL,18086,NULL,NULL,20702,2,1,2,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-03-28 19:13:30');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE (ID,SERVICE_TYPE_NAME,SERVICE_CODE,PARENT_ID,SERVICE_CREATE_TYPE,SERVICE_USE_TYPE,MGT_PORT,MGT_GATEWAY_PORT,BUSI_PORT,BUSI_GATEWAY_PORT,REMOTE_PORT,EXPAND_PORT,TCP_PORT,MONITOR_PORT,IS_RESTART,IS_CREATE_MGT_ROUTE,IS_CREATE_BUSI_ROUTE,IS_CREATE_LOG_PLUGIN,LOG_PLUGIN_FORMAT,IS_MONITOR_DEVICE,CONFIG_DEVICE_TYPE,AVAILABLE_DEVICE_TYPE,CONNECT_TIME,SEND_TIME,READ_TIME,IMAGE_ID,SERVICE_PATH,DB_UPDATE_CONFIG_FLAG,DB_COMMON,DB_TENANT_COMMON,DB_NAME,SERVICE_CLASS,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (12,'数字证书认证服务','ca',1,1,1,20800,NULL,20801,NULL,18086,NULL,NULL,20899,2,1,1,2,'{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}',1,1,'0',180,180,180,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-03-28 19:13:30');
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1,1,1,'加解密业务',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2,2,2,'签名验签业务',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (4,4,4,'密钥管理业务',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5,5,5,'时间戳业务',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6,6,6,'协同签名业务',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (7,7,7,'动态令牌服务',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (8,8,8,'数据库加密业务',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (9,9,9,'文件加密业务',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (10,10,10,'电子签章业务',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (11,11,11,'SSLVPN加密通道业务',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_SERVICEMGT.SERVICE_TYPE_TO_BUSI_TYPE (ID,SERVICE_TYPE_ID,BUSI_TYPE_ID,BUSI_TYPE_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (12,12,12,'数字证书认证业务',NULL,NULL,NULL,NULL,NULL);
