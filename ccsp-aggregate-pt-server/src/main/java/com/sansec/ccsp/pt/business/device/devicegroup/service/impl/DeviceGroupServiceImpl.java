package com.sansec.ccsp.pt.business.device.devicegroup.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sansec.ccsp.device.devicegroup.api.DeviceGroupServiceApi;
import com.sansec.ccsp.device.devicegroup.request.*;
import com.sansec.ccsp.device.devicegroup.response.*;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.FreeDeviceByTenantDTO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoInGroupVO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoVO;
import com.sansec.ccsp.device.deviceinfo.response.GatewayVO;
import com.sansec.ccsp.pt.business.app.group.response.AppBusiToGroupVO;
import com.sansec.ccsp.pt.business.app.group.service.AppBusiToGroupService;
import com.sansec.ccsp.pt.business.common.config.service.CommonConfigService;
import com.sansec.ccsp.pt.business.device.device.service.DeviceInfoService;
import com.sansec.ccsp.pt.business.device.deviceInfo.service.impl.GatewayConvert;
import com.sansec.ccsp.pt.business.device.deviceType.service.DeviceTypeService;
import com.sansec.ccsp.pt.business.device.devicegroup.response.DeviceAndGroupVo;
import com.sansec.ccsp.pt.business.device.devicegroup.response.ServiceTypeToDeviceGroupVO;
import com.sansec.ccsp.pt.business.device.devicegroup.service.DeviceGroupService;
import com.sansec.ccsp.pt.business.device.devicegroup.service.ServiceTypeToDeviceGroupService;
import com.sansec.ccsp.pt.business.region.service.RegionService;
import com.sansec.ccsp.pt.business.servicemgt.gateway.request.ServiceGatewayDTO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.request.ServiceGatewayRouteDTO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.request.ServiceRouteBindDTO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.response.ServiceGatewayRouteVO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.response.ServiceGatewayVO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.service.ServiceGatewayRouteService;
import com.sansec.ccsp.pt.business.servicemgt.gateway.service.ServiceGatewayService;
import com.sansec.ccsp.pt.business.servicemgt.servicegroup.service.ServiceGroupService;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.convert.InterInvokerConvert;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.service.ServiceInfoService;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request.DeviceInvokeDTO;
import com.sansec.ccsp.pt.business.servicemgt.servicetype.service.ServiceTypeService;
import com.sansec.ccsp.pt.business.tenant.request.TenantToDeviceGroupAddDTO;
import com.sansec.ccsp.pt.business.tenant.response.TenantVO;
import com.sansec.ccsp.pt.business.tenant.service.TenantService;
import com.sansec.ccsp.pt.business.tenant.service.TenantToDeviceGroupService;
import com.sansec.ccsp.pt.business.tenant.service.TenantToServiceGroupService;
import com.sansec.ccsp.pt.common.constant.CommonConstant;
import com.sansec.ccsp.pt.common.enums.DeviceTypeEnum;
import com.sansec.ccsp.pt.common.enums.GroupTypeEnum;
import com.sansec.ccsp.pt.common.enums.ServiceEnum;
import com.sansec.ccsp.pt.common.enums.ServiceTypeEnum;
import com.sansec.ccsp.pt.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.pt.common.util.BusiTypeUtil;
import com.sansec.ccsp.pt.common.util.Utils;
import com.sansec.ccsp.security.util.LoginUserUtil;
import com.sansec.ccsp.servicemgt.servicegroup.response.ServiceGroupVO;
import com.sansec.ccsp.servicemgt.serviceinfo.api.ServiceInfoServiceApi;
import com.sansec.ccsp.servicemgt.serviceinfo.request.ServiceInfoDTO;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceInfoVO;
import com.sansec.ccsp.servicemgt.servicetype.response.ServiceTypeAndBusiVO;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import com.sansec.component.algorithm.utill.ComponentSynthesisEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Describe
 * @Author: d.s
 * @create 2023/3/3 14:36
 */
@Service
@Slf4j
public class DeviceGroupServiceImpl implements DeviceGroupService {

    @Resource
    private DeviceGroupServiceApi deviceGroupServiceApi;
    @Resource
    private DeviceInfoService deviceInfoService;
    @Resource
    private TenantService tenantService;
    @Resource
    private ServiceInfoService serviceInfoService;
    @Resource
    private AppBusiToGroupService appBusiToGroupService;
    @Resource
    private ServiceInfoServiceApi serviceInfoServiceApi;
    @Resource
    private ServiceTypeService serviceTypeService;
    @Resource
    private InterInvokerConvert interInvokerConvert;
    @Resource
    private BusiTypeUtil busiTypeUtil;
    @Resource
    private ServiceTypeToDeviceGroupService serviceTypeToDeviceGroupService;
    @Resource
    private CommonConfigService commonConfigService;
    @Resource
    private RegionService regionService;
    @Resource
    private ServiceGatewayRouteService serviceGatewayRouteService;
    @Resource
    private ServiceGatewayService serviceGatewayService;
    @Resource
    private DeviceTypeService deviceTypeService;

    @Resource
    GatewayConvert gatewayConvert;

    @Resource
    TenantToDeviceGroupService tenantToDeviceGroupService;
    @Resource
    ServiceGroupService serviceGroupService;
    @Resource
    TenantToServiceGroupService tenantToServiceGroupService;


    @Override
    public SecRestResponse<SecPageVO<DeviceGroupVO>> find(DeviceGroupPageDTO deviceGroupPageDTO) {
        Long tenantId = null ;
        if( LoginUserUtil.isTopTenant() ){
            tenantId = deviceGroupPageDTO.getTenantId();
            tenantService.checkTenantExist(tenantId);
        }else{
            //非顶级租户只查询租户自己的服务
            tenantId = LoginUserUtil.getTenantId();
        }

        //所有设备组都属于顶级租户
        if(CommonConstant.TOP_TENANT_ID.equals(tenantId)){
            deviceGroupPageDTO.setTenantId(null);
        }

        //如果租户id不为null，说明要根据租户id查询，根据租户id查询的方法是先查询出来关联的设备组id，再根据设备组id查询
        if(tenantId != null ){
            List<Long> deviceGroupIdList = tenantToDeviceGroupService.getAllDeviceGroupIdByTenantId(tenantId);
            if(deviceGroupIdList.isEmpty()){
                SecPageVO<DeviceGroupVO> secPageVO = new SecPageVO<>();
                secPageVO.setPageNum(deviceGroupPageDTO.getPageNum());
                secPageVO.setPageSize(deviceGroupPageDTO.getPageSize());
                secPageVO.setTotal(0);
                secPageVO.setList(new ArrayList<>());
                return ResultUtil.ok(secPageVO);
            }
            deviceGroupPageDTO.setDeviceGroupIdList(deviceGroupIdList);
            deviceGroupPageDTO.setTenantId(null);
        }

        //区域ID不为空，判断区域是否存在
        if (deviceGroupPageDTO.getRegionId() != null) {
            regionService.checkAndSearchRegion(deviceGroupPageDTO.getRegionId());
        }

        // 获取设备组分页列表
        SecRestResponse<SecPageVO<DeviceGroupVO>> restResponse = deviceGroupServiceApi.find(deviceGroupPageDTO);
        // 请求成功并有值
        if (restResponse.isSuccess() && restResponse.getResult() != null) {
            List<DeviceGroupVO> deviceGroupVOS = restResponse.getResult().getList();
            // 添加租户名称
            setTenantName(deviceGroupVOS);
            //区域名称
            setRegionName(deviceGroupVOS);
            //支持对外调用设备组添加网关业务地址
            setBusiUrl(deviceGroupVOS, tenantId);
        }

        return restResponse;
    }


    /**
     * @param deviceGroupFindListDTO 筛选条件
     * @return SecPageVO
     * @Description: 分页查询租户绑定的设备组
     */
    @Override
    public SecRestResponse<List<DeviceGroupVO>> tenantBindDeviceGroupList(DeviceGroupFindListDTO deviceGroupFindListDTO) {
        Long tenantId = null ;
        if( LoginUserUtil.isTopTenant() ){
            tenantId = deviceGroupFindListDTO.getTenantId();
            tenantService.checkTenantExist(tenantId);
        }else{
            tenantId = LoginUserUtil.getTenantId();
        }

        if(tenantId==null){
            throw new BusinessException(SecErrorCodeConstant.TENANT_ID_NULL);
        }
        List<Long> deviceGroupIdList = tenantToDeviceGroupService.getAllDeviceGroupIdByTenantId(tenantId);
        if( deviceGroupIdList.isEmpty() ){
            return ResultUtil.ok(new ArrayList<>());
        }

        deviceGroupFindListDTO.setDeviceGroupIdList(deviceGroupIdList);
        deviceGroupFindListDTO.setTenantId(null);

        SecRestResponse<List<DeviceGroupVO>> secRestResponse= list(deviceGroupFindListDTO);
        // 请求成功并有值
        if (secRestResponse.isSuccess() && secRestResponse.getResult() != null) {
            List<DeviceGroupVO> deviceGroupVOS = secRestResponse.getResult();
            // 添加租户名称
            setTenantName(deviceGroupVOS);
            //区域名称
            setRegionName(deviceGroupVOS);
            //支持对外调用设备组添加网关业务地址
            setBusiUrl(deviceGroupVOS, tenantId);
        }

        return secRestResponse;
    }

    @Override
    public SecRestResponse<List<DeviceGroupVO>> list(DeviceGroupFindListDTO deviceGroupFindListDTO) {
        Long tenantId = deviceGroupFindListDTO.getTenantId();
        //如果租户id不为null，说明要根据租户id查询，根据租户id查询的方法是先查询出来关联的设备组id，再根据设备组id查询
        if(tenantId != null && !tenantId.equals(CommonConstant.TOP_TENANT_ID)){
            TenantVO tenantVO = tenantService.checkTenantExist(tenantId);
            List<Long> deviceGroupIdList = tenantToDeviceGroupService.getUnsharedDeviceGroupIdByTenantId(tenantId);
            deviceGroupFindListDTO.setDeviceGroupIdList(deviceGroupIdList);
            deviceGroupFindListDTO.setTenantId(null);
        }
        //所有设备组都属于顶级租户
        if(CommonConstant.TOP_TENANT_ID.equals(tenantId)){
            deviceGroupFindListDTO.setTenantId(null);
        }

        //区域ID不为空，判断区域是否存在
        if (deviceGroupFindListDTO.getRegionId() != null) {
            regionService.checkAndSearchRegion(deviceGroupFindListDTO.getRegionId());
        }

        SecRestResponse<List<DeviceGroupVO>> response = deviceGroupServiceApi.list(deviceGroupFindListDTO);
        List<DeviceGroupVO> deviceGroupVOS = response.getResult();
        if (response.isSuccess() && deviceGroupVOS != null) {
            // 添加租户名称
            setTenantName(deviceGroupVOS);
            //区域名称
            setRegionName(deviceGroupVOS);

            //支持对外调用设备组添加网关业务地址
            setBusiUrl(deviceGroupVOS, tenantId);
        }
        return response;
    }

    @Override
    public SecRestResponse<List<DeviceGroupIdNameVO>> getDeviceGroupIdNameList(DeviceGroupIdNameDTO deviceGroupIdNameDTO) {
        checkDeviceGroupIdName(deviceGroupIdNameDTO);
        return deviceGroupServiceApi.getDeviceGroupIdNameList(deviceGroupIdNameDTO);
    }

    /**
     * 根据服务组id获取设备组
     * @param dto
     * @return
     */
    @Override
    public SecRestResponse<List<DeviceGroupIdNameVO>> getDeviceGroupByServiceGroupId(GetDeviceGroupByServiceGroupDTO dto) {
        Long serviceGroupId = dto.getServiceGroupId();
        ServiceGroupVO serviceGroupVO = serviceGroupService.getServiceGroupById(serviceGroupId);
        Integer isShare = serviceGroupVO.getIsShare();
        Long regionId = serviceGroupVO.getRegionId();
        DeviceGroupIdNameDTO deviceGroupIdNameDTO = new DeviceGroupIdNameDTO();
        deviceGroupIdNameDTO.setIsShare(isShare);
        deviceGroupIdNameDTO.setRegionId(regionId);
        //独享服务组根据租户id查询
        if(serviceGroupVO.checkIsShare()==false){
            Map<Long, Long> serviceGroupTenantMap = tenantToServiceGroupService.getNotShareGroupIdMapBusiType0(Arrays.asList(serviceGroupId));
            Long tenantId = serviceGroupTenantMap.get(serviceGroupId);
            //如果租户id不为null，说明要根据租户id查询，根据租户id查询的方法是先查询出来关联的设备组id，再根据设备组id查询
            if(tenantId != null && !tenantId.equals(CommonConstant.TOP_TENANT_ID)){
                List<Long> deviceGroupIdList = tenantToDeviceGroupService.getAllDeviceGroupIdByTenantId(tenantId);
                deviceGroupIdNameDTO.setDeviceGroupIdList(deviceGroupIdList);
            }

            deviceGroupIdNameDTO.setBusiTypeIdList(new ArrayList<>());
        }else{
            //共享服务组
            deviceGroupIdNameDTO.setBusiTypeIdList(dto.getBusiTypeIdList());
        }
        //添加服务时使用，应该排除掉异构设备组
        deviceGroupIdNameDTO.setIsRest(0);
        return deviceGroupServiceApi.getDeviceGroupIdNameList(deviceGroupIdNameDTO);
    }

    @Override
    public SecRestResponse<List<DeviceGroupIdNameVO>> getAllDeviceGroupIdNameList(DeviceGroupIdNameDTO deviceGroupIdNameDTO) {
        checkDeviceGroupIdName(deviceGroupIdNameDTO);
        return deviceGroupServiceApi.getAllDeviceGroupIdNameList(deviceGroupIdNameDTO);
    }

    private void checkDeviceGroupIdName(DeviceGroupIdNameDTO deviceGroupIdNameDTO) {
        Long tenantId = deviceGroupIdNameDTO.getTenantId();
        //如果租户id不为null，说明要根据租户id查询，根据租户id查询的方法是先查询出来关联的设备组id，再根据设备组id查询
        if(tenantId != null && !tenantId.equals(CommonConstant.TOP_TENANT_ID)){
            TenantVO tenantVO = tenantService.checkTenantExist(tenantId);
            List<Long> deviceGroupIdList = tenantToDeviceGroupService.getAllDeviceGroupIdByTenantId(tenantId);
            deviceGroupIdNameDTO.setDeviceGroupIdList(deviceGroupIdList);
            deviceGroupIdNameDTO.setTenantId(null);
        }
        //所有设备组都属于顶级租户
        if(CommonConstant.TOP_TENANT_ID.equals(tenantId)){
            deviceGroupIdNameDTO.setTenantId(null);
        }

        //区域ID不为空，判断区域是否存在
        if (deviceGroupIdNameDTO.getRegionId() != null) {
            regionService.checkAndSearchRegion(deviceGroupIdNameDTO.getRegionId());
        }

        List<Long> busiTypeIdList = deviceGroupIdNameDTO.getBusiTypeIdList();
        if (busiTypeIdList == null) {
            deviceGroupIdNameDTO.setBusiTypeIdList(new ArrayList<>());
        } else {
            Map<Long, String> busiTypeMap = busiTypeUtil.getBusiTypeMap();
            busiTypeIdList.forEach(busiTypeId -> {
                if (!busiTypeMap.containsKey(busiTypeId)) {
                    throw new BusinessException(SecErrorCodeConstant.BUSI_TYPE_NOT_EXIST);
                }
            });
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SecRestResponse<Long> add(DeviceGroupDTO deviceGroupDTO) {
        Long tenantId = deviceGroupDTO.getTenantId();
        if(tenantId != null){
            tenantService.checkTenantExist(tenantId);
        }
        //区域ID不为空，判断区域是否存在
        if (deviceGroupDTO.getRegionId() != null) {
            regionService.checkAndSearchRegion(deviceGroupDTO.getRegionId());
        }
        SecRestResponse<DeviceGroupContextVO> response = deviceGroupServiceApi.add(deviceGroupDTO);
        if(response.isSuccess()){
            DeviceGroupContextVO contextVO = response.getResult();
            //独享设备组添加TenantToDeviceGroup记录
            if(contextVO.getDeviceGroupVO().checkIsShare() == false){
                TenantToDeviceGroupAddDTO tenantToDeviceGroupAddDTO = new TenantToDeviceGroupAddDTO();
                tenantToDeviceGroupAddDTO.setTenantId(contextVO.getTenantId());
                tenantToDeviceGroupAddDTO.setDeviceGroupId(contextVO.getDeviceGroupId());
                tenantToDeviceGroupAddDTO.setIsShare(contextVO.getDeviceGroupVO().getIsShare());
                tenantToDeviceGroupAddDTO.setIsRest(contextVO.getDeviceGroupVO().getIsRest());
                tenantToDeviceGroupAddDTO.setBusiTypeIdList(contextVO.getBusiTypeIdList());
                tenantToDeviceGroupService.addRecord(tenantToDeviceGroupAddDTO);
            }
            return ResultUtil.ok(contextVO.getDeviceGroupId());
        }else{
            throw new BusinessException(response.getCode());
        }
    }

    @Override
    public Long addResourceGroup(DeviceGroupDTO deviceGroupDTO) {
        SecRestResponse<DeviceGroupContextVO> restResponse = deviceGroupServiceApi.add(deviceGroupDTO);
        if (!restResponse.isSuccess()) {
            throw new BusinessException(restResponse.getCode());
        }
        return restResponse.getResult().getDeviceGroupId();
    }

    @Override
    public SecRestResponse<Long> edit(DeviceGroupEditDTO deviceGroupDTO) {
        return deviceGroupServiceApi.edit(deviceGroupDTO);
    }

    @Override
    public SecRestResponse<Object> deleteById(DeviceGroupDeleteDTO deviceGroupDTO) {
        log.info("DeviceGroupServiceImpl.deleteById() , deviceGroupDTO= {}", deviceGroupDTO);
        DevicesGroupIdDTO devicesGroupIdDTO = new DevicesGroupIdDTO();
        devicesGroupIdDTO.setDeviceGroupId(deviceGroupDTO.getDeviceGroupId());
        DeviceGroupVO deviceGroupVO = this.info(devicesGroupIdDTO);
        //异构设备组
        if(deviceGroupVO.checkIsRest()){
            if(deviceGroupVO.checkIsShare()){
                List<Long> tenantIdList = tenantToDeviceGroupService.getAllTenantIdByGroupId(deviceGroupDTO.getDeviceGroupId());
                if(tenantIdList.size() > 0){
                    log.error("DeviceGroupServiceImpl.deleteById,删除设备组影响租户使用，请先解绑设备组和租户的关系,error={}",SecErrorCodeConstant.DELETE_DEVICE_GROUP_INFLUENCE_TENANT_ERROR);
                    throw new BusinessException(SecErrorCodeConstant.DELETE_DEVICE_GROUP_INFLUENCE_TENANT_ERROR);
                }
            }
            //不再校验异构设备组和应用的关系
//            List<AppBusiToGroupVO> appBusiToGroupPOS = appBusiToGroupService.getBusiGroupListByGroupId(deviceGroupDTO.getDeviceGroupId());
//            if(appBusiToGroupPOS.size()>0){
//                log.error("DeviceGroupServiceImpl.deleteById,删除设备组影响应用使用，请先解绑设备组和应用的关系,error={}",SecErrorCodeConstant.DELETE_DEVICE_GROUP_INFLUENCE_BUSI_ERROR);
//                throw new BusinessException(SecErrorCodeConstant.DELETE_DEVICE_GROUP_INFLUENCE_BUSI_ERROR);
//            }
        }else{
            SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.findList(ServiceInfoDTO.builder().deviceGroupId(deviceGroupDTO.getDeviceGroupId()).build());
            if (res.isSuccess() && CollectionUtils.isNotEmpty(res.getResult())) {
                throw new BusinessException(SecErrorCodeConstant.DELETE_DEVICE_GROUP_INFLUENCE_SERVICE_ERROR);
            }
        }

        SecRestResponse<Object> response = deviceGroupServiceApi.deleteById(deviceGroupDTO);

        if (response.isSuccess()) {
            if (Objects.equals(deviceGroupVO.getIsRest(), 1)) {
                deleteDeviceGroupRoute(deviceGroupVO);
            }else{
                String add_service_is_auto_add_device = commonConfigService.getConfigValueByConfigCode(CommonConstant.ADD_SERVICE_IS_AUTO_ADD_DEVICE);
                if (CommonConstant.TRUE.equals(add_service_is_auto_add_device)) {
                    serviceTypeToDeviceGroupService.deleteByDeviceGroupId(Arrays.asList(deviceGroupDTO.getDeviceGroupId()));
                }
            }
            tenantToDeviceGroupService.delete(null,deviceGroupVO.getDeviceGroupId(),null);
        }

        return response;
    }

    /**
     * 根据租户Id批量删除设备组列表
     *
     * @param tenantId
     * @return
     */
    @Override
    public Integer deleteListByTenantId(Long tenantId) {
        List<Long> deviceGroupIdList = tenantToDeviceGroupService.getUnsharedDeviceGroupIdByTenantId(tenantId);
        DeleteByTenantIdDTO deleteByTenantIdDTO = new DeleteByTenantIdDTO();
        deleteByTenantIdDTO.setTenantId(tenantId);
        deleteByTenantIdDTO.setDeviceGroupIdList(deviceGroupIdList);
        SecRestResponse<List<Long>> response = deviceGroupServiceApi.deleteListByTenantId(deleteByTenantIdDTO);
        if (response.isSuccess()) {
            //删除设备和租户关系表
            tenantToDeviceGroupService.delete(tenantId,null,null);
            List<Long> deviceGroupIds = response.getResult();
            if (CollectionUtils.isEmpty(deviceGroupIds)) {
                return 0;
            } else {
                serviceTypeToDeviceGroupService.deleteByDeviceGroupId(deviceGroupIds);
                return deviceGroupIds.size();
            }
        } else {
            log.error("deviceGroupServiceApi.deleteListByTenantId(); code={};error={}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getCode());
        }
    }

    @Override
    public DeviceGroupVO info(DevicesGroupIdDTO devicesGroupIdDTO) {
        SecRestResponse<DeviceGroupVO> response = deviceGroupServiceApi.info(devicesGroupIdDTO);
        if (response.isSuccess() && response.getResult() != null) {
            DeviceGroupVO deviceGroupVO = response.getResult();

            if (deviceGroupVO.checkIsShare()==false) {
                Map<Long, Long> groupToTenantIdMap = tenantToDeviceGroupService.getUnsharedGroupToTenantIdMap(Arrays.asList(deviceGroupVO.getDeviceGroupId()));
                Long tenantId = groupToTenantIdMap.get(deviceGroupVO.getDeviceGroupId());
                if(tenantId != null){
                    TenantVO tenantVO = tenantService.getTenantById(tenantId);
                    deviceGroupVO.setTenantId(tenantId);
                    deviceGroupVO.setTenantName(tenantVO.getTenantName());
                }
            }

            if (deviceGroupVO.getRegionId() != null) {
                deviceGroupVO.setRegionName(regionService.getRegionNameById(deviceGroupVO.getRegionId()));
            }
            return deviceGroupVO;
        } else {
            throw new BusinessException(response.getCode());
        }


    }

    @Override
    public SecRestResponse<Long> addDeviceToGroup(DeviceToGroupDTO addDeviceToGroupDTO) {
        log.info("DeviceGroupServiceImpl.addDeviceToGroup() , addDeviceToGroupDTO= {}", addDeviceToGroupDTO);
        //获取设备组对象
        DeviceGroupVO deviceGroupVO = getDeviceGroupInfoById(addDeviceToGroupDTO.getDeviceGroupId());
        TenantVO tenantVO = null;
        if (!Objects.equals(deviceGroupVO.getTenantId(), CommonConstant.TOP_TENANT_ID) && deviceGroupVO.getDeviceGroupType() != 1) {
            tenantVO = tenantService.getTenantById(deviceGroupVO.getTenantId());
            checkDeleteDevice(tenantVO.getDeviceGroupId(), Collections.singletonList(addDeviceToGroupDTO.getDeviceId()), true);
        }
        //获取区域网关
        ServiceGatewayVO serviceGatewayVO=deviceInfoService.getGateWayByDeviceId(addDeviceToGroupDTO.getDeviceId());
        Map<Long, ServiceGatewayVO> aliveGateWayRegion = serviceGatewayService.getAllAliveGateWayRegion();
        Map<Long, GatewayVO> gatewayVOMap = gatewayConvert.serviceGateWayToDeviceMap(aliveGateWayRegion);
        if(serviceGatewayVO!=null){
            addDeviceToGroupDTO.setProxyRouteIs(true);
            addDeviceToGroupDTO.setGatewayIp(serviceGatewayVO.getIp());
            addDeviceToGroupDTO.setGatewayPort(serviceGatewayVO.getPort());
            addDeviceToGroupDTO.setGatewayVOMap(gatewayVOMap);
        }else{
            addDeviceToGroupDTO.setProxyRouteIs(false);
        }
        SecRestResponse<Long> response = deviceGroupServiceApi.addDevice(addDeviceToGroupDTO);
        if (response.isSuccess()) {
            if (Objects.equals(deviceGroupVO.getIsRest(), 1)) {
                DeviceInfoInGroupVO deviceInfoVO = deviceInfoService.getDeviceInfoInGroupVO(addDeviceToGroupDTO.getDeviceId());
                List<Long> deviceIdList = Collections.singletonList(deviceInfoVO.getDeviceId());
                List<String> busiIpPorts = Collections.singletonList(deviceInfoVO.getBusiIp() + ":" + deviceInfoVO.getBusiPort());
                //迁移成功，修改路由
                if (tenantVO != null) {
                    setGroupDeviceRoute(tenantVO, deviceGroupVO, deviceIdList, busiIpPorts, true);
                }
            } else {
                if (tenantVO != null) {
                    deviceInfoToInvoke(tenantVO.getDeviceGroupId());
                }
                deviceInfoToInvoke(addDeviceToGroupDTO.getDeviceGroupId());
            }

            return response;
        } else {
            throw new BusinessException(response.getCode(), response.getMessage());
        }
    }

    /**
     * 设备组绑定设备时，路由处理
     *
     * @param tenantVO      租户对象
     * @param deviceGroupVO 设备组对象
     * @param deviceIdList  设备对象IDS
     * @param busiIpPorts   设备IP和端口
     * @param isBind        是否绑定
     * @return
     */
    private SecRestResponse setGroupDeviceRoute(TenantVO tenantVO, DeviceGroupVO deviceGroupVO, List<Long> deviceIdList,
                                                List<String> busiIpPorts, Boolean isBind) {

        Integer routeType;
        if (Objects.equals(deviceGroupVO.getNetPro(), CommonConstant.DEVICE_GROUP_NET_PRO_TCP)) {
            //TCP路由类型
            routeType = 4;
        } else {
            //HTTPS路由类型
            routeType = 2;
        }

        ServiceRouteBindDTO serviceRouteBindDTO = ServiceRouteBindDTO.builder()
                .regionId(deviceGroupVO.getRegionId())
                .routeType(routeType)
                .serviceIdList(deviceIdList)
                .groupId(deviceGroupVO.getDeviceGroupId())
                .groupName(deviceGroupVO.getDeviceGroupName())
                .serviceTypeId(deviceGroupVO.getDeviceTypeId())
                .groupCode(deviceGroupVO.getDeviceGroupName())
                .groupType(GroupTypeEnum.DEVICE.getCode())
                .serviceType(ServiceEnum.DEVICE.getCode())
                .tenantId(tenantVO.getTenantId())
                .tenantCode(tenantVO.getTenantCode())
                .ipPorts(busiIpPorts)
                .build();

        SecRestResponse response;
        if (isBind) {
            response = serviceGatewayRouteService.serviceBindRoute(serviceRouteBindDTO);
        } else {
            response = serviceGatewayRouteService.serviceUnBindRoute(serviceRouteBindDTO);
        }
        return response;
    }

    /**
     * 删除设备组路由
     *
     * @param deviceGroupVO
     */
    private void deleteDeviceGroupRoute(DeviceGroupVO deviceGroupVO) {
        Integer routeType;
        if (Objects.equals(deviceGroupVO.getNetPro(), CommonConstant.DEVICE_GROUP_NET_PRO_TCP)) {
            //TCP路由类型
            routeType = 4;
        } else {
            //HTTPS路由类型
            routeType = 2;
        }
        //todo  设备类型处理
        serviceGatewayRouteService.deleteRoute(null, deviceGroupVO.getDeviceGroupId(), null, routeType);
    }

    /**
     * 直接绑定空闲设备到租户下服务类型的设备组
     *
     * @param tenantId
     * @param serviceTypeId
     * @param deviceIdList
     * @return
     */
    @Override
    public DeviceAndGroupVo addFreeDeviceToGroup(Long tenantId, Long serviceTypeId, List<Long> deviceIdList) {
        log.info("直接绑定空闲设备到租户下服务类型的设备组addFreeDeviceToGroup,tenantId = {}, serviceTypeId = {}", tenantId, serviceTypeId);
        //传入设备为空时，随机获取一个空闲设备
        if (deviceIdList == null) {
            deviceIdList = new ArrayList<>();
            log.info("addFreeDeviceToGroup: device=null");
            String device_auto_add_no_type = commonConfigService.getConfigValueByConfigCode(CommonConstant.DEVICE_AUTO_ADD_NO_TYPE);
            //todo 过滤掉对外提供业务服务的设备
            List<DeviceInfoInGroupVO> deviceInfoInGroupVOS = deviceInfoService.getFreeDeviceInfoExcludeType(device_auto_add_no_type);

            if (CollectionUtils.isNotEmpty(deviceInfoInGroupVOS)) {
                Long deviceId = deviceInfoInGroupVOS.get(0).getDeviceId();
                deviceIdList.add(deviceId);
                log.info("addFreeDeviceToGroup: device is Null; device={}", deviceId);
            } else {
                // 无空闲设备可用
                log.error("DeviceGroupServiceImpl.addFreeDeviceToGroup(); deviceInfoInGroupVOS is empty error={}", "无空闲设备可用");
                throw new BusinessException(SecErrorCodeConstant.NO_FREE_DEVICE_AVAILABLE);
            }
        } else {
            log.info("addFreeDeviceToGroup: deviceIdList={}", deviceIdList.stream().map(String::valueOf).collect(Collectors.toList()));
            List<DeviceInfoVO> deviceInfoVOList = deviceInfoService.getDeviceInfoListByIds(deviceIdList);

            if (CollectionUtils.isEmpty(deviceInfoVOList)) {
                log.error("DeviceGroupServiceImpl.addFreeDeviceToGroup(); deviceInfoVOList is empty  error={}", "设备不存在");
                throw new BusinessException(SecErrorCodeConstant.DEVICE_NOT_EXIST);
            }
        }

        TenantVO tenantVO = tenantService.getTenantById(tenantId);

        //判断租户下是否已存在对应设备组，如果无，创建一个新的设备组
        Long deviceGroupId = null;
        Long serviceTypeToDeviceGroupId = null;
        ServiceTypeToDeviceGroupVO serviceTypeToDeviceGroupVO = serviceTypeToDeviceGroupService.getDeviceGroupId(tenantId, serviceTypeId);
        if (serviceTypeToDeviceGroupVO == null) {

            DeviceGroupDTO deviceGroupDTO = new DeviceGroupDTO();
            if (ServiceTypeEnum.byId(serviceTypeId) != null) {
                String serviceTypeName = ServiceTypeEnum.byId(serviceTypeId).getName();
                deviceGroupDTO.setDeviceGroupName(String.format("%s_%s", tenantVO.getTenantCode(), serviceTypeName));
            } else {
                log.error("DeviceGroupServiceImpl.addFreeDeviceToGroup(); ServiceTypeEnum.byId(serviceTypeId) == null error={}", "绑定服务失败，服务类型需为加解密服务、签名验签服务、杂凑服务或密钥管理服务");
                throw new BusinessException(SecErrorCodeConstant.SERVICE_TYPE_ERROR);
            }

            deviceGroupDTO.setDeviceGroupType(2);
            deviceGroupDTO.setIsShare(0);
            deviceGroupDTO.setTenantId(tenantId);
            //todo 需更新代码
//            deviceGroupDTO.setRegionId(tenantVO.getRegionId());
            SecRestResponse<DeviceGroupContextVO> deviceGroupResponse = deviceGroupServiceApi.add(deviceGroupDTO);

            if (Utils.isSuccess(deviceGroupResponse)) {
                deviceGroupId = deviceGroupResponse.getResult().getDeviceGroupId();
                serviceTypeToDeviceGroupId = serviceTypeToDeviceGroupService.addServiceTypeToDeviceGroup(tenantId, serviceTypeId, deviceGroupId);
                log.info("addFreeDeviceToGroup: create: deviceGroupId={}, serviceTypeToDeviceGroupId={}", deviceGroupId, serviceTypeToDeviceGroupId);
            } else {
                log.error("DeviceGroupServiceImpl.addFreeDeviceToGroup(); deviceGroupResponse is not success error={}", deviceGroupResponse.getMessage());
                throw new BusinessException(deviceGroupResponse.getCode());
            }
        } else {
            deviceGroupId = serviceTypeToDeviceGroupVO.getDeviceGroupId();
            log.info("addFreeDeviceToGroup: deviceGroupId={}", deviceGroupId);
        }

        try {
            DeviceToTenantGroupDTO deviceToTenantGroupDTO = new DeviceToTenantGroupDTO();
            deviceToTenantGroupDTO.setDeviceGroupId(deviceGroupId);
            deviceToTenantGroupDTO.setDeviceIdList(deviceIdList);
            //获取区域网关
            ServiceGatewayVO serviceGatewayVO=deviceInfoService.getGateWayByDeviceId(deviceToTenantGroupDTO.getDeviceIdList().get(0));
            Map<Long, ServiceGatewayVO> aliveGateWayRegion = serviceGatewayService.getAllAliveGateWayRegion();
            Map<Long, GatewayVO> gatewayVOMap = gatewayConvert.serviceGateWayToDeviceMap(aliveGateWayRegion);
            if(serviceGatewayVO!=null){
                deviceToTenantGroupDTO.setProxyRouteIs(true);
                deviceToTenantGroupDTO.setGatewayIp(serviceGatewayVO.getIp());
                deviceToTenantGroupDTO.setGatewayPort(serviceGatewayVO.getPort());
                deviceToTenantGroupDTO.setGatewayVOMap(gatewayVOMap);
            }else{
                deviceToTenantGroupDTO.setProxyRouteIs(false);
            }
            SecRestResponse<Long> response = deviceGroupServiceApi.addDeviceToTenantGroup(deviceToTenantGroupDTO);
            if (!response.isSuccess()) {
                log.error("DeviceGroupServiceImpl.addFreeDeviceToGroup();!response.isSuccess() error={}", response.getMessage());
                throw new BusinessException(response.getCode());
            } else {

                return DeviceAndGroupVo.builder().deviceGroupId(deviceGroupId)
                        .serviceTypeToDeviceGroupId(serviceTypeToDeviceGroupId).deviceIds(deviceIdList).build();
            }
        } catch (BusinessException e) {
            log.error("DeviceGroupServiceImpl.addFreeDeviceToGroup(); with BusinessException error={}", e.getMessage());
            if (serviceTypeToDeviceGroupId != null) {
                DeviceGroupDeleteDTO deviceGroupDeleteDTO = new DeviceGroupDeleteDTO();
                deviceGroupDeleteDTO.setDeviceGroupId(deviceGroupId);
                this.deleteById(deviceGroupDeleteDTO);
            }
            throw e;
        }
    }

    @Override
    public SecRestResponse<List<Long>> addDeviceListToGroup(DeviceListToGroupDTO addDeviceListToGroupDTO) {
        log.info("DeviceGroupServiceImpl.addDeviceListToGroup() , addDeviceListToGroupDTO= {}", addDeviceListToGroupDTO);
        DevicesGroupIdDTO devicesGroupIdDTO = new DevicesGroupIdDTO();
        devicesGroupIdDTO.setDeviceGroupId(addDeviceListToGroupDTO.getDeviceGroupId());
        DeviceGroupVO deviceGroupVO = this.info(devicesGroupIdDTO);
        //独享设备组获取所属租户id和该租户下所有的设备组id
        if(deviceGroupVO.checkIsShare()==false){
            Map<Long, Long> unsharedGroupToTenantIdMap = tenantToDeviceGroupService.getUnsharedGroupToTenantIdMap(Arrays.asList(deviceGroupVO.getDeviceGroupId()));
            Long tenantId = unsharedGroupToTenantIdMap.get(deviceGroupVO.getDeviceGroupId());
            List<Long> deviceGroupIdList = tenantToDeviceGroupService.getUnsharedDeviceGroupIdByTenantId(tenantId);
            addDeviceListToGroupDTO.setTenantId(tenantId);
            addDeviceListToGroupDTO.setDeviceGroupIdList(deviceGroupIdList);
        }

        //获取区域网关
        ServiceGatewayVO serviceGatewayVO=deviceInfoService.getGateWayByDeviceId(addDeviceListToGroupDTO.getDeviceIdList().get(0));
        Map<Long, ServiceGatewayVO> aliveGateWayRegion = serviceGatewayService.getAllAliveGateWayRegion();
        Map<Long, GatewayVO> gatewayVOMap = gatewayConvert.serviceGateWayToDeviceMap(aliveGateWayRegion);
        if(serviceGatewayVO!=null){
            addDeviceListToGroupDTO.setProxyRouteIs(true);
            addDeviceListToGroupDTO.setGatewayIp(serviceGatewayVO.getIp());
            addDeviceListToGroupDTO.setGatewayPort(serviceGatewayVO.getPort());
            addDeviceListToGroupDTO.setGatewayVOMap(gatewayVOMap);
        }else{
            addDeviceListToGroupDTO.setProxyRouteIs(false);
        }
        SecRestResponse<List<DeviceInfoInGroupVO>> response = deviceGroupServiceApi.addDeviceList(addDeviceListToGroupDTO);
        if (response.isSuccess() && response.getResult() != null) {
            List<DeviceInfoInGroupVO> deviceInfoInGroupVOList = response.getResult();
            List<Long> deviceIdList = deviceInfoInGroupVOList.stream().map(DeviceInfoInGroupVO::getDeviceId)
                    .distinct().collect(Collectors.toList());
            try {
                //异构设备组查询有没有绑定租户，绑定租户需要更新路由
                if (Objects.equals(deviceGroupVO.getIsRest(), 1)) {
                    List<String> busiIpPorts = deviceInfoInGroupVOList.stream().map(item ->
                            item.getBusiIp() + ":" + item.getBusiPort()).collect(Collectors.toList());
                    List<Long> allTenantIdByGroupId = tenantToDeviceGroupService.getAllTenantIdByGroupId(addDeviceListToGroupDTO.getDeviceGroupId());
                    for (Long tenantId : allTenantIdByGroupId) {
                        TenantVO tenantVO = tenantService.getTenantById(tenantId);
                        setGroupDeviceRoute(tenantVO, deviceGroupVO, deviceIdList, busiIpPorts, true);
                    }
                } else {
                    //非异构设备组需要重新设置服务的连接设备
                    deviceInfoToInvoke(addDeviceListToGroupDTO.getDeviceGroupId());
                }

                return ResultUtil.ok(deviceIdList);
            } catch (Exception e) {  //执行错误回滚
                log.error("DeviceGroupServiceImpl.addDeviceListToGroup(); error={}",
                        e.getMessage());
                //回滚释放设备
                for (Long deviceId : deviceIdList) {
                    DeviceToGroupDTO deviceToGroupDTO = new DeviceToGroupDTO();
                    deviceToGroupDTO.setDeviceGroupId(deviceGroupVO.getDeviceGroupId());
                    deviceToGroupDTO.setDeviceId(deviceId);
                    deviceToGroupDTO.setProxyRouteIs(addDeviceListToGroupDTO.isProxyRouteIs());
                    if(addDeviceListToGroupDTO.isProxyRouteIs()){
                        deviceToGroupDTO.setGatewayIp(addDeviceListToGroupDTO.getGatewayIp());
                        deviceToGroupDTO.setGatewayPort(addDeviceListToGroupDTO.getGatewayPort());
                    }
                    deviceGroupServiceApi.delDevice(deviceToGroupDTO);
                }
                if (Objects.equals(deviceGroupVO.getIsRest(), 1)) {
                    log.error("DeviceGroupServiceImpl.addDeviceListToGroup(); code={};error={}",
                            SecErrorCodeConstant.DEVICE_GROUP_ADD_ROUTE_ERROR, "设备组添加网关路由错误");
                    throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_ADD_ROUTE_ERROR);
                } else {
                    log.error("DeviceGroupServiceImpl.addDeviceListToGroup(); code={};error={}",
                            SecErrorCodeConstant.DEVICE_GROUP_DOWN_SERVICE_ERROR, "设备组下发的服务时报错");
                    throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_DOWN_SERVICE_ERROR);
                }
            }
        } else {
            throw new BusinessException(response.getCode());
        }
    }

    @Override
    public SecRestResponse<Object> delDeviceToGroup(DeviceToGroupDTO delDeviceToGroupDTO) {
        Long deviceGroupId = delDeviceToGroupDTO.getDeviceGroupId();
        DevicesGroupIdDTO devicesGroupIdDTO = new DevicesGroupIdDTO();
        devicesGroupIdDTO.setDeviceGroupId(delDeviceToGroupDTO.getDeviceGroupId());
        DeviceGroupVO deviceGroupVO = this.info(devicesGroupIdDTO);

        if (deviceGroupVO == null) {
            throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_NOT_EXIST);
        }

        //异构设备不需要校验应用和服务
        if(deviceGroupVO.checkIsRest()==false){
            checkDeleteDevice(deviceGroupId, Arrays.asList(delDeviceToGroupDTO.getDeviceId()), false);
        }

        SecRestResponse<Object> restResponse = deviceGroupServiceApi.delDevice(delDeviceToGroupDTO);

        if (restResponse.isSuccess()) {
            if (Objects.equals(deviceGroupVO.getIsRest(), 1)) {
                DeviceInfoInGroupVO deviceInfoVO = deviceInfoService.getDeviceInfoInGroupVO(delDeviceToGroupDTO.getDeviceId());
                List<Long> deviceIdList = Collections.singletonList(deviceInfoVO.getDeviceId());
                List<String> busiIpPorts = Collections.singletonList(deviceInfoVO.getBusiIp() + ":" + deviceInfoVO.getBusiPort());

                List<Long> allTenantIdByGroupId = tenantToDeviceGroupService.getAllTenantIdByGroupId(deviceGroupId);
                for (Long tenantId : allTenantIdByGroupId) {
                    TenantVO tenantVO = tenantService.getTenantById(tenantId);
                    //迁移成功，修改路由
                    setGroupDeviceRoute(tenantVO, deviceGroupVO, deviceIdList, busiIpPorts, false);
                }

                //如果设备组下已无异构设备，清除对应路由
                List<DeviceInfoVO> deviceInfoVOList = deviceInfoService.getDeviceListByGroupId(deviceGroupId);
                if (CollectionUtils.isEmpty(deviceInfoVOList)) {
                    log.info("DeviceGroupServiceImpl.delDeviceToGroup() 设备组下已无异构设备，清除对应路由, deviceGroupVO= {}", deviceGroupVO);
                    deleteDeviceGroupRoute(deviceGroupVO);
                }
            } else {
                deviceInfoToInvoke(deviceGroupId);
            }
        }

        return restResponse;
    }

    @Override
    public Long delDeviceToPt(Long tenantId, Long deviceGroupId, List<Long> deviceIds, Long serviceTypeToDeviceGroupId) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NOT_EXIST);
        }

        DevicesGroupIdDTO devicesGroupIdDTO = new DevicesGroupIdDTO();
        devicesGroupIdDTO.setDeviceGroupId(deviceGroupId);
        DeviceGroupVO deviceGroupVO = this.info(devicesGroupIdDTO);

        if (deviceGroupVO == null) {
            throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_NOT_EXIST);
        }

        checkDeleteDevice(deviceGroupId, deviceIds, false);

        Long finalDeviceGroupId = deviceGroupId;

        log.info("DeviceGroupServiceImpl.delDeviceToPt() 获取设备组中存在的设备列表, deviceGroupId= {}", deviceGroupId);
        SecRestResponse<List<DeviceInfoInGroupVO>> response = getDevicesByGroupId(devicesGroupIdDTO);
        List<Long> deviceInfoIdList = new ArrayList<>();
        if (response.isSuccess()) {
            List<DeviceInfoInGroupVO> deviceInfoInGroupVOList = response.getResult();
            if (CollectionUtils.isNotEmpty(deviceInfoInGroupVOList)) {
                deviceInfoIdList = deviceInfoInGroupVOList.stream().map(DeviceInfoInGroupVO::getDeviceId)
                        .collect(Collectors.toList());
                log.info("DeviceGroupServiceImpl.delDeviceToPt() 获取设备组中存在的设备列表, deviceInfoIdList= {}", deviceInfoIdList);
            }
        } else {
            log.error("DeviceGroupServiceImpl.delDeviceToPt(); code={};error={}",
                    response.getCode(), response.getMessage());
            throw new BusinessException(response.getCode());
        }

        List<Long> finalDeviceInfoIdList = deviceInfoIdList;
        deviceIds.forEach(deviceId -> {
            if (finalDeviceInfoIdList.contains(deviceId)) {
                DeviceToGroupDTO deviceToGroupDTO = new DeviceToGroupDTO();
                deviceToGroupDTO.setDeviceGroupId(finalDeviceGroupId);
                deviceToGroupDTO.setDeviceId(deviceId);
                SecRestResponse<Object> restResponse = deviceGroupServiceApi.delDeviceToPt(deviceToGroupDTO);

                if (!restResponse.isSuccess()) {
                    log.error("DeviceGroupServiceImpl.delDeviceToPt(); code={};error={}", restResponse.getCode(), restResponse.getMessage());
                    throw new BusinessException(restResponse.getCode());
                }
            }
        });

        if (serviceTypeToDeviceGroupId != null) {
            DeviceGroupDeleteDTO deviceGroupDeleteDTO = new DeviceGroupDeleteDTO();
            deviceGroupDeleteDTO.setDeviceGroupId(deviceGroupId);
            this.deleteById(deviceGroupDeleteDTO);
        }
        log.info("DeviceGroupServiceImpl.delDeviceToPt() 执行成功");
        return deviceGroupId;
    }

    /**
     * 设备组删除设备校验
     *
     * @param deviceGroupId
     * @param deviceIds
     */
    private void checkDeleteDevice(Long deviceGroupId, List<Long> deviceIds, boolean isDefault) {
        GetBusiTypeListDTO getBusiTypeListDTO = new GetBusiTypeListDTO();
        getBusiTypeListDTO.setDeviceGroupId(deviceGroupId);
        SecRestResponse<List<GetBusiTypeListVO>> response = this.getBusiTypeList(getBusiTypeListDTO);

        List<Long> busiTypeIds = new ArrayList<>();
        if (response.isSuccess()) {
            List<GetBusiTypeListVO> getBusiTypeListVOS = response.getResult();
            getBusiTypeListVOS.forEach(item -> {
                if (item.getDeviceList() != null && !item.getDeviceList().isEmpty() && deviceIds.containsAll(item.getDeviceList())) {
                    busiTypeIds.add(item.getBusiTypeId());
                }
            });
        }

        List<AppBusiToGroupVO> errorAppBusi = new ArrayList<>();
        if (!busiTypeIds.isEmpty()) {
            List<AppBusiToGroupVO> appBusiToGroupPOS = appBusiToGroupService.getBusiGroupListByGroupId(deviceGroupId);
            appBusiToGroupPOS.forEach(item -> {
                if (busiTypeIds.contains(item.getBusiTypeId())) {
                    errorAppBusi.add(item);
                }
            });
        }

        if (!errorAppBusi.isEmpty()) {
            if (isDefault) {
                throw new BusinessException(SecErrorCodeConstant.DEFAULT_DEVICE_INFLUENCE_APP_ERROR);
            } else {
                throw new BusinessException(SecErrorCodeConstant.ARRAY_DEVICE_INFLUENCE_APP_ERROR);
            }
        }

        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.findList(ServiceInfoDTO.builder().deviceGroupId(deviceGroupId).build());
        if (res.isSuccess()) {
            List<ServiceInfoVO> serviceInfoVOS = res.getResult();

            if (!serviceInfoVOS.isEmpty()) {
                SecRestResponse<List<ServiceTypeAndBusiVO>> restResponse = serviceTypeService.findServiceTypeList();
                if (restResponse.isSuccess()) {
                    List<ServiceTypeAndBusiVO> serviceTypeAndBusiVOS = restResponse.getResult();
                    Map<Long, List<Long>> serviceTypeBusiMap = serviceTypeAndBusiVOS.stream().collect(Collectors
                            .toMap(ServiceTypeAndBusiVO::getId, ServiceTypeAndBusiVO::getBusiTypeIdList));

                    serviceInfoVOS.forEach(item -> {
                        if (serviceTypeBusiMap.containsKey(item.getServiceTypeId())) {
                            List<Long> busiTypeIDs = serviceTypeBusiMap.get(item.getServiceTypeId());

                            busiTypeIDs.forEach(id -> {
                                if (busiTypeIds.contains(id)) {
                                    if (isDefault) {
                                        throw new BusinessException(SecErrorCodeConstant.DEFAULT_DEVICE_INFLUENCE_SERVICE_ERROR);
                                    } else {
                                        throw new BusinessException(SecErrorCodeConstant.ARRAY_DEVICE_INFLUENCE_SERVICE_ERROR);
                                    }
                                }
                            });

                        }
                    });
                }
            }
        }
    }

    @Override
    public SecRestResponse<List<DeviceInfoInGroupVO>> getDevicesByGroupId(DevicesGroupIdDTO devicesByGroupIdDTO) {
        return deviceGroupServiceApi.getDevicesByGroupId(devicesByGroupIdDTO);
    }


    /**
     * 查询可下发给服务的设备列表
     *
     * @param devicesByGroupIdDTO
     * @return
     */
    @Override
    public SecRestResponse<List<DeviceInfoInGroupVO>> getServiceReceiveDeviceByGroupId(DevicesGroupIdDTO devicesByGroupIdDTO) {
        return deviceGroupServiceApi.getServiceReceiveDeviceByGroupId(devicesByGroupIdDTO);
    }


    public List<DeviceInfoInGroupVO> getFreeDeviceByGroup(FreeDeviceByGroupDTO dto) {
        SecRestResponse<List<DeviceInfoInGroupVO>> restResponse = deviceGroupServiceApi.getFreeDeviceByGroup(dto);
        if (!restResponse.isSuccess()) {
            log.error("DeviceGroupServiceImpl.getFreeDeviceByGroup(); code={};error={}",
                    restResponse.getCode(), restResponse.getMessage());
            throw new BusinessException(restResponse.getCode());
        }
        return restResponse.getResult();
    }

    @Override
    public SecRestResponse<SecPageVO<DeviceInfoInGroupVO>> getDevicesByGroupIdPage(DevicesGroupIdDTO devicesByGroupIdDTO) {
        //防止租户越权查询
        if( !LoginUserUtil.isTopTenant()  ){
            List<Long> deviceGroupIds = tenantToDeviceGroupService.getAllDeviceGroupIdByTenantId(LoginUserUtil.getTenantId());
            if( !deviceGroupIds.contains(devicesByGroupIdDTO.getDeviceGroupId()) ){
                log.error("租户越权查询其他设备组信息");
                throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_NOT_EXIST);
            }
        }
        return deviceGroupServiceApi.getDevicesByGroupIdPage(devicesByGroupIdDTO);
    }

    @Override
    public SecRestResponse<List<GetBusiTypeListVO>> getBusiTypeList(GetBusiTypeListDTO getBusiTypeListDTO) {
        Long tenantId = getBusiTypeListDTO.getTenantId();
        //如果租户id不为null，说明要根据租户id查询，根据租户id查询的方法是先查询出来关联的设备组id，再根据设备组id查询
        if(tenantId != null && !tenantId.equals(CommonConstant.TOP_TENANT_ID)){
            TenantVO tenantVO = tenantService.checkTenantExist(tenantId);
            List<Long> deviceGroupIdList = tenantToDeviceGroupService.getAllDeviceGroupIdByTenantId(tenantId);
            getBusiTypeListDTO.setDeviceGroupIdList(deviceGroupIdList);
        }
        return deviceGroupServiceApi.getBusiTypeList(getBusiTypeListDTO);
    }

    @Override
    public SecRestResponse<DeviceCountVO> getDeviceNumByTenantId(DeviceCountDTO deviceCountByTenantDTO) {
        Long tenantId = deviceCountByTenantDTO.getTenantId();
        //如果租户id不为null，说明要根据租户id查询，根据租户id查询的方法是先查询出来关联的设备组id，再根据设备组id查询
        if(tenantId != null && !tenantId.equals(CommonConstant.TOP_TENANT_ID)){
            TenantVO tenantVO = tenantService.checkTenantExist(tenantId);
            List<Long> deviceGroupIdList = tenantToDeviceGroupService.getUnsharedDeviceGroupIdByTenantId(tenantId);
            deviceCountByTenantDTO.setDeviceGroupIdList(deviceGroupIdList);
            deviceCountByTenantDTO.setTenantId(null);
        }
        //所有设备组都属于顶级租户
        if(CommonConstant.TOP_TENANT_ID.equals(tenantId)){
            deviceCountByTenantDTO.setTenantId(null);
        }

        return deviceGroupServiceApi.getDeviceNumByTenantId(deviceCountByTenantDTO);
    }

    @Override
    public SecPageVO<DeviceListByTenantIdPageVO> getDevicesByGroupIds(DeviceListByGroupIdsDTO devicePageDTO) {
        Long tenantId = devicePageDTO.getTenantId();
        if(tenantId != null && !tenantId.equals(CommonConstant.TOP_TENANT_ID)){
            List<Long> deviceGroupIdList = tenantToDeviceGroupService.getUnsharedDeviceGroupIdByTenantId(tenantId);
            devicePageDTO.setDeviceGroupIdList(deviceGroupIdList);
            devicePageDTO.setTenantId(null);
        }
        //所有设备组都属于顶级租户
        if(CommonConstant.TOP_TENANT_ID.equals(tenantId)){
            devicePageDTO.setTenantId(null);
        }
        SecRestResponse<SecPageVO<DeviceListByTenantIdPageVO>> restResponse = deviceGroupServiceApi.getDevicesByGroupIds(devicePageDTO);
        if (!restResponse.isSuccess()) {
            throw new BusinessException(restResponse.getCode());
        }
        return restResponse.getResult();
    }

    @Override
    public List<DeviceInfoInGroupVO> getCanBindDeviceByGroupId(DevicesGroupIdDTO devicesGroupIdDTO) {

        // 校验设备组
        DeviceGroupVO deviceGroupVO = info(devicesGroupIdDTO);
        // 设备类型
        Long deviceTypeId = deviceGroupVO.getDeviceTypeId();
        Boolean isRedionMode = commonConfigService.isRegionMode();

        FreeDeviceByTenantDTO freeDeviceByTenantDTO = new FreeDeviceByTenantDTO();
        freeDeviceByTenantDTO.setDeviceTypeId(deviceTypeId);
        if(isRedionMode){
            freeDeviceByTenantDTO.setRegionId(deviceGroupVO.getRegionId());
        }
        freeDeviceByTenantDTO.setIsRest(deviceGroupVO.getIsRest());
        return deviceInfoService.getFreeDeviceByTenant(freeDeviceByTenantDTO);
    }

    /**
     * @param deviceGroupVOList
     * @Description: 设置租户名称
     */
    private void setTenantName(List<DeviceGroupVO> deviceGroupVOList) {
        if(deviceGroupVOList.size()>0){
            Map<Long, String> tenantNameByIdMap = tenantService.getTenantIdNameMap();
            List<Long> deviceGroupIdList = deviceGroupVOList.stream().map(item -> item.getDeviceGroupId()).collect(Collectors.toList());
            Map<Long, Long> unsharedGroupToTenantIdMap = tenantToDeviceGroupService.getUnsharedGroupToTenantIdMap(deviceGroupIdList);
            deviceGroupVOList.forEach(item -> {
                if(item.checkIsShare()==false){
                    Long tenantId = unsharedGroupToTenantIdMap.get(item.getDeviceGroupId());
                    if (tenantId != null && !tenantId.equals(CommonConstant.TOP_TENANT_ID)) {
                        item.setTenantId(tenantId);
                        item.setTenantName(tenantNameByIdMap.get(tenantId));
                    }
                }
            });
        }
    }

    /**
     * @param deviceGroupVOList
     * @Description: 设置区域名称
     */
    private void setRegionName(List<DeviceGroupVO> deviceGroupVOList) {
        Map<Long, String> regionIdNameMap = regionService.getRegionIdNameMap();
        deviceGroupVOList.forEach(item -> {
            Long regionId = item.getRegionId();
            if (regionId != null) {
                item.setRegionName(regionIdNameMap.get(regionId));
            }
        });
    }

    /**
     * 支持对外调用设备组添加网关业务地址
     *
     * @param deviceGroupVOList
     */
    private void setBusiUrl(List<DeviceGroupVO> deviceGroupVOList, Long tenantId) {
        log.info("DeviceGroupServiceImpl.setBusiUrl() , deviceGroupVOList= {}", deviceGroupVOList);
        ServiceGatewayRouteDTO serviceGatewayRouteDTO = new ServiceGatewayRouteDTO();
        serviceGatewayRouteDTO.setRouteType(4);
        serviceGatewayRouteDTO.setTenantId(tenantId);
        SecRestResponse<List<ServiceGatewayRouteVO>> response = serviceGatewayRouteService.findTcpPort(serviceGatewayRouteDTO);
        Map<Long, Integer> groupIdToTcpMap = new HashMap<>();
        if (response.isSuccess()) {
            List<ServiceGatewayRouteVO> serviceGatewayRouteVOList = response.getResult();
            if (CollectionUtils.isNotEmpty(serviceGatewayRouteVOList)) {
                for (ServiceGatewayRouteVO serviceGatewayRouteVO : serviceGatewayRouteVOList) {
                    groupIdToTcpMap.put(serviceGatewayRouteVO.getGroupId(), serviceGatewayRouteVO.getServerPort());
                }
            }
        } else {
            log.error("DeviceGroupServiceImpl.setBusiUrl(); code={};error={}", response.getCode(), response.getMessage());
            throw new BusinessException(response.getCode());
        }

        Map<Long, ServiceGatewayVO> serviceGatewayVOMap = new HashMap<>();
        for (DeviceGroupVO deviceGroupVO : deviceGroupVOList) {
            if (deviceGroupVO.getIsRest() != null && deviceGroupVO.getIsRest() == 1
                    && deviceGroupVO.getDeviceNum() != null && deviceGroupVO.getDeviceNum() > 0) {
                ServiceGatewayVO serviceGatewayVO = null;
                //获取区域网关
                if (deviceGroupVO.getRegionId() == null) {
                    if (serviceGatewayVOMap.containsKey(1L)) {
                        serviceGatewayVO = serviceGatewayVOMap.get(1L);
                    } else {
                        serviceGatewayVO = getServiceGateway(null);
                        serviceGatewayVOMap.put(1L, serviceGatewayVO);
                    }
                } else {
                    if (serviceGatewayVOMap.containsKey(deviceGroupVO.getRegionId())) {
                        serviceGatewayVO = serviceGatewayVOMap.get(deviceGroupVO.getRegionId());
                    } else {
                        serviceGatewayVO = getServiceGateway(deviceGroupVO.getRegionId());
                        serviceGatewayVOMap.put(deviceGroupVO.getRegionId(), serviceGatewayVO);
                    }
                }

                if (deviceGroupVO.getNetPro() == 1) {
                    if (groupIdToTcpMap.containsKey(deviceGroupVO.getDeviceGroupId())) {
                        Integer port = groupIdToTcpMap.get(deviceGroupVO.getDeviceGroupId());
                        String busiUrl = String.format("%s: %s:%d", "TCP请求", serviceGatewayVO.getIp(), port);
                        deviceGroupVO.setBusiUrl(busiUrl);
                    }
                } else {
                    String busiUrl = String.format("%s%s:%d", CommonConstant.HTTPS_URL, serviceGatewayVO.getIp(), serviceGatewayVO.getPort());
                    deviceGroupVO.setBusiUrl(busiUrl);
                }
            }
        }
    }

    /**
     * 获取业务路由对象
     *
     * @param regionId
     * @return
     */
    private ServiceGatewayVO getServiceGateway(Long regionId) {
        ServiceGatewayDTO gatewayDTO = new ServiceGatewayDTO();
        gatewayDTO.setGatewayType(2);
        gatewayDTO.setRegionId(regionId);
        List<ServiceGatewayVO> gatewayVOList = serviceGatewayService.queryByParam(gatewayDTO);
        if (CollectionUtils.isEmpty(gatewayVOList)) {
            log.error("DeviceGroupServiceImpl.setBusiUrl(); code={};error={}",
                    SecErrorCodeConstant.GATEWAY_BUSI_NOT_EXIST, "系统中不存在业务类型网关，请先添加网关");
            throw new BusinessException(SecErrorCodeConstant.GATEWAY_BUSI_NOT_EXIST);
        }

        return gatewayVOList.get(0);
    }

    @Override
    public List<DeviceGroupIdNameVO> getDeviceGroupIdName(Long tenantId) {
        DeviceGroupIdNameDTO deviceGroupIdNameDTO = new DeviceGroupIdNameDTO();
        deviceGroupIdNameDTO.setTenantId(tenantId);
        SecRestResponse<List<DeviceGroupIdNameVO>> deviceGroupResponse = this.getDeviceGroupIdNameList(deviceGroupIdNameDTO);
        if (deviceGroupResponse.isSuccess()) {
            return deviceGroupResponse.getResult();
        } else {
            throw new BusinessException(SecErrorCodeConstant.GET_DEVICE_GROUP_ERROR);
        }
    }

    @Override
    public List<DeviceGroupIdNameVO> getAllDeviceGroupIdName(Long tenantId, Integer isRest) {
        DeviceGroupIdNameDTO deviceGroupIdNameDTO = new DeviceGroupIdNameDTO();
        deviceGroupIdNameDTO.setTenantId(tenantId);
        deviceGroupIdNameDTO.setIsRest(isRest);
        SecRestResponse<List<DeviceGroupIdNameVO>> deviceGroupResponse = this.getAllDeviceGroupIdNameList(deviceGroupIdNameDTO);
        if (deviceGroupResponse.isSuccess()) {
            return deviceGroupResponse.getResult();
        } else {
            throw new BusinessException(SecErrorCodeConstant.GET_DEVICE_GROUP_ERROR);
        }
    }

    @Override
    public Map<Long, List<DeviceGroupIdNameVO>> getDeviceGroupIdNameMap(List<Long> busiTypeIds, Long tenantId) {
        List<DeviceGroupIdNameVO> deviceGroupIdNameVOS = getDeviceGroupIdName(tenantId);
        return createIdNameMap(busiTypeIds, deviceGroupIdNameVOS);
    }

    @Override
    public Map<Long, List<DeviceGroupIdNameVO>> getAllDeviceGroupIdNameMap(List<Long> busiTypeIds, Long tenantId) {
        List<DeviceGroupIdNameVO> deviceGroupIdNameVOS = getAllDeviceGroupIdName(tenantId,1);
        return createIdNameMap(busiTypeIds, deviceGroupIdNameVOS);
    }

    private Map<Long, List<DeviceGroupIdNameVO>> createIdNameMap(List<Long> busiTypeIds, List<DeviceGroupIdNameVO> deviceGroupIdNameVOS) {
        Map<Long, List<DeviceGroupIdNameVO>> map = new HashMap<>();

        if (CollectionUtils.isNotEmpty(deviceGroupIdNameVOS)) {
            deviceGroupIdNameVOS.forEach(item -> {
                List<BusiTypeVO> busiTypeVOList = item.getBusiTypeList();
                if (CollectionUtils.isNotEmpty(busiTypeVOList)) {
                    busiTypeVOList.forEach(busiType -> {
                        Long busiTypeId = busiType.getBusiTypeId();
                        if (busiTypeIds.contains(busiTypeId)) {

                            List<DeviceGroupIdNameVO> deviceGroupIdNameVOList = null;
                            if (map.containsKey(busiTypeId)) {
                                deviceGroupIdNameVOList = map.get(busiTypeId);
                            } else {
                                deviceGroupIdNameVOList = new ArrayList<>();
                            }

                            deviceGroupIdNameVOList.add(item);
                            map.put(busiTypeId, deviceGroupIdNameVOList);
                        }
                    });
                }
            });
        }

        return map;
    }

    /**
     * 配置设备组关联服务连接设备
     *
     * @param deviceGroupId
     */
    private void deviceInfoToInvoke(Long deviceGroupId) {
        List<DeviceInvokeDTO> deviceInvokeDTOS = getDeviceInvokeListByGroupId(deviceGroupId);
        serviceInfoService.receiveDeviceInfo(deviceGroupId, deviceInvokeDTOS);
    }

    /**
     * 获取设备组下下发给服务的数据列表
     *
     * @param deviceGroupId
     * @return
     */
    @Override
    public List<DeviceInvokeDTO> getDeviceInvokeListByGroupId(Long deviceGroupId) {
        DevicesGroupIdDTO devicesGroupIdDTO = new DevicesGroupIdDTO();
        devicesGroupIdDTO.setDeviceGroupId(deviceGroupId);
        SecRestResponse<List<DeviceInfoInGroupVO>> restResponse = getServiceReceiveDeviceByGroupId(devicesGroupIdDTO);

        if (restResponse.isSuccess()) {
            List<DeviceInfoInGroupVO> deviceInfoInGroupVOS = restResponse.getResult();

            String device_invoke_port = commonConfigService.getConfigValueByConfigCode(CommonConstant.DEVICE_INVOKE_PORT);


            List<DeviceInvokeDTO> deviceInvokeDTOS = new ArrayList<>();
            deviceInfoInGroupVOS.forEach(device -> {
                DeviceInvokeDTO deviceInvokeDTO = null;
                if (CommonConstant.BUSI.equals(device_invoke_port)) {
                    deviceInvokeDTO = interInvokerConvert.convertBusiVo(device);
                } else {
                    deviceInvokeDTO = interInvokerConvert.convertVo(device);
                }

                //todo  临时写死，后期通过config配置选择
                if (Objects.equals(device.getDeviceTypeId(), DeviceTypeEnum.HUWEI_DEVICE_TYPE.getId())
                        || Objects.equals(device.getDeviceTypeId(), DeviceTypeEnum.HUWEI_TENANT_DEVICE_TYPE.getId())) {
                    deviceInvokeDTO.setProtocol(DeviceTypeEnum.HUWEI_DEVICE_TYPE.getProtocol());
                } else {
                    deviceInvokeDTO.setProtocol(DeviceTypeEnum.DEVICE_TYPE.getProtocol());
                }

                deviceInvokeDTOS.add(deviceInvokeDTO);
            });

            return deviceInvokeDTOS;
        } else {
            throw new BusinessException(restResponse.getCode());
        }
    }

    /**
     * 获取设备组下下发给服务的数据列表，自动选取空闲设备
     *
     * @return
     */
    @Override
    public List<DeviceInvokeDTO> getDeviceInvokeList(Long deviceGroupId, Long deviceId, List<Long> deviceIdList) {
        String device_invoke_port = commonConfigService.getConfigValueByConfigCode(CommonConstant.DEVICE_INVOKE_PORT);
        List<DeviceInvokeDTO> deviceInvokeDTOS = new ArrayList<>();
        List<DeviceInfoInGroupVO> deviceInfoInGroupVOS = new ArrayList<>();

        if (deviceGroupId != null) {
            DevicesGroupIdDTO devicesGroupIdDTO = new DevicesGroupIdDTO();
            devicesGroupIdDTO.setDeviceGroupId(deviceGroupId);
            SecRestResponse<List<DeviceInfoInGroupVO>> restResponse = getServiceReceiveDeviceByGroupId(devicesGroupIdDTO);

            if (restResponse.isSuccess()) {
                deviceInfoInGroupVOS = restResponse.getResult();
            } else {
                throw new BusinessException(restResponse.getCode());
            }
        } else if (deviceId != null) {
            DeviceInfoInGroupVO deviceInfoInGroupVO = deviceInfoService.getDeviceInfoInGroupVO(deviceId);

            if (deviceInfoInGroupVO.getSupportExternalService() == 1) {
                log.error("DeviceGroupServiceImpl.getDeviceInvokeList(); code={};error={}",
                        SecErrorCodeConstant.DEVICE_TYPE_NOT_SERVICE, "该设备类型设备不可被密码服务使用");
                throw new BusinessException(SecErrorCodeConstant.DEVICE_TYPE_NOT_SERVICE);
            }

            String connectAuthCode = deviceInfoInGroupVO.getConnectAuthCode();
            if (StringUtils.isNotBlank(connectAuthCode)) {
                String decPwd = ComponentSynthesisEncryptionUtil.decPwd(connectAuthCode);
                deviceInfoInGroupVO.setConnectAuthCode(decPwd);
            }

            deviceInfoInGroupVOS.add(deviceInfoInGroupVO);
        } else if (deviceIdList != null) {
            if (CollectionUtils.isNotEmpty(deviceIdList)) {
                List<DeviceInfoInGroupVO> deviceInfoInGroupVOList = deviceInfoService.getDeviceInfoInGroupVOList(deviceIdList);
                //循环将加密的密码解密
                for (DeviceInfoInGroupVO deviceInfoInGroupVO : deviceInfoInGroupVOList) {
                    if (deviceInfoInGroupVO.getSupportExternalService() == 1) {
                        log.error("DeviceGroupServiceImpl.getDeviceInvokeList(); code={};error={}",
                                SecErrorCodeConstant.DEVICE_TYPE_NOT_SERVICE, "该设备类型设备不可被密码服务使用");
                        throw new BusinessException(SecErrorCodeConstant.DEVICE_TYPE_NOT_SERVICE);
                    }
                    String connectAuthCode = deviceInfoInGroupVO.getConnectAuthCode();
                    if (StringUtils.isNotBlank(connectAuthCode)) {
                        String decPwd = ComponentSynthesisEncryptionUtil.decPwd(connectAuthCode);
                        deviceInfoInGroupVO.setConnectAuthCode(decPwd);
                    }
                }
                deviceInfoInGroupVOS = deviceInfoInGroupVOList;
            }
        } else {
            String device_auto_add_no_type = commonConfigService.getConfigValueByConfigCode(CommonConstant.DEVICE_AUTO_ADD_NO_TYPE);
            List<DeviceInfoInGroupVO> freeDeviceInfoInGroupVOS = deviceInfoService.getFreeDeviceInfoExcludeType(device_auto_add_no_type);

            for (DeviceInfoInGroupVO deviceInfoInGroupVO : freeDeviceInfoInGroupVOS) {
                if (deviceInfoInGroupVO.getSupportExternalService() != 1) {
                    deviceInfoInGroupVOS.add(deviceInfoInGroupVO);
                }
            }
        }

        if (deviceInfoInGroupVOS.size() > 0) {
            deviceInfoInGroupVOS.forEach(device -> {
                DeviceInvokeDTO deviceInvokeDTO = null;
                if (CommonConstant.BUSI.equals(device_invoke_port)) {
                    deviceInvokeDTO = interInvokerConvert.convertBusiVo(device);
                } else {
                    deviceInvokeDTO = interInvokerConvert.convertVo(device);
                }

                //todo  临时写死，后期通过config配置选择
                if (Objects.equals(device.getDeviceTypeId(), DeviceTypeEnum.HUWEI_DEVICE_TYPE.getId())
                        || Objects.equals(device.getDeviceTypeId(), DeviceTypeEnum.HUWEI_TENANT_DEVICE_TYPE.getId())) {
                    deviceInvokeDTO.setProtocol(DeviceTypeEnum.HUWEI_DEVICE_TYPE.getProtocol());
                } else {
                    deviceInvokeDTO.setProtocol(DeviceTypeEnum.DEVICE_TYPE.getProtocol());
                }

                deviceInvokeDTOS.add(deviceInvokeDTO);
            });
        }
        return deviceInvokeDTOS;
    }

    @Override
    public DeviceGroupVO getDeviceGroupInfoById(Long deviceGroupId) {
        log.info("DeviceGroupServiceImpl.getDeviceGroupInfoById() , deviceGroupId= {}", deviceGroupId);
        DevicesGroupIdDTO devicesGroupIdDTO = new DevicesGroupIdDTO();
        devicesGroupIdDTO.setDeviceGroupId(deviceGroupId);

        DeviceGroupVO deviceGroupVO = info(devicesGroupIdDTO);
        if (deviceGroupVO == null) {
            throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_NOT_EXIST);
        }
        return deviceGroupVO;
    }


    /**
     * 根据区域ID查找设备组列表
     * @param deviceGroupByRegionIdDTO
     * @return
     */
    @Override
    public SecRestResponse<List<DeviceGroupVO>> getDeviceGroupListByRegionId(DeviceGroupByRegionIdDTO deviceGroupByRegionIdDTO) {
        return deviceGroupServiceApi.getDeviceGroupListByRegionId(deviceGroupByRegionIdDTO);
    }

    @Override
    public SecPageVO<DeviceListByTenantIdPageVO> getDeviceListByTenantId(DeviceListByTenantDTO deviceGroupByRegionIdDTO) {
        Long tenantId = deviceGroupByRegionIdDTO.getTenantId();
        if(tenantId != null && !tenantId.equals(CommonConstant.TOP_TENANT_ID)){
            List<Long> deviceGroupIdList = tenantToDeviceGroupService.getUnsharedDeviceGroupIdByTenantId(tenantId);
            deviceGroupByRegionIdDTO.setDeviceGroupIdList(deviceGroupIdList);
            deviceGroupByRegionIdDTO.setTenantId(null);
        }
        //所有设备组都属于顶级租户
        if(CommonConstant.TOP_TENANT_ID.equals(tenantId)){
            deviceGroupByRegionIdDTO.setTenantId(null);
        }
        SecRestResponse<SecPageVO<DeviceListByTenantIdPageVO>> restResponse = deviceGroupServiceApi.getDeviceListByTenantId(deviceGroupByRegionIdDTO);
        if (!restResponse.isSuccess()) {
            throw new BusinessException(restResponse.getCode());
        }
        return restResponse.getResult();
    }

    /**
     * 根据租户id查询可绑定的异构设备组
     * @param dto
     * @return
     */
    @Override
    public SecRestResponse<List<DeviceGroupVO>> getCanBingExtraDeivceGroup(GetCanBingExtraDeivceGroupDTO dto) {
        Long tenantId = dto.getTenantId();
        TenantVO tenantVO = tenantService.checkTenantExist(tenantId);
        List<Long> deviceGroupIdList = tenantToDeviceGroupService.getAllDeviceGroupIdByTenantId(tenantId);
        dto.setRegionId(tenantVO.getRegionId());
        dto.setDeviceGroupIdList(deviceGroupIdList);
        SecRestResponse<List<DeviceGroupVO>> response = deviceGroupServiceApi.getCanBingExtraDeivceGroup(dto);
        if(response.isSuccess()){
            setRegionName(response.getResult());
        }
        return response;
    }

    /**
     * 租户和共享异构设备组绑定
     * @param dto
     * @return
     */
    @Override
    public SecRestResponse<Object> bingExtraDeivceGroup(BingExtraDeivceGroupDTO dto) {
        Long tenantId = dto.getTenantId();
        Long deviceGroupId = dto.getDeviceGroupId();
        TenantVO tenantVO = tenantService.checkTenantExist(tenantId);
        DevicesGroupIdDTO devicesGroupIdDTO = new DevicesGroupIdDTO();
        devicesGroupIdDTO.setDeviceGroupId(deviceGroupId);
        DeviceGroupVO deviceGroupVO = info(devicesGroupIdDTO);
        if(!deviceGroupVO.checkIsRest() || !deviceGroupVO.checkIsShare()){
            log.error("DeviceGroupServiceImpl.bingExtraDeivceGroup,设备组必须是支持业务调用的共享设备组,error={}",SecErrorCodeConstant.DEVICE_GROUP_NOT_SUPPORT_BUSI_REST);
            throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_NOT_SUPPORT_BUSI_REST);
        }
        //添加tenantToDeviceGroup
        TenantToDeviceGroupAddDTO tenantToDeviceGroupAddDTO = new TenantToDeviceGroupAddDTO();
        tenantToDeviceGroupAddDTO.setTenantId(tenantId);
        tenantToDeviceGroupAddDTO.setDeviceGroupId(deviceGroupId);
        tenantToDeviceGroupAddDTO.setIsRest(deviceGroupVO.getIsRest());
        tenantToDeviceGroupAddDTO.setIsShare(deviceGroupVO.getIsShare());
        List<Long> busiTypeIdList = deviceGroupVO.getBusiTypeList().stream().map(item -> item.getBusiTypeId()).collect(Collectors.toList());
        tenantToDeviceGroupAddDTO.setBusiTypeIdList(busiTypeIdList);
        tenantToDeviceGroupService.addRecord(tenantToDeviceGroupAddDTO);
        //添加路由
        List<DeviceInfoInGroupVO> deviceInfoInGroupVOList = getDevicesByGroupId(devicesGroupIdDTO).getResult();
        List<String> busiIpPorts = deviceInfoInGroupVOList.stream().map(item ->
                item.getBusiIp() + ":" + item.getBusiPort()).collect(Collectors.toList());
        List<Long> deviceIdList = deviceInfoInGroupVOList.stream().map(item -> item.getDeviceId()).collect(Collectors.toList());
        setGroupDeviceRoute(tenantVO, deviceGroupVO, deviceIdList, busiIpPorts, true);
        //增加app路由
        appBusiToGroupService.setRouteApp(deviceGroupId);
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Object> unBingExtraDeivceGroup(BingExtraDeivceGroupDTO dto) {
        Long tenantId = dto.getTenantId();
        Long deviceGroupId = dto.getDeviceGroupId();
        TenantVO tenantVO = tenantService.checkTenantExist(tenantId);
        DevicesGroupIdDTO devicesGroupIdDTO = new DevicesGroupIdDTO();
        devicesGroupIdDTO.setDeviceGroupId(deviceGroupId);
        DeviceGroupVO deviceGroupVO = info(devicesGroupIdDTO);
        if(!deviceGroupVO.checkIsRest() || !deviceGroupVO.checkIsShare()){
            log.error("DeviceGroupServiceImpl.unBingExtraDeivceGroup,设备组必须是支持业务调用的共享设备组,error={}",SecErrorCodeConstant.DEVICE_GROUP_NOT_SUPPORT_BUSI_REST);
            throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_NOT_SUPPORT_BUSI_REST);
        }
        List<Long> deviceGroupIdByTenantId = tenantToDeviceGroupService.getAllDeviceGroupIdByTenantId(tenantId);
        if(!deviceGroupIdByTenantId.contains(deviceGroupId)){
            log.error("DeviceGroupServiceImpl.unBingExtraDeivceGroup,租户未绑定该设备组,error={}",SecErrorCodeConstant.TENANT_NOT_BIND_DEVICE_GROUP);
            throw new BusinessException(SecErrorCodeConstant.TENANT_NOT_BIND_DEVICE_GROUP);
        }
        //删除tenantToDeviceGroup
        tenantToDeviceGroupService.delete(tenantId,deviceGroupId,null);
        //删除路由
        deleteDeviceGroupRoute(deviceGroupVO);

        return ResultUtil.ok();
    }


    /**
     * 更新租户和设备组关系表，3.3.0升级3.3.1使用，可以重复执行
     */
    @Override
    public void updateTenantToDeviceGroup() {
        try {
            DeviceGroupPageDTO pageDTO=new DeviceGroupPageDTO();
            pageDTO.setPageNum(1);
            pageDTO.setPageSize(10000);
            SecRestResponse<SecPageVO<DeviceGroupVO>> restResponse = find(pageDTO);
            List<DeviceGroupVO> list = restResponse.getResult().getList();
            for (DeviceGroupVO deviceGroupVO : list) {
                //目前只考虑不支持业务调用的专享设备组
                if(deviceGroupVO.getTenantId() != null && deviceGroupVO.getTenantId() != 1
                        && deviceGroupVO.checkIsRest()==false && deviceGroupVO.checkIsShare()==false){
                    Long deviceGroupId = deviceGroupVO.getDeviceGroupId();
                    List<Long> allTenantIdByGroupId = tenantToDeviceGroupService.getAllTenantIdByGroupId(deviceGroupId);
                    if(allTenantIdByGroupId.isEmpty()){
                        //数组为空，说明专享设备组缺少记录
                        TenantToDeviceGroupAddDTO tenantToDeviceGroupAddDTO = new TenantToDeviceGroupAddDTO();
                        tenantToDeviceGroupAddDTO.setTenantId(deviceGroupVO.getTenantId());
                        tenantToDeviceGroupAddDTO.setDeviceGroupId(deviceGroupVO.getDeviceGroupId());
                        tenantToDeviceGroupAddDTO.setIsShare(deviceGroupVO.getIsShare());
                        tenantToDeviceGroupAddDTO.setIsRest(deviceGroupVO.getIsRest());
                        tenantToDeviceGroupAddDTO.setBusiTypeIdList(new ArrayList<>());
                        tenantToDeviceGroupService.addRecord(tenantToDeviceGroupAddDTO);
                    }
                }
            }

        }catch (Exception e){
            log.error("更新租户和设备组关系表，主要是升级时使用，可以重复执行，方法执行失败");
            log.error("DeviceGroupServiceImpl.updateTenantToDeviceGroup,error={}",e);
        }

    }
}
