package com.sansec.ccsp.datastatic.business.monitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @description : 密钥状态字典表;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-5-13
 */
@TableName("DIC_KEY_STATUS")
@Data
public class DicKeyStatusPO extends BasePO{
    /**
     * 
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 名称
     */
    private String dName;
    /**
     * 值
     */
    private String dValue;
    /**
     * 是否启用 1启用 0停用
     */
    private Integer isAvailable;

}