package com.sansec.ccsp.device.business.devicedic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @Description: 虚拟机镜像类型字典表;
 * <AUTHOR> http://www.chiner.pro
 * @Date: 2023-2-18
 */
@TableName("DIC_DEVICE_IMAGE_TYPE")
@Data
public class DicDeviceImageTypePO extends BasePO{
    /**
     * 镜像类型ID
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 镜像名称
     */
    private String imageName;
    /**
     * 镜像值
     */
    private String imageValue;
    /**
     * 镜像版本
     */
    private String imageVersion;
     /**
      * 厂商ID
      */
    private Long vendorId;

    /**
     * 是否作废;默认为0
     */
    private Integer invalidFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}