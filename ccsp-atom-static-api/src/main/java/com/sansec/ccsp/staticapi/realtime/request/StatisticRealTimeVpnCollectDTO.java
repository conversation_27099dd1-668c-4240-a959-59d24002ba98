package com.sansec.ccsp.staticapi.realtime.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @Description: 数据实时采集, 对接采集插件实体类
 * @Author: zhangweicheng
 * @Date: 2024/3/6
 */

@Data
public class StatisticRealTimeVpnCollectDTO {


    private String collectTime;

    /**
     * 租户标识
     */
    private String tenantCode;

    /**
     * 服务IP
     */
    private String serviceIp;


    private Integer servicePort;

    /**
     * 流量
     */
    private Long serviceFlowNum;

    private String regionCode;

}