package com.sansec.ccsp.app.common.util;

import lombok.extern.slf4j.Slf4j;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * byte工具
 *
 * <AUTHOR>
 */
@Slf4j
public class ByteUtils {

	/**
	 * 生成指定长度的byte数组
	 *
	 * @param length 长度
	 * @return byte数组
	 */
	public static byte[] generate(Integer length) {
		if (length <= 0) {
			return new byte[0];
		}
		return generateRandom(length);
	}


	/**
	 * @return byte[] 随机数
	 * <AUTHOR>
	 * @Description 生成随机数
	 * @Date 2022/4/16
	 * @Param length 随机数长度
	 */
	public static byte[] generateRandom(int length) {
        byte[] random = null;
        try {
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            byte[] seed = secureRandom.generateSeed(length);
            secureRandom.setSeed(seed);
            random = new byte[length];
            secureRandom.nextBytes(random);
        } catch (NoSuchAlgorithmException e) {
            log.error("JCE调用失败：", e);
        } finally {
            log.info("JCE调用完成");
        }
        return random;
    }
}