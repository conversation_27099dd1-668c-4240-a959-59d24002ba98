-- 系统配置表
-- dify_config
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(1, 'dify_config', 'default_app_id', '448baabf-4609-4137-8eb8-846c35092791', 0, 0, 'dify默认应用ID', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(2, 'dify_config', 'default_app_name', '通用智能助手', 0, 0, 'dify默认应用名称', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(3, 'dify_config', 'default_port', '9000', 0, 0, 'Dify实例默认端口', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(4, 'dify_config', 'template_database_name_dify', 'dify', 0, 0, '模板数据库名称——业务数据库', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(5, 'dify_config', 'template_database_name_dify_plugin', 'dify_plugin', 0, 0, '模板数据库名称——插件数据库', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(6, 'dify_config', 'database_username', 'zwKh91pHiXgXLiOL86rmEQ==', 1, 0, '统一数据库连接账号', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(7, 'dify_config', 'database_password', 'a7TWhSiUaCCJamLfHT13KQ==', 1, 0, '统一数据库连接密码', NULL, NULL, NULL, NULL);
-- dify容器
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(8, 'docker_container_name_suffix_list', 'docker-web', 'web-1', 0, 0, 'dify的docker容器名称前缀', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(9, 'docker_container_name_suffix_list', 'docker-nginx', 'nginx-1', 0, 0, 'dify的docker容器名称前缀', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(10, 'docker_container_name_suffix_list', 'docker-api', 'api-1', 0, 0, 'dify的docker容器名称前缀', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(11, 'docker_container_name_suffix_list', 'docker-worker', 'worker-1', 0, 0, 'dify的docker容器名称前缀', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(12, 'docker_container_name_suffix_list', 'docker-plugin_daemon', 'plugin_daemon-1', 0, 0, 'dify的docker容器名称前缀', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(13, 'docker_container_name_suffix_list', 'docker-weaviate', 'weaviate-1', 0, 0, 'dify的docker容器名称前缀', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(14, 'docker_container_name_suffix_list', 'docker-ssrf_proxy', 'ssrf_proxy-1', 0, 0, 'dify的docker容器名称前缀', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(15, 'docker_container_name_suffix_list', 'docker-sandbox', 'sandbox-1', 0, 0, 'dify的docker容器名称前缀', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(16, 'docker_container_name_suffix_list', 'docker-redis', 'redis-1', 0, 0, 'dify的docker容器名称前缀', NULL, NULL, NULL, NULL);
-- env模板文件，配置项集合
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(17, 'template_env_config_map', '_INSTANCE_CODE', 'instanceCode', 0, 0, '实例编码', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(18, 'template_env_config_map', '_CONSOLE_API_URL', 'gatewayUrl', 0, 0, '控制台API', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(19, 'template_env_config_map', '_CONSOLE_WEB_URL', 'gatewayUrl', 0, 0, '控制台WEB', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(20, 'template_env_config_map', '_SERVICE_API_URL', 'gatewayUrl', 0, 0, '服务API', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(21, 'template_env_config_map', '_APP_API_URL', 'gatewayUrl', 0, 0, '应用API', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(22, 'template_env_config_map', '_APP_WEB_URL', 'gatewayUrl', 0, 0, '应用WEB', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(23, 'template_env_config_map', '_FILES_URL', 'gatewayUrl', 0, 0, '文件访问', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(24, 'template_env_config_map', '_SECRET_KEY', 'secretKey', 0, 0, '秘钥', NULL, NULL, NULL, NULL);
INSERT INTO sys_config (id, type_code, code, config_value, flag_encrypt, flag_del, remark, create_by, create_time, update_by, update_time) VALUES(25, 'template_env_config_map', '_EXPOSE_NGINX_PORT', 'servicePort', 0, 0, '对外端口', NULL, NULL, NULL, NULL);
