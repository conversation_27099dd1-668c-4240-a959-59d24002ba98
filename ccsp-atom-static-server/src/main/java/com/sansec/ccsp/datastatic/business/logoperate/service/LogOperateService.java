package com.sansec.ccsp.datastatic.business.logoperate.service;

import com.sansec.ccsp.staticapi.logoperate.request.LogOperateAddDTO;
import com.sansec.ccsp.staticapi.logoperate.request.LogOperateAuditDTO;
import com.sansec.ccsp.staticapi.logoperate.request.LogOperatePageDTO;
import com.sansec.ccsp.staticapi.logoperate.request.LogOperateTenantPageDTO;
import com.sansec.ccsp.staticapi.logoperate.response.LogOperateVo;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

public interface LogOperateService {

    /**
     * 管理侧切面日志入库，内部接口专用
     * @param logOperateAddDTO
     */
    public void ptAdd(LogOperateAddDTO logOperateAddDTO);

    /**
     * 接收批量服务日志，内部接口专用
     * @param addDTOList
     */
    public void serviceBatchAdd(List<LogOperateAddDTO> addDTOList);

    /**
     * 平台操作员操作日志分页查询
     * @param pageDTO
     * @return
     */
    SecRestResponse<SecPageVO<LogOperateVo>> find(LogOperatePageDTO pageDTO);

    /**
     * 平台操作员日志审计
     * @param auditDTO
     * @return
     */
    SecRestResponse<Object> audit(LogOperateAuditDTO auditDTO);


    /**
     * 平台操作员根据查询条件查询总数
     * @param pageDTO
     * @return
     */
    SecRestResponse<Long> getCount(LogOperatePageDTO pageDTO);

    /**
     * 租户操作员操作日志分页查询
     * @param pageDTO
     * @return
     */
    SecRestResponse<SecPageVO<LogOperateVo>> findByTenant(LogOperateTenantPageDTO pageDTO);

    /**
     * 租户操作员日志审计
     * @param auditDTO
     * @return
     */
    SecRestResponse<Object> auditByTenant(LogOperateAuditDTO auditDTO);

    /**
     * 租户操作员根据查询条件查询总数
     * @param pageDTO
     * @return
     */
    SecRestResponse<Long> getCountByTenant(LogOperateTenantPageDTO pageDTO);

}
