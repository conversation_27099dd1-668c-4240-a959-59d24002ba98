package com.sansec.ccsp.datastatic.business.statistic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sansec.ccsp.common.config.api.CommonConfigServiceApi;
import com.sansec.ccsp.datastatic.business.monitor.convert.StatisticIncreRecordConvert;
import com.sansec.ccsp.datastatic.business.monitor.entity.StatisticIncreCalPO;
import com.sansec.ccsp.datastatic.business.monitor.entity.StatisticIncreRecordPO;
import com.sansec.ccsp.datastatic.business.monitor.entity.StatisticIncreTotalCalPO;
import com.sansec.ccsp.datastatic.business.monitor.entity.StatisticUnincreCalPO;
import com.sansec.ccsp.datastatic.business.monitor.service.StatisticIncreCalService;
import com.sansec.ccsp.datastatic.business.monitor.service.StatisticIncreTotalCalService;
import com.sansec.ccsp.datastatic.business.statistic.mapper.StatisticIncreCalMapper;
import com.sansec.ccsp.datastatic.business.statistic.mapper.StatisticIncreRecordMapper;
import com.sansec.ccsp.datastatic.business.statistic.mapper.StatisticIncreTotalCalMapper;
import com.sansec.ccsp.datastatic.business.statistic.mapper.StatisticUnincreCalMapper;
import com.sansec.ccsp.datastatic.business.statistic.service.StatisticHandlerService;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.utils.DateUtils;
import com.sansec.redis.utils.RedisUtill;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Description: 统计日志小时统计表
 * @CreateTime: 2023-05-13
 * @Author: wangjunjie
 */
@Service
@Slf4j
//@EnableScheduling
public class StatisticHandlerServiceImpl implements StatisticHandlerService {

    public static final String FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String THE_LAST_INCRE_TOTAL_TIME_STAMP = "theLastIncreTotalTimeStamp";

    @Resource
    private StatisticIncreRecordMapper increRecordMapper;
    @Resource
    private StatisticUnincreCalMapper unincreCalMapper;
    @Resource
    private StatisticIncreCalMapper increCalMapper;
    @Resource
    private StatisticIncreTotalCalMapper increTotalCalMapper;
    @Resource
    private StatisticIncreRecordConvert convert;
    @Resource
    private CommonConfigServiceApi commonConfigServiceApi;
    @Resource
    private StatisticIncreCalService statisticIncreCalService;
    @Resource
    private StatisticIncreTotalCalService statisticIncreTotalCalService;
    @Resource
    private RedisUtill redisUtill;

    /**
     * 每2秒插入数据
     */
//    @Scheduled(cron = "0/2 * * * * ?")
//     private void insert1() {
//         StatisticIncreRecordPO statisticIncreRecordPO = new StatisticIncreRecordPO();
//         statisticIncreRecordPO.setId(IdGenerator.ins().generator());
//         statisticIncreRecordPO.setTenantCode("wjj1");
//         statisticIncreRecordPO.setAppCode("app");
//         statisticIncreRecordPO.setServiceCode("kms");
//         statisticIncreRecordPO.setServiceId(5674639910345248389L);
//         statisticIncreRecordPO.setStatisticName("kmsBusiStatistic");
//         statisticIncreRecordPO.setTotalStatistic(1l);
//         statisticIncreRecordPO.setSuccessStatistic(0l);
//         statisticIncreRecordPO.setErrorStatistic(0l);
//         statisticIncreRecordPO.setStatisticYear(1);
//         statisticIncreRecordPO.setStatisticMonth(1);
//         statisticIncreRecordPO.setStatisticDay(1);
//         statisticIncreRecordPO.setStatisticHour(1);
//         statisticIncreRecordPO.setStatisticMinute(1);
//         statisticIncreRecordPO.setStatisticSecond(2);
//         statisticIncreRecordPO.setTimeStamp(DateUtils.localDateTime2Timestamp(LocalDateTime.now()));
//         statisticIncreRecordPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
//         increRecordMapper.insert(statisticIncreRecordPO);
//     }

    /**
     * 每2秒插入数据
     */
//    @Scheduled(cron = "0/2 * * * * ?")
//     private void insert2() {
//         StatisticIncreRecordPO statisticIncreRecordPO = new StatisticIncreRecordPO();
//         statisticIncreRecordPO.setId(IdGenerator.ins().generator());
//         statisticIncreRecordPO.setTenantCode("wjj1");
//         statisticIncreRecordPO.setAppCode("app");
//         statisticIncreRecordPO.setServiceCode("svs");
//         statisticIncreRecordPO.setServiceId(5674639910345248389L);
//         statisticIncreRecordPO.setStatisticName("signBusiStatistic");
//         statisticIncreRecordPO.setTotalStatistic(1l);
//         statisticIncreRecordPO.setSuccessStatistic(0l);
//         statisticIncreRecordPO.setErrorStatistic(0l);
//         statisticIncreRecordPO.setStatisticYear(1);
//         statisticIncreRecordPO.setStatisticMonth(1);
//         statisticIncreRecordPO.setStatisticDay(1);
//         statisticIncreRecordPO.setStatisticHour(1);
//         statisticIncreRecordPO.setStatisticMinute(1);
//         statisticIncreRecordPO.setStatisticSecond(2);
//         statisticIncreRecordPO.setTimeStamp(DateUtils.localDateTime2Timestamp(LocalDateTime.now()));
//         statisticIncreRecordPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
//         increRecordMapper.insert(statisticIncreRecordPO);
//     }

    /**
     * 增量计算表
     */
    @Override
    public void increCalCompute() {
        log.info("增量计算表-StatisticHandlerServiceImpl-increCalCompute,executeTime={}", DateUtils.getCurrentLocalDateTime2String());

        // 查本次需要消费的记录
        String startTime = getDateBefore(1);
        String endTime = getDateBefore(0);
        List<StatisticIncreCalPO> increrRecordSumList = increRecordMapper.computeSumByGroupToList(startTime, endTime);
        // 批处理插入计算表
        List<StatisticIncreCalPO> statisticIncreCalPOList = new ArrayList<>();
        for (StatisticIncreCalPO increCalPO : increrRecordSumList) {
            increCalPO.setId(IdGenerator.ins().generator());
            increCalPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
            statisticIncreCalPOList.add(increCalPO);
        }
        log.info("增量计算表批量插入-StatisticHandlerServiceImpl-increCalCompute,statisticIncreCalPOList={}", statisticIncreCalPOList.size());
        if (CollectionUtils.isNotEmpty(statisticIncreCalPOList)) {
            statisticIncreCalService.addBatch(statisticIncreCalPOList);
        }
    }

    /**
     * 增量总量表
     */
    @Override
    public void increTotalCompute() {
        log.info("计算总量表-StatisticHandlerServiceImpl-increTotalCompute,executeTime={}", DateUtils.getCurrentLocalDateTime2String());
        // 获取上次计算时间
        String theLastIncreTotalTimeStamp = redisUtill.strGet(THE_LAST_INCRE_TOTAL_TIME_STAMP);
        // 如果未空，则获取表中最晚一条记录的时间戳
        if (theLastIncreTotalTimeStamp == null) {
            StatisticIncreRecordPO theFirstRecord = increRecordMapper.selectTheFirstRecord();
            theLastIncreTotalTimeStamp = theFirstRecord.getTimeStamp().toString();
            redisUtill.strSet(THE_LAST_INCRE_TOTAL_TIME_STAMP, theLastIncreTotalTimeStamp);
            return;
        }

        // 查本次最后一条记录的时间戳
        StatisticIncreRecordPO statisticIncreRecordPO = increRecordMapper.selectTheLastRecord(theLastIncreTotalTimeStamp);
        // 为空则结束
        if (statisticIncreRecordPO == null) {
            return;
        }
        // 更新时间戳
        redisUtill.strSet(THE_LAST_INCRE_TOTAL_TIME_STAMP, statisticIncreRecordPO.getTimeStamp());
        log.info("更新总量计算时间戳-StatisticHandlerServiceImpl-increTotalCompute, increTotalTimeStamp={}", statisticIncreRecordPO.getTimeStamp());

        // 根据时间戳查询记录
        LambdaQueryWrapper<StatisticIncreRecordPO> lambdaQueryWrapper= new LambdaQueryWrapper<>();
        lambdaQueryWrapper.gt(StatisticIncreRecordPO::getTimeStamp, Long.valueOf(theLastIncreTotalTimeStamp))
                .orderByDesc(StatisticIncreRecordPO::getTimeStamp);
        List<StatisticIncreRecordPO> statisticIncreRecordPOS = increRecordMapper.selectList(lambdaQueryWrapper);

        List<StatisticIncreTotalCalPO> increTotalCalList = convert.recordToTocal(statisticIncreRecordPOS);
        // 标记总量表存在的key
        List<StatisticIncreTotalCalPO> statisticIncreTotalCalPOList = increTotalCalMapper.selectList(new QueryWrapper<>());
        Map<String, StatisticIncreTotalCalPO> statisticIncreTotalCalPOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(statisticIncreTotalCalPOList)) {
            for (StatisticIncreTotalCalPO statisticIncreTotalCalPO : statisticIncreTotalCalPOList) {
                String key = createTotalCalKey(statisticIncreTotalCalPO);
                statisticIncreTotalCalPOMap.put(key, statisticIncreTotalCalPO);
            }
        }

        // 插入\更新总量表
        for (StatisticIncreTotalCalPO increTotalCalPO : increTotalCalList) {
            String key = createTotalCalKey(increTotalCalPO);

            // 判断总量是否存在
            if (!statisticIncreTotalCalPOMap.containsKey(key)) {
                increTotalCalPO.setId(IdGenerator.ins().generator());
                increTotalCalPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
            } else {
                StatisticIncreTotalCalPO curIncreTotalCalPO = statisticIncreTotalCalPOMap.get(key);
                increTotalCalPO.setId(curIncreTotalCalPO.getId());
                increTotalCalPO.setTotalStatistic(increTotalCalPO.getTotalStatistic() + curIncreTotalCalPO.getTotalStatistic());
                increTotalCalPO.setSuccessStatistic(increTotalCalPO.getSuccessStatistic() + curIncreTotalCalPO.getSuccessStatistic());
                increTotalCalPO.setErrorStatistic(increTotalCalPO.getErrorStatistic() + curIncreTotalCalPO.getErrorStatistic());
                increTotalCalPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
            }
            // 持续更新value，保证数据正确
            statisticIncreTotalCalPOMap.put(createTotalCalKey(increTotalCalPO), increTotalCalPO);
        }
        List<StatisticIncreTotalCalPO> statisticIncreTotalCalPOS = new ArrayList<>(statisticIncreTotalCalPOMap.values());
        log.info("更新增量总量表-StatisticHandlerServiceImpl-updateCalTotal,statisticIncreTotalCalPOS={}", statisticIncreTotalCalPOS.size());
        if (CollectionUtils.isNotEmpty(statisticIncreTotalCalPOS)) {
            // todo 批处理可能出问题（本应在上次插入数据库的记录，存在了批处理缓存中，导致下一次更新做成了插入）
            statisticIncreTotalCalService.saveOrUpdateBatch(statisticIncreTotalCalPOS);
        }
    }

    private String createTotalCalKey(StatisticIncreTotalCalPO statisticIncreTotalCalPO) {
        StringBuilder key = new StringBuilder();
        key.append(statisticIncreTotalCalPO.getServiceCode());
        if(statisticIncreTotalCalPO.getRegionId() != null){
            key.append(statisticIncreTotalCalPO.getRegionId());
        }
        if(statisticIncreTotalCalPO.getServiceGroupId() != null){
            key.append(statisticIncreTotalCalPO.getServiceGroupId());
        }
        if (StringUtils.isNotBlank(statisticIncreTotalCalPO.getTenantCode())) {
            key.append(statisticIncreTotalCalPO.getTenantCode());
        }
        if (StringUtils.isNotBlank(statisticIncreTotalCalPO.getStatisticName())) {
            key.append(statisticIncreTotalCalPO.getStatisticName());
        }
        if (StringUtils.isNotBlank(statisticIncreTotalCalPO.getAppCode())) {
            key.append(statisticIncreTotalCalPO.getAppCode());
        }

        return key.toString();
    }

    /**
     * 清理增量计算表
     */
    @Override
    public void increCalClear() {
        log.info("清理增量计算表-StatisticHandlerServiceImpl-increCalClear,executeTime={}", DateUtils.getCurrentLocalDateTime2String());
        // 计算表定时清理时间
        String increCalClearTime = commonConfigServiceApi.getConfigValueByConfigCode("incre_cal_clear_time").getResult();
        String dateByDay = getDateStartByDay(Integer.parseInt(increCalClearTime));
        // 删除计算表
        LambdaQueryWrapper<StatisticIncreCalPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.lt(StatisticIncreCalPO::getCreateTime, dateByDay);
        increCalMapper.delete(queryWrapper);
    }

    /**
     * 清理增量记录表
     */
    @Override
    public void increRecordClear() {
        // 记录表定时清理时间
        log.info("清理增量记录表-StatisticHandlerServiceImpl-increRecordClear,executeTime={}",DateUtils.getCurrentLocalDateTime2String());
        String increRecordClearTime = commonConfigServiceApi.getConfigValueByConfigCode("incre_record_clear_time").getResult();
        String dateByDay = getDateStartByDay(Integer.parseInt(increRecordClearTime));
        // 删除记录表
        LambdaQueryWrapper<StatisticIncreRecordPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.lt(StatisticIncreRecordPO::getCreateTime, dateByDay);
        increRecordMapper.delete(queryWrapper);
    }

    /**
     * 清理非增量表
     */
    @Override
    public void unIncreCalClear() {
        // 记录表定时清理时间
        log.info("清理非增量记录表-StatisticHandlerServiceImpl-unIncreCalClear,executeTime={}",DateUtils.getCurrentLocalDateTime2String());
        String unincreCalClearTime = commonConfigServiceApi.getConfigValueByConfigCode("unincre_cal_clear_time").getResult();
        String dateBeforeByHour = getDateBefore(Integer.parseInt(unincreCalClearTime));
        // 删除记录表
        LambdaQueryWrapper<StatisticUnincreCalPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.lt(StatisticUnincreCalPO::getUpdateTime, dateBeforeByHour)
                .or().isNull(StatisticUnincreCalPO::getUpdateTime)
                .lt(StatisticUnincreCalPO::getCreateTime, dateBeforeByHour);
        unincreCalMapper.delete(queryWrapper);
    }

    /**
     * 获取当前日期前几天的零点
     */
    public static String getDateStartByDay(int day) {
        SimpleDateFormat format = new SimpleDateFormat(FORMAT);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);
        calendar.add(Calendar.DATE, -day);
        Date date = calendar.getTime();
        return format.format(date);
    }

    /**
     * 获取当前时间的几小时之前的整点
     */
    private String getDateBefore(int time) {
        SimpleDateFormat format = new SimpleDateFormat(FORMAT);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getTime().getHours() - time);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date date = calendar.getTime();
        return format.format(date);
    }


}
