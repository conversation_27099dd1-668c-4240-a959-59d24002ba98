package com.sansec.ccsp.common.business.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.common.business.config.entity.ConfigPO;
import com.sansec.ccsp.common.business.config.service.ConfigService;
import com.sansec.ccsp.common.business.organization.service.OrganizationInfoService;
import com.sansec.ccsp.common.business.role.constant.UserRoleConstant;
import com.sansec.ccsp.common.business.role.entity.UserRolePO;
import com.sansec.ccsp.common.business.role.mapper.UserRoleMapper;
import com.sansec.ccsp.common.business.role.service.UserRoleService;
import com.sansec.ccsp.common.business.user.constant.UserConstant;
import com.sansec.ccsp.common.business.user.convert.UserInfoConvert;
import com.sansec.ccsp.common.business.user.entity.*;
import com.sansec.ccsp.common.business.user.mapper.*;
import com.sansec.ccsp.common.business.user.service.*;
import com.sansec.ccsp.common.common.constant.CommonConstant;
import com.sansec.ccsp.common.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.common.common.util.PrivacyUtil;
import com.sansec.ccsp.common.common.util.VerifySM2Sign;
import com.sansec.ccsp.common.config.request.EditConfigDTO;
import com.sansec.ccsp.common.config.request.EditConfigEasyDTO;
import com.sansec.ccsp.common.config.response.ConfigVO;
import com.sansec.ccsp.common.organization.response.OrganizationInfoVO;
import com.sansec.ccsp.common.role.response.UserRoleVO;
import com.sansec.ccsp.common.user.request.*;
import com.sansec.ccsp.common.user.response.LoginUserInfoVO;
import com.sansec.ccsp.common.user.response.ReLoginNoticeVO;
import com.sansec.ccsp.common.user.response.UserInfoVO;
import com.sansec.ccsp.security.util.LoginUserUtil;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import com.sansec.common.utils.SM3Util;
import com.sansec.component.algorithm.utill.ComponentSynthesisEncryptionUtil;
import com.sansec.redis.utils.RedisUtill;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xiaojiawei
 * @Description: 用户信息表;(USER_INFO)表服务实现类
 * @Date: 2023-2-18
 */
@Service
@Slf4j
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfoPO> implements UserInfoService {
    @Resource
    private UserInfoConvert userInfoConvert;

    @Resource
    private UserRegisterMapper userRegisterMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private UserCertMapper userCertMapper;

    @Resource
    private ConfigService configService;

    @Resource
    private UserAuthCodeMapper userAuthCodeMapper;

    @Resource
    private UserSecurityExtendMapper userSecurityExtendMapper;

    @Resource
    private AuthCodeBlacklistService authCodeBlacklistService;

    @Resource
    private UserRootCaService userRootCaService;

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private UserAuthCodeService userAuthCodeService;

    @Resource
    private UserCertService userCertService;

    @Resource
    private UserLockService userLockService;

    @Resource
    private UserSecurityExtendService userSecurityExtendService;

    @Resource
    private RedisUtill redisUtill;

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private OrganizationInfoService organizationService;

    /**
     * token有效期
     */
    @Value("${key.jwt.expireTime:30}")
    private Integer tokenExpireTime;

    /**
     * 获取用户列表
     *
     * @return
     */
    @Override
    public List<UserInfoPO> selectList() {
        LambdaQueryWrapper<UserInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID)
                .orderByDesc(UserInfoPO::getCreateTime);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询
     *
     * @param userInfoPageDTO 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<UserInfoVO>> find(UserInfoPageDTO userInfoPageDTO) {
        QueryWrapper<UserInfoPO> queryWrapper = Wrappers.query();
        LambdaQueryWrapper<UserInfoPO> lambdaQueryWrapper = queryWrapper.lambda();
        if (StringUtils.isNotEmpty(userInfoPageDTO.getUserCode())) {
            lambdaQueryWrapper.like(UserInfoPO::getUserCode, userInfoPageDTO.getUserCode());
        }
        if (StringUtils.isNotEmpty(userInfoPageDTO.getUserName())) {
            lambdaQueryWrapper.like(UserInfoPO::getUserName, userInfoPageDTO.getUserName());
        }
        if (StringUtils.isNotBlank(userInfoPageDTO.getPhoneNum())) {
            lambdaQueryWrapper.eq(UserInfoPO::getPhoneNum, ComponentSynthesisEncryptionUtil.encPwd(userInfoPageDTO.getPhoneNum()));
        }
        if (!CommonConstant.TOP_TENANT_ID.equals(LoginUserUtil.getTenantId())) {
            lambdaQueryWrapper.eq(UserInfoPO::getTenantId, LoginUserUtil.getTenantId());
        } else {
            if (userInfoPageDTO.getTenantId() != null) {
                lambdaQueryWrapper.eq(UserInfoPO::getTenantId, userInfoPageDTO.getTenantId());
            }
        }
        lambdaQueryWrapper.eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID)
                .orderByDesc(UserInfoPO::getCreateTime);
        IPage<UserInfoPO> page = new Page<>(userInfoPageDTO.getPageNum(), userInfoPageDTO.getPageSize());
        IPage<UserInfoPO> userInfoPOPage = baseMapper.selectPage(page, queryWrapper);
        SecPageVO<UserInfoVO> userInfoPageVO = userInfoConvert.pagePOToSecPageVOPage(userInfoPOPage);

        List<Long> organizationIdList = userInfoPageVO.getList().stream().map(UserInfoVO::getOrganizationId).collect(Collectors.toList());
        SecRestResponse<Map<Long, OrganizationInfoVO>> organizationInfoMapResult = organizationService.selectByIds(organizationIdList);
        Map<Long, OrganizationInfoVO> organizationInfoMap = organizationInfoMapResult.getResult();
        Map<Long, UserRolePO> roleMap = new HashMap<>();
        for (UserInfoVO userInfoVO : userInfoPageVO.getList()) {
            userInfoVO.setPhoneNum(PrivacyUtil.getPhoneNumber(userInfoVO.getPhoneNum()));
            userInfoVO.setEmailAddress(PrivacyUtil.getEmail(userInfoVO.getEmailAddress()));
            if (organizationInfoMap.containsKey(userInfoVO.getOrganizationId())) {
                userInfoVO.setOrganizationName(organizationInfoMap.get(userInfoVO.getOrganizationId()).getOrganizationName());
            }

            if (roleMap.containsKey(userInfoVO.getRoleId())) {
                userInfoVO.setRoleName(roleMap.get(userInfoVO.getRoleId()).getRoleName());
            } else {
                UserRolePO userRolePO = userRoleMapper.selectById(userInfoVO.getRoleId());
                roleMap.put(userInfoVO.getRoleId(), userRolePO);
                userInfoVO.setRoleName(userRolePO.getRoleName());
            }
            boolean verifyResult = userHmacVerify(userInfoVO.getUserId());
            if (verifyResult) {
                userInfoVO.setIntegralityStatus(UserConstant.INTEGRALITY_STATUS_1);
            } else {
                userInfoVO.setIntegralityStatus(UserConstant.INTEGRALITY_STATUS_0);
            }
            UserSecurityExtendPO userSecurityExtendPO = userSecurityExtendService.getByUserId(userInfoVO.getUserId());
            if (userSecurityExtendPO != null) {
                userInfoVO.setAccountStartTime(userSecurityExtendPO.getAccountStartTime());
                userInfoVO.setAccountEndTime(userSecurityExtendPO.getAccountEndTime());
            }
        }
        return ResultUtil.ok(userInfoPageVO);
    }

    /**
     * 新增数据
     *
     * @param addUserInfo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SecRestResponse<Object> add(AddUserInfoDTO addUserInfo) {
        //逻辑校验
        addUserCheck(addUserInfo);
        return createUser(addUserInfo, Boolean.FALSE);
    }

    /**
     * 添加用户的底层实现
     * 没有校验逻辑，具体校验由上层方法进行
     *
     * @param addUserInfo
     * @param isRegister  数据来源方式，注册表来源的数据已被加密处理,true注册来源，false其他
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> createUser(AddUserInfoDTO addUserInfo, Boolean isRegister) {
        UserInfoPO userInfoPO = userInfoConvert.addDtoToPo(addUserInfo);
        userInfoPO.setUserId(IdGenerator.ins().generator());
        userInfoPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));

        //口令加盐Sm3hmac处理
        String salt = IdGenerator.ins().generator().toString();
        userInfoPO.setSalt(ComponentSynthesisEncryptionUtil.encPwd(salt));

        //手机号加密存储
        if (StringUtils.isNotEmpty(userInfoPO.getPhoneNum())) {
            userInfoPO.setPhoneNum(ComponentSynthesisEncryptionUtil.encPwd(userInfoPO.getPhoneNum()));
        }
        //邮箱加密存储
        if (StringUtils.isNotEmpty(userInfoPO.getEmailAddress())) {
            userInfoPO.setEmailAddress(ComponentSynthesisEncryptionUtil.encPwd(userInfoPO.getEmailAddress()));
        }
        //默认数据处理
        userInfoPO.setUserStatus(UserConstant.USER_STATUS_1);
        userInfoPO.setDisableReasonId(UserConstant.USER_DISABLE_REASON_ID_0);
        userInfoPO.setInvalidFlag(CommonConstant.NOT_INVALID);
        userInfoPO.setCreateBy(LoginUserUtil.getUserId());
        String createTime = DateUtils.getCurrentLocalDateTime2String();
        userInfoPO.setCreateTime(createTime);
        //user_info 数据持久化
        baseMapper.insert(userInfoPO);

        //更新用户口令
        Integer authCodeCreateType = UserConstant.AUTH_CODE_CREATE_TYPE_4;
        if (StringUtils.isEmpty(addUserInfo.getAuthCode())) {
            authCodeCreateType = UserConstant.AUTH_CODE_CREATE_TYPE_1;
        }
        updateAuthCode(userInfoPO.getUserId(), salt, addUserInfo.getAuthCode(), authCodeCreateType);

        //更新用户安全信息
        UserSecurityExtendPO userSecurityExtendPO = new UserSecurityExtendPO();
        userSecurityExtendPO.setId(IdGenerator.ins().generator());
        userSecurityExtendPO.setUserId(userInfoPO.getUserId());
        userSecurityExtendPO.setLastActiveTime(createTime);
        userSecurityExtendPO.setLastUpdatePwdTime(createTime);
        userSecurityExtendPO.setLoginErrorTimes(0);
        //设置是否登录后强制修改口令 初始化使用默认口令强制修改 手输口令无需修改
        userSecurityExtendPO.setPasswordChangeFlag(UserConstant.AUTH_CODE_CREATE_TYPE_1.equals(authCodeCreateType) ? UserConstant.PASSWORD_CHANGE_FLAG_NEED_1 : UserConstant.PASSWORD_CHANGE_FLAG_NOT_NEED_0);
        userSecurityExtendPO.setInvalidFlag(CommonConstant.NOT_INVALID);
        userSecurityExtendPO.setCreateBy(LoginUserUtil.getUserId());
        userSecurityExtendPO.setCreateTime(createTime);
        userSecurityExtendMapper.insert(userSecurityExtendPO);

        if (addUserInfo.getUKeyInfo() != null && StringUtils.isNotEmpty(addUserInfo.getUKeyInfo().getSerial())) {
            UserCertPO userCertPO = new UserCertPO();
            userCertPO.setId(IdGenerator.ins().generator());
            userCertPO.setUserId(userInfoPO.getUserId());
            if (isRegister) {
                userCertPO.setCert(addUserInfo.getUKeyInfo().getCert());
                userCertPO.setSerial(addUserInfo.getUKeyInfo().getSerial());
            } else {
                userCertPO.setCert(ComponentSynthesisEncryptionUtil.encPwd(addUserInfo.getUKeyInfo().getCert()));
                userCertPO.setSerial(ComponentSynthesisEncryptionUtil.encPwd(addUserInfo.getUKeyInfo().getSerial()));
            }
            userCertPO.setCertType(UserConstant.USER_CERT_TYPE_UKEY_1);
            userCertPO.setInvalidFlag(CommonConstant.NOT_INVALID);
            userCertPO.setCreateBy(LoginUserUtil.getUserId());
            userCertPO.setCreateTime(createTime);
            userCertMapper.insert(userCertPO);
        }
        //用户认证信息完整性校验
        updateUserHmac(userInfoPO.getUserId());
        return ResultUtil.ok(userInfoPO.getUserId());
    }

    /**
     * 批量添加
     *
     * @param userInfoList 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public SecRestResponse<Map<String, Long>> batchAdd(List<AddUserInfoDTO> userInfoList) {
        Map<String, Long> addUserMap = new HashMap<>();
        for (AddUserInfoDTO addUserInfo : userInfoList) {
            SecRestResponse<Object> addUserResult = add(addUserInfo);
            if (addUserResult.isSuccess()) {
                addUserMap.put(addUserInfo.getUserCode(), (Long) addUserResult.getResult());
            }
        }
        return ResultUtil.ok(addUserMap);
    }

    /**
     * ukey序列号批量校验
     * 已被使用返回true
     *
     * @param uKeySerialList 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Map<String, Boolean>> uKeySerialCheck(List<String> uKeySerialList) {
        Map<String, Boolean> uKeySerialCheckMap = new HashMap<>();
        for (String uKeySerial : uKeySerialList) {

            String serialEnc = ComponentSynthesisEncryptionUtil.encPwd(uKeySerial);
            LambdaQueryWrapper<UserCertPO> uKeySerialQueryWrapper = new LambdaQueryWrapper<>();
            uKeySerialQueryWrapper.eq(UserCertPO::getSerial, serialEnc)
                    .eq(UserCertPO::getInvalidFlag, CommonConstant.NOT_INVALID);
            List<UserCertPO> userCertList = userCertMapper.selectList(uKeySerialQueryWrapper);
            if (!userCertList.isEmpty()) {
                uKeySerialCheckMap.put(uKeySerial, true);
                continue;
            }
            //user_register 不能有账户名称重复的的
            LambdaQueryWrapper<UserRegisterPO> userRegisterUKeyCertQuery = new LambdaQueryWrapper<>();
            userRegisterUKeyCertQuery.eq(UserRegisterPO::getUkeySerial, serialEnc)
                    .ne(UserRegisterPO::getAuditStatus, UserConstant.REGISTER_AUDIT_STATUS_2);
            List<UserRegisterPO> userRegisterPOList4 = userRegisterMapper.selectList(userRegisterUKeyCertQuery);
            if (!userRegisterPOList4.isEmpty()) {
                uKeySerialCheckMap.put(uKeySerial, true);
                continue;
            }
            uKeySerialCheckMap.put(uKeySerial, false);
        }
        return ResultUtil.ok(uKeySerialCheckMap);
    }

    /**
     * 更新数据
     *
     * @param editSelfUserInfoDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> editSelfInfo(EditSelfUserInfoDTO editSelfUserInfoDTO) {
        Long userId = LoginUserUtil.getUserId();
        editUserCheck(editSelfUserInfoDTO, userId);
        UserInfoPO userInfoPO = userInfoConvert.editSelfUserInfoDtoToPo(editSelfUserInfoDTO);
        if (StringUtils.isNotEmpty(userInfoPO.getPhoneNum())) {
            userInfoPO.setPhoneNum(ComponentSynthesisEncryptionUtil.encPwd(userInfoPO.getPhoneNum()));
        }
        if (StringUtils.isNotEmpty(userInfoPO.getEmailAddress())) {
            userInfoPO.setEmailAddress(ComponentSynthesisEncryptionUtil.encPwd(userInfoPO.getEmailAddress()));
        }
        userInfoPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.updateById(userInfoPO);
        //用户认证信息完整性校验
        updateUserHmac(userInfoPO.getUserId());
        return ResultUtil.ok();
    }

    /**
     * 更新数据
     *
     * @param editUserInfoDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(EditUserInfoDTO editUserInfoDTO) {
        if (editUserInfoDTO.getOrganizationId() != null) {
            SecRestResponse<OrganizationInfoVO> organizationInfoVOSecRestResponse = organizationService.selectById(editUserInfoDTO.getOrganizationId());
            if( !organizationInfoVOSecRestResponse.isSuccess() || organizationInfoVOSecRestResponse.getResult()==null ){
                throw new BusinessException(SecErrorCodeConstant.ORGANIZATION_INFO_NOT_EXIST);
            }
        }

        UserInfoPO userInfoPO = baseMapper.selectById(editUserInfoDTO.getUserId());
        if (userInfoPO == null) {
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        LambdaUpdateWrapper<UserInfoPO> updateWrapper = Wrappers.lambdaUpdate();
        if (StringUtils.isNotBlank(editUserInfoDTO.getUserName())) {
            updateWrapper.set(UserInfoPO::getUserName, editUserInfoDTO.getUserName());
        }

        updateWrapper.set(UserInfoPO::getOrganizationId, editUserInfoDTO.getOrganizationId());

        if (StringUtils.isNotBlank(editUserInfoDTO.getRemark())) {
            updateWrapper.set(UserInfoPO::getRemark, editUserInfoDTO.getRemark());
        }
        if (StringUtils.isNotBlank(editUserInfoDTO.getPhoneNum())) {
            updateWrapper.set(UserInfoPO::getPhoneNum, editUserInfoDTO.getPhoneNum());
        }

        updateWrapper.set(UserInfoPO::getUpdateBy, LoginUserUtil.getUserId())
                .set(UserInfoPO::getUpdateTime, DateUtils.localDateTime2String(LocalDateTime.now()))
                .eq(UserInfoPO::getUserId, editUserInfoDTO.getUserId());
        baseMapper.update(null, updateWrapper);
        //用户认证信息完整性校验
        updateUserHmac(userInfoPO.getUserId());
        return ResultUtil.ok();
    }

    /**
     * 通过主键删除数据
     *
     * @param userId 用户id
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> deleteById(Long userId) {
        UserInfoPO userInfoPO = baseMapper.selectById(userId);
        if (userInfoPO == null) {
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        if (userInfoPO.getInvalidFlag().equals(CommonConstant.INVALID)) {
            throw new BusinessException(SecErrorCodeConstant.USER_ALREADY_DELETED);
        }
        if (LoginUserUtil.getUserId().equals(userId)) {
            throw new BusinessException(SecErrorCodeConstant.UNABLE_DELETE_SELF);
        }
        //系统每个角色至少存在一个可用用户
        LambdaQueryWrapper<UserInfoPO> userInfoQueryWrapper = new LambdaQueryWrapper<>();
        userInfoQueryWrapper.eq(UserInfoPO::getRoleId, userInfoPO.getRoleId());
        userInfoQueryWrapper.eq(UserInfoPO::getTenantId, userInfoPO.getTenantId());
        userInfoQueryWrapper.eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);
        userInfoQueryWrapper.eq(UserInfoPO::getUserStatus, UserConstant.USER_STATUS_1);
        userInfoQueryWrapper.ne(UserInfoPO::getUserId, userId);
        List<UserInfoPO> userInfoList = baseMapper.selectList(userInfoQueryWrapper);
        if (CollectionUtils.isEmpty(userInfoList)) {
            throw new BusinessException(SecErrorCodeConstant.USER_NOT_ONLY_ONE);
        }
        // userInfo
        UserInfoPO updateUserInfo = new UserInfoPO();
        updateUserInfo.setUserId(userId);
        updateUserInfo.setInvalidFlag(CommonConstant.INVALID);
        baseMapper.updateById(updateUserInfo);

        // userCert
        LambdaUpdateWrapper<UserCertPO> updateWrapper = Wrappers.<UserCertPO>lambdaUpdate().eq(UserCertPO::getUserId, userId).set(UserCertPO::getInvalidFlag, CommonConstant.INVALID);
        userCertMapper.update(null, updateWrapper);

        //用户认证信息完整性校验
        updateUserHmac(userInfoPO.getUserId());
        return ResultUtil.ok();
    }

    /**
     * 通过主键删除数据
     *
     * @param userIdList 用户id
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> deleteByIds(List<Long> userIdList) {
        // userInfo
        LambdaQueryWrapper<UserInfoPO> updateUserInfoQuery = new LambdaQueryWrapper<>();
        updateUserInfoQuery.in(UserInfoPO::getUserId, userIdList);
        UserInfoPO updateUserInfo = new UserInfoPO();
        updateUserInfo.setInvalidFlag(CommonConstant.INVALID);
        baseMapper.update(updateUserInfo, updateUserInfoQuery);

        // userCert
        LambdaQueryWrapper<UserCertPO> updateUserCertQuery = new LambdaQueryWrapper<>();
        updateUserCertQuery.in(UserCertPO::getUserId, userIdList);
        UserCertPO updateUserCert = new UserCertPO();
        updateUserCert.setInvalidFlag(CommonConstant.INVALID);
        userCertMapper.update(updateUserCert, updateUserCertQuery);
        return ResultUtil.ok();
    }

    /**
     * 通过主键删除数据
     *
     * @param tenantId 租户id
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> deleteByTenantId(Long tenantId) {
        LambdaQueryWrapper<UserInfoPO> updateQuery = new LambdaQueryWrapper<>();
        updateQuery.eq(UserInfoPO::getTenantId, tenantId);
        List<UserInfoPO> userInfoPOList = baseMapper.selectList(updateQuery);
        List<Long> userIdList = userInfoPOList.stream().map(UserInfoPO::getUserId).distinct().collect(Collectors.toList());

//		UserInfoPO updateUserInfo=new UserInfoPO();
//		updateUserInfo.setInvalidFlag(CommonConstant.INVALID);
//		baseMapper.update(updateUserInfo,updateQuery);

        return deleteByIds(userIdList);
    }

    /**
     * 添加用户时对用户信息进行校验
     *
     * @param addUserInfoDTO 用户信息
     * @return 结果
     */
    private void addUserCheck(AddUserInfoDTO addUserInfoDTO) {

        //口令长度校验
        if (StringUtils.isNotEmpty(addUserInfoDTO.getAuthCode()) && addUserInfoDTO.getAuthCode().length() != UserConstant.AUTH_CODE_HMAC_LENGTH) {
            throw new BusinessException(SecErrorCodeConstant.USER_AUTH_CODE_ERROR);
        }

        //user_info 未作废的账户名称不能重复
        LambdaQueryWrapper<UserInfoPO> userCodeQueryWrapper = new LambdaQueryWrapper<>();
        userCodeQueryWrapper.eq(UserInfoPO::getUserCode, addUserInfoDTO.getUserCode())
                .eq(UserInfoPO::getTenantId, addUserInfoDTO.getTenantId())
                .eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);
        List<UserInfoPO> userInfoPOList = baseMapper.selectList(userCodeQueryWrapper);
        if (!userInfoPOList.isEmpty()) {
            throw new BusinessException(SecErrorCodeConstant.USER_CODE_IS_EXIST);
        }
        //user_register 待审核的账户名称不能重复
        LambdaQueryWrapper<UserRegisterPO> userCodeRegisterQuery = new LambdaQueryWrapper<>();
        userCodeRegisterQuery.eq(UserRegisterPO::getUserCode, addUserInfoDTO.getUserCode())
                .eq(UserRegisterPO::getTenantId, addUserInfoDTO.getTenantId())
                .eq(UserRegisterPO::getAuditStatus, UserConstant.REGISTER_AUDIT_STATUS_0);
        List<UserRegisterPO> userRegisterPOList = userRegisterMapper.selectList(userCodeRegisterQuery);
        if (!userRegisterPOList.isEmpty()) {
            throw new BusinessException(SecErrorCodeConstant.USER_CODE_IS_EXIST);
        }

        //手机号不能重复
        if (StringUtils.isNotEmpty(addUserInfoDTO.getPhoneNum())) {
            //user_info 不能有账户名称重复的的未作废的记录
            String phoneNumEnc = ComponentSynthesisEncryptionUtil.encPwd(addUserInfoDTO.getPhoneNum());
            LambdaQueryWrapper<UserInfoPO> userPhoneQueryWrapper = new LambdaQueryWrapper<>();
            userPhoneQueryWrapper.eq(UserInfoPO::getPhoneNum, phoneNumEnc)
                    .eq(UserInfoPO::getTenantId, addUserInfoDTO.getTenantId())
                    .eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);
            List<UserInfoPO> userInfoPOList2 = baseMapper.selectList(userPhoneQueryWrapper);
            if (!userInfoPOList2.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.USER_PHONE_IS_EXIST);
            }
            //user_register 不能有账户名称重复的的
            LambdaQueryWrapper<UserRegisterPO> userPhoneRegisterQuery = new LambdaQueryWrapper<>();
            userPhoneRegisterQuery.eq(UserRegisterPO::getPhoneNum, phoneNumEnc)
                    .ne(UserRegisterPO::getAuditStatus, UserConstant.REGISTER_AUDIT_STATUS_2);
            List<UserRegisterPO> userRegisterPOList2 = userRegisterMapper.selectList(userPhoneRegisterQuery);
            if (!userRegisterPOList2.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.USER_PHONE_IS_EXIST);
            }
        }

        //邮箱不能重复
        if (StringUtils.isNotEmpty(addUserInfoDTO.getEmailAddress())) {
            //user_info 不能有账户名称重复的的未作废的记录
            String emailEnc = ComponentSynthesisEncryptionUtil.encPwd(addUserInfoDTO.getEmailAddress());
            LambdaQueryWrapper<UserInfoPO> userEmailQueryWrapper = new LambdaQueryWrapper<>();
            userEmailQueryWrapper.eq(UserInfoPO::getEmailAddress, emailEnc)
                    .eq(UserInfoPO::getTenantId, addUserInfoDTO.getTenantId())
                    .eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);
            List<UserInfoPO> userInfoList3 = baseMapper.selectList(userEmailQueryWrapper);
            if (!userInfoList3.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.USER_EMAIL_IS_EXIST);
            }
            //user_register 不能有账户名称重复的的
            LambdaQueryWrapper<UserRegisterPO> userEmailRegisterQuery = new LambdaQueryWrapper<>();
            userEmailRegisterQuery.eq(UserRegisterPO::getEmailAddress, emailEnc)
                    .ne(UserRegisterPO::getAuditStatus, UserConstant.REGISTER_AUDIT_STATUS_2);
            List<UserRegisterPO> userRegisterList3 = userRegisterMapper.selectList(userEmailRegisterQuery);
            if (!userRegisterList3.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.USER_EMAIL_IS_EXIST);
            }
        }
        //角色id必须是有效值
        LambdaQueryWrapper<UserRolePO> userRoleQueryWrapper = new LambdaQueryWrapper<>();
        userRoleQueryWrapper.eq(UserRolePO::getRoleId, addUserInfoDTO.getRoleId())
                .eq(UserRolePO::getInvalidFlag, CommonConstant.NOT_INVALID);
        UserRolePO userRole = userRoleMapper.selectOne(userRoleQueryWrapper);
        if (userRole == null) {
            throw new BusinessException(SecErrorCodeConstant.USER_ROLE_IS_NOT_EXIST);
        }

        //ukey序列号如果存在，不允许重复
        if (addUserInfoDTO.getUKeyInfo() != null && addUserInfoDTO.getUKeyInfo().getSerial() != null) {
            //user_info 不能有账户名称重复的的未作废的记录
            String serialEnc = ComponentSynthesisEncryptionUtil.encPwd(addUserInfoDTO.getUKeyInfo().getSerial());
            LambdaQueryWrapper<UserCertPO> uKeySerialQueryWrapper = new LambdaQueryWrapper<>();
            uKeySerialQueryWrapper.eq(UserCertPO::getSerial, serialEnc).eq(UserCertPO::getCertType, UserConstant.USER_CERT_TYPE_UKEY_1)
                    .eq(UserCertPO::getInvalidFlag, CommonConstant.NOT_INVALID);
            List<UserCertPO> userCertList = userCertMapper.selectList(uKeySerialQueryWrapper);
            if (!userCertList.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.USER_CERT_IS_EXIST);
            }
            //user_register 不能有账户名称重复的的
            LambdaQueryWrapper<UserRegisterPO> userRegisterUKeyCertQuery = new LambdaQueryWrapper<>();
            userRegisterUKeyCertQuery.eq(UserRegisterPO::getUkeySerial, serialEnc)
                    .ne(UserRegisterPO::getAuditStatus, UserConstant.REGISTER_AUDIT_STATUS_2);
            List<UserRegisterPO> userRegisterPOList4 = userRegisterMapper.selectList(userRegisterUKeyCertQuery);
            if (!userRegisterPOList4.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.USER_CERT_IS_EXIST);
            }

            //TODO 证书签名校验
        }

    }

    /**
     * 编辑用户时对用户信息进行校验
     *
     * @param editSelfUserInfoDTO 用户信息
     * @return 结果
     */
    private void editUserCheck(EditSelfUserInfoDTO editSelfUserInfoDTO, Long userId) {
        UserInfoPO userInfoPO = baseMapper.selectById(userId);
        if (userInfoPO == null) {
            throw new BusinessException(SecErrorCodeConstant.INFO_NOT_EXIST);
        }

        //手机号不能重复
        if (StringUtils.isNotEmpty(editSelfUserInfoDTO.getPhoneNum())) {
            //user_info 不能有账户名称重复的的未作废的记录
            String phoneNumEnc = ComponentSynthesisEncryptionUtil.encPwd(editSelfUserInfoDTO.getPhoneNum());
            LambdaQueryWrapper<UserInfoPO> userPhoneQueryWrapper = new LambdaQueryWrapper<>();
            userPhoneQueryWrapper.eq(UserInfoPO::getPhoneNum, phoneNumEnc)
                    .eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID)
                    .eq(UserInfoPO::getTenantId, userInfoPO.getTenantId())
                    .ne(UserInfoPO::getUserId, userId);
            List<UserInfoPO> userInfoPOList2 = baseMapper.selectList(userPhoneQueryWrapper);
            if (!userInfoPOList2.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.USER_PHONE_IS_EXIST);
            }
            //user_register 不能有账户名称重复的的
            LambdaQueryWrapper<UserRegisterPO> userPhoneRegisterQuery = new LambdaQueryWrapper<>();
            userPhoneRegisterQuery.eq(UserRegisterPO::getPhoneNum, phoneNumEnc)
                    .eq(UserRegisterPO::getTenantId, userInfoPO.getTenantId())
                    .ne(UserRegisterPO::getAuditStatus, UserConstant.REGISTER_AUDIT_STATUS_2);
            List<UserRegisterPO> userRegisterPOList2 = userRegisterMapper.selectList(userPhoneRegisterQuery);
            if (!userRegisterPOList2.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.USER_PHONE_IS_EXIST);
            }
        }

        //邮箱不能重复
        if (StringUtils.isNotEmpty(editSelfUserInfoDTO.getEmailAddress())) {
            //user_info 不能有账户名称重复的的未作废的记录
            String emailEnc = ComponentSynthesisEncryptionUtil.encPwd(editSelfUserInfoDTO.getEmailAddress());
            LambdaQueryWrapper<UserInfoPO> userEmailQueryWrapper = new LambdaQueryWrapper<>();
            userEmailQueryWrapper.eq(UserInfoPO::getEmailAddress, emailEnc)
                    .eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID)
                    .ne(UserInfoPO::getUserId, userId);
            List<UserInfoPO> userInfoList3 = baseMapper.selectList(userEmailQueryWrapper);
            if (!userInfoList3.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.USER_EMAIL_IS_EXIST);
            }
            //user_register 不能有账户名称重复的的
            LambdaQueryWrapper<UserRegisterPO> userEmailRegisterQuery = new LambdaQueryWrapper<>();
            userEmailRegisterQuery.eq(UserRegisterPO::getEmailAddress, emailEnc)
                    .ne(UserRegisterPO::getAuditStatus, UserConstant.REGISTER_AUDIT_STATUS_2);
            List<UserRegisterPO> userRegisterList3 = userRegisterMapper.selectList(userEmailRegisterQuery);
            if (!userRegisterList3.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.USER_EMAIL_IS_EXIST);
            }
        }


    }

    /**
     * 启用用户
     * 校验用户是否存在
     * 校验用户状态是不是禁用状态
     * 更新账户最后活跃时间
     * 更新用户状态和
     *
     * @param userId 用户id
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> enable(Long userId) {
        UserInfoPO userInfoPO = baseMapper.selectById(userId);
        if (userInfoPO == null || CommonConstant.INVALID.equals(userInfoPO.getInvalidFlag())) {
            //用户信息不存在
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        if (!UserConstant.USER_STATUS_2.equals(userInfoPO.getUserStatus())) {
            //只能启用禁用状态下的账户
            throw new BusinessException(SecErrorCodeConstant.ONLY_DISABLED_STATUS_CAN_BE_ENABLED);
        }

        LambdaQueryWrapper<UserSecurityExtendPO> userSecurityExtendPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userSecurityExtendPOLambdaQueryWrapper.eq(UserSecurityExtendPO::getUserId, userId);
        UserSecurityExtendPO userSecurityExtendPO = userSecurityExtendMapper.selectOne(userSecurityExtendPOLambdaQueryWrapper);
        userSecurityExtendPO.setLastActiveTime(DateUtils.getCurrentLocalDateTime2String());
        userSecurityExtendMapper.updateById(userSecurityExtendPO);

        userInfoPO.setUserStatus(UserConstant.USER_STATUS_1);
        userInfoPO.setDisableReasonId(UserConstant.USER_DISABLE_REASON_ID_0);
        baseMapper.updateById(userInfoPO);

        //用户认证信息完整性校验
        updateUserHmac(userInfoPO.getUserId());
        return ResultUtil.ok();
    }

    /**
     * 禁用用户
     * 校验用户是否存在
     * 校验用户状态是不是启用状态
     * 更新用户状态
     *
     * @param userId 用户id
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> disable(Long userId) {
        UserInfoPO userInfoPO = baseMapper.selectById(userId);
        if (userInfoPO == null || CommonConstant.INVALID.equals(userInfoPO.getInvalidFlag())) {
            //用户信息不存在
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        if (!UserConstant.USER_STATUS_1.equals(userInfoPO.getUserStatus())) {
            //只能禁用启用状态下的账户
            throw new BusinessException(SecErrorCodeConstant.ONLY_ENABLED_STATUS_CAN_BE_DISABLED);
        }

        //系统每个角色至少存在一个可用用户
        LambdaQueryWrapper<UserInfoPO> userInfoQueryWrapper = new LambdaQueryWrapper<>();
        userInfoQueryWrapper.eq(UserInfoPO::getRoleId, userInfoPO.getRoleId());
        userInfoQueryWrapper.eq(UserInfoPO::getTenantId, userInfoPO.getTenantId());
        userInfoQueryWrapper.eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);
        userInfoQueryWrapper.eq(UserInfoPO::getUserStatus, UserConstant.USER_STATUS_1);
        List<UserInfoPO> userInfoList = baseMapper.selectList(userInfoQueryWrapper);
        if (!CollectionUtils.isEmpty(userInfoList) && userInfoList.size() < 2) {
            throw new BusinessException(SecErrorCodeConstant.USER_NOT_ONLY_ONE);
        }

        userInfoPO.setUserStatus(UserConstant.USER_STATUS_2);
        userInfoPO.setDisableReasonId(UserConstant.USER_DISABLE_REASON_ID_1);
        baseMapper.updateById(userInfoPO);

        //用户认证信息完整性校验
        updateUserHmac(userInfoPO.getUserId());
        return ResultUtil.ok();
    }


    /**
     * 解锁用户
     * 校验用户是否存在
     * 校验用户状态是不是锁定状态
     * 更新账户自动解锁时间
     * 更新用户状态
     *
     * @param userId 用户id
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> unlock(Long userId) {
        UserInfoPO userInfoPO = baseMapper.selectById(userId);
        if (userInfoPO == null || CommonConstant.INVALID.equals(userInfoPO.getInvalidFlag())) {
            //用户信息不存在
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        if (!UserConstant.USER_STATUS_3.equals(userInfoPO.getUserStatus())) {
            //只能解锁锁定状态下的账户
            throw new BusinessException(SecErrorCodeConstant.ONLY_LOCK_STATUS_CAN_BE_UNLOCK);
        }

        userLockService.unLock(userId);
        return ResultUtil.ok();
    }

    /**
     * 重置口令
     * 校验用户是否存在
     * 判断userId是否为当前登录人的userId，是的话返回“不允许重置自己的口令”
     * 从配置获取接口获取系统默认口令。
     * 作废历史口令
     * 拼接用户 salt+默认口令 ,计算 SM3 HMac，插入到 user_auth_code表的启用记录中
     * 更新用户口令为强制修改状态
     *
     * @param userId 用户id
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> resetAuthCode(Long userId) {
        UserInfoPO userInfoPO = baseMapper.selectById(userId);
        if (userInfoPO == null || CommonConstant.INVALID.equals(userInfoPO.getInvalidFlag())) {
            //用户信息不存在
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        if (userId.equals(LoginUserUtil.getUserId())) {
            //当前登录账号的口令只能修改不能重置
            throw new BusinessException(SecErrorCodeConstant.NOT_ALLOW_REST_SELF_AUTH_CODE);
        }

        //口令加盐Sm3hmac处理
        String salt = ComponentSynthesisEncryptionUtil.decPwd(userInfoPO.getSalt());
        //更新口令
        updateAuthCode(userId, salt, null, UserConstant.AUTH_CODE_CREATE_TYPE_3);
        //设置是否登录后强制修改口令 重置口令后强制修改
        LambdaUpdateWrapper<UserSecurityExtendPO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(UserSecurityExtendPO::getPasswordChangeFlag, UserConstant.PASSWORD_CHANGE_FLAG_NEED_1).eq(UserSecurityExtendPO::getUserId, userId);
        userSecurityExtendMapper.update(null, lambdaUpdateWrapper);

        //用户认证信息完整性校验
        updateUserHmac(userInfoPO.getUserId());
        return ResultUtil.ok();
    }


    /**
     * 修改口令
     * 使用用户ID查询user_info表是否存在用户信息，不存在返回用户信息不存在。
     * 拼接用户盐值 salt+输入的旧口令SM3Hmac值 计算SM3Hmac 比对user_auth_code表的启用记录，如果不匹配，返回错误“口令输入错误”，同时累计账号口令错误次数。
     * 累计次数到达系统设置的累计次数，锁定这个账号。返回错误码，后端直接调用注销登录功能。
     * 拼接用户盐值 salt+输入的旧口令SM3Hmac值 计算SM3Hmac 按时间倒序查询user_auth_code表，limit次数为系统配置参数中设置的历史密钥匹配次数。
     * 比对新的口令hmac是否与历史密钥重复，如果重复，提示新口令不符合安全策略，不能使用历史口令。
     *
     * @param editAuthCodeDTO 口令信息
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> editAuthCode(EditAuthCodeDTO editAuthCodeDTO) {
        UserInfoPO userInfoPO = baseMapper.selectById(LoginUserUtil.getUserId());
        if (userInfoPO == null) {
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        //口令长度校验
        if (editAuthCodeDTO.getAuthCode().length() != UserConstant.AUTH_CODE_HMAC_LENGTH) {
            throw new BusinessException(SecErrorCodeConstant.AUTH_CODE_VERIFY_ERROR);
        }
        //口令长度校验
        if (editAuthCodeDTO.getNewAuthCode().length() != UserConstant.AUTH_CODE_HMAC_LENGTH) {
            throw new BusinessException(SecErrorCodeConstant.NEW_AUTH_CODE_VERIFY_ERROR);
        }

        String salt = ComponentSynthesisEncryptionUtil.decPwd(userInfoPO.getSalt());
        String oldAuthCodeHmac = SM3Util.digestWithHex(salt + editAuthCodeDTO.getAuthCode());

        LambdaQueryWrapper<UserAuthCodePO> oldUserAuthCodePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        oldUserAuthCodePOLambdaQueryWrapper.eq(UserAuthCodePO::getUserId, LoginUserUtil.getUserId()).
                eq(UserAuthCodePO::getAuthCode, oldAuthCodeHmac).
                eq(UserAuthCodePO::getInvalidFlag, CommonConstant.NOT_INVALID);
        UserAuthCodePO oldUserAuthCode = userAuthCodeMapper.selectOne(oldUserAuthCodePOLambdaQueryWrapper);
        if (oldUserAuthCode == null) {
            //todo 记录口令输入失败次数
            throw new BusinessException(SecErrorCodeConstant.AUTH_CODE_VERIFY_ERROR);
        }
        //口令黑名单校验
        boolean isAuthCodeInBlackList = authCodeBlacklistService.isAuthCodeInBlackList(editAuthCodeDTO.getNewAuthCode());
        if (isAuthCodeInBlackList) {
            throw new BusinessException(SecErrorCodeConstant.NEW_AUTH_CODE_VERIFY_ERROR);
        }

        //新口令摘要值
        String newAuthCodeHmac = SM3Util.digestWithHex(salt + editAuthCodeDTO.getNewAuthCode());
        String authCodeHistoryLimitConfigCode = "authCodeHistoryLimit";
        int authCodeHistoryLimit = Integer.parseInt(configService.getConfigValueByConfigCode(authCodeHistoryLimitConfigCode).getResult());
        if (authCodeHistoryLimit > 0) {
            LambdaQueryWrapper<UserAuthCodePO> newAuthCodeCheckQuery = new LambdaQueryWrapper<>();
            newAuthCodeCheckQuery.eq(UserAuthCodePO::getUserId, LoginUserUtil.getUserId()).
                    orderByDesc(UserAuthCodePO::getCreateTime).orderByDesc(UserAuthCodePO::getId);
            List<UserAuthCodePO> userAuthCodePOList = userAuthCodeMapper.selectList(newAuthCodeCheckQuery);

            if (userAuthCodePOList.stream().limit(authCodeHistoryLimit).anyMatch(userAuthCodePO -> newAuthCodeHmac.equals(userAuthCodePO.getAuthCode()))) {
                throw new BusinessException(SecErrorCodeConstant.NEW_AUTH_CODE_VERIFY_ERROR);
            }
        }
        updateAuthCode(LoginUserUtil.getUserId(), salt, editAuthCodeDTO.getNewAuthCode(), UserConstant.AUTH_CODE_CREATE_TYPE_2);
        //设置是否登录后强制修改口令 修改口令后不强制修改
        LambdaUpdateWrapper<UserSecurityExtendPO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(UserSecurityExtendPO::getPasswordChangeFlag, UserConstant.PASSWORD_CHANGE_FLAG_NOT_NEED_0).eq(UserSecurityExtendPO::getUserId, LoginUserUtil.getUserId());
        userSecurityExtendMapper.update(null, lambdaUpdateWrapper);

        //用户认证信息完整性校验
        updateUserHmac(userInfoPO.getUserId());
        return ResultUtil.ok();
    }


    /**
     * 更新口令表数据
     *
     * @param userId             用户id not null
     * @param salt               盐值 not null
     * @param authCode           null 为null时取系统默认口令
     * @param authCode           null 为null时取系统默认口令
     * @param authCodeCreateType 来源id
     */
    private void updateAuthCode(Long userId, String salt, String authCode, Integer authCodeCreateType) {
        if (userId == null || StringUtils.isEmpty(salt) || authCodeCreateType == null) {
            //用户信息不存在
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        if (StringUtils.isEmpty(authCode)) {
            SecRestResponse<String> authCodeResult = configService.getConfigValueByConfigCode(UserConstant.DEFAULT_AUTH_CODE_CONFIG_CODE);
            if (!authCodeResult.isSuccess() || StringUtils.isEmpty(authCodeResult.getResult())) {
                log.error("UserInfoServiceImpl updateAuthCode 默认口令配置不存在");
                throw new BusinessException(SecErrorCodeConstant.DEFAULT_AUTH_CODE_NOT_EXIST);
            }
            authCode = authCodeResult.getResult();
        }
        authCode = SM3Util.digestWithHex(salt + authCode);

        LambdaQueryWrapper<UserAuthCodePO> userAuthCodePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userAuthCodePOLambdaQueryWrapper.eq(UserAuthCodePO::getUserId, userId);
        UserAuthCodePO updateUserAuthCodePO = new UserAuthCodePO();
        updateUserAuthCodePO.setInvalidFlag(CommonConstant.INVALID);
        userAuthCodeMapper.update(updateUserAuthCodePO, userAuthCodePOLambdaQueryWrapper);

        UserAuthCodePO userAuthCodePO = new UserAuthCodePO();
        userAuthCodePO.setId(IdGenerator.ins().generator());
        userAuthCodePO.setUserId(userId);
        userAuthCodePO.setAuthCode(authCode);
        userAuthCodePO.setCreateType(authCodeCreateType);
        userAuthCodePO.setInvalidFlag(CommonConstant.NOT_INVALID);
        userAuthCodePO.setCreateBy(LoginUserUtil.getUserId());
        userAuthCodePO.setCreateTime(DateUtils.getCurrentLocalDateTime2String());
        userAuthCodeMapper.insert(userAuthCodePO);
    }

    /**
     * 获取当前登录人员信息
     *
     * @return 实例对象
     */
    @Override
    public SecRestResponse<LoginUserInfoVO> getLoginUserInfo() {
        Long userId = LoginUserUtil.getUserId();
        if (userId == null) {
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        UserInfoPO userInfoPO = baseMapper.selectById(userId);
        if (userInfoPO == null) {
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        LoginUserInfoVO loginUserInfoVO = userInfoConvert.convertLoginUserInfoVo(userInfoPO);
        if (StringUtils.isNotEmpty(loginUserInfoVO.getPhoneNum())) {
            loginUserInfoVO.setPhoneNum(ComponentSynthesisEncryptionUtil.decPwd(loginUserInfoVO.getPhoneNum()));
        }
        if (StringUtils.isNotEmpty(loginUserInfoVO.getEmailAddress())) {
            loginUserInfoVO.setEmailAddress(ComponentSynthesisEncryptionUtil.decPwd(loginUserInfoVO.getEmailAddress()));
        }
        UserRolePO userRolePO = userRoleMapper.selectById(userInfoPO.getRoleId());
        loginUserInfoVO.setRoleName(userRolePO.getRoleName());
        userRolePO.getRoleId();
        loginUserInfoVO.setRoleId(userRolePO.getRoleId());
        loginUserInfoVO.setRoleType(userRolePO.getRoleType());

        //获取配置
        List<ConfigPO> configList = configService.getConfigAll();
        Map<String, String> configMap = configList.stream().collect(Collectors.toMap(ConfigPO::getConfigCode, ConfigPO::getConfigValue, (key1, key2) -> key2));

        //获取当前是否使用口令登录
        boolean openAuthCodeLogin = Boolean.parseBoolean(configMap.get("openAuthCodeLogin"));

        if (openAuthCodeLogin) {
            LambdaQueryWrapper<UserAuthCodePO> authCodePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            authCodePOLambdaQueryWrapper.eq(UserAuthCodePO::getUserId, userId).
                    eq(UserAuthCodePO::getInvalidFlag, CommonConstant.NOT_INVALID);
            UserAuthCodePO userAuthCodePO = userAuthCodeMapper.selectOne(authCodePOLambdaQueryWrapper);

            //获取口令有效期配置
            long authCodeExpireDateConfig = Long.parseLong(configMap.get("authCodeExpireDate"));
            long authCodeUpdateTime = DateUtils.string2Datetime(userAuthCodePO.getCreateTime()).getTime();
            long authCodeExpireDate = authCodeUpdateTime + authCodeExpireDateConfig * 24 * 60 * 60 * 1000;
            if (authCodeExpireDate < System.currentTimeMillis()) {
                //口令已过期
                loginUserInfoVO.setAuthCodeCheckStatus(UserConstant.AUTH_CODE_CHECK_STATUS_1);
                loginUserInfoVO.setAuthCodeCheckMessage("账号登录口令已过期,请修改口令");
            } else {
                //是否为默认口令校验
                boolean force_update_passwordB = Boolean.parseBoolean(configMap.get("forceUpdatePassword"));
                UserSecurityExtendPO userSecurityExtendPO = getUserSecurityInfo(userId);
                if (force_update_passwordB && 1 == userSecurityExtendPO.getPasswordChangeFlag()) {
                    //默认口令修改
                    loginUserInfoVO.setAuthCodeCheckStatus(UserConstant.AUTH_CODE_CHECK_STATUS_1);
                    loginUserInfoVO.setAuthCodeCheckMessage("登录成功，请先修改初始密码!");
                } else {
                    //获取口令有效期告警配置
                    long authCodeExpirationReminderConfig = Long.parseLong(configMap.get("authCodeExpirationReminder"));
                    long authCodeExpirationReminderDate = authCodeUpdateTime + (authCodeExpireDateConfig - authCodeExpirationReminderConfig) * 24 * 60 * 60 * 1000;
                    if (authCodeExpirationReminderDate < System.currentTimeMillis()) {
                        loginUserInfoVO.setAuthCodeCheckStatus(UserConstant.AUTH_CODE_CHECK_STATUS_2);
                        loginUserInfoVO.setAuthCodeCheckMessage("账号登录口令即将过期，请修改口令");
                    } else {
                        loginUserInfoVO.setAuthCodeCheckStatus(UserConstant.AUTH_CODE_CHECK_STATUS_0);
                    }
                }
            }
        } else {
            loginUserInfoVO.setAuthCodeCheckStatus(UserConstant.AUTH_CODE_CHECK_STATUS_0);
        }

        List<String> perms = userRoleService.getPerms();
        loginUserInfoVO.setPerms(perms);
        //tenantId
        loginUserInfoVO.setTenantId(userInfoPO.getTenantId());
        //appId
        loginUserInfoVO.setAppId(userInfoPO.getAppId());

        return ResultUtil.ok(loginUserInfoVO);
    }

    /**
     * 通过租户标识和用户账号获取用户信息
     *
     * @param tenantCode 租户标识
     * @param userCode   用户帐号
     * @return 用户信息
     */
    @Override
    public UserInfoPO getUserInfo(String tenantCode, String userCode) {
        //user_info 查询
        LambdaQueryWrapper<UserInfoPO> userCodeQueryWrapper = new LambdaQueryWrapper<>();
        userCodeQueryWrapper.eq(UserInfoPO::getUserCode, userCode)
                .eq(UserInfoPO::getTenantCode, tenantCode)
                .eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);
        return baseMapper.selectOne(userCodeQueryWrapper);
    }

    /**
     * 通过userId获取人员口令
     *
     * @param userId 用户id
     * @return 口令-密文
     */
    @Override
    public String getUserAuthCode(Long userId) {
        //user_info 查询
        LambdaQueryWrapper<UserAuthCodePO> authCodeQueryWrapper = new LambdaQueryWrapper<>();
        authCodeQueryWrapper.eq(UserAuthCodePO::getUserId, userId).eq(UserAuthCodePO::getInvalidFlag, CommonConstant.NOT_INVALID);
        return userAuthCodeMapper.selectOne(authCodeQueryWrapper).getAuthCode();
    }

    /**
     * 口令校验
     *
     * @param userId   用户id
     * @param salt     盐
     * @param authCode 口令
     * @return 是否通过
     */
    @Override
    public Boolean authCodeVerify(Long userId, String salt, String authCode) {
        String oldAuthCode = getUserAuthCode(userId);
        authCode = SM3Util.digestWithHex(salt + authCode);
        return authCode.equals(oldAuthCode);
    }

    /**
     * 获取用户账户安全信息
     *
     * @param userId 用户id
     * @return 账户安全信息
     */
    @Override
    public UserSecurityExtendPO getUserSecurityInfo(Long userId) {
        LambdaQueryWrapper<UserSecurityExtendPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSecurityExtendPO::getUserId, userId);
        return userSecurityExtendMapper.selectOne(queryWrapper);
    }

    /**
     * 更新用户账号登录状态
     * 更新活跃时间
     * 更新累计锁定次数
     *
     * @param userId 用户id
     */
    @Override
    public void updateUserLoginStatus(Long userId) {
        UserSecurityExtendPO userSecurityExtendPO = getUserSecurityInfo(userId);
        String createTime = DateUtils.getCurrentLocalDateTime2String();
        userSecurityExtendPO.setLastActiveTime(createTime);
        userSecurityExtendPO.setLoginErrorTimes(0);
        userSecurityExtendPO.setUnlockTime(null);
        userSecurityExtendMapper.updateById(userSecurityExtendPO);
    }

    /**
     * 验证ukey证书签名
     *
     * @param cert   证书
     * @param random 校验码
     * @param sign   签名值
     * @return
     */
    @Override
    public boolean checkCert(String cert, String random, String sign) {
        if (StringUtils.isEmpty(cert) || StringUtils.isEmpty(random) || StringUtils.isEmpty(sign)) {
            throw new BusinessException(SecErrorCodeConstant.CHECK_CERT_ERROR);
        }

        UserRootCaPO rootCert = userRootCaService.getUserRootCaByCertType(UserConstant.USER_CERT_TYPE_UKEY_1);
        //检查根证书是否存在
        if (rootCert == null) {
            log.error("UserInfoServiceImpl checkCert 根证书不存在");
            throw new BusinessException(SecErrorCodeConstant.CHECK_CERT_ERROR);
        }
        byte[] certBytes = Base64.decodeBase64(cert);

        try {
            CertificateFactory cf = CertificateFactory.getInstance("X.509", "SwxaJCE");
            X509Certificate certificate = (X509Certificate) cf.generateCertificate(new ByteArrayInputStream(certBytes));

            //获取到期时间
            Date endTime = certificate.getNotAfter();
            Date current = new Date();
            if (!endTime.after(current)) {
                log.error("UserInfoServiceImpl checkCert 证书已过期");
                throw new BusinessException(SecErrorCodeConstant.CHECK_CERT_ERROR);
            }

            boolean verifyRoot = VerifySM2Sign.verifyCert(cert, rootCert.getVerifyCert());
            if (!verifyRoot) {
                log.error("UserInfoServiceImpl checkCert 证书签发者不匹配");
                throw new BusinessException(SecErrorCodeConstant.CHECK_CERT_ERROR);
            }
            return VerifySM2Sign.sm2VerifyWithCert(random, sign, cert);

        } catch (Exception e) {
            log.error("UserInfoServiceImpl checkCert 校验失败", e);
            throw new BusinessException(SecErrorCodeConstant.CHECK_CERT_ERROR);
        }

    }

    /**
     * 绑定ukey
     *
     * @param bindUKeyDTO 绑定ukey
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> uKeyBind(BindUKeyDTO bindUKeyDTO) {
        UserInfoPO userInfoPO = baseMapper.selectById(bindUKeyDTO.getUserId());
        if (userInfoPO == null) {
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        //验签校验
        boolean verifyResult = userInfoService.checkCert(bindUKeyDTO.getCert(), bindUKeyDTO.getRandom(), bindUKeyDTO.getSign());
        if (!verifyResult) {
            log.error("UserInfoServiceImpl uKeyBind 验证签名值失败");
            throw new BusinessException(SecErrorCodeConstant.CHECK_CERT_ERROR);
        }

        LambdaQueryWrapper<UserCertPO> userCertPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userCertPOLambdaQueryWrapper.eq(UserCertPO::getSerial, ComponentSynthesisEncryptionUtil.encPwd(bindUKeyDTO.getSerial()))
                .eq(UserCertPO::getCertType, UserConstant.USER_CERT_TYPE_UKEY_1)
                .eq(UserCertPO::getInvalidFlag, CommonConstant.NOT_INVALID);
        List<UserCertPO> userCertPOList = userCertMapper.selectList(userCertPOLambdaQueryWrapper);
        if (!userCertPOList.isEmpty()) {
            throw new BusinessException(SecErrorCodeConstant.USER_CERT_IS_EXIST);
        }

        LambdaQueryWrapper<UserCertPO> userCertPOLambdaQueryWrapper2 = new LambdaQueryWrapper<>();
        userCertPOLambdaQueryWrapper2.eq(UserCertPO::getUserId, bindUKeyDTO.getUserId()).eq(UserCertPO::getCertType, UserConstant.USER_CERT_TYPE_UKEY_1);
        UserCertPO userCertPO = userCertMapper.selectOne(userCertPOLambdaQueryWrapper2);
        if (userCertPO == null) {
            userCertPO = new UserCertPO();
            userCertPO.setId(IdGenerator.ins().generator());
            userCertPO.setUserId(bindUKeyDTO.getUserId());
            userCertPO.setCert(ComponentSynthesisEncryptionUtil.encPwd(bindUKeyDTO.getCert()));
            userCertPO.setSerial(ComponentSynthesisEncryptionUtil.encPwd(bindUKeyDTO.getSerial()));
            userCertPO.setCertType(UserConstant.USER_CERT_TYPE_UKEY_1);
            userCertPO.setInvalidFlag(CommonConstant.NOT_INVALID);
            userCertPO.setCreateBy(LoginUserUtil.getUserId());
            userCertPO.setCreateTime(DateUtils.getCurrentLocalDateTime2String());
            userCertMapper.insert(userCertPO);
        } else {
            userCertPO.setCert(ComponentSynthesisEncryptionUtil.encPwd(bindUKeyDTO.getCert()));
            userCertPO.setSerial(ComponentSynthesisEncryptionUtil.encPwd(bindUKeyDTO.getSerial()));
            userCertMapper.updateById(userCertPO);
        }
        //用户认证信息完整性校验
        updateUserHmac(userInfoPO.getUserId());
        return ResultUtil.ok();
    }


    /**
     * 通过用户id获取用户信息
     *
     * @param userId 用户id
     * @return 用户信息
     */
    @Override
    public UserInfoPO getUserInfoById(Long userId) {
        return baseMapper.selectById(userId);
    }

    /**
     * 设置账号有效期
     *
     * @param setExpiryDateDTO 有效期信息
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> expiryDate(SetExpiryDateDTO setExpiryDateDTO) {
        UserInfoPO userInfoPO = baseMapper.selectById(setExpiryDateDTO.getUserId());
        if (userInfoPO == null) {
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }

        if (UserRoleConstant.PT_USER_ROLE_2.equals(userInfoPO.getRoleId())) {
            //只有一个系统管理员时不可设置有效期
            LambdaQueryWrapper<UserInfoPO> userInfoQueryWrapper = new LambdaQueryWrapper<>();
            userInfoQueryWrapper.eq(UserInfoPO::getRoleId, userInfoPO.getRoleId());
            userInfoQueryWrapper.eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);
            userInfoQueryWrapper.eq(UserInfoPO::getUserStatus, UserConstant.USER_STATUS_1);
            List<UserInfoPO> userInfoList = baseMapper.selectList(userInfoQueryWrapper);
            if (!CollectionUtils.isEmpty(userInfoList) && userInfoList.size() <= 1) {
                throw new BusinessException(SecErrorCodeConstant.ADMIN_USER_NOT_ONLY_ONE_NOT_TIME);
            }
        }


        Long startTime = null, endTime = null;
        if (StringUtils.isNotEmpty(setExpiryDateDTO.getAccountStartTime())) {
            startTime = DateUtils.localDateTime2Timestamp(DateUtils.string2LocalDateTime(setExpiryDateDTO.getAccountStartTime()));
        }
        if (StringUtils.isNotEmpty(setExpiryDateDTO.getAccountEndTime())) {
            endTime = DateUtils.localDateTime2Timestamp(DateUtils.string2LocalDateTime(setExpiryDateDTO.getAccountEndTime()));
        }
        if ((startTime != null && endTime != null) && startTime >= endTime) {
            throw new BusinessException(SecErrorCodeConstant.START_TIME_CAN_NOT_BIG_THAN_END_TIME);
        }
        LambdaUpdateWrapper<UserSecurityExtendPO> updateWrapper = Wrappers.<UserSecurityExtendPO>lambdaUpdate()
                .set(UserSecurityExtendPO::getAccountStartTime, setExpiryDateDTO.getAccountStartTime())
                .set(UserSecurityExtendPO::getAccountEndTime, setExpiryDateDTO.getAccountEndTime())
                .set(UserSecurityExtendPO::getUpdateBy, LoginUserUtil.getUserId())
                .set(UserSecurityExtendPO::getUpdateTime, DateUtils.localDateTime2String(LocalDateTime.now()))
                .eq(UserSecurityExtendPO::getUserId, setExpiryDateDTO.getUserId());
        userSecurityExtendMapper.update(null, updateWrapper);
        //用户认证信息完整性校验
        updateUserHmac(userInfoPO.getUserId());
        return ResultUtil.ok();
    }

    /**
     * 获取需要初始化的角色列表
     *
     * @return 角色列表
     */
    @Override
    public SecRestResponse<List<UserRoleVO>> getInitUserRoleList() {
        List<UserRoleVO> userRoleVOList = new ArrayList<>();
        Long operRoleId = 1L;
        boolean operExist = checkRoleUserExist(operRoleId);
        if (!operExist) {
            userRoleVOList.add(userRoleService.getById(operRoleId));
        }
        Long adminRoleId = 2L;
        boolean adminExist = checkRoleUserExist(adminRoleId);
        if (!adminExist) {
            userRoleVOList.add(userRoleService.getById(adminRoleId));
        }
        Long auditRoleId = 3L;
        boolean auditExist = checkRoleUserExist(auditRoleId);
        if (!auditExist) {
            userRoleVOList.add(userRoleService.getById(auditRoleId));
        }
        return ResultUtil.ok(userRoleVOList);
    }

    /**
     * 检测角色下是否有用户
     *
     * @param roleId 角色id
     * @return 存在用户返回true
     */
    private boolean checkRoleUserExist(Long roleId) {
        LambdaQueryWrapper<UserInfoPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID).eq(UserInfoPO::getRoleId, roleId);
        List<UserInfoPO> userInfoPOList = baseMapper.selectList(lambdaQueryWrapper);
        return !userInfoPOList.isEmpty();
    }

    /**
     * 初始化管理员
     *
     * @param userInfoDTO
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> initAdminUser(InitAdminUserInfoDTO userInfoDTO) {
        if (userInfoDTO.getRoleId() == null || userInfoDTO.getRoleId() < 1L || userInfoDTO.getRoleId() > 3L) {
            throw new BusinessException(SecErrorCodeConstant.USER_ROLE_IS_NOT_EXIST);
        }
        boolean roleUserExist = checkRoleUserExist(userInfoDTO.getRoleId());
        if (roleUserExist) {
            //已存在系统管理员不允许重复初始化
            throw new BusinessException(SecErrorCodeConstant.ALL_INIT_USER_EXIST);
        }
        userInfoDTO.setTenantId(CommonConstant.TOP_TENANT_ID);
        userInfoDTO.setTenantCode(CommonConstant.TOP_TENANT_CODE);
        AddUserInfoDTO addUserInfoDTO = userInfoConvert.initAdminToUserInfo(userInfoDTO);
        addUserCheck(addUserInfoDTO);
        SecRestResponse<Object> response = createUser(addUserInfoDTO, Boolean.FALSE);

        if (response.isSuccess() && userInfoDTO.getRoleId() == 3L) {
            ConfigVO configVO = configService.getConfigByCode(CommonConstant.CCSP_INIT_TIME);
            if (configVO != null) {
                EditConfigEasyDTO editConfigDTO = new EditConfigEasyDTO();
                editConfigDTO.setConfigCode(CommonConstant.CCSP_INIT_TIME);
                editConfigDTO.setConfigValue(DateUtils.localDateTime2String(LocalDateTime.now()));
                configService.edit(editConfigDTO);
            }
        }
        return response;
    }

    /**
     * 用户信息完整性校验
     *
     * @param userId 用户id
     */
    @Override
    public void updateUserHmac(Long userId) {
        String hmac = userHmac(userId);
        LambdaUpdateWrapper<UserSecurityExtendPO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(UserSecurityExtendPO::getHmac, hmac).eq(UserSecurityExtendPO::getUserId, userId);
        userSecurityExtendMapper.update(null, lambdaUpdateWrapper);
    }

    /**
     * 获取用户信息完整性校验值
     *
     * @param userId 用户id
     * @return hmac
     */
    private String userHmac(Long userId) {
        StringBuilder dataStr = new StringBuilder();
        UserInfoPO userInfoPO = baseMapper.selectById(userId);
        if (userInfoPO == null) {
            return null;
        }
        dataStr.append(userInfoPO.getUserCode())
                .append(userInfoPO.getUserName())
                .append(userInfoPO.getUserStatus())
                .append(userInfoPO.getPhoneNum())
                .append(userInfoPO.getEmailAddress())
                .append(userInfoPO.getTenantId())
                .append(userInfoPO.getTenantCode())
                .append(userInfoPO.getOrganizationId())
                .append(userInfoPO.getRoleId())
                .append(userInfoPO.getSalt());

        UserAuthCodePO userAuthCodePO = userAuthCodeService.getByUserId(userId);
        if (userAuthCodePO != null) {
            dataStr.append(userAuthCodePO.getAuthCode());
        }

        UserCertPO userCertPO = userCertService.getUsbKeyCertByUserId(userId);
        if (userCertPO != null) {
            dataStr.append(userCertPO.getCert()).append(userCertPO.getSerial());
        }
        UserSecurityExtendPO userSecurityExtendPO = userSecurityExtendService.getByUserId(userId);
        if (userSecurityExtendPO != null) {
            dataStr.append(userSecurityExtendPO.getLastUpdatePwdTime())
                    .append(userSecurityExtendPO.getAccountStartTime())
                    .append(userSecurityExtendPO.getAccountEndTime())
                    .append(userSecurityExtendPO.getPasswordChangeFlag());
        }
        return ComponentSynthesisEncryptionUtil.sm3Hmac(dataStr.toString());
    }

    /**
     * 用户信息完整性校验
     *
     * @param userId 用户id
     */
    @Override
    public Boolean userHmacVerify(Long userId) {
        UserSecurityExtendPO userSecurityExtendPO = userSecurityExtendService.getByUserId(userId);
        if (userSecurityExtendPO == null || StringUtils.isEmpty(userSecurityExtendPO.getHmac())) {
            return false;
        }
        String hmac = userHmac(userId);
        return userSecurityExtendPO.getHmac().equals(hmac);
    }

    /**
     * 通过用户id获取用户姓名
     *
     * @param userId 用户id
     * @return用户姓名
     */
    @Override
    public String getUserNameById(Long userId) {
        if (userId == null) {
            return null;
        }
        UserInfoPO userInfoPO = baseMapper.selectById(userId);
        return userInfoPO == null ? null : userInfoPO.getUserName();
    }

    /**
     * 获取异地登录提醒信息
     *
     * @return 实例对象
     */
    @Override
    public SecRestResponse<ReLoginNoticeVO> getReLoginNotice() {
        Long userId = LoginUserUtil.getUserId();
        String reLoginMsgRedisKey = String.format("ccsp:login:reLogin:%s", userId);
        ReLoginNoticeVO oldReLoginNoticeVO = redisUtill.strGet(reLoginMsgRedisKey, ReLoginNoticeVO.class);

        ReLoginNoticeVO reLoginNotice = new ReLoginNoticeVO();
        reLoginNotice.setReLogin(true);
        reLoginNotice.setIp(LoginUserUtil.getLoginIp());
        reLoginNotice.setTime(DateUtils.getCurrentLocalDateTime2String());
        log.info("getReLoginNotice reLoginNotice is {}", JSON.toJSONString(reLoginNotice));
        redisUtill.strSet(reLoginMsgRedisKey, reLoginNotice, tokenExpireTime, TimeUnit.MINUTES);
        if (oldReLoginNoticeVO != null && !oldReLoginNoticeVO.getIp().equals(LoginUserUtil.getLoginIp())) {
            return ResultUtil.ok(oldReLoginNoticeVO);
        } else {
            ReLoginNoticeVO reLoginNoticeResult = new ReLoginNoticeVO();
            reLoginNoticeResult.setReLogin(false);
            return ResultUtil.ok(reLoginNoticeResult);
        }

    }

    /**
     * 获取用户map
     *
     * @return map<Long, String> key userID value userName
     */
    @Override
    public Map<Long, String> getUserNameMap() {
        Map<Long, String> userMap = new HashMap<>();
        List<UserInfoPO> userInfoPOList = baseMapper.selectList(new QueryWrapper<>());
        userInfoPOList.forEach(userInfoPO -> {
            userMap.put(userInfoPO.getUserId(), userInfoPO.getUserName());
        });
        return userMap;
    }

    /**
     * 获取用户id->name map
     *
     * @param userIds
     * @return map<Long, String> key userID value userName
     */
    @Override
    public Map<Long, String> getUserNameMapByUserIds(List<Long> userIds) {
        Map<Long, String> userId2NameMap = new HashMap<>();
        List<UserInfoPO> userInfoPOList = baseMapper.selectBatchIds(userIds);
        userInfoPOList.forEach(userInfoPO -> {
            if (CommonConstant.NOT_INVALID.equals(userInfoPO.getInvalidFlag())) {
                userId2NameMap.put(userInfoPO.getUserId(), userInfoPO.getUserName());
            } else {
                userId2NameMap.put(userInfoPO.getUserId(), null);
            }
        });
        return userId2NameMap;
    }

    /**
     * 获取某一租户下的用户列表
     *
     * @param tenantId
     * @return List<UserInfoVO>
     */
    @Override
    public List<UserInfoVO> getUserInfoListByTenantId(Long tenantId) {
        QueryWrapper<UserInfoPO> queryWrapper = Wrappers.query();
        LambdaQueryWrapper<UserInfoPO> lambdaQueryWrapper = queryWrapper.lambda();
        lambdaQueryWrapper.eq(UserInfoPO::getTenantId, tenantId)
		        .eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID)
		        .orderByDesc(UserInfoPO::getCreateTime);
        List<UserInfoPO> userInfoPOList = baseMapper.selectList(lambdaQueryWrapper);
        List<UserInfoVO> userInfoVOList = userInfoConvert.convert(userInfoPOList);
        return userInfoVOList;
    }

    /**
     * 查询用户信息ByUserId
     *
     * @param userId
     * @return UserInfoVO
     */
    @Override
    public UserInfoVO selectByUserId(Long userId) {
        UserInfoPO userInfoPO = baseMapper.selectById(userId);
        UserInfoVO userInfoVO = userInfoConvert.convertVo(userInfoPO);
        return userInfoVO;
    }

    /**
     * 获取用户完整信息map
     *
     * @return map<Long, String> key userID value userName
     */
    @Override
    public Map<Long, UserInfoVO> getUserInfoMap() {
        Map<Long, UserInfoVO> userMap = new HashMap<>();
        List<UserInfoPO> userInfoPOList = baseMapper.selectList(new QueryWrapper<>());
        userInfoPOList.forEach(userInfoPO -> {
            userMap.put(userInfoPO.getUserId(), userInfoConvert.convertVo(userInfoPO));
        });
        return userMap;
    }

    @Override
    public SecRestResponse<UserInfoVO> findById(UserInfoDTO userInfoDTO) {
        UserInfoPO userInfoPO = baseMapper.selectById(userInfoDTO.getUserId());
        if (userInfoPO == null) {
            throw new BusinessException(SecErrorCodeConstant.USER_INFO_IS_NOT_EXIST);
        }
        UserInfoVO userInfoVO = userInfoConvert.convertVo(userInfoPO);
        return ResultUtil.ok(userInfoVO);
    }


    @Override
    public SecRestResponse<UserInfoVO> findByPhone(UserInfoDTO userInfoDTO) {
        LambdaQueryWrapper<UserInfoPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserInfoPO::getPhoneNum, userInfoDTO.getPhone())
                .eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);
        UserInfoPO userInfoPO = baseMapper.selectOne(lambdaQueryWrapper);
        if (userInfoPO == null) {
            return ResultUtil.ok(null);
        }
        UserInfoVO userInfoVO = userInfoConvert.convertVo(userInfoPO);
        return ResultUtil.ok(userInfoVO);
    }

    /**
     * 通过组织部门信息查询
     *
     * @param organizationId
     * @return
     */
    @Override
    public List<UserInfoVO> findByOrganizationId(Long organizationId) {
        LambdaQueryWrapper<UserInfoPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserInfoPO::getOrganizationId, organizationId)
                .eq(UserInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);
        List<UserInfoPO> userInfoPoList = baseMapper.selectList(lambdaQueryWrapper);
        return userInfoConvert.convert(userInfoPoList);
    }

    @Override
    public SecRestResponse<Object> clearPhone(UserInfoIdDTO dto) {
        baseMapper.clearPhone(dto.getUserId());
        updateUserHmac(dto.getUserId());
        return ResultUtil.ok();
    }
}
