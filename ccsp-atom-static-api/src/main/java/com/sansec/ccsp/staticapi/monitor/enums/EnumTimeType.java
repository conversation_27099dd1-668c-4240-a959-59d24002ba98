package com.sansec.ccsp.staticapi.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EnumTimeType {

    REAL_TIME("yyyy-MM-dd HH:mm:ss", "HH:mm",0),
    HOUR("yyyy-MM-dd HH", "HH:00",0),
    DAY("yyyy-MM-dd", "MM-dd",1),
    ;

    private final String timeFormat;

    private final String resultTimeFormat;

    private final Integer flag;
}
