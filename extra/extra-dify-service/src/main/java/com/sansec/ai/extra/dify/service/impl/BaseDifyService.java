package com.sansec.ai.extra.dify.service.impl;

import com.sansec.ai.common.service.SysConfigService;
import com.sansec.ai.extra.dify.constant.DifyConfigEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Dify服务基类
 *
 * <AUTHOR>
 * @since 2025/5/20 13:53
 */
@Slf4j
public abstract class BaseDifyService implements InitializingBean {

	@Autowired
	private SysConfigService sysConfigService;

	@Value("${ai.dify.root_dir:/opt/sansec/dify}")
	private String rootDir;

	/**
	 * Dify系统配置项集合
	 */
	private Map<String, String> difyConfigMap;
	/**
	 * 容器名称后缀
	 */
	private List<String> containerNameSuffixList = new ArrayList<>();


	/**
	 * 在Bean初始化完成后执行，从系统配置中获取业务数据库和插件数据库模板名称
	 *
	 * @throws Exception
	 */
	@Override
	public void afterPropertiesSet() throws Exception {
		//加载所有配置项
		difyConfigMap = sysConfigService.getConfigValueMap(DifyConfigEnum.ALL_CONFIG);
		//加载Dify容器列表
		containerNameSuffixList.addAll(sysConfigService.getConfigValueMap(DifyConfigEnum.DOCKER_CONTAINER_NAME_SUFFIX).values());
	}

	/**
	 * 获取Dify实例的容器列表
	 *
	 * @param portalCode
	 * @return
	 */
	List<String> getContainerNameList(String portalCode) {
		return containerNameSuffixList.stream().map(suffix -> portalCode + "-" + suffix).collect(Collectors.toList());
	}

	/**
	 * @param configEnum
	 * @return
	 */
	String getConfig(DifyConfigEnum configEnum) {
		return difyConfigMap.get(configEnum.getCode());
	}

	/**
	 * @param configEnum
	 * @param defaultValue
	 * @return
	 */
	String getConfigOrDefault(DifyConfigEnum configEnum, String defaultValue) {
		return difyConfigMap.getOrDefault(configEnum.getCode(), defaultValue);
	}

	/**
	 * @param configEnum
	 * @return
	 */
	Map<String, String> getConfigMap(DifyConfigEnum configEnum) {
		return sysConfigService.getConfigValueMap(configEnum);
	}

	/**
	 * 获取Dify部署根路径
	 *
	 * @return
	 */
	String getRootDir() {
		return rootDir;
	}

	/**
	 * 获取实例部署路径
	 *
	 * @param portalCode
	 * @return
	 */
	String getDeployDir(String portalCode) {
		return rootDir + File.separator + portalCode;
	}

	/**
	 * 获取实例知识库路径
	 *
	 * @param portalCode
	 * @return
	 */
	String getAppStorageDir(String portalCode) {
		if(portalCode == null){
			return rootDir + File.separator + "app_storage";
		}
		return rootDir + File.separator + "app_storage" + File.separator + portalCode;
	}

	String getUploadFileDir(String portalCode) {
		return rootDir + File.separator + "app_storage" + File.separator + portalCode + File.separator + "upload_files";
	}

	/**
	 * 获取实例的业务数据库名称
	 */
	String getDatabaseName(String portalCode) {
		return getConfigOrDefault(DifyConfigEnum.TEMPLATE_DATABASE_NAME_DIFY, "dify") + "_" + portalCode;
	}

	/**
	 * 获取实例的插件数据库名称
	 */
	String getPluginDatabaseName(String portalCode) {
		return getConfigOrDefault(DifyConfigEnum.TEMPLATE_DATABASE_NAME_DIFY_PLUGIN, "dify_plugin") + "_" + portalCode;
	}


}
