package com.sansec.ccsp.pt.business.statistic.monitor.entity;

import lombok.Data;

/**
 * 监控服务返回的信息类
 *
 * <AUTHOR>
 * @date 2024/03/07
 */
@Data
public class MonitorDeviceInfo {

    // 设备ip
    private String ip;
    // 设备端口
    private Integer port;
    // 设备分组
    private String group = "default";
    // 设备类型 这个地方存储的是key->0,1
    private String type;

    // 设备标签，多个逗号分隔
    private String tag = "";
    // 是否开启ssl 默认https
    private String sslType;

    // 状态 1正常0异常 这个是算出来的
    // 参考 EnumMultipleDeviceStatus
    private Integer status;

    //获取指标的接口
    private String url;

    // 是否在线
    private Boolean enable;
}
