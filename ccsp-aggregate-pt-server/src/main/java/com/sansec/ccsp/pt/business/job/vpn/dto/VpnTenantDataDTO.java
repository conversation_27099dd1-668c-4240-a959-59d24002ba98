package com.sansec.ccsp.pt.business.job.vpn.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 租户流量信息
 * @Author: zhangweicheng
 * @Date: 2024/9/24
 */

@Data
public class VpnTenantDataDTO {

    //租户标识
    private String tenantName;
    //时间yyyy-MM-dd HH:mm:ss
    private String time;
    //流量
    private Double traffic;

    /**
     * 校验参数合法，任何一个为空都返回false
     * true:合法
     * false:不合法
     */

    public Boolean checkParam() {
        if (StringUtils.isNotBlank(tenantName) && StringUtils.isNotBlank(time) && traffic != null) {
            return true;
        }
        return false;
    }
}
