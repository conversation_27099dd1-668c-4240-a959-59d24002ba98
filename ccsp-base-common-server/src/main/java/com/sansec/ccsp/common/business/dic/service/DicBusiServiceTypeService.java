package com.sansec.ccsp.common.business.dic.service;

import com.sansec.ccsp.common.dic.request.DicBusiServiceTypeDTO;
import com.sansec.ccsp.common.dic.request.DicBusiServiceTypePageDTO;
import com.sansec.ccsp.common.dic.response.DicBusiServiceTypeVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

 /**
 * @Description: 业务服务类型字典表;(DIC_BUSI_SERVICE_TYPE)表服务接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
public interface DicBusiServiceTypeService{
    /**
     * 分页查询
     *
     * @param dicBusiServiceTypePageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<DicBusiServiceTypeVO>> find(DicBusiServiceTypePageDTO dicBusiServiceTypePageDTO);
    /**
     * 新增数据
     *
     * @param dicBusiServiceTypeDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(DicBusiServiceTypeDTO dicBusiServiceTypeDTO);
    /**
     * 更新数据
     *
     * @param dicBusiServiceTypeDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(DicBusiServiceTypeDTO dicBusiServiceTypeDTO);
    /**
     * 通过主键删除数据
     *
     * @param dicBusiServiceTypeDTO
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(DicBusiServiceTypeDTO dicBusiServiceTypeDTO);
}
