package com.sansec.ccsp.pt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * @Description: 服务功能操作状态枚举
 * <AUTHOR>
 * @Date: 2022/4/20 17:38
 * @copyright sansec
 */
@Getter
@AllArgsConstructor
public enum ServiceOperStatusEnum {

    RUN(1,"运行中"),
    RUN_INIT_ING(2,"初始化中"),
    RUN_ERROR(3,"运行异常"),
    ;
    /**
     * code
     */
    private final Integer id;

    /**
     * name
     */
    private final String name;

    public static String getName(Integer id){
        ServiceOperStatusEnum[] serviceTypes = values();
        for (ServiceOperStatusEnum serviceType : serviceTypes) {
            if (serviceType.id.equals(id)) {
                return serviceType.name;
            }
        }
        return null;
    }

}
