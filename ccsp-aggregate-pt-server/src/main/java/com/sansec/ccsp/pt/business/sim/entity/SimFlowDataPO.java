package com.sansec.ccsp.pt.business.sim.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

/**
 * @description : SIM操作日志;
 * @CreateTime: 2023-09-12
 * @Author: wangjun<PERSON>e
 */
@TableName("SIM_FLOW_DATA")
@Data
public class SimFlowDataPO extends BasePO {
    /**
     * id
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 日志类型;1sim盾  2simkey
     */
    private Integer flowType;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 操作类型(验签、证书等)
     */
    private String operType;
    /**
     * 接口名称
     */
    private String funcName;
    /**
     * 1密码平台接口  2SIM盾接口 3业务系统接口
     */
    private String funcType;
    /**
     * 请求参数
     */
    private String requestData;
    /**
     * 返回结果
     */
    private String responseData;
    /**
     * 操作结果 0成功 其他失败
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * hmac
     */
    private String logHmac;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}