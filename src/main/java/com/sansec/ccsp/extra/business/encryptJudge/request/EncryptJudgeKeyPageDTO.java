package com.sansec.ccsp.extra.business.encryptJudge.request;

import com.sansec.ccsp.extra.business.constant.CommonPattern;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
public class EncryptJudgeKeyPageDTO extends SecPageDTO {

    /**
     * 密钥名称
     */
    @Size(max = 60, message = "密钥名称长度最大为60")
    @Pattern(regexp = CommonPattern.COMMON_CODE, message = "密钥名称必须以大小写字母或者数字开头并只能包含大小写字母、数字、特殊字符-_")
    String keyName;

    /**
     * 密钥名称
     */
    Long appId;

    /**
     * 密钥类型   1内部密钥，2外部密钥，3外部发送密钥，4外部接收密钥
     */
    @Min(value = 1, message = "密钥类型取值只能为1、2、3或4")
    @Max(value = 4, message = "密钥类型取值只能为1、2、3或4")
    Integer type;
}
