package com.sansec.ccsp.pt.business.statistic.statistic.request;

import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * @Describe 大屏统计密码服务线型图
 * <AUTHOR>
 * @Date 2023/7/7 17:45
 *
 **/
@Data
public class ScreenMultiLineAndSumDTO {
    @Size(max = 30, message = "租户标识超长")
    private String tenantCode;
    private Integer period;
    private List<String> statisticNameList;
}
