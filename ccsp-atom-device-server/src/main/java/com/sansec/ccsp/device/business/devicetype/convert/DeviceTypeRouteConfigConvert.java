package com.sansec.ccsp.device.business.devicetype.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.device.business.devicetype.entity.DeviceTypeRouteConfigPO;
import com.sansec.ccsp.device.business.devicetype.entity.DeviceTypeWithRouteConfigQueryPO;
import com.sansec.ccsp.device.devicetype.request.DeviceTypeRouteConfigDTO;
import com.sansec.ccsp.device.devicetype.response.DeviceTypeRouteConfigVO;
import com.sansec.ccsp.device.devicetype.response.DeviceTypeWithRouteConfigVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DeviceTypeRouteConfigConvert {

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<DeviceTypeRouteConfigVO> pagePOToSecPageVOPage(IPage<DeviceTypeRouteConfigPO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<DeviceTypeRouteConfigVO> convert(List<DeviceTypeRouteConfigPO> list);

    @Mappings({})
    DeviceTypeRouteConfigVO convertVo(DeviceTypeRouteConfigPO request);

    @Mappings({})
    DeviceTypeRouteConfigPO dtoToPo(DeviceTypeRouteConfigDTO deviceTypeRouteConfigDTO);

    DeviceTypeWithRouteConfigVO queryPoToVo(DeviceTypeWithRouteConfigQueryPO po);
}
