package com.sansec.ccsp.pt.business.servicemgt.gateway.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>aw<PERSON>
 * @Description: API网关路由表;
 * @Date: 2023-2-18
 */
@Data
public class ServiceGatewayRoutePageDTO extends SecPageDTO {

    @NotNull(message = "网关id不能为空")
    private Long gatewayId;

    @Size(max = 50, message = "路由名称长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "路由名称只能包含中文、英文、数字或特殊字符-_")
    private String routeName;
}