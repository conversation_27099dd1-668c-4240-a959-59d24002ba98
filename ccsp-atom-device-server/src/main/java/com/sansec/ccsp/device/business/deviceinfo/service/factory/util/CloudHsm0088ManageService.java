package com.sansec.ccsp.device.business.deviceinfo.service.factory.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.sansec.ccsp.device.business.deviceapi.service.DeviceApiService;
import com.sansec.ccsp.device.business.devicedic.entity.DicDeviceImageTypePO;
import com.sansec.ccsp.device.business.devicedic.entity.DicDeviceVsmResourcePO;
import com.sansec.ccsp.device.business.devicedic.mapper.DicDeviceImageTypeMapper;
import com.sansec.ccsp.device.business.devicedic.mapper.DicDeviceVsmResourceMapper;
import com.sansec.ccsp.device.business.deviceinfo.convert.DeviceInfoConvert;
import com.sansec.ccsp.device.business.deviceinfo.entity.DeviceInfoPO;
import com.sansec.ccsp.device.business.deviceinfo.entity.OperateVsmDTO;
import com.sansec.ccsp.device.business.deviceinfo.handler.AbstractDeviceHandler;
import com.sansec.ccsp.device.business.deviceinfo.handler.VsmHandler;
import com.sansec.ccsp.device.business.deviceinfo.handler.VsmHostHandler;
import com.sansec.ccsp.device.business.deviceinfo.handler.dic.DeviceInfoDic;
import com.sansec.ccsp.device.business.deviceinfo.handler.request.common.MgtRequest;
import com.sansec.ccsp.device.business.deviceinfo.handler.request.vsm.*;
import com.sansec.ccsp.device.business.deviceinfo.handler.response.vsm.VsmInfoResponse;
import com.sansec.ccsp.device.business.deviceinfo.handler.response.vsmhost.VsmHostInfoResponse;
import com.sansec.ccsp.device.business.deviceinfo.mapper.DeviceInfoMapper;
import com.sansec.ccsp.device.business.deviceinfo.service.DeviceInfoService;
import com.sansec.ccsp.device.business.deviceinfo.service.impl.AbstractDeviceInfoServiceImpl;
import com.sansec.ccsp.device.business.devicenet.entity.DeviceNetDetailPO;
import com.sansec.ccsp.device.business.devicenet.entity.DeviceVsmNetConfigPO;
import com.sansec.ccsp.device.business.devicenet.mapper.DeviceNetDetailMapper;
import com.sansec.ccsp.device.business.devicenet.mapper.DeviceVsmNetConfigMapper;
import com.sansec.ccsp.device.business.devicetype.entity.DeviceTypePO;
import com.sansec.ccsp.device.business.devicetype.mapper.DeviceTypeMapper;
import com.sansec.ccsp.device.common.constant.CommonConstant;
import com.sansec.ccsp.device.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.device.deviceinfo.response.vsm.VsmDeviceInfoVO;
import com.sansec.ccsp.task.common.util.ExceptionUtil;
import com.sansec.ccsp.task.task.request.SysTaskLogDTO;
import com.sansec.ccsp.task.task.service.SysTaskLogService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

import static com.sansec.ccsp.device.business.deviceinfo.handler.dic.DeviceInfoDic.*;

@Service
@Slf4j
public class CloudHsm0088ManageService implements AbstractDeviceInfoServiceImpl {




    @Resource
    private DeviceInfoConvert deviceInfoConvert;
    @Resource
    private VsmHostHandler vsmHostHandler;

    @Resource
    private DeviceInfoMapper deviceInfoMapper;
    @Resource
    private DeviceApiService deviceApiService;

    @Resource
    private VsmHandler vsmHandler;

    @Resource
    private DeviceTypeMapper deviceTypeMapper;

    @Resource
    private DicDeviceImageTypeMapper dicDeviceImageTypeMapper;

    @Resource
    private DeviceNetDetailMapper deviceNetDetailMapper;

    @Resource
    private DeviceVsmNetConfigMapper deviceVsmNetConfigMapper;

    @Resource
    private SysTaskLogService sysTaskLogService;

    @Resource
    private DeviceInfoService deviceInfoService;

    @Resource
    DicDeviceVsmResourceMapper dicDeviceVsmResourceMapper;



    public void configPublicKey(DeviceInfoPO info,boolean proxyRouteIs,String gatewayIp,int gatewayPort){
        MgtRequest mgtRequest;
        if(proxyRouteIs){
            mgtRequest = MgtRequest.builder()
                    .ip(gatewayIp)
                    .port(gatewayPort)
                    .protocol(info.getConnectProtocol())
                    .routeParam(info.getMgtIp()+":"+info.getMgtPort())
                    .proxyRoteIs(proxyRouteIs).build();
        }else{
            mgtRequest = MgtRequest.builder()
                    .ip(info.getMgtIp())
                    .port(info.getMgtPort())
                    .protocol(info.getConnectProtocol())
                    .proxyRoteIs(proxyRouteIs).build();
        }
        Map<String, String> publicKeyConfig = getPublicKeyConfig((AbstractDeviceHandler) vsmHostHandler);
        String finger = publicKeyConfig.get("finger");
        String publicKeyVal = publicKeyConfig.get("publicKey");
        String publicKeyAlgo = publicKeyConfig.get("publicKeyAlgo");

        //查找device_api表获取apiPath
        String PublicKeyFingerPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_GETCHSMAUTHPK,info.getDeviceTypeId());

        String configChsmPublicKeyPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_CONFIGCHSMAUTHPK ,info.getDeviceTypeId());


        //获取公钥指纹 平台先调各个服务查询公钥指纹接口，查询到公钥指纹后与平台公钥指纹一致则不再次配置公钥，若不一致则先调各个服务清除公钥接口，再调各个服务配置公钥接口
        String deviceFinger = vsmHostHandler.getPublicKeyFinger(mgtRequest,PublicKeyFingerPath);
        if (StringUtils.isNotBlank(deviceFinger)) {
            if (!deviceFinger.contains(finger)) {
                log.error("公钥已配置,与当前平台不一致,无法管理设备：{} ,设备公钥指纹：{} ，本地指纹：{}",info.getDeviceName(),deviceFinger, finger);
                throw new BusinessException(SecErrorCodeConstant.DEVICE_INFO_VSM_HOST_DIFFERENT_FINGER_ERROR);
            }else {
                log.info("云密码机已配置公钥，且与平台公钥指纹相同");
            }
        } else{
            //无公钥指纹
            vsmHostHandler.configPublicKey(mgtRequest, publicKeyVal, publicKeyAlgo,configChsmPublicKeyPath);
        }
        // 设置公钥和公钥指纹
        info.setPublicKey(publicKeyVal);
        info.setPublicKeyFinger(finger);
    }

    public VsmHostInfoResponse getCloudHsmInfo(DeviceInfoPO deviceInfoPO,boolean proxyRouteIs,String gatewayIp,int gatewayPort){
        MgtRequest mgtRequest = deviceInfoConvert.poToMgtRequest(deviceInfoPO);
        mgtRequest.setProxyRoteIs(proxyRouteIs);
        if(proxyRouteIs){
            mgtRequest.setIp(gatewayIp);
            mgtRequest.setPort(gatewayPort);
            mgtRequest.setRouteParam(deviceInfoPO.getMgtIp()+":"+deviceInfoPO.getMgtPort());
        }
        //查找device_api表获取apiPath
        String apiPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_GETCHSMDETAIL, deviceInfoPO.getDeviceTypeId());

        return vsmHostHandler.getInfo(mgtRequest,apiPath);
    }


    public int getCHsmStatus(DeviceInfoPO deviceInfoPO,boolean proxyRouteIs,String gatewayIp,int gatewayPort){
        int runStatus= -1; //运行状态

        try {
            MgtRequest mgtRequest;

            if(proxyRouteIs){
                if(gatewayIp == null){
                    log.error("this region not have available gateways regionId={},deviceIp={}",deviceInfoPO.getRegionId(),deviceInfoPO.getMgtIp());
                    runStatus = 2;
                    return runStatus;
                }
                mgtRequest = MgtRequest.builder()
                        .ip(gatewayIp)
                        .port(gatewayPort)
                        .protocol(deviceInfoPO.getConnectProtocol())
                        .routeParam(deviceInfoPO.getMgtIp()+":"+deviceInfoPO.getMgtPort())
                        .proxyRoteIs(proxyRouteIs).build();
            }else{
                mgtRequest = MgtRequest.builder()
                        .ip(deviceInfoPO.getMgtIp())
                        .port(deviceInfoPO.getMgtPort())
                        .protocol(deviceInfoPO.getConnectProtocol())
                        .proxyRoteIs(proxyRouteIs).build();
            }
            //查找device_api表获取apiPath
            String apiPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_GETCHSMSTATUS, deviceInfoPO.getDeviceTypeId());

            String status = vsmHostHandler.getStatus(mgtRequest,apiPath);
            runStatus = DeviceInfoDic.VSM_STATUS_NORMAL.equals(status) ? 1 : 0;

        }catch (Exception e){
            log.error("定时任务宿主机获取设备状态出错===>"+e.getMessage());
            runStatus = 2;
        }finally {
            AbstractDeviceInfoServiceImpl.updateRunStatusById(deviceInfoMapper,deviceInfoPO.getDeviceId(),runStatus);
        }
        return runStatus;
    }

    public int getVsmStatus(DeviceInfoPO deviceInfoPO,boolean proxyRouteIs,String gatewayIp,int gatewayPort){
        int runStatus= -1;
        try {
            Long vsmHostId = deviceInfoPO.getHccsDeviceId();
            DeviceInfoPO vsmHost = deviceInfoMapper.selectById(vsmHostId);
            MgtRequest mgtRequest;
            if(proxyRouteIs){
                if(gatewayIp == null){
                    log.error("this region not have available gateways regionId={},deviceIp={}",deviceInfoPO.getRegionId(),deviceInfoPO.getMgtIp());
                    runStatus = 2;
                    return runStatus;
                }
                mgtRequest = MgtRequest.builder()
                        .ip(gatewayIp)
                        .port(gatewayPort)
                        .protocol(deviceInfoPO.getConnectProtocol())
                        .routeParam(vsmHost.getMgtIp()+":"+vsmHost.getMgtPort())
                        .proxyRoteIs(proxyRouteIs).build();
            }else{
                mgtRequest = MgtRequest.builder()
                        .ip(vsmHost.getMgtIp())
                        .port(vsmHost.getMgtPort())
                        .protocol(vsmHost.getConnectProtocol())
                        .proxyRoteIs(proxyRouteIs)
                        .build();
            }
            //查找device_api表获取apiPath
            String apiPath = deviceApiService.getDeviceApiPathByApiName("getVsmStatus", vsmHost.getDeviceTypeId());
            // 构建虚机操作情况
            VsmStatusRequest vsmStatusRequest = VsmStatusRequest.builder()
                    .requestId(IdGenerator.ins().generatorString()).vsmId(deviceInfoPO.getDeviceSerialnum()).build();
            String status = vsmHandler.getStatus(mgtRequest, vsmStatusRequest,apiPath);
            runStatus = DeviceInfoDic.VSM_STATUS_NORMAL.equals(status) ? 1 : 0;
        }catch (Exception e){
            log.error("通过云密码机获取虚拟机[{}]状态出错:{}",deviceInfoPO.getDeviceName(),e.getMessage());
            runStatus = 2;
        }finally {
            AbstractDeviceInfoServiceImpl.updateRunStatusById(deviceInfoMapper,deviceInfoPO.getDeviceId(),runStatus);
        }
        return runStatus;
    }
    public boolean createVsm(DeviceInfoPO vsm, Long taskId,boolean proxyRouteIs,String gatewayIp,int gatewayPort){
        try {
            DeviceTypePO deviceType = deviceTypeMapper.selectById(vsm.getDeviceTypeId());
            // 获取镜像类型
            DicDeviceImageTypePO dicDeviceImageType = dicDeviceImageTypeMapper.selectById(deviceType.getHccsImageId());
            String imageValue = dicDeviceImageType.getImageValue();
            //获取虚拟机资源配置
            DicDeviceVsmResourcePO dicDeviceVsmResourcePO = dicDeviceVsmResourceMapper.selectById(vsm.getVsmResource());
            String resourceValue = dicDeviceVsmResourcePO.getResourceValue();
            // 构建宿主机管理请求
            Long vsmHostId = vsm.getHccsDeviceId();
            DeviceInfoPO vsmHost = deviceInfoMapper.selectById(vsmHostId);
            MgtRequest mgtRequest = deviceInfoConvert.poToMgtRequest(vsmHost);
            mgtRequest.setProxyRoteIs(proxyRouteIs);
            if(proxyRouteIs){
                mgtRequest.setIp(gatewayIp);
                mgtRequest.setPort(gatewayPort);
                mgtRequest.setRouteParam(vsmHost.getMgtIp()+":"+vsmHost.getMgtPort());
            }
            //查找device_api表获取apiPath
            String createVsmApiPath = deviceApiService.getDeviceApiPathByApiName(DeviceInfoDic.OPER_0088_CREATECHSMVSM, vsmHost.getDeviceTypeId());
            String statusApiPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_GETVSMSTATUS, vsmHost.getDeviceTypeId());
            String networkApiPath =deviceApiService.getDeviceApiPathByApiName(OPER_0088_CONFIGVSMNETWORK, vsmHost.getDeviceTypeId());

            /*  虚拟机创建请求
                1030项目修改：
                1. V3不启动hostService。
                2. 使用vsm_resource字段保存虚机资源分配。
                3. 使用resource参数替换flavor配置虚机资源分配
             */
            VsmCreateRequest vsmCreateRequest = VsmCreateRequest.builder().requestId(String.valueOf(vsm.getDeviceId()))
                    .oprType(DeviceInfoDic.CREATE).vsmType(imageValue).resource(resourceValue)
                    .callbackUrl(getCallBackUrl(DeviceInfoDic.CREATE)).build();
            if(StringUtils.isNotBlank(dicDeviceImageType.getImageVersion())){
                vsmCreateRequest.setVsmVersion(dicDeviceImageType.getImageVersion());
            }
            try {
                String vsmId = vsmHandler.create(mgtRequest, vsmCreateRequest, createVsmApiPath);
                log.info("create vsm success vsmId :{}",vsmId);
                vsm.setDeviceSerialnum(vsmId);
                // 更新虚机填入虚机序列号
                deviceInfoMapper.update(null, new LambdaUpdateWrapper<DeviceInfoPO>()
                        .eq(DeviceInfoPO::getDeviceId, vsm.getDeviceId())
                        .set(DeviceInfoPO::getDeviceSerialnum, vsmId));
                if (loopGetVsmStatus(mgtRequest, vsm, DeviceInfoDic.CREATE, statusApiPath)) {
                    //等待三秒再配置网络。云密码机创建虚机为异步操作，获取状态成功不等于虚机创建完成
                    try {
                        Thread.sleep(3_000);
                    }catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException(e);
                    }
                    createSuccessDeal(vsm, mgtRequest, networkApiPath);
                } else {
                    createFailDeal(vsm);
                }
            } catch (Exception e) {
                // 处理异常失败
                createFailDeal(vsm);
                //抛出异常，由上一层处理
                throw e;
            }
        } catch (Exception e) {
            log.error("createVsm errorContext", e);
            String taskMsg = "虚拟机创建失败IP:" + vsm.getMgtIp() + " vsmId:" + vsm.getDeviceId();
            String exceptionInfo = ExceptionUtil.getExceptionMessage(e);

            SysTaskLogDTO sysTaskLogDTO = new SysTaskLogDTO();
            sysTaskLogDTO.setTaskLogId(IdGenerator.ins().generator());
            sysTaskLogDTO.setTaskId(taskId);
            sysTaskLogDTO.setTaskMessage(taskMsg);
            sysTaskLogDTO.setStatus("0");
            sysTaskLogDTO.setExceptionInfo(StringUtils.substring(exceptionInfo, 0, 2000));
            sysTaskLogService.add(sysTaskLogDTO);

            return false;
        }
        return true;
    }


    /**
     * 虚拟机创建成功后初始化虚拟机
     *
     * @param vsmInfoPo
     */
    public void initVsm(DeviceInfoPO vsmInfoPo,boolean proxyRouteIs,String gatewayIp,int gatewayPort){

        Long cHsmId = vsmInfoPo.getHccsDeviceId();
        DeviceInfoPO cHsmInfo = deviceInfoMapper.selectById(cHsmId);
        MgtRequest mgtRequest = deviceInfoConvert.poToMgtRequest(cHsmInfo);
        mgtRequest.setProxyRoteIs(proxyRouteIs);
        if(proxyRouteIs){
            mgtRequest.setIp(gatewayIp);
            mgtRequest.setPort(gatewayPort);
            mgtRequest.setRouteParam(cHsmInfo.getMgtIp()+":"+cHsmInfo.getMgtPort());
        }


        Map<String, String> publicKeyConfig = getPublicKeyConfig((AbstractDeviceHandler) vsmHostHandler);
        String finger = publicKeyConfig.get("finger");
        String publicKeyVal = publicKeyConfig.get("publicKey");
        String publicKeyAlgo = publicKeyConfig.get("publicKeyAlgo");

        String hexFinger = Hex.encodeHexString(Base64.decodeBase64(finger)).toLowerCase();
        String configKey = "setpubkey";
        String configValue = "{pubkey:"+publicKeyVal+"; value:"+hexFinger+"}";
        String[] config = {configKey,configValue};

        String requestId = String.valueOf(vsmInfoPo.getDeviceId());
        ConfigVsmRequest configVsmRequest = new ConfigVsmRequest(requestId,vsmInfoPo.getDeviceSerialnum(),config);

        String apiPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_CONFIG_VSM,cHsmInfo.getDeviceTypeId());

        vsmHostHandler.configVsm(mgtRequest,apiPath,configVsmRequest);

        // 更新虚机公钥和指纹信息
        deviceInfoMapper.update(null, new LambdaUpdateWrapper<DeviceInfoPO>()
                .eq(DeviceInfoPO::getDeviceId, vsmInfoPo.getDeviceId())
                .set(DeviceInfoPO::getPublicKey, publicKeyVal)
                .set(DeviceInfoPO::getPublicKeyFinger,finger));
        // 更新完整性校验
        deviceInfoService.updateHmac(vsmInfoPo.getDeviceId());
    }

    private String getCallBackUrl(String operator) {
        String ptUrl = CommonConstant.PT_URL_0;
        switch (operator) {
            case DeviceInfoDic.CREATE:
                return ptUrl + "/ccsp/api/chsm/vsm/createCallBack";
            case DeviceInfoDic.STOP:
                return ptUrl + "/ccsp/api/chsm/vsm/stopCallBack";
            case DeviceInfoDic.RESTART:
                return ptUrl + "/ccsp/api/chsm/vsm/restartCallBack";
            case DeviceInfoDic.DELETE:
                return ptUrl + "/ccsp/api/chsm/vsm/deleteCallBack";
            default:
                return ptUrl;
        }
    }



    /**
     * 2分钟循环判断,每2秒查询一次
     *
     *
     * @param mgtRequest
     * @param vsm
     * @param oprType
     * @return
     */
    private boolean loopGetVsmStatus(MgtRequest mgtRequest, DeviceInfoPO vsm, String oprType,String apiPath) {
        String status;
        for (int i = 60; i > 0; i--) {
            try {
                Thread.sleep(3000);

                VsmStatusRequest vsmStatusRequest = VsmStatusRequest.builder().requestId(IdGenerator.ins().generatorString())
                        .vsmId(vsm.getDeviceSerialnum()).build();
                status = vsmHandler.getStatus(mgtRequest, vsmStatusRequest,apiPath);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }catch (Exception e){
                log.info("获取虚机[{}]状态接口异常：{}",vsm.getDeviceName(),e.getMessage());
                //获取失败，暂不处理，等待下一循环
                status = "-1";
            }
            switch (oprType) {
                case DeviceInfoDic.CREATE:
                case DeviceInfoDic.START:
                case DeviceInfoDic.RESTART:
                    if (DeviceInfoDic.VSM_STATUS_NORMAL.equals(status)) {
                        return true;
                    } else if (VSM_STATUS_ERROR.equals(status)) {
                        log.error("通过云密码机获取虚机状态为error，虚机已启动，但是虚机服务异常");
                        return true;
                    }
                    break;
                case DeviceInfoDic.STOP:
                    if (DeviceInfoDic.VSM_STATUS_SHUTDOWN.equals(status)) {
                        return true;
                    }
                    break;
                default:
                    throw new BusinessException(SecErrorCodeConstant.DEVICE_INFO_VSM_LOOP_GET_STATUE_NOT_SUPPORT);
            }
        }
        log.info("循环调用查询虚机[{}]状态接口，状态异常", vsm.getDeviceName());
        return false;
    }

    private void createSuccessDeal(DeviceInfoPO vsm, MgtRequest mgtRequest,String apiPath) {
        DeviceNetDetailPO deviceNetDetail = deviceNetDetailMapper.selectOne(new LambdaQueryWrapper<DeviceNetDetailPO>()
                .eq(DeviceNetDetailPO::getMgtIp, vsm.getMgtIp())
                .eq(DeviceNetDetailPO::getDeviceId,vsm.getHccsDeviceId()));
        DeviceVsmNetConfigPO vsmNetConfig = deviceVsmNetConfigMapper.selectOne(new LambdaQueryWrapper<DeviceVsmNetConfigPO>()
                .eq(DeviceVsmNetConfigPO::getGateway, deviceNetDetail.getGateway())
                .eq(DeviceVsmNetConfigPO::getHostId,vsm.getHccsDeviceId()));

        // 配置虚拟机网络
        VsmNetworkRequest vsmNetworkRequest = VsmNetworkRequest.builder().requestId(IdGenerator.ins().generatorString())
                .vsmId(vsm.getDeviceSerialnum()).ip(vsm.getMgtIp()).gateway(vsmNetConfig.getGateway())
                .mask(vsmNetConfig.getMask()).build();
        vsmHandler.network(mgtRequest, vsmNetworkRequest,apiPath);

        // 将IP标记为使用状态
        deviceNetDetailMapper.update(null, new LambdaUpdateWrapper<DeviceNetDetailPO>()
                .eq(DeviceNetDetailPO::getIpId, deviceNetDetail.getIpId())
                .set(DeviceNetDetailPO::getStatus, DeviceInfoDic.NET_USED));


    }

    private void createFailDeal(DeviceInfoPO vsm) {
        log.error("createFailDeal虚拟机创建异常,vsmId = {}", vsm.getDeviceId());
        // 更新操作状态为创建失败、设备组为空、IP为空
        deviceInfoMapper.update(null, new LambdaUpdateWrapper<DeviceInfoPO>()
                .eq(DeviceInfoPO::getDeviceId, vsm.getDeviceId())
                .set(DeviceInfoPO::getOperStatus, DeviceInfoDic.CREATE_FAIL_STATE)
                .set(DeviceInfoPO::getDeviceGroupId, null));

        // 更新IP锁定状态
        deviceNetDetailMapper.update(null, new LambdaUpdateWrapper<DeviceNetDetailPO>()
                .eq(DeviceNetDetailPO::getMgtIp, vsm.getMgtIp())
                .set(DeviceNetDetailPO::getStatus, DeviceInfoDic.NET_LOCK));
    }
    public boolean operateVsm(OperateVsmDTO operator,boolean proxyRouteIs,String gatewayIp,int gatewayPort){
        DeviceInfoPO vsm = operator.getVsmInfo();
        Long vsmHostId = vsm.getHccsDeviceId();
        DeviceInfoPO vsmHost = deviceInfoMapper.selectById(vsmHostId);
        try {
            // 存在虚机序列号的调用宿主机删除虚机方法。不存在虚机序列号的直接删除虚拟机数据记录
            if (StringUtils.isBlank(vsm.getDeviceSerialnum())) {
                delSuccess(vsm, vsmHost);
                return true;
            }
            // 构建宿主机管理请求
            MgtRequest mgtRequest = deviceInfoConvert.poToMgtRequest(vsmHost);
            mgtRequest.setProxyRoteIs(proxyRouteIs);
            if(proxyRouteIs){
                mgtRequest.setIp(gatewayIp);
                mgtRequest.setPort(gatewayPort);
                mgtRequest.setRouteParam(vsmHost.getMgtIp()+":"+vsmHost.getMgtPort());
            }
            //查找device_api表获取apiPath
            String vsmOperateApiPath = deviceApiService.getDeviceApiPathByApiName("vsmOperate", vsmHost.getDeviceTypeId());
            String getVsmStatusApiPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_GETVSMSTATUS, vsmHost.getDeviceTypeId());

            VsmOprRequest vsmOprRequest = VsmOprRequest.builder().requestId(IdGenerator.ins().generatorString())
                    .oprType(operator.getOprType()).callbackUrl(getCallBackUrl(operator.getOprType())).vsmId(vsm.getDeviceSerialnum()).build();
            try {

                if(DeviceInfoDic.DESTROY.equals(operator.getOprType()) && !stopVsmBeforeDelete(mgtRequest,vsm, vsmOperateApiPath,getVsmStatusApiPath)){
                    return false;
                }
                vsmHandler.opr(mgtRequest, vsmOprRequest, vsmOperateApiPath);

                // 根据虚机状态进行操作
                if (DeviceInfoDic.DESTROY.equals(operator.getOprType())) {
                    if (loopGetVsmDestroyStatus(mgtRequest, vsm, getVsmStatusApiPath)) {
                        delSuccess(vsm, vsmHost);
                    } else {
                        delFail(vsm);
                    }
                } else {
                    loopGetVsmStatus(mgtRequest, vsm, operator.getOprType(), getVsmStatusApiPath);
                    // 如果操作之后无论状态是否正确，清除之前的操作状态
                    deviceInfoMapper.update(null, new LambdaUpdateWrapper<DeviceInfoPO>()
                            .eq(DeviceInfoPO::getDeviceId, vsm.getDeviceId()).set(DeviceInfoPO::getOperStatus, DeviceInfoDic.RUNNING_STATE));
                    getVsmStatus(vsm, vsmHost, proxyRouteIs,gatewayIp,gatewayPort);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw e;
            }
        } catch (Exception e) {
            SysTaskLogDTO sysTaskLogDTO = new SysTaskLogDTO();
            sysTaskLogDTO.setTaskLogId(IdGenerator.ins().generator());
            sysTaskLogDTO.setTaskId(operator.getTaskId());
            String taskMsg = "虚拟机执行:" + operator.getOprType() + ":操作失败失败IP:" + vsm.getMgtIp() + "vsmid :" + vsm.getDeviceId();
            sysTaskLogDTO.setTaskMessage(taskMsg);
            sysTaskLogDTO.setStatus("0");
            String exceptionInfo = ExceptionUtil.getExceptionMessage(e);
            sysTaskLogDTO.setExceptionInfo(StringUtils.substring(exceptionInfo, 0, 2000));
            sysTaskLogService.add(sysTaskLogDTO);

            log.error("context:", e);
            return false;
        }
        return true;
    }

    public VsmDeviceInfoVO getVsmInfo(DeviceInfoPO cHsmInfo, String vsmSerialNum,boolean proxyRouteIs,String gatewayIp,int gatewayPort){
        String apiPath= deviceApiService.getDeviceApiPathByApiName(OPER_0088_GETCHSMVSMDETAIL, cHsmInfo.getDeviceTypeId());
        VsmOprRequest vsmOprRequest = VsmOprRequest.builder()
                .requestId(IdGenerator.ins().generatorString())
                .oprType(DeviceInfoDic.GETINFO).vsmId(vsmSerialNum).callbackUrl(null).build();

        MgtRequest mgtRequest = deviceInfoConvert.poToMgtRequest(cHsmInfo);
        mgtRequest.setProxyRoteIs(proxyRouteIs);
        if(proxyRouteIs){
            mgtRequest.setIp(gatewayIp);
            mgtRequest.setPort(gatewayPort);
            mgtRequest.setRouteParam(cHsmInfo.getMgtIp()+":"+cHsmInfo.getMgtPort());
        }
        // 获取虚机详情
        VsmInfoResponse vsmInfoResponse = vsmHandler.getInfo(mgtRequest, vsmOprRequest,apiPath);
        return deviceInfoConvert.vsmInfoResponseToVo(vsmInfoResponse);
    }


    //虚拟机获取设备状态
    public void getVsmStatus(DeviceInfoPO deviceInfoPO,DeviceInfoPO vsmHost,boolean proxyRouteIs,String gatewayIp,int gatewayPort){
        int runStatus= -1;
        try {

            MgtRequest mgtRequest ;
            if(proxyRouteIs){
                mgtRequest = MgtRequest.builder()
                        .ip(gatewayIp)
                        .port(gatewayPort)
                        .protocol(vsmHost.getConnectProtocol())
                        .proxyRoteIs(proxyRouteIs)
                        .routeParam(vsmHost.getMgtIp()+":"+vsmHost.getMgtPort())
                        .build();
            }else {
                mgtRequest = MgtRequest.builder()
                        .ip(vsmHost.getMgtIp())
                        .port(vsmHost.getMgtPort())
                        .protocol(vsmHost.getConnectProtocol())
                        .proxyRoteIs(proxyRouteIs)
                        .build();
            }
            //查找device_api表获取apiPath
            String apiPath = deviceApiService.getDeviceApiPathByApiName(OPER_0088_GETVSMSTATUS, vsmHost.getDeviceTypeId());

            // 构建虚机操作情况
            VsmStatusRequest vsmStatusRequest = VsmStatusRequest.builder()
                    .requestId(IdGenerator.ins().generatorString()).vsmId(deviceInfoPO.getDeviceSerialnum()).build();
            String status = vsmHandler.getStatus(mgtRequest, vsmStatusRequest,apiPath);
            runStatus = DeviceInfoDic.VSM_STATUS_NORMAL.equals(status) ? 1 : 0;
        }catch (Exception e){
            log.error("虚拟机操作成功后获取设备状态出错===>"+e.getMessage());
            runStatus = 2;
        }finally {
            UpdateWrapper<DeviceInfoPO> updateWrapper= new UpdateWrapper<>();
            updateWrapper.eq("DEVICE_ID",deviceInfoPO.getDeviceId()).set("RUN_STATUS",runStatus);
            deviceInfoMapper.update(null,updateWrapper);
        }
    }

    /**
     * 循环获取删除状态，请求异常则删除成功
     *
     * @param mgtRequest
     * @param vsm
     * @return
     */
    private boolean loopGetVsmDestroyStatus(MgtRequest mgtRequest, DeviceInfoPO vsm,String apiPath) {
        for (int i = 60; i > 0; i--) {
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
            VsmStatusRequest vsmStatusRequest = VsmStatusRequest.builder()
                    .requestId(IdGenerator.ins().generatorString()).vsmId(vsm.getDeviceSerialnum()).build();
            try {
                String status = vsmHandler.getStatus(mgtRequest, vsmStatusRequest,apiPath);
                if(!VSM_STATUS_NORMAL.equalsIgnoreCase(status)){
                    return true;
                }
            } catch (Exception e) {
                return true;
            }
        }
        return false;
    }

    private boolean stopVsmBeforeDelete(MgtRequest mgtRequest,DeviceInfoPO vsm, String vsmOperateApiPath,String getVsmStatusApiPath){
        VsmOprRequest stopVsmRequest = VsmOprRequest.builder().requestId(IdGenerator.ins().generatorString())
                .oprType(STOP).callbackUrl(getCallBackUrl(STOP)).vsmId(vsm.getDeviceSerialnum()).build();
        try {
            vsmHandler.opr(mgtRequest,stopVsmRequest,vsmOperateApiPath);
            return loopGetVsmStatus(mgtRequest, vsm, STOP, getVsmStatusApiPath);
        }catch (Exception e){
            log.error("删除虚机前，停止虚机操作异常，deviceName:{},error info:{}",vsm.getDeviceName(),e.getMessage());
            return false;
        }
    }

    private void delSuccess(DeviceInfoPO vsm, DeviceInfoPO vsmHost) {
        // 删除虚拟机记录
        deviceInfoMapper.deleteById(vsm.getDeviceId());

        // 虚机删除成功时归还占用的ip地址
        if (StringUtils.isNotBlank(vsm.getMgtIp())) {
            deviceNetDetailMapper.update(null, new LambdaUpdateWrapper<DeviceNetDetailPO>()
                    .eq(DeviceNetDetailPO::getMgtIp, vsm.getMgtIp())
                    .set(DeviceNetDetailPO::getStatus, DeviceInfoDic.NET_NOT_USE));
        }

        // 恢复宿主机虚机可用数和已用数
        deviceInfoMapper.update(null, new LambdaUpdateWrapper<DeviceInfoPO>()
                .eq(DeviceInfoPO::getDeviceId, vsmHost.getDeviceId())
                .set(DeviceInfoPO::getHccsVsmUsable, vsmHost.getHccsVsmUsable() + 1)
                .set(DeviceInfoPO::getHccsVsmUsed, vsmHost.getHccsVsmUsed() - 1));
    }


    private void delFail(DeviceInfoPO vsm) {
        // 删除异常情况
        deviceInfoMapper.update(null, new LambdaUpdateWrapper<DeviceInfoPO>()
                .eq(DeviceInfoPO::getDeviceId, vsm.getDeviceId())
                .set(DeviceInfoPO::getOperStatus, DeviceInfoDic.DELETE_FAIL_STATE));
    }
}
