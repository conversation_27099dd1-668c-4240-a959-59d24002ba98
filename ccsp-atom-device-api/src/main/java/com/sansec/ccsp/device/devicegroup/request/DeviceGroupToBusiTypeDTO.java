package com.sansec.ccsp.device.devicegroup.request;

import lombok.Data;

 /**
 * <AUTHOR> x<PERSON><PERSON><PERSON>aw<PERSON>
 * @Date: 2023-2-18
 */
@Data
public class DeviceGroupToBusiTypeDTO{
    /**
     * 主键
     */
    private Long id;
    /**
     * 设备组ID
     */
    private Long deviceGroupId;
    /**
     * 业务类型ID
     */
    private Long busiTypeId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间1
     */
    private String updateTime;
}