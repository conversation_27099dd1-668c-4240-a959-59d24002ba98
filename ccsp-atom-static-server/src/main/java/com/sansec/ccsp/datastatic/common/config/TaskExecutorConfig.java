package com.sansec.ccsp.datastatic.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.AsyncConfigurerSupport;


@Slf4j
//@Configuration
/** 开启异步支持 */
//@EnableAsync
public class TaskExecutorConfig extends AsyncConfigurerSupport {

   /* @Value("${dataCollect.threadSize}")
    int dataCollectThreadSize;

    *//**
     * 可以建多个线程池,然后使用@Async(value = "asyOneExecutor")可以指定不同线程池
     * 根据不同线程池的主要使用方式去选择对应的线程池
     * 不指定则使用getAsyncExecutor方法返回的线程池
     * @return
     *//*
    @Bean(name = "schedulePool")
    public ThreadPoolTaskScheduler asyOneExecutor() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(4);
        scheduler.setThreadNamePrefix("schedule-");
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        return scheduler;
    }

    @Bean(name = "dataCollectPool")
    public Executor dataCollectPool() {
        log.info("数据采集线程数：{}",dataCollectThreadSize);
        ThreadPoolTaskExecutor threadPool = new ThreadPoolTaskExecutor();
        threadPool.setCorePoolSize(dataCollectThreadSize); // 表示线程池核心线程，正常情况下开启的线程数量
        threadPool.setMaxPoolSize(dataCollectThreadSize);   //如果queueCapacity存满了，还有任务就会启动更多的线程，直到线程数达到maxPoolSize，还有就会拒绝
        threadPool.setKeepAliveSeconds(120);
        threadPool.setQueueCapacity(999);  // 当核心线程都在跑任务，还有多余的任务会存到此处
        threadPool.setThreadNamePrefix("dataCollect");
        threadPool.setRejectedExecutionHandler((r, executor) -> {
            log.warn("数据采集任务队列已满。。。");
        });
        return threadPool;
    }

    @Override
    public Executor getAsyncExecutor() {
        return this.asyOneExecutor();
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        //异常捕捉
        return new TaskExecutorConfig.SpringAsyncExceptionHandler();
    }

    class SpringAsyncExceptionHandler implements AsyncUncaughtExceptionHandler {
        @Override
        public void handleUncaughtException(Throwable throwable, Method method, Object... obj) {
            // 错误日志
            log.error("Exception occurs in async method", throwable);
        }
    }*/

}