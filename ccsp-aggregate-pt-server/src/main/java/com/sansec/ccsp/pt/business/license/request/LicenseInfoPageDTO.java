package com.sansec.ccsp.pt.business.license.request;

import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR> wwl
 * @description : 许可证表;
 * @date : 2023-5-8
 */
@Data
public class LicenseInfoPageDTO extends SecPageDTO {
    /**
     * 许可证类型;1-业务服务，2-平台服务
     */
    @Min(value = 1, message = "许可证类型参数错误，业务服务: 1,平台服务: 2")
    @Max(value = 2, message = "许可证型参数错误，业务服务: 1,平台服务: 2")
    private Integer serverType;
}