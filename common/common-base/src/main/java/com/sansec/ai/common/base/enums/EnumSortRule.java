package com.sansec.ai.common.base.enums;

import lombok.Getter;

/**
 * 排序枚举
 *
 * <AUTHOR>
 * @since 2025/3/5 15:56
 */
@Getter
public enum EnumSortRule {

    ASC,    // 升序
    DESC;   // 降序

    public static EnumSortRule getEnum(String rule) {
        for (EnumSortRule sortRule : EnumSortRule.values()) {
            if (sortRule.name().equalsIgnoreCase(rule)) {
                return sortRule;
            }
        }
        return DESC;
    }
}
