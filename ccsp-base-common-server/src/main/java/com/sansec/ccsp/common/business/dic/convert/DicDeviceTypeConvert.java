package com.sansec.ccsp.common.business.dic.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.common.business.dic.entity.DicDeviceTypePO;
import com.sansec.ccsp.common.dic.request.DicDeviceTypeDTO;
import com.sansec.ccsp.common.dic.response.DicDeviceTypeVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

 /**
 * @Description: 设备类型字典表;(DIC_DEVICE_TYPE)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Mapper(componentModel = "spring")
public interface DicDeviceTypeConvert{
    /**
     * dtoToPo
     * @param dicDeviceTypeDTO
     * @return
     */
    @Mappings({})
    DicDeviceTypePO dtoToPo(DicDeviceTypeDTO dicDeviceTypeDTO);

    /**
     * poToDto
     * @param dicDeviceTypePO
     * @return
     */
    DicDeviceTypeDTO poToDto(DicDeviceTypePO dicDeviceTypePO);

    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<DicDeviceTypeDTO> poToDto(List<DicDeviceTypePO> list);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<DicDeviceTypeVO> pagePOToSecPageVOPage(IPage<DicDeviceTypePO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<DicDeviceTypeVO> convert(List<DicDeviceTypePO> list);

    @Mappings({})
    DicDeviceTypeVO convertVo(DicDeviceTypePO request);
}
