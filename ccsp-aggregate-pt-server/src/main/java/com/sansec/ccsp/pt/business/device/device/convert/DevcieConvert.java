package com.sansec.ccsp.pt.business.device.device.convert;

import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoInGroupVO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request.DeviceInvokeDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @Describe
 * <AUTHOR>
 * @create 2023/3/15 21:12
 */
@Mapper(componentModel = "spring")
public interface DevcieConvert {

    @Mappings({
            @Mapping(source = "mgtIp", target = "ip"),
            @Mapping(source = "mgtPort", target = "port")
    })
    List<DeviceInvokeDTO> deviceInfoToInvoke(List<DeviceInfoInGroupVO> list);
}
