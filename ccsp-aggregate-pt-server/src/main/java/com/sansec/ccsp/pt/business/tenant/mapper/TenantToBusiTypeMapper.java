package com.sansec.ccsp.pt.business.tenant.mapper;

import com.sansec.ccsp.pt.business.tenant.entity.TenantToBusiTypePO;
import com.sansec.db.mapper.SansecBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 租户和业务类型关联表;(TENANT_TO_BUSI_TYPE)表数据库访问层
 * @Date: 2023-2-18
 */
@Mapper
public interface TenantToBusiTypeMapper extends SansecBaseMapper<TenantToBusiTypePO> {

    Integer batchInsertToRusiType(List<TenantToBusiTypePO> list);
}