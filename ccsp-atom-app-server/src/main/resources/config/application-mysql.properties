spring.datasource.driver=mysql
spring.datasource.url=*******************************************************************************************************************************************************************************
spring.datasource.userName=N03NvcDbgUdrkASmrwW3sQ==
spring.datasource.passWord=IdCF5A1OLh7ntrpVIEmd6A==
pagehelper.helper-dialect=mysql

spring.datasource.initialSize = 30
spring.datasource.maxActive = 500
spring.datasource.minIdle = 30
spring.datasource.maxWait = 20000
spring.datasource.timeBetweenConnectErrorMillis = 90000
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT 1
spring.datasource.testOnBorrow = false
spring.datasource.testOnReturn = false
spring.datasource.testWhileIdle = true
