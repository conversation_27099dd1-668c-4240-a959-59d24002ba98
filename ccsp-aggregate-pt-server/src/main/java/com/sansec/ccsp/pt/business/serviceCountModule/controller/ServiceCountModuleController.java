package com.sansec.ccsp.pt.business.serviceCountModule.controller;

import com.sansec.ccsp.pt.business.serviceCountModule.request.ServiceCountModuleEditDTO;
import com.sansec.ccsp.pt.business.serviceCountModule.request.ServiceCountModuleIdDTO;
import com.sansec.ccsp.security.annotation.HasAnyPermissions;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.param.response.SecPageVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sansec.ccsp.pt.business.serviceCountModule.request.ServiceCountModuleDTO;
import com.sansec.ccsp.pt.business.serviceCountModule.request.ServiceCountModulePageDTO;
import com.sansec.ccsp.pt.business.serviceCountModule.response.ServiceCountModuleVO;
import com.sansec.ccsp.pt.business.serviceCountModule.service.ServiceCountModuleService;
import javax.annotation.Resource;


/**
 * @description : 网关统计组件表（330）;(SERVICE_COUNT_MODULE)表控制层
 * <AUTHOR> xiaojiawei
 * @date : 2024-3-9
 */
@RestController
@RequestMapping("/serviceCountModule")
@Validated
public class ServiceCountModuleController{
    @Resource
    private ServiceCountModuleService serviceCountModuleService;

    /**
     * 分页查询
     *
     * @param serviceCountModulePageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    @HasAnyPermissions("region:gatewayStatic:list")
    public SecRestResponse<SecPageVO<ServiceCountModuleVO>> find(@Validated @RequestBody ServiceCountModulePageDTO serviceCountModulePageDTO){
        return serviceCountModuleService.find(serviceCountModulePageDTO);
    }

    /**
     * 新增数据
     *
     * @param serviceCountModuleDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    @HasAnyPermissions("region:gatewayStatic:add")
    public SecRestResponse<Object> add(@Validated @RequestBody ServiceCountModuleDTO serviceCountModuleDTO){
        return serviceCountModuleService.add(serviceCountModuleDTO);
    }

    /**
     * 更新数据
     *
     * @param serviceCountModuleEditDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/edit")
    @HasAnyPermissions("region:gatewayStatic:edit")
    public SecRestResponse<Object> edit(@Validated @RequestBody ServiceCountModuleEditDTO serviceCountModuleEditDTO){
        return serviceCountModuleService.edit(serviceCountModuleEditDTO);
    }

    /**
     * 通过主键删除数据
     *
     * @param serviceCountModuleIdDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/delete")
    @HasAnyPermissions("region:gatewayStatic:delete")
    public SecRestResponse<Object> delete(@Validated @RequestBody ServiceCountModuleIdDTO serviceCountModuleIdDTO){
        return serviceCountModuleService.delete(serviceCountModuleIdDTO);
    }
}