package com.sansec.ccsp.extra.business.constant;

/**
 * 错误码常量类
 *
 * <AUTHOR>
 * @date 2022-05-24 16:17
 */
public class SecErrorCodeConstant {

	private SecErrorCodeConstant() {
	}


	private static final String PREFIX = "0A13";
	public static final String REQ_PARAM_ERROR = PREFIX + "0010";

	/**
	 * token信息不存在
	 */
	public static final String REQUEST_WITHOUT_TOKEN_ERROR= PREFIX + "0001";

	/**
	 * token校验错误
	 */
	public static final String TOKEN_VERIFY_ERROR= PREFIX + "0002";

	/**
	 * 无权访问
	 */
	public static final String NO_PERMISSION= PREFIX + "0003";

	/**
	 * 参数校验：租户id为空
	 */
	public static final String TENANT_ID_IS_NULL= PREFIX + "0004";

	//通过http调用其他服务模块错误 0100-0200
	//调用服务模块HTTP接口错误
	public static final String HTTP_CALL_ERROR = PREFIX + "0101";
	//根据配置编码获取配置值调用接口返回值为空
	public static final String CONFIG_VALUE_BY_CONFIG_CODE_NULL = PREFIX + "0102";
	//根据配置编码获取配置值调用接口返回值解析异常
	public static final String CONFIG_VALUE_BY_CONFIG_CODE_PARSE_ERROR = PREFIX + "0103";
	//鉴权信息校验接口返回值为空
	public static final String HAS_ANY_PERMISSIONS_NULL = PREFIX + "0104";
	//获取用户信息调用接口返回值为空
	public static final String GET_USER_NAME_MAP_NULL = PREFIX + "0105";
	//获取用户信息调用接口返回值解析异常
	public static final String GET_USER_NAME_MAP_PARSE_ERROR = PREFIX + "0106";
	//获取租户信息调用接口返回值为空
	public static final String GET_TENANT_MAP_NULL = PREFIX + "0107";
	//获取租户信息调用接口返回值解析异常
	public static final String GET_TENANT_MAP_PARSE_ERROR = PREFIX + "0108";
	//添加消息调用接口返回值为空
	public static final String ADD_MESSAGE_NULL = PREFIX + "0109";
	//添加消息调用接口返回值解析异常
	public static final String ADD_MESSAGE_PARSE_ERROR = PREFIX + "0110";
	//调用添加产品到期提醒接口返回值异常
	public static final String ADD_PRODUCTEXPIRE_ERROR = PREFIX + "0111";
	//调用修改产品到期提醒接口返回值异常
	public static final String EDIT_PRODUCTEXPIRE_ERROR = PREFIX + "0112";
	//根据租户标识获取租户信息返回值为空
	public static final String GET_TENANT_BY_TENANT_CODE_NULL = PREFIX + "0113";
	//根据租户标识获取租户信息返回值解析异常
	public static final String GET_TENANT_BY_TENANT_CODE_PARSE_ERROR = PREFIX + "0114";
	//根据租户标识获取租户信息返回错误
	public static final String GET_TENANT_BY_TENANT_CODE_ERROR = PREFIX + "0115";
	//当前平台无可用管理网关
	public static final String NO_AVAILABLE_MANAGE_GATEWAY = PREFIX + "0116";

	//http请求为空的异常
	public static final String HTTP_EMPTY_RESULT_ERROR = PREFIX + "0117";

	//内部服务异常
	public static final String HTTP_SERVICE_INVOKE_FAIL = PREFIX + "0118";

	//服务组不存在
	public static final String SERVICE_GROUP_NOT_EXIST = PREFIX + "0119";

	//服务组不是共享组
	public static final String SERVICE_GROUP_NOT_SHARE = PREFIX + "0120";

	//该产品不支持进行配额
	public static final String SERVICE_NOT_INIT_QUOTA = PREFIX + "0121";

	//服务组下不存在该服务
	public static final String GROUP_SERVICE_NOT_EXIST = PREFIX + "0122";

	//租户下不存在该服务
	public static final String TENANT_SERVICE_NOT_EXIST = PREFIX + "0123";

	//租户下存在该独享服务
	public static final String TENANT_SERVICE_EXIST = PREFIX + "0124";

	/**
	 * hadoop错误码 0201-0220
	 */
    //hdfs未知异常
	public static final String HDFS_UNKNOWN_ERROR = PREFIX + "0201";
	//本地文件不存在
	public static final String HDFS_LOCAL_FILE_PATH_NOT_EXIST = PREFIX + "0202";
	//hdfs文件已存在
	public static final String HDFS_FILE_PATH_IS_EXIST = PREFIX + "0203";


	/**
	 * 工单模块文件上传与下载错误 0221-0300
	 */
	//附件上传文件为空
	public static final String WORKORDER_UPLOAD_FILE_NULL = PREFIX + "0221";
    //附件上传文件大小超过限制
	public static final String WORKORDER_UPLOAD_FILE_SIZE_EXCEED = PREFIX + "0222";
	//附件写入临时目录失败
	public static final String WORKORDER_UPLOAD_TEMP_FILE_FAIL = PREFIX + "0223";
	//附件上传hadoop失败
	public static final String WORKORDER_UPLOAD_HDFS_FAIL = PREFIX + "0224";
	//文件不存在
	public static final String WORKORDER_ATTACHMENT_NOT_EXIST = PREFIX + "0225";
	//文件系统下载文件失败
	public static final String WORKORDER_ATTACHMENT_DOWNLOAD_FAIL = PREFIX + "0226";
	//文件删除失败
	public static final String WORKORDER_ATTACHMENT_DELETE_FAIL = PREFIX + "0227";
	//附件上传文件名为空
	public static final String WORKORDER_UPLOAD_FILE_NAME_NULL = PREFIX + "0228";
	//附件信息记录不存在
	public static final String WORKORDER_ATTACHMENT_RECORD_NOT_EXIST = PREFIX + "0229";
	//token为空
	public static final String WORKORDER_ATTACHMENT_DOWNLOAD_TOKEN_NOT_NULL = PREFIX + "0230";
	//token校验失败
	public static final String WORKORDER_ATTACHMENT_DOWNLOAD_TOKEN_VERIFY_ERROR = PREFIX + "0231";
	//附件文件类型不允许上传
	public static final String WORKORDER_UPLOAD_FILE_TYPE_NOT_ALLOW = PREFIX + "0232";


	/**
	 * 工单管理模块错误 0301-0350
	 */
	//工单不存在
	public static final String WORKORDER_NOT_EXIST = PREFIX + "0301";
	//工单不是待处理状态，无法开始处理
	public static final String WORKORDER_STATUS_NOT_BEFORE_PROCESS = PREFIX + "0302";
	//工单不是处理中状态，无法完成处理
	public static final String WORKORDER_STATUS_NOT_ON_PROCESS = PREFIX + "0303";
	//工单类型不存在
	public static final String WORKORDER_TYPE_NOT_EXIST = PREFIX + "0304";
	//申请密码产品时,申请密码产品id和申请数量不可为空
	public static final String APPLY_PRODUCT_OR_QUANTITY_IS_NULL = PREFIX + "0305";
	//评论提交时，评论正文和附件列表不能全为空
	public static final String CONTENT_ATTACH_ALL_NULL = PREFIX + "0306";
	//工单状态错误，无法撤销
	public static final String WORKORDER_CANT_CANCEL = PREFIX + "0307";
	//提交的文件属于其他工单/评论
	public static final String ATTACH_ALREADY_USED = PREFIX + "0308";
	//处理完成与撤销状态的工单不允许评论
	public static final String WORKORDER_STATUS_NOT_ALLOW_COMMENTS = PREFIX + "0309";
	//手机号码和邮箱至少填写一项
	public static final String WORKORDER_ADD_PHONE_EMAIL_IS_NULL = PREFIX + "0310";
	//工单类型ID和产品ID不允许同时为空
	public static final String WORKORDER_TYPE_AND_PRODUCT_NOT_EMPTY = PREFIX + "0311";
	//工单邮箱长度不能超过50位
	public static final String WORKORDER_EMAIL_LENGTH_IS_LIMIT = PREFIX + "0312";

	/**
	 * 密码产品 错误 0351-0400
	 */
	//密码产品不存在
	public static final String PRODUCT_NOT_EXIST = PREFIX + "0351";
	//密码产品Id为空
	public static final String PRODUCT_ID_IS_NULL = PREFIX + "0352";
	//密码产品不允许删除
	public static final String PRODUCT_NOT_DELETE = PREFIX + "0353";
	//密码产品删除失败
	public static final String PRODUCT_DELETE_FAILURE = PREFIX + "0354";
	//密码产品配额不存在
	public static final String TENANT_TO_PRODUCT_NOT_EXIST = PREFIX + "0355";
	//密码产品配额已开通，请勿重复开通
	public static final String TENANT_TO_PRODUCT_ALREADY_OPEN = PREFIX + "0356";
	//密码产品配额已关闭，请勿重复关闭
	public static final String TENANT_TO_PRODUCT_ALREADY_CLOSE = PREFIX + "0357";
	//密码产品已经为上架状态
	public static final String PRODUCT_ALREADY_OPEN = PREFIX + "0358";
	//密码产品已经为下架状态
	public static final String PRODUCT_ALREADY_CLOSE = PREFIX + "0359";
	//密码产品基础信息为空
	public static final String PRODUCT_BASE_INFO_IS_NULL = PREFIX + "0360";
	//密码产品为上架状态不允许删除
	public static final String PRODUCT_STATUS_IS_OPEN_NOT_DELETE = PREFIX + "0361";
	//密码产品配额信息存在不允许删除
	public static final String PRODUCT_HAS_QUOTA_NOT_DELETE = PREFIX + "0362";
	//密码产品处于关闭状态，无法进行操作
	public static final String PRODUCT_IS_CLOSE = PREFIX + "0363";
	//密码产品数据类型为正式数据，不允许删除
	public static final String PRODUCT_DATA_TYPE_IS_FORMAL_NOT_DELETE = PREFIX + "0364";
	//密码产品配额处于关闭状态，无法进行操作
	public static final String TENANT_TO_PRODUCT_IS_CLOSE = PREFIX + "0365";
	//编辑失败，请重试
	public static final String EDIT_CONFLICT = PREFIX + "0366";
	//附件信息不匹配
	public static final String PRODUCT_ATTACH_NOT_MATCH = PREFIX + "0367";

	//密码产品服务组不可变更
	public static final String TENANT_TO_PRODUCT_SERVICE_GROUP_CHANGE = PREFIX + "0368";


	/**
	 * 密码产品配额变更记录 错误 0401-0450
	 */
	//密码产品配额变更记录不存在
	public static final String TENANT_TO_PRODUCT_RECORD_NOT_EXIST = PREFIX + "0401";
	//过期时间不能早于当前时间
	public static final String EXPIRATIONTIME_BEFORE_CURRENTTIME = PREFIX + "0402";
	//密码产品配额记录Hmac更新错误
	public static final String TENANT_TO_PRODUCT_RECORD_HMAC_UPDATE_ERROR = PREFIX + "0403";


	/**
	 * 加密判断密钥管理 错误 0451-0500
	 */
	//调用获取区域Id->name映射map接口返回值异常
	public static final String GET_REGION_ID2NAME_MAP_ERROR = PREFIX + "0451";
	//调用获取租户Id->name映射map接口返回值异常
	public static final String GET_TENANT_ID2NAME_MAP_ERROR = PREFIX + "0452";
	//调用获取业务账号Id->name映射map接口返回值异常
	public static final String GET_APP_ID2NAME_MAP_ERROR = PREFIX + "0453";
	//密钥不存在
	public static final String ENCRYPT_JUDGE_KEY_NOT_EXIST = PREFIX + "0454";
	//调用获取是否是区域模式接口返回值异常
	public static final String GET_IS_REGION_MODE_ERROR = PREFIX + "0455";
	//外部密钥接收方信息不完整
	public static final String RECEIVER_INFO_INCOMPLETE = PREFIX + "0456";
	//调用pt与kms交互接口错误
	public static final String GET_PT_KMS_INTERACTION_ERROR = PREFIX + "0457";
	//调用pt通过租户id获取区域id接口错误
	public static final String GET_PT_TENANTID_TO_REGIONID_ERROR = PREFIX + "0458";
	//发送方和接收方是一个租户，不允许
	public static final String SENDER_EQUALS_RECEIVER = PREFIX + "0459";

	/**
	 * 许可管理 错误 0501-0510
	 */
	public static final String LICENSE_AUTH_FAILURE = PREFIX + "0501";
	public static final String LICENSE_HMAC_ERROR = PREFIX + "0502";
	public static final String WRITE_FILE_ERROR = PREFIX + "0503";
	public static final String LICENSE_CONTENT_ERROR = PREFIX + "0504";
}
