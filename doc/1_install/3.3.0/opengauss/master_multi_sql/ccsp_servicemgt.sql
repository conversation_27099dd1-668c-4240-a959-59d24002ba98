/*
 Navicat Premium Data Transfer

 Source Server         : ************vastbase
 Source Server Type    : PostgreSQL
 Source Server Version : 90204
 Source Host           : ************:5432
 Source Catalog        : ykytest
 Source Schema         : ccsp_servicemgt

 Target Server Type    : PostgreSQL
 Target Server Version : 90204
 File Encoding         : 65001

 Date: 01/04/2024 13:44:33
*/


-- ----------------------------
-- Table structure for database_info
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."database_info";
CREATE TABLE "ccsp_servicemgt"."database_info" (
  "id" int8 NOT NULL,
  "database_type_id" int8 NOT NULL,
  "database_name" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "database_ip" varchar(300) COLLATE "pg_catalog"."default",
  "database_port" varchar(60) COLLATE "pg_catalog"."default",
  "database_map_ip" varchar(300) COLLATE "pg_catalog"."default",
  "database_map_port" varchar(60) COLLATE "pg_catalog"."default",
  "region_id" int8,
  "case_name" varchar(300) COLLATE "pg_catalog"."default",
  "admin_user" varchar(200) COLLATE "pg_catalog"."default",
  "admin_auth_code" varchar(200) COLLATE "pg_catalog"."default",
  "auto_created" int4 DEFAULT 0,
  "hmac" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."id" IS '数据库id';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."database_type_id" IS '数据库类型id';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."database_name" IS '数据库名称';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."database_ip" IS '数据库IP';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."database_port" IS '数据库端口';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."database_map_ip" IS '数据库映射IP';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."database_map_port" IS '数据库映射端口';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."region_id" IS '区域ID';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."case_name" IS '数据库实例名称';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."admin_user" IS '数据库管理员用户';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."admin_auth_code" IS '数据库密码';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."auto_created" IS '数据库是否自动创建 1是 0否';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."hmac" IS '完整性校验';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."database_info"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_servicemgt"."database_info" IS '数据库信息表';

-- ----------------------------
-- Records of database_info
-- ----------------------------

-- ----------------------------
-- Table structure for database_minimum_unit
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."database_minimum_unit";
CREATE TABLE "ccsp_servicemgt"."database_minimum_unit" (
  "id" int8 NOT NULL,
  "database_unit_name" varchar(300) COLLATE "pg_catalog"."default" NOT NULL,
  "service_type_id" int8 NOT NULL,
  "database_id" int8 NOT NULL,
  "unit_user" varchar(200) COLLATE "pg_catalog"."default",
  "auth_code" varchar(200) COLLATE "pg_catalog"."default",
  "hmac" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."id" IS 'id';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."database_unit_name" IS '单元名称';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."service_type_id" IS '服务类型id';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."database_id" IS '数据库id';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."unit_user" IS '用户名';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."auth_code" IS '用户密码';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."hmac" IS '完整性校验';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."database_minimum_unit"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_servicemgt"."database_minimum_unit" IS '数据库最小单元';

-- ----------------------------
-- Records of database_minimum_unit
-- ----------------------------

-- ----------------------------
-- Table structure for database_unit_to_service_group
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."database_unit_to_service_group";
CREATE TABLE "ccsp_servicemgt"."database_unit_to_service_group" (
  "id" int8 NOT NULL,
  "database_unit_id" int8 NOT NULL,
  "database_id" int8 NOT NULL,
  "service_group_id" int8 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."database_unit_to_service_group"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_servicemgt"."database_unit_to_service_group"."database_unit_id" IS '数据库单元ID';
COMMENT ON COLUMN "ccsp_servicemgt"."database_unit_to_service_group"."database_id" IS '数据库ID';
COMMENT ON COLUMN "ccsp_servicemgt"."database_unit_to_service_group"."service_group_id" IS '服务组ID';
COMMENT ON COLUMN "ccsp_servicemgt"."database_unit_to_service_group"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_servicemgt"."database_unit_to_service_group"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."database_unit_to_service_group"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."database_unit_to_service_group"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."database_unit_to_service_group"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_servicemgt"."database_unit_to_service_group" IS '数据库单元和服务组的关联表';

-- ----------------------------
-- Records of database_unit_to_service_group
-- ----------------------------

-- ----------------------------
-- Table structure for database_upgrade_record
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."database_upgrade_record";
CREATE TABLE "ccsp_servicemgt"."database_upgrade_record" (
  "id" int8 NOT NULL,
  "database_minimum_unit_id" int8 NOT NULL,
  "database_type_id" int8,
  "service_type_id" int8,
  "request_data" varchar(2000) COLLATE "pg_catalog"."default",
  "response_data" varchar(2000) COLLATE "pg_catalog"."default",
  "status" int4,
  "type" int4,
  "backup_path" varchar(255) COLLATE "pg_catalog"."default",
  "backup_name" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."id" IS '数据库升级记录id';
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."database_minimum_unit_id" IS '数据库实例id';
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."database_type_id" IS '数据库类型id';
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."service_type_id" IS '服务类型id';
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."request_data" IS '请求数据';
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."response_data" IS '返回数据';
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."status" IS '1成功 2升级失败 3备份中 4备份失败 5还原中 6还原失败';
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."type" IS '操作类型;1升级  2备份 3恢复 4服务升级';
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."backup_path" IS '备份路径';
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."backup_name" IS '备份文件名';
COMMENT ON COLUMN "ccsp_servicemgt"."database_upgrade_record"."create_time" IS '创建时间';
COMMENT ON TABLE "ccsp_servicemgt"."database_upgrade_record" IS '数据库升级记录表';

-- ----------------------------
-- Records of database_upgrade_record
-- ----------------------------

-- ----------------------------
-- Table structure for dic_busi_type_to_database
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."dic_busi_type_to_database";
CREATE TABLE "ccsp_servicemgt"."dic_busi_type_to_database" (
  "id" int8 NOT NULL,
  "service_type_id" int8 NOT NULL,
  "database_unit_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."dic_busi_type_to_database"."id" IS '主键';
COMMENT ON COLUMN "ccsp_servicemgt"."dic_busi_type_to_database"."service_type_id" IS '业务类型ID';
COMMENT ON COLUMN "ccsp_servicemgt"."dic_busi_type_to_database"."database_unit_name" IS '数据库实例默认名称';
COMMENT ON TABLE "ccsp_servicemgt"."dic_busi_type_to_database" IS '业务服务对应数据库实例名称';

-- ----------------------------
-- Records of dic_busi_type_to_database
-- ----------------------------
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (1, 1, 'ccsp_pki');
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (2, 2, 'ccsp_svs');
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (4, 4, 'ccsp_kms');
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (5, 5, 'ccsp_tsa');
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (6, 6, 'ccsp_sms');
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (7, 7, 'ccsp_secauth');
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (8, 8, 'ccsp_secdb');
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (9, 9, 'ccsp_storage');
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (10, 10, 'ccsp_electronic_seal');
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (11, 11, 'ccsp_vpn');
INSERT INTO "ccsp_servicemgt"."dic_busi_type_to_database" VALUES (12, 12, 'ccsp_ca');

-- ----------------------------
-- Table structure for dic_database_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."dic_database_type";
CREATE TABLE "ccsp_servicemgt"."dic_database_type" (
  "id" int8 NOT NULL,
  "database_type_code" varchar(300) COLLATE "pg_catalog"."default",
  "verison" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."dic_database_type"."id" IS '数据库类型主键';
COMMENT ON COLUMN "ccsp_servicemgt"."dic_database_type"."database_type_code" IS '数据库类型名称';
COMMENT ON COLUMN "ccsp_servicemgt"."dic_database_type"."verison" IS '版本号';
COMMENT ON COLUMN "ccsp_servicemgt"."dic_database_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_servicemgt"."dic_database_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."dic_database_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."dic_database_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."dic_database_type"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_servicemgt"."dic_database_type" IS '数据库类型字典表';

-- ----------------------------
-- Records of dic_database_type
-- ----------------------------
INSERT INTO "ccsp_servicemgt"."dic_database_type" VALUES (1, 'mysql', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."dic_database_type" VALUES (2, 'gauss', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."dic_database_type" VALUES (3, 'opengauss', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."dic_database_type" VALUES (4, 'dm', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."dic_database_type" VALUES (5, 'kingbase', NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for service_group
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."service_group";
CREATE TABLE "ccsp_servicemgt"."service_group" (
  "service_group_id" int8 NOT NULL,
  "service_group_code" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
  "service_group_name" varchar(270) COLLATE "pg_catalog"."default" NOT NULL,
  "service_group_type" int4,
  "service_group_status" int4 NOT NULL,
  "tenant_id" int8,
  "region_id" int8,
  "is_share" int4 NOT NULL DEFAULT 0,
  "invalid_flag" int4 DEFAULT 0,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."service_group_id" IS '服务组ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."service_group_code" IS '服务组标识';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."service_group_name" IS '服务组名称';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."service_group_type" IS '服务组类型';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."service_group_status" IS '服务组状态:1:创建中；2：运行中；3：创建失败';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."region_id" IS '区域ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."is_share" IS '是否共享；0：否；1：是';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."invalid_flag" IS '是否作废；0：否；1：是';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_servicemgt"."service_group" IS '服务组';

-- ----------------------------
-- Records of service_group
-- ----------------------------

-- ----------------------------
-- Table structure for service_group_to_busi_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."service_group_to_busi_type";
CREATE TABLE "ccsp_servicemgt"."service_group_to_busi_type" (
  "id" int8 NOT NULL,
  "service_group_id" int8 NOT NULL,
  "busi_type_id" int8 NOT NULL,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_busi_type"."id" IS '主键';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_busi_type"."service_group_id" IS '服务组ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_busi_type"."busi_type_id" IS '业务类型ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_busi_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_busi_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_busi_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_busi_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_busi_type"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_servicemgt"."service_group_to_busi_type" IS '服务组和业务类型关联表';

-- ----------------------------
-- Records of service_group_to_busi_type
-- ----------------------------

-- ----------------------------
-- Table structure for service_group_to_group
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."service_group_to_group";
CREATE TABLE "ccsp_servicemgt"."service_group_to_group" (
  "id" int8 NOT NULL,
  "service_group_id" int8 NOT NULL,
  "kms_group_id" int8 NOT NULL,
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_group"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_group"."service_group_id" IS '加解密或签名验签服务组ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_group"."kms_group_id" IS 'kms服务组ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_group"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_group"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_group"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_group_to_group"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_servicemgt"."service_group_to_group" IS '服务组关联服务组（330）';

-- ----------------------------
-- Records of service_group_to_group
-- ----------------------------

-- ----------------------------
-- Table structure for service_info
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."service_info";
CREATE TABLE "ccsp_servicemgt"."service_info" (
  "id" int8 NOT NULL,
  "service_type_id" int8 NOT NULL,
  "service_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "device_group_id" int8,
  "service_group_id" int8,
  "tenant_id" int8 NOT NULL,
  "region_id" int8,
  "oper_status" int4,
  "run_status" int8 NOT NULL DEFAULT 2,
  "mgt_ip" varchar(20) COLLATE "pg_catalog"."default",
  "mgt_port" int4,
  "mgt_gateway_ip" varchar(20) COLLATE "pg_catalog"."default",
  "mgt_gateway_port" int4,
  "busi_ip" varchar(20) COLLATE "pg_catalog"."default",
  "busi_port" int4,
  "busi_gateway_ip" varchar(20) COLLATE "pg_catalog"."default",
  "busi_gateway_port" int4,
  "remote_ip" varchar(20) COLLATE "pg_catalog"."default",
  "remote_port" int4,
  "expand_port" int4,
  "tcp_port" int4,
  "gateway_id" int8,
  "route_id" int8,
  "is_active_standby" int4 NOT NULL DEFAULT 1,
  "db_created" int4,
  "invalid_flag" int4 NOT NULL DEFAULT 0,
  "hmac" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."id" IS 'id';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."service_type_id" IS '服务类型';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."service_name" IS '服务名称';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."device_group_id" IS '设备组id';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."service_group_id" IS '服务组id';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."tenant_id" IS '租户id';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."region_id" IS '区域ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."oper_status" IS '1运行中（操作完成）、2初始化中、3未运行';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."run_status" IS '1功能运行正常 2.功能运行异常 3启动中 4停止中 5重启中 6升级中 7升级失败';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."mgt_ip" IS '管理IP';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."mgt_port" IS '管理端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."mgt_gateway_ip" IS '管理网关IP';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."mgt_gateway_port" IS '管理网关端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."busi_ip" IS '业务IP';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."busi_port" IS '业务端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."busi_gateway_ip" IS '业务网关IP';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."busi_gateway_port" IS '业务网关端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."remote_ip" IS '管控IP';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."remote_port" IS '管控端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."expand_port" IS '扩展端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."tcp_port" IS 'TCP/UDP端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."gateway_id" IS 'API网关表主键';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."route_id" IS '路由表主键';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."is_active_standby" IS '是否为主机或者备机  1主机 2备机';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."db_created" IS '数据库是否已创建 1是 0否';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."hmac" IS '完整性校验';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_info"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_servicemgt"."service_info" IS '服务信息表';

-- ----------------------------
-- Records of service_info
-- ----------------------------

-- ----------------------------
-- Table structure for service_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."service_type";
CREATE TABLE "ccsp_servicemgt"."service_type" (
  "id" int8 NOT NULL,
  "service_type_name" varchar(150) COLLATE "pg_catalog"."default" NOT NULL,
  "service_code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" int8 NOT NULL,
  "service_create_type" int4,
  "service_use_type" int4,
  "mgt_port" int4,
  "mgt_gateway_port" int4,
  "busi_port" int4,
  "busi_gateway_port" int4,
  "remote_port" int8 DEFAULT 18086,
  "expand_port" int4,
  "tcp_port" int4,
  "monitor_port" int4,
  "is_restart" int4 DEFAULT 1,
  "is_create_mgt_route" int4 DEFAULT 1,
  "is_create_busi_route" int4 DEFAULT 1,
  "is_create_log_plugin" int4 DEFAULT 1,
  "log_plugin_format" varchar(500) COLLATE "pg_catalog"."default",
  "is_monitor_device" int4 DEFAULT 2,
  "config_device_type" int4 DEFAULT 2,
  "available_device_type" varchar(200) COLLATE "pg_catalog"."default",
  "connect_time" int4,
  "send_time" int4,
  "read_time" int4,
  "image_id" int8,
  "service_path" varchar(100) COLLATE "pg_catalog"."default",
  "db_update_config_flag" int4,
  "db_common" int4,
  "db_tenant_common" int4,
  "db_name" varchar(150) COLLATE "pg_catalog"."default",
  "service_class" int4,
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."id" IS '服务类型主键';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."service_type_name" IS '服务类型名称';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."service_code" IS '服务简称';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."parent_id" IS '服务信息父类型';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."service_create_type" IS '服务创建类型 1手动创建 2自动创建';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."service_use_type" IS '1独享 2共享';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."mgt_port" IS '内网管理端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."mgt_gateway_port" IS '管理网关端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."busi_port" IS '业务端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."busi_gateway_port" IS '业务网关端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."remote_port" IS '管控端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."expand_port" IS '扩展端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."tcp_port" IS 'TCP/UDP端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."monitor_port" IS '监控组件端口';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."is_restart" IS '是否允许启动停止 1允许 2不允许';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."is_create_mgt_route" IS '是否创建管理路由 1创建 2不创建';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."is_create_busi_route" IS '是否创建业务路由 1创建 2不创建';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."is_create_log_plugin" IS '是否创建日志插件 1创建 2不创建';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."log_plugin_format" IS '日志插件日志格式';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."is_monitor_device" IS '是否监控服务设备 1是 2否';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."config_device_type" IS '分配设备类型 1不分配 2自动分配 3按设备类型下拉选择';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."available_device_type" IS '服务类型可用设备类型 空代表全部可用 不全部可用逗号分隔';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."connect_time" IS '连接超时时间';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."send_time" IS '发送超时时间';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."read_time" IS '接收超时时间';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."image_id" IS '镜像ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."service_path" IS '服务前缀';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."db_update_config_flag" IS '平台是否需要更新服务数据库配置 1是 0否';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."db_common" IS '服务是否共用数据库 1是 0否';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."db_tenant_common" IS '服务是否同租户下共用数据库 1是 0否';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."db_name" IS '数据库实例名称';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."service_class" IS '服务分类 1基础服务 2增值服务 3.组件服务';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_servicemgt"."service_type" IS '服务类型字典表';

-- ----------------------------
-- Records of service_type
-- ----------------------------
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (1, '加解密服务', 'pki', 1, 1, 1, 20000, NULL, 20004, NULL, 18086, 20005, NULL, 20002, 1, 1, 1, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, '2023-07-04 09:45:51');
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (2, '签名验签服务', 'svs', 1, 1, 1, 20010, NULL, 20014, NULL, 18086, 20015, NULL, 20012, 1, 1, 1, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-03-28 19:13:30');
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (4, '密钥管理服务', 'kms', 1, 1, 1, 20100, NULL, 20121, NULL, 18086, 20134, 20122, 20102, 1, 1, 1, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-03-28 19:13:30');
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (5, '时间戳服务', 'tsa', 1, 1, 1, 20300, NULL, 20304, NULL, 18086, NULL, NULL, 20306, 2, 1, 1, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-03-28 18:58:41');
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (6, '协同签名服务', 'sms', 1, 1, 1, 20513, NULL, 20524, NULL, 18086, NULL, NULL, 20512, 1, 1, 1, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-03-28 19:13:30');
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (7, '动态令牌服务', 'secauth', 1, 1, 1, 20613, NULL, 20624, NULL, 18086, NULL, NULL, 20612, 1, 1, 1, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-03-28 18:58:41');
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (8, '数据库加密服务', 'secdb', 1, 1, 1, 20400, NULL, 20400, NULL, 18086, NULL, NULL, 20402, 2, 1, 1, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-04-21 11:14:43');
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (9, '文件加密服务', 'secstorage', 1, 1, 1, 20200, NULL, 20200, NULL, 18086, NULL, 20222, 20202, 2, 1, 1, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-03-28 19:13:30');
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (10, '电子签章服务', 'tsc', 1, 1, 1, 8011, NULL, 8011, NULL, 18086, NULL, NULL, 8099, 1, 1, 1, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-03-28 18:58:41');
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (11, 'SSLVPN加密通道服务', 'vpn', 1, 1, 1, 20700, NULL, 20700, NULL, 18086, NULL, NULL, 20702, 2, 1, 2, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-03-28 19:13:30');
INSERT INTO "ccsp_servicemgt"."service_type" VALUES (12, '数字证书认证服务', 'ca', 1, 1, 1, 20800, NULL, 20801, NULL, 18086, NULL, NULL, 20899, 2, 1, 1, 2, '{"host":"$host","timestamp":"$time_iso8601","client_ip":"$remote_addr","tenantCode":"$http_X-SW-Authorization-TenantCode","appCode":"$http_X-SW-Authorization-AppCode","httpStatus":"$status","clientIp":"$client_ip","hostIp":"$upstream_addr","uri":"$uri"}', 1, 1, '0', 180, 180, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-03-28 19:13:30');

-- ----------------------------
-- Table structure for service_type_to_busi_type
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."service_type_to_busi_type";
CREATE TABLE "ccsp_servicemgt"."service_type_to_busi_type" (
  "id" int8 NOT NULL,
  "service_type_id" int8 NOT NULL,
  "busi_type_id" int8 NOT NULL,
  "busi_type_name" varchar(150) COLLATE "pg_catalog"."default",
  "remark" varchar(1000) COLLATE "pg_catalog"."default",
  "create_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."service_type_to_busi_type"."id" IS 'id';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type_to_busi_type"."service_type_id" IS '服务类型ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type_to_busi_type"."busi_type_id" IS '业务类型ID';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type_to_busi_type"."busi_type_name" IS '业务名称';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type_to_busi_type"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type_to_busi_type"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type_to_busi_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type_to_busi_type"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."service_type_to_busi_type"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_servicemgt"."service_type_to_busi_type" IS '服务类型和业务类型关联表';

-- ----------------------------
-- Records of service_type_to_busi_type
-- ----------------------------
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (1, 1, 1, '加解密业务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (2, 2, 2, '签名验签业务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (4, 4, 4, '密钥管理业务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (5, 5, 5, '时间戳业务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (6, 6, 6, '协同签名业务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (7, 7, 7, '动态令牌服务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (8, 8, 8, '数据库加密业务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (9, 9, 9, '文件加密业务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (10, 10, 10, '电子签章业务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (11, 11, 11, 'SSLVPN加密通道业务', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "ccsp_servicemgt"."service_type_to_busi_type" VALUES (12, 12, 12, '数字证书认证业务', NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."sys_job";
CREATE TABLE "ccsp_servicemgt"."sys_job" (
  "job_id" int8 NOT NULL,
  "job_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "job_group" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "server_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "method_url" varchar(1500) COLLATE "pg_catalog"."default",
  "json_param" text COLLATE "pg_catalog"."default" NOT NULL,
  "cron_expression" varchar(255) COLLATE "pg_catalog"."default",
  "misfire_policy" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 3,
  "concurrent" varchar(1) COLLATE "pg_catalog"."default" DEFAULT 1,
  "job_status" varchar(1) COLLATE "pg_catalog"."default" DEFAULT 0,
  "created_by" int8,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "updated_by" int8,
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(1500) COLLATE "pg_catalog"."default"
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."job_id" IS '任务号';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."job_name" IS '任务名称';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."job_group" IS '任务组名';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."server_id" IS '服务模块';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."method_url" IS '调用接口';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."json_param" IS 'json格式参数';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."cron_expression" IS 'CRON执行表达式';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."misfire_policy" IS '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."concurrent" IS '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."job_status" IS '状态（0正常 1暂停）';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."created_by" IS '创建人';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."updated_by" IS '更新人';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job"."remark" IS '备注';
COMMENT ON TABLE "ccsp_servicemgt"."sys_job" IS '定时任务表';

-- ----------------------------
-- Records of sys_job
-- ----------------------------

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."sys_job_log";
CREATE TABLE "ccsp_servicemgt"."sys_job_log" (
  "job_log_id" int8 NOT NULL,
  "job_id" int8 NOT NULL,
  "job_message" varchar(1500) COLLATE "pg_catalog"."default",
  "status" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_info" varchar(6000) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "trigger_time" int8
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job_log"."job_log_id" IS '任务日志ID';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job_log"."job_id" IS '任务ID';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job_log"."job_message" IS '日志信息';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job_log"."status" IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_job_log"."trigger_time" IS '触发时间';
COMMENT ON TABLE "ccsp_servicemgt"."sys_job_log" IS '定时任务执行日志表';

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."sys_task";
CREATE TABLE "ccsp_servicemgt"."sys_task" (
  "task_id" int8 NOT NULL,
  "task_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "task_group" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "server_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "method_url" varchar(1000) COLLATE "pg_catalog"."default",
  "json_param" text COLLATE "pg_catalog"."default" NOT NULL,
  "task_status" int4 NOT NULL DEFAULT 0,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "update_time" varchar(30) COLLATE "pg_catalog"."default",
  "remark" varchar(300) COLLATE "pg_catalog"."default",
  "timeout" int4,
  "start_time" varchar(255) COLLATE "pg_catalog"."default",
  "end_time" varchar(255) COLLATE "pg_catalog"."default",
  "policy" varchar(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 0
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."task_id" IS '任务号';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."task_name" IS '任务名称';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."task_group" IS '任务组名;执行任务串行';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."server_id" IS '服务模块';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."method_url" IS '调用接口';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."json_param" IS 'json格式参数';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."task_status" IS '状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时)';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."update_time" IS '更新时间';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."timeout" IS '超时时间;单位秒';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."start_time" IS '开始时间';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."end_time" IS '结束时间';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task"."policy" IS '是否允许重复执行;0-不允许，1允许';
COMMENT ON TABLE "ccsp_servicemgt"."sys_task" IS '异步任务表';

-- ----------------------------
-- Records of sys_task
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task_log
-- ----------------------------
DROP TABLE IF EXISTS "ccsp_servicemgt"."sys_task_log";
CREATE TABLE "ccsp_servicemgt"."sys_task_log" (
  "task_log_id" int8 NOT NULL,
  "task_id" int8 NOT NULL,
  "task_message" varchar(3000) COLLATE "pg_catalog"."default",
  "status" varchar(1) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_info" varchar(6000) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "trigger_time" int8
)
WITH (fillfactor=80)
;
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task_log"."task_log_id" IS '任务日志ID';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task_log"."task_id" IS '任务ID';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task_log"."task_message" IS '日志信息';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task_log"."status" IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task_log"."create_time" IS '创建时间;单位毫秒';
COMMENT ON COLUMN "ccsp_servicemgt"."sys_task_log"."trigger_time" IS '触发时间;任务服务上送';
COMMENT ON TABLE "ccsp_servicemgt"."sys_task_log" IS '异步任务执行日志表';

-- ----------------------------
-- Records of sys_task_log
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table database_info
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."database_info" ADD CONSTRAINT "database_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table database_minimum_unit
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."database_minimum_unit" ADD CONSTRAINT "database_minimum_unit_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table database_unit_to_service_group
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."database_unit_to_service_group" ADD CONSTRAINT "database_unit_to_service_group_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table database_upgrade_record
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."database_upgrade_record" ADD CONSTRAINT "database_upgrade_record_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_busi_type_to_database
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."dic_busi_type_to_database" ADD CONSTRAINT "dic_busi_type_to_database_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dic_database_type
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."dic_database_type" ADD CONSTRAINT "dic_database_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_group
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."service_group" ADD CONSTRAINT "service_group_pkey" PRIMARY KEY ("service_group_id");

-- ----------------------------
-- Primary Key structure for table service_group_to_busi_type
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."service_group_to_busi_type" ADD CONSTRAINT "service_group_to_busi_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_group_to_group
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."service_group_to_group" ADD CONSTRAINT "service_group_to_group_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_info
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."service_info" ADD CONSTRAINT "service_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table service_type
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."service_type" ADD CONSTRAINT "service_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_job
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."sys_job" ADD CONSTRAINT "sys_job_pkey" PRIMARY KEY ("job_id");

-- ----------------------------
-- Primary Key structure for table sys_job_log
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."sys_job_log" ADD CONSTRAINT "sys_job_log_pkey" PRIMARY KEY ("job_log_id");

-- ----------------------------
-- Primary Key structure for table sys_task
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."sys_task" ADD CONSTRAINT "sys_task_pkey" PRIMARY KEY ("task_id");

-- ----------------------------
-- Primary Key structure for table sys_task_log
-- ----------------------------
ALTER TABLE "ccsp_servicemgt"."sys_task_log" ADD CONSTRAINT "sys_task_log_pkey" PRIMARY KEY ("task_log_id");
