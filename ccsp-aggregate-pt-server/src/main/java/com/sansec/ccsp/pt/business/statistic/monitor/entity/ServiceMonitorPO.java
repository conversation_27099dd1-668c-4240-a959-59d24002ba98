package com.sansec.ccsp.pt.business.statistic.monitor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @description : 监控服务表;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-3-4
 */
@TableName("service_swmonitor")
@Data
public class ServiceMonitorPO extends BasePO{
    /**
     * 监控服务ID
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 区域ID
     */
    private Long regionId;
    /**
     * 服务名称
     */
    private String name;
    /**
     * 监控服务IP
     */
    private String ip;
    /**
     * 监控服务端口
     */
    private Integer port;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}