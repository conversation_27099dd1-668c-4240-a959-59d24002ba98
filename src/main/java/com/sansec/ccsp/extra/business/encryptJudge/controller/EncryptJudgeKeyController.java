package com.sansec.ccsp.extra.business.encryptJudge.controller;

import com.sansec.ccsp.extra.business.encryptJudge.request.EncryptJudgeKeyAddDTO;
import com.sansec.ccsp.extra.business.encryptJudge.request.EncryptJudgeKeyEditDTO;
import com.sansec.ccsp.extra.business.encryptJudge.request.EncryptJudgeKeyIdDTO;
import com.sansec.ccsp.extra.business.encryptJudge.request.EncryptJudgeKeyPageDTO;
import com.sansec.ccsp.extra.business.encryptJudge.response.EncryptJudgeKeyVO;
import com.sansec.ccsp.extra.securityconf.annotation.HasAnyPermissions;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sansec.ccsp.extra.business.encryptJudge.service.EncryptJudgeKeyService;
import javax.annotation.Resource;

 /**
 * @description : 加密判定密钥表;(ENCRYPT_JUDGE_KEY)表控制层
 * <AUTHOR> zhaozhuang
 * @date : 2024-4-11
 */
@RestController
@RequestMapping("/encryptJudgeKey/v1")
@Validated
public class EncryptJudgeKeyController{
    @Resource
    private EncryptJudgeKeyService encryptJudgeKeyService;
    
    /** 
     * 分页查询密钥
     *
     * @param encryptJudgeKeyPageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    @HasAnyPermissions("encrypt:judge:keylist")
    public SecRestResponse<SecPageVO<EncryptJudgeKeyVO>> find(@RequestBody EncryptJudgeKeyPageDTO encryptJudgeKeyPageDTO){
        return encryptJudgeKeyService.find(encryptJudgeKeyPageDTO);
    }
    
    /** 
     * 新增密钥
     *
     * @param encryptJudgeKeyAddDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    @HasAnyPermissions("encrypt:judge:addkey")
    public SecRestResponse<Object> add(@RequestBody EncryptJudgeKeyAddDTO encryptJudgeKeyAddDTO){
        return encryptJudgeKeyService.add(encryptJudgeKeyAddDTO);
    }
    
    /** 
     * 更新密钥信息
     *
     * @param encryptJudgeKeyEditDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/edit")
    @HasAnyPermissions("encrypt:judge:editkey")
    public SecRestResponse<Object> edit(@RequestBody EncryptJudgeKeyEditDTO encryptJudgeKeyEditDTO){
        return encryptJudgeKeyService.edit(encryptJudgeKeyEditDTO);
    }
    
    /** 
     * 通过主键删除密钥
     *
     * @param encryptJudgeKeyidDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/delete")
    @HasAnyPermissions("encrypt:judge:deletekey")
    public SecRestResponse<Object> deleteById(@RequestBody EncryptJudgeKeyIdDTO encryptJudgeKeyidDTO){
        return encryptJudgeKeyService.deleteById(encryptJudgeKeyidDTO);
    }
}