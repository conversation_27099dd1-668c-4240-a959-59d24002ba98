package com.sansec.ccsp.common.business.role.service;


import com.sansec.ccsp.common.business.role.entity.UserRolePO;
import com.sansec.ccsp.common.role.request.UserRoleDTO;
import com.sansec.ccsp.common.role.request.UserRolePageDTO;
import com.sansec.ccsp.common.role.response.UserRoleVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * @Description: 用户角色表;(USER_ROLE)表服务接口
 * <AUTHOR> xia<PERSON>jiaw<PERSON>
 * @Date: 2023-2-18
 */
public interface UserRoleService{
    /**
     * 分页查询
     *
     * @param userRolePageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<UserRoleVO>> find(UserRolePageDTO userRolePageDTO);
    /**
     * 新增数据
     *
     * @param userRoleDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(UserRoleDTO userRoleDTO);
    /**
     * 更新数据
     *
     * @param userRoleDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(UserRoleDTO userRoleDTO);
    /**
     * 通过主键删除数据
     *
     * @param roleId
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(Long roleId);

     /**
      * 检验角色名称是否唯一
      * @param role
      * @return
      */
    Long checkRoleNameUnique(UserRolePO role);

     /**
      * 校验角色名称是否唯一，修改时允许自己的名字
      *
      * @param role 角色信息
      * @return 结果
      */
     boolean checkRoleNameUniqueNoEqual(UserRolePO role);

     /**
      * 查询角色使用情况
      * @param id
      * @return
      */
     Long countUserRoleByRoleId(Long id);
     /**
      * 通过id获取角色信息
      * @param roleId
      * @return
      */
     UserRoleVO getById(Long roleId);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    List<Long> selectMenuListByRoleId(Long roleId);

    /**
     * 根据用户ID查询角色ID
     * @param userId
     * @return
     */
    Long  selectRoleIdByUserId(Long userId);

    /**
     * 获取添加角色，审核角色的角色下拉列表
     *
     * @return
     */
    SecRestResponse<Object> getRoleSelectForAddUser() ;


    /**
     * 获取登录人员角色权限
     *
     * @return
     */
    List<String> getPerms() ;


    /**
     * 通过用户类型id获取角色信息
     * @param userTypeId
     * @return
     */
    List<UserRoleVO> getUserRoleByUserTypeId(Integer userTypeId);

    /**
     * 为userRole表，全表更新hmac
     * @return
     */
    int updateAllHmac();

    boolean verifyHmac(Long id);

    String checkHmac();
}
