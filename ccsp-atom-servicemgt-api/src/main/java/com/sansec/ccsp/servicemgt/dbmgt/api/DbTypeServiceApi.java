package com.sansec.ccsp.servicemgt.dbmgt.api;

import com.sansec.ccsp.servicemgt.dbmgt.api.fallback.DbTypeServiceFallBack;
import com.sansec.ccsp.servicemgt.dbmgt.request.DatabaseTypeFindDTO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Date 2023/7/11 11:24
 */
@FeignClient(name = "${spring.protocol.prefix}${spring.application.servicemgt.name}", path = "/ccsp/servicemgt", fallbackFactory = DbTypeServiceFallBack.class)
public interface DbTypeServiceApi {
    /**
     * 根据code查询id
     * @param databaseTypeDTO
     * @return
     */
    @PostMapping(value = "/api/dbtype/v1/findDbIdByCode")
    SecRestResponse<Long> findDbIdByCode(@RequestBody @Validated DatabaseTypeFindDTO databaseTypeDTO);
}
