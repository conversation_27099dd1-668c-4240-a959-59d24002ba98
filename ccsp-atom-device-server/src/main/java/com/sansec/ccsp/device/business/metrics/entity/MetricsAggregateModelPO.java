package com.sansec.ccsp.device.business.metrics.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @description : 指标聚合配置模板;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-6-22
 */
@TableName("METRICS_AGGREGATE_MODEL")
@Data
public class MetricsAggregateModelPO extends BasePO{
    /**
     * ID
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 分组，分为CPU、磁盘、内存
     */
    private String metricsGroup;
    /**
     * 目标指标标识
     */
    private String targetMetricsCode;
    /**
     * 聚合类型，分为普通聚合和聚合表达式聚合
     */
    private String aggType;
    /**
     * 聚合方法，仅在普通聚合时使用
     */
    private String aggMethod;
    /**
     * 聚合表达式
     */
    private String aggExpression;
    /**
     * 数据源指标标识，仅在普通聚合时使用
     */
    private String sourceMetricsCode;

}