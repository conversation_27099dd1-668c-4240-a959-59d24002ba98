package com.sansec.ccsp.device.business.devicemasterkey.entity;

import lombok.Data;

@Data
public class DeviceMasterCoverRecordPagePO {
    /**
     * 恢复状态0-恢复成功，1-恢复中，2-恢复失败
     */
    private Integer coverStatus;
    /**
     * 恢复记录类型 1-主密钥，2-内部密钥
     */
    private Integer coverType;
    /**
     * 恢复时间
     */
    private String coverTime;

    /**
     * 设备ID
     */
    private Long deviceId;
    /**
     * 设备内部ID
     */
    private String deviceSelfId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 厂商ID
     */
    private Long vendorId;
    /**
     * 厂商名称
     */
    private String vendorName;
    /**
     * 设备类型ID
     */
    private Long deviceTypeId;
    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备组类型
     */
    private Integer deviceGroupType;
    /**
     * 所属宿主机ID（虚拟机使用）
     */
    private Long hccsDeviceId;
    /**
     * 可创建虚拟机总数
     */
    private Integer hccsVsmTotal;
    /**
     * 可创建虚拟机数
     */
    private Integer hccsVsmUsable;
    /**
     * 已创建数量
     */
    private Integer hccsVsmUsed;
    /**
     * 设备组ID
     */
    private Long deviceGroupId;

    /**
     * 设备组名称
     */
    private String deviceGroupName;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户id
     */
    private String tenantName;
    /**
     * 管理IP
     */
    private String mgtIp;
    /**
     * 管理端口
     */
    private Integer mgtPort;
    /**
     * 服务IP
     */
    private String busiIp;
    /**
     * 服务端口
     */
    private Integer busiPort;

    /**
     * 协议（HTTP、HTTPS）
     */
    private String connectProtocol;
    /**
     * 设备版本号
     */
    private String deviceVersion;
    /**
     * 设备序列号
     */
    private String deviceSerialnum;
    /**
     * 公钥指纹
     */
    private String publicKeyFinger;
    /**
     * 设备权重
     */
    private Double deviceWeight;
    /**
     * HMAC
     */
    private String hmac;
    /**
     * 云机回调token
     */
    private String cloudToken;
    /**
     * 使用状态ID
     */
    private Long managementStatusId;
    /**
     * 操作状态ID
     */
    private Long operStatus;
    /**
     * 是否生成主密钥(1是，0否);默认0
     */
    private Integer masterKeyFlag;
    /**
     * 是否作废;默认为0
     */
    private String invalidFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 虚拟机所属主机名称
     */
    private String vsmHostName;

    /**
     * 虚拟机所属主机id
     */
    private String vsmHostId;

    /**
     * 设备物理类型；1：云密码机；2：物理机；3：虚拟机
     */
    private Integer familyType;

    /**
     * 扩展端口
     */
    private Integer busiPortExtend;

    /**
     * 是否支持共享，0：独享，1：共享
     */
    private Integer isShare;
}
