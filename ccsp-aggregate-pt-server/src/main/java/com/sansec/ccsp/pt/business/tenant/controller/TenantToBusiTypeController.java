package com.sansec.ccsp.pt.business.tenant.controller;

import com.sansec.ccsp.pt.business.tenant.entity.BusiType;
import com.sansec.ccsp.pt.business.tenant.request.TenantToBusiTypeDTO;
import com.sansec.ccsp.pt.business.tenant.request.TenantToBusiTypePageDTO;
import com.sansec.ccsp.pt.business.tenant.response.TenantToBusiTypeVO;
import com.sansec.ccsp.pt.business.tenant.service.TenantToBusiTypeService;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> xia<PERSON>jiawei
 * @Description: 租户和业务类型关联表;(TENANT_TO_BUSI_TYPE)表控制层
 * @Date: 2023-2-18
 */
@RestController
@RequestMapping("/tenantToBusiType/v1")
@Validated
public class TenantToBusiTypeController {
    @Resource
    private TenantToBusiTypeService tenantToBusiTypeService;

    /**
     * @param tenantToBusiTypePageDTO 筛选条件
     * @return 查询结果
     * @Description: 分页查询
     */
    @PostMapping("/find")
    public SecRestResponse<SecPageVO<TenantToBusiTypeVO>> find(@RequestBody TenantToBusiTypePageDTO tenantToBusiTypePageDTO) {
        return tenantToBusiTypeService.find(tenantToBusiTypePageDTO);
    }

    /**
     * @param tenantToBusiTypeDTO 实例对象
     * @return 实例对象
     * @Description: 新增数据
     */
    @PostMapping("/add")
    public SecRestResponse<Object> add(@RequestBody TenantToBusiTypeDTO tenantToBusiTypeDTO) {
        return tenantToBusiTypeService.add(tenantToBusiTypeDTO);
    }

    /**
     * @param tenantToBusiTypeDTO 实例对象
     * @return 实例对象
     * @Description: 更新数据
     */
    @PostMapping("/edit")
    public SecRestResponse<Object> edit(@RequestBody TenantToBusiTypeDTO tenantToBusiTypeDTO) {
        return tenantToBusiTypeService.edit(tenantToBusiTypeDTO);
    }

    /**
     * @param tenantToBusiTypeDTO 实例对象
     * @return 实例对象
     * @Description: 通过主键删除数据
     */
    @PostMapping("/deleteById")
    public SecRestResponse<Object> deleteById(@RequestBody TenantToBusiTypeDTO tenantToBusiTypeDTO) {
        return tenantToBusiTypeService.deleteById(tenantToBusiTypeDTO);
    }

    /**
     * @return 业务类型列表
     * @Description: 租户业务类型列表
     */
    @PostMapping("/getBusiTypeListByTenantId")
    public SecRestResponse<List<BusiType>> getBusiTypeListByTenantId() {
        List<BusiType> busiTypes = tenantToBusiTypeService.getBusiTypeListByTenantId();
        return ResultUtil.ok(busiTypes);
    }
}