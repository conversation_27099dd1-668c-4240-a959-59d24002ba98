package com.sansec.ccsp.servicemgt.servicegroup.response;

import lombok.Data;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description : 服务组关联服务组（330）;
 * @date : 2024-3-12
 */
@Data
public class ServiceGroupToGroupVO {
    /**
     * ID
     */
    private Long id;
    /**
     * 加解密或签名验签服务组ID
     */
    private Long serviceGroupId;
    /**
     * kms服务组ID
     */
    private Long kmsGroupId;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}