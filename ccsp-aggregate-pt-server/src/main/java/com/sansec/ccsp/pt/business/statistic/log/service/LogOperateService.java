package com.sansec.ccsp.pt.business.statistic.log.service;

import com.sansec.ccsp.staticapi.logoperate.request.LogOperateAddDTO;
import com.sansec.ccsp.staticapi.logoperate.request.LogOperateAuditDTO;
import com.sansec.ccsp.staticapi.logoperate.request.LogOperatePageDTO;
import com.sansec.ccsp.staticapi.logoperate.request.LogOperateTenantPageDTO;
import com.sansec.ccsp.staticapi.logoperate.response.LogOperateVo;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface LogOperateService {

    SecRestResponse<SecPageVO<LogOperateVo>> find(LogOperatePageDTO pageDTO);

    SecRestResponse<Object> audit(LogOperateAuditDTO auditDTO);

    void export(LogOperatePageDTO pageDTO, HttpServletResponse response) throws IOException;

    SecRestResponse<SecPageVO<LogOperateVo>> findByTenant(LogOperateTenantPageDTO pageDTO);

    SecRestResponse<Object> auditByTenant(LogOperateAuditDTO auditDTO);

    void exportByTenant(LogOperateTenantPageDTO pageDTO, HttpServletResponse response) throws IOException;

    /**
     * 扩展服务添加日志远程调用接口，扩展服务专用
     * @param logOperateAddDTO
     */
    void extraServiceAddLog(LogOperateAddDTO logOperateAddDTO);
}
