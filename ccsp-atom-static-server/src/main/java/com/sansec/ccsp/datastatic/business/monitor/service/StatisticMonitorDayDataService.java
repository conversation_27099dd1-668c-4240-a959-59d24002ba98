package com.sansec.ccsp.datastatic.business.monitor.service;

import com.sansec.ccsp.datastatic.business.monitor.request.MonitorDataAggDTO;

import java.util.List;

/**
 * @description : 监控统计天数据;(STATISTIC_MONITOR_DAY_DATA)表服务接口
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2024-11-6
 */
public interface StatisticMonitorDayDataService{
    /** 
     * 新增数据
     *
     * @param monitorDataAggDTO
     * @return 实例对象
     */
    void add(MonitorDataAggDTO monitorDataAggDTO);

    /**
     * 添加
     *
     * @param monitorDataAggDTOList 监视器数据列表
     * <AUTHOR>
     * @date 2024/11/07
     */
    void add(List<MonitorDataAggDTO> monitorDataAggDTOList);

}