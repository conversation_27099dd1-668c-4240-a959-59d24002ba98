package com.sansec.ccsp.app.info.request;

import com.sansec.ccsp.app.common.enums.EnumDelFlag;
import com.sansec.ccsp.app.common.enums.EnumSelectCondition;
import com.sansec.ccsp.app.common.wrapperutil.SelectCondition;
import com.sansec.ccsp.common.pattern.CommonPattern;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @Date: 2023/2/18 14:25
 */
@Data
public class AppRegisterPageListDTO extends SecPageDTO {

    /**
     * 应用标识，模糊搜索
     */
    @SelectCondition(EnumSelectCondition.LIKE)
    @Size(max = 50, message = "应用标识长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_CODE, message = "应用标识必须以大小写字母或者数字开头并只能包含大小写字母、数字、特殊字符-_")
    private String appCode;

    /**
     * 应用名称，模糊搜索
     */
    @SelectCondition(EnumSelectCondition.LIKE)
    @Size(max = 50, message = "应用名称长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "应用名称只能包含中文、英文、数字或特殊字符 - _")
    private String appName;

    /**
     * 应用简称，模糊搜索
     */
    @SelectCondition(EnumSelectCondition.LIKE)
    @Size(max = 50, message = "应用简称长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "应用简称只能包含中文、英文、数字或特殊字符 - _")
    private String appShort;

    /**
     * 申请状态
     * 1:未审批; 2:审批通过; 3:审批未通过;
     */
    @SelectCondition(EnumSelectCondition.EQ)
    private Integer appRegisterStatus;

    /**
     * 申请人
     */
    @SelectCondition(EnumSelectCondition.EQ)
    private Long createBy;

    /**
     * 是否查询审批历史
     * 为1时，排除未审批，查询审批通过和审批未通过应用
     */
    private Integer auditHistory;

    @SelectCondition(EnumSelectCondition.EQ)
    private Integer invalidFlag = EnumDelFlag.NORMAL.getKey();
}
