<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sansec.ccsp.extra</groupId>
    <artifactId>ccsp-extra-service</artifactId>
    <version>3.2.15-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>ccsp-extra-service</name>
    <description>ccsp-extra-service</description>

    <parent>
        <groupId>com.sansec</groupId>
        <artifactId>sansec-parent</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>8</java.version>
		<!--jce组件版本-->
		<swxajce-v5.version>********</swxajce-v5.version>
		<crypto-v5.version>********</crypto-v5.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.sansec.db</groupId>
            <artifactId>sansec-db</artifactId>
        </dependency>

        <!--分量合成加解密算法-->
        <dependency>
            <groupId>com.sansec.component.algorithm</groupId>
            <artifactId>sansec-component-algorithm</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sansec.springboot</groupId>
            <artifactId>sansec-springboot-dependencies</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sansec.springboot</groupId>
            <artifactId>sansec-springboot-dependencies</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>Dm8JdbcDriver18</artifactId>
            <version>8.1.1.49</version>
        </dependency>

        <!--Druid-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <!-- LomBok插件 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- commons util -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <!--mysql驱动-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>


        <dependency>
            <groupId>com.sansec.redis</groupId>
            <artifactId>sansec-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <!-- hadoop依赖 -->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-client</artifactId>
            <version>3.3.6</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <version>3.3.6</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
            <version>3.3.6</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- opengauss 支持 -->
        <dependency>
            <groupId>com.gauss.opengauss</groupId>
            <artifactId>jdbc</artifactId>
            <version>5.0.1</version>
        </dependency>
        <!-- kingbase8 支持 -->
        <dependency>
            <groupId>com.kingbase8</groupId>
            <artifactId>kingbase8</artifactId>
            <version>8.2.0</version>
        </dependency>


    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <finalName>${project.artifactId}</finalName>
                </configuration>
                <executions>  <!--执行器 mvn assembly:assembly-->
                    <execution>
                        <id>release</id><!--名字任意 -->
                        <phase>package</phase><!-- 绑定到package生命周期阶段上 -->
                        <goals>
                            <goal>single</goal><!-- 只运行一次 -->
                        </goals>
                        <configuration>
                            <descriptors> <!--描述文件路径-->
                                <descriptor>./assembly/assembly.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.sansec.common.secure.plugin</groupId>
                <artifactId>sansec-secure-plugin</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <configuration>
                    <!--  此处展示最小化的Springboot项目配置示例  具体的插件配置信息，详见 下一小节 配置项说明 -->
                    <!--  待加密的目标jar包路径及名称 -->
                    <target>${project.build.directory}/${project.artifactId}.jar</target>
                    <targetType>SPRINGBOOT</targetType>
                    <!--  true 表示会将项目依赖的外部jar包一并加密，过滤条件按照filters的配置来过滤,false - 表示只加密工程代码，其他依赖jar包不加密 -->
                    <bootLibEncrypt>true</bootLibEncrypt>
                    <filters>
                        <!-- 需要加密的class类的过滤条件，支持通配符 ** * -->
                        <filter>com.sansec.ccsp.**</filter>
                    </filters>
                </configuration>
                <executions>
                    <execution>
                        <!-- 绑定到 package 生命周期 -->
                        <phase>package</phase>
                        <goals>
                            <!-- 插件目标 无需调整 -->
                            <goal>encrypt</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>4.0.5</version>
                <configuration>
                    <dateFormatTimeZone>Asia/Shanghai</dateFormatTimeZone>
                    <dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <generateGitPropertiesFilename>${project.build.outputDirectory}/META-INF/version.txt
                    </generateGitPropertiesFilename>
                    <format>json</format>
                    <gitDescribe>
                        <always>true</always>
                        <tags>false</tags>
                    </gitDescribe>
                    <excludeProperties>
                        <excludeProterty>git.build.host</excludeProterty>
                        <excludeProterty>git.build.user.*</excludeProterty>
                        <excludeProterty>git.remote.origin.url</excludeProterty>
                        <excludeProterty>git.commit.id.describe</excludeProterty>
                        <excludeProterty>git.commit.message.full</excludeProterty>
                    </excludeProperties>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>sonar</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <sonar.jdbc.username>admin</sonar.jdbc.username>
                <sonar.jdbc.password>sansec@2021</sonar.jdbc.password>
                <sonar.host.url>http://**********:9000</sonar.host.url>
                <sonar.projectKey>${project.artifactId}</sonar.projectKey>
                <sonar.projectName>${project.artifactId}</sonar.projectName>
            </properties>
        </profile>
    </profiles>

</project>
