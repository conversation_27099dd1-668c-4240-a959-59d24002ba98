package com.sansec.ccsp.servicemgt.dbmgt.request;

import lombok.Data;

 /**
 * @description : 数据库类型字典表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>aw<PERSON>
 * @date : 2023-7-7
 */
@Data
public class DatabaseTypeDTO{
    /**
     * 数据库类型主键
     */
    private Long id;
    /**
     * 数据库类型名称
     */
    private String databaseTypeCode;
    /**
     * 版本号
     */
    private String verison;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
}