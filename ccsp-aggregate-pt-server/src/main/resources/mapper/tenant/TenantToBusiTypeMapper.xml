<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ccsp.pt.business.tenant.mapper.TenantToBusiTypeMapper">
    <sql id="Base_Column_List">
        ID
        ,TENANT_ID,BUSI_TYPE_ID,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>

    <insert id="batchInsertToRusiType" parameterType="java.util.List">
        INSERT INTO TENANT_TO_BUSI_TYPE
        (
        ID,
        TENANT_ID,
        BUSI_TYPE_ID,
        REMARK,
        CREATE_BY,
        CREATE_TIME,
        UPDATE_BY,
        UPDATE_TIME,
		ENCRYPT_STATUS
        )
        VALUES
        <foreach collection="list" item="toRusiType" index="index" separator=",">
            (
            #{toRusiType.id},
            #{toRusiType.tenantId},
            #{toRusiType.busiTypeId},
            #{toRusiType.remark},
            #{toRusiType.createBy},
            #{toRusiType.createTime},
            #{toRusiType.updateBy},
            #{toRusiType.updateTime},
            #{toRusiType.encryptStatus}
            )
        </foreach>
    </insert>

</mapper>