package com.sansec.ccsp.datastatic.business.logoperate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

@TableName("LOG_OPERATE")
@Data
public class LogOperatePO extends BasePO {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 操作结果
     */
    private String result;
    /**
     * 业务类型ID
     */
    private Long busiTypeId;
    /**
     * 保存时间
     */
    private String createTime;
    /**
     * 请求IP
     */
    private String operIp;
    /**
     * 操作人
     */
    private String operName;
    /**
     * 模块名
     */
    private String moduleName;
    /**
     * 操作内容
     */
    private String operContent;
    /**
     * 完整性保护字段
     */
    private String hmac;
    /**
     * 审计状态;0-审计，1-未审计
     */
    private Integer auditStatus;
    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 日志类型，1-平台侧日志，2-服务侧日志
     */
    private Integer logType;
    /**
     * 备用1
     */
    private String extend1;
    /**
     * 备用2
     */
    private String extend2;
}
