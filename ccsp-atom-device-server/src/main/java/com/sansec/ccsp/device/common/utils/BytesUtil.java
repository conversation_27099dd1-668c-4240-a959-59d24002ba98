package com.sansec.ccsp.device.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.URLDecoder;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;

/**
 * @author: hou<PERSON>xiang
 * @Date: 2021/5/10
 * @time: 14:08
 */
@Slf4j
public class BytesUtil {
    private static String HASH_ALG_SHA256="SHA-256";
    public static byte[] is2ByeteArray(InputStream is) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buff = new byte[100];
        int rc = 0;
        while((rc=is.read(buff, 0, 100))>0) {
            baos.write(buff, 0, rc);
        }

        return baos.toByteArray();
    }

    /**
     * 字节数组转换成java中的字符串
     *
     * @param bytes
     * @return
     */
    public static String byteToString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < bytes.length && bytes[i] != (byte) 0; i++) {
            sb.append((char) (bytes[i]));
        }

        return sb.toString();
    }

    /**
     * 字符串转换成字节流（字节数组）型
     *
     * @param str
     * @return
     */
    public static byte[] stringToByte(String str, int size) {
        int len = (str == null) ? 0 : str.length();
        byte[] bytes = new byte[size];
        if (len > 0) {
            for (int i = 0; i < len; i++) {
                bytes[i] = (byte) (str.charAt(i));
            }
        }

        return bytes;
    }

    public static boolean differByte(byte[] a, byte[] b, int count) {
        boolean flag = false;
        int length = a.length < b.length ? a.length : b.length;
        if (count <= length) {
            for (int i = 0; i < count; i++) {
                if (a[i] != b[i]) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }

    public static byte[] int2bytes(int num) {
        byte[] b = new byte[4];
        for (int i = 0; i < 4; i++) {
            b[i] = (byte) (num >>> (24 - i * 8) & 0xff);
        }
        return b;
    }


    public static byte[] int2bytesReverse(int num) {
        byte[] b = new byte[4];
        for (int i = 0; i < 4; i++) {
            b[3 - i] = (byte) (num >>> (24 - i * 8) & 0xff);
        }
        return b;
    }

    public static int bytes2int(byte[] b) {
        int s = 0;
        for (int i = 0; i < b.length; i++) {
            s = s | ((b[i] & 0xff) << ((b.length - i - 1) * 8));
        }
        return s;
    }

    public static int bytes2intReverse(byte[] b) {
        int s = 0;
        byte[] tmp = new byte[4];
        for (int i = 0; i < 4; i++) {
            tmp[i] = b[3 - i];
        }
        return bytes2int(tmp);
    }

    public static int checkSum(byte[] arr) {
        byte[] tmp = new byte[4];
        int sum = 0;
        for (int i = 0; i < arr.length; i += 4) {
            System.arraycopy(arr, i, tmp, 0, 4);
            int t = BytesUtil.bytes2intReverse(tmp);
            sum ^= t;
        }

        return sum;
    }

    public static int checkSum(byte[] arr, int len) {
        byte[] tmp = new byte[len];
        System.arraycopy(arr, 0, tmp, 0, len);

        return checkSum(tmp);
    }

    public static String getHex(int num) {
        final char[] digits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        int length = 32;
        StringBuilder sb = new StringBuilder();       //定义一个可变长字符串
        char[] result = new char[length];
        String tmp = "0x0";
        do {
            result[--length] = digits[num & 15];
            num >>>= 4;
        } while (num != 0);
        for (int i = length; i < result.length; i++) {
            sb.append((char) (result[i]));
        }
        tmp += new String(sb);
        return tmp;
    }

    /**
     * 以offset开始的四字节数组转换为整型值（小序）
     *
     * @param bytes  字节数组缓冲区
     * @param offset 起始位置
     * @return 整型值
     */
    public static int bytes2int(byte[] bytes, int offset) {
        int num = 0;
        for (int i = 0; i < 4; i++) {
            num += (0x000000ffL & (bytes[i + offset])) << (i * 8);
        }

        return num;
    }

    /**
     * 截取字节数组
     *
     * @param bytes  数组
     * @param srcPos 起始位置
     * @param length 长度
     * @return 返回
     */
    public static byte[] subbytes(byte[] bytes, int srcPos, int length) {
        byte[] buf = new byte[length];
        System.arraycopy(bytes, srcPos, buf, 0, length);

        return buf;
    }

    /**
     * 截取字节数组
     *
     * @param bytes  数组
     * @return 返回
     */
    public static byte[] subTrueBytes(byte[] bytes) {

        int src = 0;
        for (int i = 0; i < bytes.length && bytes[i] != (byte) 0; i++) {
            src = i;
        }

        return  subbytes(bytes,0,src+1);
    }

    /**
     * 合并2个字节数组
     *
     * @param byte1 数组1
     * @param byte2 数组2
     * @return 返回
     */
    public static byte[] combineBytes(byte[] byte1, byte[] byte2) {
        byte[] result = new byte[byte1.length + byte2.length];
        System.arraycopy(byte1, 0, result, 0, byte1.length);
        System.arraycopy(byte2, 0, result, byte1.length, byte2.length);
        return result;

    }

    /**
     * 文件转化为字节数组
     * @param fileName
     * @return
     * @throws IOException
     */
    public static byte[] getFileValue(String fileName) throws IOException {
        FileInputStream in=null;
        ByteArrayOutputStream outputStream;
        try {
            outputStream = new ByteArrayOutputStream();
            in = new FileInputStream(fileName);
            byte[] buffer = new byte[1024];
            int len = 0;

            while ((len = in.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
        } finally {
            if (in != null) {
                in.close();
            }
        }

        return outputStream.toByteArray();

    }

    /**
     * byte数组转换16进制字符串
     * @param data
     * @return
     */
    public static String byte2Hex(byte[] data) {
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < data.length; i++) {

            String hex = Integer.toHexString(data[i] & 0xFF);

            if (hex.length() == 1) {

                hex = '0' + hex;

            }

            buffer.append(hex.toUpperCase());
        }
        return buffer.toString();
    }

    /**
     * 16进制字符串转byte数组
     * @param data
     * @return
     */
    public static byte[] hex2Byte(String data){
        if(data == null || data.trim().length() == 0 || data.trim().length()%2 != 0){
            throw new IllegalArgumentException("Data cannot be null and length must be even");
        }
        ByteBuffer buffer = ByteBuffer.allocate(data.length()/2);
        for(int index=0;index<data.length();index+=2){
            byte h = (byte)Integer.parseInt(data.substring(index,index+2),16);
            buffer.put(h);
        }
        return buffer.array();
    }

    public static String byte2String(byte d) {
        return new String(new byte[]{d}, StandardCharsets.UTF_8);
    }

    public static String byte2String(byte[] d) {
        return new String(d, StandardCharsets.UTF_8);
    }

    public static byte[] string2Byte(String d) {
        return d.getBytes(StandardCharsets.UTF_8);
    }
    public static String byte2StringTrimO(byte[] buffer){
        try {
            int length = 0;
            for (int i = buffer.length - 1; i > -1; i--) {
                if (buffer[i] != 0) {
                    length = i+1;
                    break;
                }
            }
            return new String(buffer, 0, length, StandardCharsets.UTF_8);
        } catch (Exception e) {
            return "";
        }
    }

    public static byte[] getBytes(byte[] data){
        return data==null?null: Arrays.copyOfRange(data, 0, data.length);
    }

    public static int[] getInts(int[] data){
        return data==null?null:Arrays.copyOfRange(data, 0, data.length);
    }

    public static String utf2String(String data){
        try {
            String tmp = data.replaceAll("\\\\x","%");
            return URLDecoder.decode(tmp, String.valueOf(StandardCharsets.UTF_8));
        } catch (UnsupportedEncodingException e) {
            log.error("utf2String Exception message" + e.getMessage());
            return data;
        }
    }

    /**
     * 利用java原生的类实现SHA256加密
     *
     * @param str
     * @return
     */
    public static String getSHA256(String str) {
        MessageDigest messageDigest;
        String encodestr = "";
        try {
            messageDigest = MessageDigest.getInstance(HASH_ALG_SHA256);
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodestr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            log.error("getSHA256 Exception message" + e.getMessage());
            return str;
        }
        return encodestr;
    }

    /**
     * 利用java原生的类实现SHA256加密
     *
     * @param text
     * @return
     */
    public static String getSHA256(byte[] text) {
        MessageDigest messageDigest;
        String encodestr = "";
        try {
            messageDigest = MessageDigest.getInstance(HASH_ALG_SHA256);
            messageDigest.update(text);
            encodestr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            log.error("getSHA256 param byte Exception message" + e.getMessage());
            return new String(text);
        }
        return encodestr;
    }

    /**
     * 利用java原生的类实现SHA256加密
     *
     * @param text
     * @return
     */
    public static String getSHA256Base64(byte[] text) {
        MessageDigest messageDigest;
        String encodestr = "";
        try {
            messageDigest = MessageDigest.getInstance(HASH_ALG_SHA256);
            messageDigest.update(text);
            encodestr = Base64.getEncoder().encodeToString(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            log.error("getSHA256Base64 Exception message" + e.getMessage());
            return new String(text);
        }
        return encodestr;
    }
}
