package com.sansec.ccsp.common.business.dic.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.common.business.dic.entity.DicKeylifeattributePO;
import com.sansec.ccsp.common.dic.request.DicKeylifeattributeDTO;
import com.sansec.ccsp.common.dic.response.DicKeylifeattributeVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

 /**
 * @Description: 密钥生命周期属性字典表;(DIC_KEYLIFEATTRIBUTE)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Mapper(componentModel = "spring")
public interface DicKeylifeattributeConvert{
    /**
     * dtoToPo
     * @param dicKeylifeattributeDTO
     * @return
     */
    @Mappings({})
    DicKeylifeattributePO dtoToPo(DicKeylifeattributeDTO dicKeylifeattributeDTO);

    /**
     * poToDto
     * @param dicKeylifeattributePO
     * @return
     */
    DicKeylifeattributeDTO poToDto(DicKeylifeattributePO dicKeylifeattributePO);

    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<DicKeylifeattributeDTO> poToDto(List<DicKeylifeattributePO> list);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<DicKeylifeattributeVO> pagePOToSecPageVOPage(IPage<DicKeylifeattributePO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<DicKeylifeattributeVO> convert(List<DicKeylifeattributePO> list);

    @Mappings({})
    DicKeylifeattributeVO convertVo(DicKeylifeattributePO request);
}
