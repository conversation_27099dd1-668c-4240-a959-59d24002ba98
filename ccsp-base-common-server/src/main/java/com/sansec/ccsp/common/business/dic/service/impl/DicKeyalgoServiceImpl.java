package com.sansec.ccsp.common.business.dic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.common.business.dic.convert.DicKeyalgoConvert;
import com.sansec.ccsp.common.business.dic.entity.DicKeyalgoPO;
import com.sansec.ccsp.common.business.dic.mapper.DicKeyalgoMapper;
import com.sansec.ccsp.common.business.dic.service.DicKeyalgoService;
import com.sansec.ccsp.common.dic.request.DicKeyalgoDTO;
import com.sansec.ccsp.common.dic.request.DicKeyalgoPageDTO;
import com.sansec.ccsp.common.dic.response.DicKeyalgoVO;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
 /**
 * @Description: 密钥算法字典表;(DIC_KEYALGO)表服务实现类
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Service
@Slf4j
public class DicKeyalgoServiceImpl extends ServiceImpl<DicKeyalgoMapper, DicKeyalgoPO> implements DicKeyalgoService{
    @Resource
    private DicKeyalgoConvert dicKeyalgoConvert;

    /**
     * 分页查询
     *
     * @param dicKeyalgoPageDTO 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<DicKeyalgoVO>> find(DicKeyalgoPageDTO dicKeyalgoPageDTO){
        QueryWrapper<DicKeyalgoPO> queryWrapper = Wrappers.query();
        IPage<DicKeyalgoPO> page = new Page<>(dicKeyalgoPageDTO.getPageNum(),dicKeyalgoPageDTO.getPageSize());
        IPage<DicKeyalgoPO> dicKeyalgoPOPage = baseMapper.selectPage(page, queryWrapper);
        SecPageVO<DicKeyalgoVO> dicKeyalgoPageVO = dicKeyalgoConvert.pagePOToSecPageVOPage(dicKeyalgoPOPage);
        return ResultUtil.ok(dicKeyalgoPageVO);
    }

    /**
     * 新增数据
     *
     * @param dicKeyalgoDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> add(DicKeyalgoDTO dicKeyalgoDTO){
        DicKeyalgoPO dicKeyalgoPO = dicKeyalgoConvert.dtoToPo(dicKeyalgoDTO);
        dicKeyalgoPO.setId(IdGenerator.ins().generator());
        dicKeyalgoPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.insert(dicKeyalgoPO);
        return ResultUtil.ok();
    }

    /**
     * 更新数据
     *
     * @param dicKeyalgoDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(DicKeyalgoDTO dicKeyalgoDTO){
        DicKeyalgoPO dicKeyalgoPO = dicKeyalgoConvert.dtoToPo(dicKeyalgoDTO);
        dicKeyalgoPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.updateById(dicKeyalgoPO);
        return ResultUtil.ok();
    }

    /**
     * 通过主键删除数据
     *
     * @param dicKeyalgoDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> deleteById(DicKeyalgoDTO dicKeyalgoDTO){
        baseMapper.deleteById(dicKeyalgoDTO.getId());
        return ResultUtil.ok();
    }
}
