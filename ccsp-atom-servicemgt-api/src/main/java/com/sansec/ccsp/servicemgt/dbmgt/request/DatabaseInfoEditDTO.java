package com.sansec.ccsp.servicemgt.dbmgt.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
* @description : 数据库信息表;
* <AUTHOR> sf.jin
* @date : 2023-7-5
*/
@Data
public class DatabaseInfoEditDTO {
   /**
    * 数据库id
    */
   @NotNull(message = "主键不可为空")
   private Long id;
   /**
    * 数据库类型id
    */
   private Long databaseTypeId;
   /**
    * 数据库名称
    */
   @NotBlank(message = "数据库名称不能为空")
   @Pattern(regexp = CommonPattern.COMMON_NAME, message = "数据库名称只能包含中文、英文、数字或特殊字符-_")
   @Size(max = 50, message = "数据库名称长度限制50")
   private String databaseName;

   /*
   数据库的ip端口信息
    */
   List<DatabaseByIpPortDTO> ipPortInfoList;

   /**
    * 数据库映射ip和端口的信息
    */
   List<DatabaseByIpPortDTO> mapIpPortInfoList;

   /**
    * 数据库实例名称
    */
   private String caseName;
   /**
    * 数据库管理员用户
    */
   private String adminUser;
   /**
    * 数据库密码
    */
   private String adminAuthCode;
   /**
    * 数据库是否自动创建 1是 0否
    */
   private Integer autoCreated;
   /**
    * 备注
    */
   @Size(max = 100, message = "备注长度限制100")
   @Pattern(regexp = CommonPattern.COMMON_DESC, message = "备注请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
   private String remark;
   /**
    * 创建人
    */
   private Long createBy;
   /**
    * 创建时间
    */
   private String createTime;
   /**
    * 更新人
    */
   private Long updateBy;
   /**
    * 更新时间
    */
   private String updateTime;
}