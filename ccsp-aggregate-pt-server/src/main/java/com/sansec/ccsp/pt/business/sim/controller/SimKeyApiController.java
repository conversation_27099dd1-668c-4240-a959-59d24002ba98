package com.sansec.ccsp.pt.business.sim.controller;

import com.sansec.ccsp.pt.business.statistic.log.aspect.OperateManageLog;
import com.sansec.ccsp.pt.business.sim.annotation.SimOperationLog;
import com.sansec.ccsp.pt.business.sim.constant.SimConstant;
import com.sansec.ccsp.pt.business.sim.request.*;
import com.sansec.ccsp.pt.business.sim.service.SimKeyCertService;
import com.sansec.ccsp.pt.business.sim.service.SimKeyUserService;
import com.sansec.ccsp.security.annotation.IgnoreToken;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> zhaozhuang
 * @Description: SimKey和APP交互接口控制层
 * @Date: 2023-9-19
 */
@RestController
@RequestMapping("/api/simKey")
@Validated
public class SimKeyApiController {

    @Resource
    private SimKeyUserService simKeyUserService;
    @Resource
    private SimKeyCertService simKeyCertService;

    /**
     * 接收签名结果
     * @param simKeySignature
     * @return
     */
    @SimOperationLog(operType = SimConstant.OperType.OPER_TYPE_SIGNVERIFY, funcType = SimConstant.FuncType.PLATFORM_INTERFACE, flowType = SimConstant.FlowType.SIM_KEY,funcName = "验证签名")
    @PostMapping("/receiveSignData")
    @IgnoreToken
    public SecRestResponse<Object> receiveSignData(@RequestBody @Validated SimKeySignature simKeySignature){
        return simKeyUserService.receiveSignData(simKeySignature);
    }

    /**
     * SIMKEY用户注册
     *
     * @param simKeyUserRegistDTO
     * @return
     */
    @SimOperationLog(operType = SimConstant.OperType.OPER_TYPE_STATUS, funcType = SimConstant.FuncType.PLATFORM_INTERFACE, flowType = SimConstant.FlowType.SIM_KEY,funcName = "APP用户注册")
    @PostMapping("/registAccount")
    @IgnoreToken
    public SecRestResponse<Object> registAccount(@RequestBody @Validated SimKeyUserRegistDTO simKeyUserRegistDTO) {
        return simKeyUserService.registAccount(simKeyUserRegistDTO);
    }

    /**
     * SIMKEY用户开通
     *
     * @param simKeyPhoneDTO
     * @return
     */
    @SimOperationLog(operType = SimConstant.OperType.OPER_TYPE_STATUS, funcType = SimConstant.FuncType.PLATFORM_INTERFACE, flowType = SimConstant.FlowType.SIM_KEY,funcName = "开通SIMKEY功能")
    @PostMapping("/openAccount")
    @IgnoreToken
    public SecRestResponse<Object> openAccount(@RequestBody @Validated SimKeyPhoneDTO simKeyPhoneDTO) {
        return simKeyUserService.openAccount(simKeyPhoneDTO.getMobilePhone());
    }

    /**
     * SIMKEY证书接口  生成证书、更新证书、下载证书、注销证书
     *
     * @param simKeyMobileCertDTO
     * @return
     */
    @SimOperationLog(operType = SimConstant.OperType.OPER_TYPE_CERT, funcType = SimConstant.FuncType.PLATFORM_INTERFACE, flowType = SimConstant.FlowType.SIM_KEY,funcName = "管理证书")
    @PostMapping("/mobileCert")
    @IgnoreToken
    public SecRestResponse<Object> mobileCert(@RequestBody @Validated SimKeyMobileCertDTO simKeyMobileCertDTO) {
        return simKeyCertService.mobileCert(simKeyMobileCertDTO);
    }

    /**
     * SIMKEY查询业务开通状态
     *
     * @param simKeyPhoneDTO
     * @return 实例对象  0未开通   1开通
     */
    @SimOperationLog(operType = SimConstant.OperType.OPER_TYPE_STATUS, funcType = SimConstant.FuncType.PLATFORM_INTERFACE, flowType = SimConstant.FlowType.SIM_KEY,funcName = "查询业务开通状态")
    @PostMapping("/queryStatus")
    @IgnoreToken
    public SecRestResponse<Object> queryStatus(@RequestBody @Validated SimKeyPhoneDTO simKeyPhoneDTO) {
        return simKeyUserService.queryStatus(simKeyPhoneDTO.getMobilePhone());
    }

    /**
     * APP登录SIMKEY
     *
     * @param simKeyUserLoginDTO
     * @return 实例对象
     */
    @SimOperationLog(operType = SimConstant.OperType.OPER_TYPE_STATUS, funcType = SimConstant.FuncType.PLATFORM_INTERFACE, flowType = SimConstant.FlowType.SIM_KEY,funcName = "APP用户登录")
    @PostMapping("/loginAccount")
    @IgnoreToken
    public SecRestResponse<Object> loginAccount(@RequestBody @Validated SimKeyUserLoginDTO simKeyUserLoginDTO){
        return simKeyUserService.loginAccount(simKeyUserLoginDTO);
    }

    /**
     * 根据手机号查询证书详情
     *
     * @param simKeyPhoneDTO
     * @return 实例对象
     */
    @OperateManageLog(module = "SIMKEY证书详情查询", desc = "SIMKEY证书详情查询")
    @PostMapping("/queryCertDetail")
    @IgnoreToken
    public SecRestResponse<Object> queryCertDetail(@RequestBody @Validated SimKeyPhoneDTO simKeyPhoneDTO){
        return simKeyCertService.queryCertDetail(simKeyPhoneDTO.getMobilePhone());
    }

}
