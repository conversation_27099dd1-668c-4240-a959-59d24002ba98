package com.sansec.ccsp.common.dic.response;

import lombok.Data;

 /**
 * @Description: 密钥长度字典表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>aw<PERSON>
 * @Date: 2023-2-18
 */
@Data
public class DicKeylenVO{
    /**
     *
     */
    private Long id;
    /**
     * 算法名称
     */
    private String dName;
    /**
     * 算法长度
     */
    private Integer dValue;
    /**
     * 是否默认 1默认 0不默认
     */
    private Integer defaultFlag;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}
