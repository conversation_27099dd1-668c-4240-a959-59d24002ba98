package com.sansec.ccsp.servicemgt.business.servicegroup.api;

import com.sansec.ccsp.servicemgt.business.servicegroup.service.ServiceGroupToGroupService;
import com.sansec.ccsp.servicemgt.servicegroup.api.ServiceGroupToGroupApi;
import com.sansec.ccsp.servicemgt.servicegroup.request.*;
import com.sansec.ccsp.servicemgt.servicegroup.response.*;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 服务组管理层接口实现
 * @Date: 2023年2月20日
 */
@Validated
@RestController
public class ServiceGroupToGroupApiImpl implements ServiceGroupToGroupApi {

    @Resource
    private ServiceGroupToGroupService serviceGroupToGroupService;

    @Override
    public SecRestResponse<Object> addKmsGroup(ServiceGroupToGroupAddDTO serviceGroupToGroupAddDTO) {
        serviceGroupToGroupService.addKmsGroup(serviceGroupToGroupAddDTO);
        return ResultUtil.ok();
    }

    /**
     * 根据服务组ID获取Kms服务组
     *
     * @param serviceGroupToGroupGetDTO
     * @return
     */
    @Override
    public SecRestResponse<ServiceGroupToGroupVO> getKmsGroup(ServiceGroupToGroupGetDTO serviceGroupToGroupGetDTO) {
        return serviceGroupToGroupService.getKmsGroup(serviceGroupToGroupGetDTO);
    }

    /**
     * 根据服务组ID获取Kms服务组
     *
     * @param kmsServiceGroupId
     * @return
     */
    @Override
    public SecRestResponse<List<ServiceGroupToGroupVO>> getServiceGroupByKmsGroupId(Long kmsServiceGroupId) {
        return serviceGroupToGroupService.getServiceGroupByKmsGroupId(kmsServiceGroupId);
    }

    @Override
    public SecRestResponse<Object> deleteToKmsGroup(ServiceGroupToGroupGetDTO serviceGroupToGroupGetDTO) {
        return serviceGroupToGroupService.deleteToKmsGroup(serviceGroupToGroupGetDTO);
    }


}
