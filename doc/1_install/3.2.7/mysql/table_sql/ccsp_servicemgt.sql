/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 100608
 Source Host           : ************:3306
 Source Schema         : ccsp_servicemgt2

 Target Server Type    : MySQL
 Target Server Version : 100608
 File Encoding         : 65001

 Date: 14/07/2023 20:49:33
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for database_info
-- ----------------------------
DROP TABLE IF EXISTS `database_info`;
CREATE TABLE `database_info`  (
  `ID` bigint NOT NULL COMMENT '数据库id',
  `DATABASE_TYPE_ID` bigint NOT NULL COMMENT '数据库类型id',
  `DATABASE_NAME` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据库名称',
  `DATABASE_IP` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库IP',
  `DATABASE_PORT` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库端口',
  `DATABASE_MAP_IP` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库映射IP',
  `DATABASE_MAP_PORT` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库映射端口',
  `CASE_NAME` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库实例名称',
  `ADMIN_USER` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库管理员用户',
  `ADMIN_AUTH_CODE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库密码',
  `AUTO_CREATED` int NULL DEFAULT 0 COMMENT '数据库是否自动创建 1是 0否',
  `HMAC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性校验',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据库信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of database_info
-- ----------------------------

-- ----------------------------
-- Table structure for database_minimum_unit
-- ----------------------------
DROP TABLE IF EXISTS `database_minimum_unit`;
CREATE TABLE `database_minimum_unit`  (
  `ID` bigint NOT NULL COMMENT 'id',
  `DATABASE_UNIT_NAME` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '单元名称',
  `SERVICE_TYPE_ID` bigint NOT NULL COMMENT '服务类型id',
  `DATABASE_ID` bigint NOT NULL COMMENT '数据库id',
  `UNIT_USER` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户名',
  `AUTH_CODE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户密码',
  `HMAC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性校验',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据库最小单元' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of database_minimum_unit
-- ----------------------------

-- ----------------------------
-- Table structure for database_unit_to_service_group
-- ----------------------------
DROP TABLE IF EXISTS `database_unit_to_service_group`;
CREATE TABLE `database_unit_to_service_group`  (
  `ID` bigint NOT NULL COMMENT 'ID',
  `DATABASE_UNIT_ID` bigint NOT NULL COMMENT '数据库单元ID',
  `DATABASE_ID` bigint NOT NULL COMMENT '数据库ID',
  `SERVICE_GROUP_ID` bigint NOT NULL COMMENT '服务组ID',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据库单元和服务组的关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of database_unit_to_service_group
-- ----------------------------

-- ----------------------------
-- Table structure for dic_busi_type_to_database
-- ----------------------------
DROP TABLE IF EXISTS `dic_busi_type_to_database`;
CREATE TABLE `dic_busi_type_to_database`  (
  `ID` bigint NOT NULL COMMENT '主键',
  `SERVICE_TYPE_ID` bigint NOT NULL COMMENT '业务类型ID',
  `DATABASE_UNIT_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据库实例默认名称',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '业务服务对应数据库实例名称' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for dic_database_type
-- ----------------------------
DROP TABLE IF EXISTS `dic_database_type`;
CREATE TABLE `dic_database_type`  (
  `ID` bigint NOT NULL COMMENT '数据库类型主键',
  `DATABASE_TYPE_CODE` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库类型名称',
  `VERISON` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本号',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据库类型字典表' ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Table structure for service_group
-- ----------------------------
DROP TABLE IF EXISTS `service_group`;
CREATE TABLE `service_group`  (
  `SERVICE_GROUP_ID` bigint NOT NULL COMMENT '服务组ID',
  `SERVICE_GROUP_CODE` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务组标识',
  `SERVICE_GROUP_NAME` varchar(270) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务组名称',
  `SERVICE_GROUP_TYPE` int NULL DEFAULT NULL COMMENT '服务组类型',
  `SERVICE_GROUP_STATUS` int NOT NULL COMMENT '服务组状态:1:创建中；2：运行中；3：创建失败',
  `TENANT_ID` bigint NULL DEFAULT NULL COMMENT '租户ID',
  `IS_SHARE` int NOT NULL DEFAULT 0 COMMENT '是否共享；0：否；1：是',
  `INVALID_FLAG` int NULL DEFAULT 0 COMMENT '是否作废；0：否；1：是',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`SERVICE_GROUP_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '服务组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of service_group
-- ----------------------------

-- ----------------------------
-- Table structure for service_group_to_busi_type
-- ----------------------------
DROP TABLE IF EXISTS `service_group_to_busi_type`;
CREATE TABLE `service_group_to_busi_type`  (
  `ID` bigint NOT NULL COMMENT '主键',
  `SERVICE_GROUP_ID` bigint NOT NULL COMMENT '服务组ID',
  `BUSI_TYPE_ID` bigint NOT NULL COMMENT '业务类型ID',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '服务组和业务类型关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of service_group_to_busi_type
-- ----------------------------

-- ----------------------------
-- Table structure for service_info
-- ----------------------------
DROP TABLE IF EXISTS `service_info`;
CREATE TABLE `service_info`  (
  `ID` bigint NOT NULL COMMENT 'id',
  `SERVICE_TYPE_ID` bigint NOT NULL COMMENT '服务类型',
  `SERVICE_NAME` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务名称',
  `DEVICE_GROUP_ID` bigint NULL DEFAULT NULL COMMENT '设备组id',
  `SERVICE_GROUP_ID` bigint NULL DEFAULT NULL COMMENT '服务组id',
  `TENANT_ID` bigint NOT NULL COMMENT '租户id',
  `OPER_STATUS` int NULL DEFAULT NULL COMMENT '1运行中（操作完成）、2初始化中、3未运行',
  `RUN_STATUS` int UNSIGNED NOT NULL DEFAULT 2 COMMENT '1功能运行正常 2.功能运行异常 3启动中 4停止中',
  `MGT_IP` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '管理IP',
  `MGT_PORT` int NULL DEFAULT NULL COMMENT '管理端口',
  `MGT_GATEWAY_IP` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '管理网关IP',
  `MGT_GATEWAY_PORT` int NULL DEFAULT NULL COMMENT '管理网关端口',
  `BUSI_IP` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务IP',
  `BUSI_PORT` int NULL DEFAULT NULL COMMENT '业务端口',
  `BUSI_GATEWAY_IP` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务网关IP',
  `BUSI_GATEWAY_PORT` int NULL DEFAULT NULL COMMENT '业务网关端口',
  `REMOTE_IP` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '管控IP',
  `REMOTE_PORT` int NULL DEFAULT NULL COMMENT '管控端口',
  `EXPAND_PORT` int NULL DEFAULT NULL COMMENT '扩展端口',
  `TCP_PORT` int NULL DEFAULT NULL COMMENT 'TCP/UDP端口',
  `GATEWAY_ID` bigint NULL DEFAULT NULL COMMENT 'API网关表主键',
  `ROUTE_ID` bigint NULL DEFAULT NULL COMMENT '路由表主键',
  `IS_ACTIVE_STANDBY` int NOT NULL DEFAULT 1 COMMENT '是否为主机或者备机  1主机 2备机',
  `DB_CREATED` int NULL DEFAULT NULL COMMENT '数据库是否已创建 1是 0否',
  `INVALID_FLAG` int NOT NULL DEFAULT 0 COMMENT '是否作废;默认为0',
  `HMAC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '完整性校验',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '服务信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of service_info
-- ----------------------------

-- ----------------------------
-- Table structure for service_type
-- ----------------------------
DROP TABLE IF EXISTS `service_type`;
CREATE TABLE `service_type`  (
  `ID` bigint NOT NULL COMMENT '服务类型主键',
  `SERVICE_TYPE_NAME` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务类型名称',
  `SERVICE_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务简称',
  `PARENT_ID` bigint NOT NULL COMMENT '服务信息父类型',
  `SERVICE_CREATE_TYPE` int NULL DEFAULT NULL COMMENT '服务创建类型 1手动创建 2自动创建',
  `SERVICE_USE_TYPE` int NULL DEFAULT NULL COMMENT '1独享 2共享',
  `MGT_PORT` int NULL DEFAULT NULL COMMENT '内网管理端口',
  `MGT_GATEWAY_PORT` int NULL DEFAULT NULL COMMENT '管理网关端口',
  `BUSI_PORT` int NULL DEFAULT NULL COMMENT '业务端口',
  `BUSI_GATEWAY_PORT` int NULL DEFAULT NULL COMMENT '业务网关端口',
  `REMOTE_PORT` int UNSIGNED NULL DEFAULT 18086 COMMENT '管控端口',
  `EXPAND_PORT` int NULL DEFAULT NULL COMMENT '扩展端口',
  `TCP_PORT` int NULL DEFAULT NULL COMMENT 'TCP/UDP端口',
  `MONITOR_PORT` int NULL DEFAULT NULL COMMENT '监控组件端口',
  `IS_RESTART` int NULL DEFAULT 1 COMMENT '是否允许启动停止 1允许 2不允许',
  `IS_CREATE_MGT_ROUTE` int NULL DEFAULT 1 COMMENT '是否创建管理路由 1创建 2不创建',
  `IS_CREATE_BUSI_ROUTE` int NULL DEFAULT 1 COMMENT '是否创建业务路由 1创建 2不创建',
  `IS_CREATE_LOG_PLUGIN` int NULL DEFAULT 1 COMMENT '是否创建日志插件 1创建 2不创建',
  `LOG_PLUGIN_FORMAT` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志插件日志格式',
  `IS_MONITOR_DEVICE` int NULL DEFAULT 2 COMMENT '是否监控服务设备 1是 2否',
  `CONFIG_DEVICE_TYPE` int NULL DEFAULT 2 COMMENT '分配设备类型 1不分配 2自动分配 3按设备类型下拉选择',
  `AVAILABLE_DEVICE_TYPE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '服务类型可用设备类型 空代表全部可用 不全部可用逗号分隔',
  `CONNECT_TIME` int NULL DEFAULT NULL COMMENT '连接超时时间',
  `SEND_TIME` int NULL DEFAULT NULL COMMENT '发送超时时间',
  `READ_TIME` int NULL DEFAULT NULL COMMENT '接收超时时间',
  `IMAGE_ID` bigint NULL DEFAULT NULL COMMENT '镜像ID',
  `SERVICE_PATH` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '服务前缀',
  `DB_UPDATE_CONFIG_FLAG` int NULL DEFAULT NULL COMMENT '平台是否需要更新服务数据库配置 1是 0否',
  `DB_COMMON` int NULL DEFAULT NULL COMMENT '服务是否共用数据库 1是 0否',
  `DB_TENANT_COMMON` int NULL DEFAULT NULL COMMENT '服务是否同租户下共用数据库 1是 0否',
  `DB_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库实例名称',
  `SERVICE_CLASS` int NULL DEFAULT NULL COMMENT '服务分类 1基础服务 2增值服务 3.组件服务',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '服务类型字典表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for service_type_to_busi_type
-- ----------------------------
DROP TABLE IF EXISTS `service_type_to_busi_type`;
CREATE TABLE `service_type_to_busi_type`
(
    `ID`              bigint NOT NULL COMMENT 'id',
    `SERVICE_TYPE_ID` bigint NOT NULL COMMENT '服务类型ID',
    `BUSI_TYPE_ID`    bigint NOT NULL COMMENT '业务类型ID',
    `BUSI_TYPE_NAME`  varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务名称',
  `REMARK` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `CREATE_BY` bigint NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` bigint NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '服务类型和业务类型关联表' ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
        `JOB_ID`          bigint                                                 NOT NULL COMMENT '任务号',
        `JOB_NAME`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '任务名称',
        `JOB_GROUP`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '任务组名',
        `SERVER_ID`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务模块',
        `METHOD_URL`      varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调用接口',
        `JSON_PARAM`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'json格式参数',
        `CRON_EXPRESSION` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'CRON执行表达式',
        `MISFIRE_POLICY`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '3' COMMENT '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
        `CONCURRENT`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）',
        `JOB_STATUS`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
        `CREATED_BY`      bigint NULL DEFAULT NULL COMMENT '创建人',
        `CREATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
        `UPDATED_BY`      bigint NULL DEFAULT NULL COMMENT '更新人',
        `UPDATE_TIME`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
        `REMARK`          varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
        PRIMARY KEY (`JOB_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '定时任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job
-- ----------------------------

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
        `JOB_LOG_ID`     bigint                                                NOT NULL COMMENT '任务日志ID',
        `JOB_ID`         bigint                                                NOT NULL COMMENT '任务ID',
        `JOB_MESSAGE`    varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志信息',
        `STATUS`         varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '执行状态（0失败 1正常）',
        `EXCEPTION_INFO` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '异常信息',
        `CREATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建时间',
        `TRIGGER_TIME`   bigint NULL DEFAULT NULL COMMENT '触发时间',
        PRIMARY KEY (`JOB_LOG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '定时任务执行日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS `sys_task`;
CREATE TABLE `sys_task`  (
     `TASK_ID`     bigint                                                 NOT NULL COMMENT '任务号',
     `TASK_NAME`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '任务名称',
     `TASK_GROUP`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  NOT NULL COMMENT '任务组名;执行任务串行',
     `SERVER_ID`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务模块',
     `METHOD_URL`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '调用接口',
     `JSON_PARAM`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'json格式参数',
     `TASK_STATUS` int                                                    NOT NULL DEFAULT 0 COMMENT '状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时)',
     `CREATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间',
     `UPDATE_TIME` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '更新时间',
     `REMARK`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
     `TIMEOUT`     int NULL DEFAULT NULL COMMENT '超时时间;单位秒',
     `START_TIME`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '开始时间',
     `END_TIME`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '结束时间',
     `POLICY`      varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   NOT NULL DEFAULT '0' COMMENT '是否允许重复执行;0-不允许，1允许',
     PRIMARY KEY (`TASK_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '异步任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_task
-- ----------------------------

-- ----------------------------
-- Table structure for sys_task_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_task_log`;
CREATE TABLE `sys_task_log`  (
     `TASK_LOG_ID`    bigint                                               NOT NULL COMMENT '任务日志ID',
     `TASK_ID`        bigint                                               NOT NULL COMMENT '任务ID',
     `TASK_MESSAGE`   varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志信息',
     `STATUS`         varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '执行状态（0失败 1正常）',
     `EXCEPTION_INFO` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '异常信息',
     `CREATE_TIME`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建时间;单位毫秒',
     `TRIGGER_TIME`   bigint NULL DEFAULT NULL COMMENT '触发时间;任务服务上送',
     PRIMARY KEY (`TASK_LOG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '异步任务执行日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_task_log
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
