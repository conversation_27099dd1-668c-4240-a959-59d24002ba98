-- CCSP_DEVICE.DEVICE_API definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_API;

CREATE TABLE CCSP_DEVICE.DEVICE_API (
	API_ID INT8 NOT NULL,
	API_NAME VARCHAR(150) NOT NULL,
	DEVICE_TYPE_ID INT8 NOT NULL,
	INTERACTION_SERIAL_NUMBER VARCHAR(100) NOT NULL,
	API_TEMPLATE_ID INT8 NOT NULL,
	API_SERIAL_NUMBER VARCHAR(100) NOT NULL,
	CONTEXT_PATH VARCHAR(100) NOT NULL,
	API_PATH VARCHAR(200) NOT NULL,
	DEFAULT_FLAG INT4 NOT NULL,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_API_PRIMARY PRIMARY KEY (API_ID)
);


-- CCSP_DEVICE.DEVICE_API_RECORD definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_API_RECORD;

CREATE TABLE CCSP_DEVICE.DEVICE_API_RECORD (
	ID INT8 NOT NULL,
	DEVIICE_ID INT8 NOT NULL,
	API_NAME VARCHAR(150) NOT NULL,
	REQUESET_ID VARCHAR(100) NOT NULL,
	API_ID VARCHAR(255),
	METHOD_NAME VARCHAR(150) NOT NULL,
	INTERFACE_URL VARCHAR(200) NOT NULL,
	STATUS VARCHAR(100) NOT NULL,
	NOTIFY_STATUS VARCHAR(50),
	NOTIFY_INFO VARCHAR(100),
	OPER_STATUS VARCHAR(50),
	OPER_INFO VARCHAR(100),
	REQINFO VARCHAR(1000),
	RESINFO VARCHAR(1000),
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_API_RECORD_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DEVICE_BUSITYPE definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_BUSITYPE;

CREATE TABLE CCSP_DEVICE.DEVICE_BUSITYPE (
	ID INT8 NOT NULL,
	DEVICE_TYPE_ID INT8 NOT NULL,
	BUSI_TYPE_ID INT8 NOT NULL,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_BUSITYPE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DEVICE_GROUP definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_GROUP;

CREATE TABLE CCSP_DEVICE.DEVICE_GROUP (
	DEVICE_GROUP_ID INT8 NOT NULL,
	DEVICE_GROUP_NAME VARCHAR(270) NOT NULL,
	DEVICE_GROUP_TYPE INT4 NOT NULL,
	REGION_ID INT8,
	TENANT_ID INT8,
	IS_REST VARCHAR(255),
	NET_PRO INT4,
	DEVICE_TYPE_ID INT8,
	IS_SHARE INT4,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_GROUP_PRIMARY PRIMARY KEY (DEVICE_GROUP_ID)
);


-- CCSP_DEVICE.DEVICE_GROUP_TO_BUSI_TYPE definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_GROUP_TO_BUSI_TYPE;

CREATE TABLE CCSP_DEVICE.DEVICE_GROUP_TO_BUSI_TYPE (
	ID INT8 NOT NULL,
	DEVICE_GROUP_ID INT8 NOT NULL,
	BUSI_TYPE_ID INT8 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_GROUP_TO_BUSI_TYPE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DEVICE_INFO definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_INFO;

CREATE TABLE CCSP_DEVICE.DEVICE_INFO (
	DEVICE_ID INT8 NOT NULL,
	DEVICE_SELF_ID VARCHAR(100),
	DEVICE_NAME VARCHAR(150) NOT NULL,
	VENDOR_ID INT8 NOT NULL,
	DEVICE_TYPE_ID INT8 NOT NULL,
	FAMILY_TYPE INT4,
	HCCS_DEVICE_ID INT8,
	HCCS_VSM_TOTAL INT4,
	HCCS_VSM_USABLE INT4,
	HCCS_VSM_USED INT4,
	DEVICE_GROUP_ID INT8,
	MGT_IP VARCHAR(40),
	MGT_PORT INT4,
	BUSI_IP VARCHAR(40),
	BUSI_PORT INT4,
	CONNECT_PASSWORD VARCHAR(200),
	CONNECT_PROTOCOL VARCHAR(40),
	DEVICE_VERSION VARCHAR(100),
	DEVICE_SERIALNUM VARCHAR(100),
	REGION_ID INT8,
	WEB_URL VARCHAR(255),
	PUBLIC_KEY TEXT,
	PUBLIC_KEY_FINGER TEXT,
	DEVICE_WEIGHT NUMERIC(24,6),
	AUTH_CODE VARCHAR(200),
	VSM_RESOURCE INT8,
	HMAC VARCHAR(200),
	CLOUD_TOKEN VARCHAR(255),
	MANAGEMENT_STATUS_ID INT8,
	OPER_STATUS INT8,
	MASTER_KEY_FLAG INT4 DEFAULT 0,
	CONNECT_AUTH_CODE VARCHAR(200),
	RUN_STATUS INT4,
	BUSI_PORT_EXTEND INT4 DEFAULT 8008 NOT NULL,
	INVALID_FLAG TEXT,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_INFO_PRIMARY PRIMARY KEY (DEVICE_ID)
);


-- CCSP_DEVICE.DEVICE_MASTER_COVER_RECORD definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_MASTER_COVER_RECORD;

CREATE TABLE CCSP_DEVICE.DEVICE_MASTER_COVER_RECORD (
	ID INT8 NOT NULL,
	DEVICE_ID INT8 NOT NULL,
	TENANT_ID INT8 NOT NULL,
	COVER_STATUS INT4,
	COVER_TYPE INT4 NOT NULL,
	COVER_TIME VARCHAR(30),
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_MASTER_COVER_RECORD_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DEVICE_MONITOR_CONFIG definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_MONITOR_CONFIG;

CREATE TABLE CCSP_DEVICE.DEVICE_MONITOR_CONFIG (
	ID INT8 NOT NULL,
	DEVICE_TYPE_ID INT8,
	MONITOR_TYPE INT4,
	URL VARCHAR(255),
	SNMP_VERSION VARCHAR(30),
	SNMP_PROTO VARCHAR(30),
	SNMP_PORT INT4,
	SAFE_LEVEL INT4,
	SECURITY_NAME VARCHAR(150),
	AUTHENTICATION_PROTOCOL INT4,
	AUTHENTICATION_AUTH_CODE VARCHAR(100),
	PRIVACY_PROTOCOL INT4,
	PRIVACY_AUTH_CODE VARCHAR(100),
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_MONITOR_CONFIG_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DEVICE_NET_DETAIL definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_NET_DETAIL;

CREATE TABLE CCSP_DEVICE.DEVICE_NET_DETAIL (
	IP_ID INT8 NOT NULL,
	MGT_IP VARCHAR(60) NOT NULL,
	BUSI_IP VARCHAR(60),
	MGT_IP_NUM INT8 NOT NULL,
	BUSI_IP_NUM INT8,
	GATEWAY VARCHAR(60) NOT NULL,
	STATUS INT4 DEFAULT 1 NOT NULL,
	DEVICE_ID INT8,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_NET_DETAIL_PRIMARY PRIMARY KEY (IP_ID)
);


-- CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG;

CREATE TABLE CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG (
	ID INT8 NOT NULL,
	DEVICE_TYPE_ID INT8,
	"OID" VARCHAR(150),
	"NAME" VARCHAR(150),
	VALUE_TYPE VARCHAR(50),
	REQUEST_TYPE VARCHAR(50),
	COLLECTOR_TYPE INT4,
	PARENT_ID INT8,
	SUB_OID_KEY INT4,
	SUB_OID_VALUE VARCHAR(150),
	COMPUTING_TYPE VARCHAR(50),
	CALCULATE_TEMPLATE VARCHAR(100),
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_SNMP_OID_CONFIG_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DEVICE_TYPE definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_TYPE;

CREATE TABLE CCSP_DEVICE.DEVICE_TYPE (
	DEVICE_TYPE_ID INT8 NOT NULL,
	DEVICE_TYPE_NAME VARCHAR(150) NOT NULL,
	DEFAULT_FLAG INT4 DEFAULT 0 NOT NULL,
	VENDOR_ID INT8 NOT NULL,
	FAMILY_TYPE INT4 NOT NULL,
	MACHINE_TYPE_ID INT8,
	PARENT_ID INT8 DEFAULT 0,
	HCCS_IMAGE_ID INT8,
	INTERACTION_SERIAL_NUMBER VARCHAR(100),
	MGT_METHOD INT4,
	MGT_PORT INT4 NOT NULL,
	BUSI_PORT INT4,
	CLOUD_VSM_TOTAL INT4,
	SUPPORT_MAIN_KEY_FLAG INT4 DEFAULT 0 NOT NULL,
	SUPPORT_SEC_MANAGE_FLAG INT4 DEFAULT 0 NOT NULL,
	SUPPORT_GEN_KEY_FLAG INT4 DEFAULT 0 NOT NULL,
	SUPPORT_SNMP_FLAG INT4 DEFAULT 0 NOT NULL,
	TOKEN_CALL_BACK_FLAG INT4 DEFAULT 0,
	NEED_PASSWORD_FLAG INT4 DEFAULT 0 NOT NULL,
	READ_INFO_FLAG INT4 DEFAULT 0 NOT NULL,
	MGT_PUBLICKEY_FLAG INT4 DEFAULT 0 NOT NULL,
	KEY_TEMPLET_IDS TEXT,
	DEFAULT_KEY_TEMPLET_IDS VARCHAR(500),
	SUPPORT_EXTERNAL_SERVICE INT4 DEFAULT 0 NOT NULL,
	SUPPORT_MANAGE_PT INT4 DEFAULT 0 NOT NULL,
	CONNECT_AUTH_CODE VARCHAR(255),
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	REMARK VARCHAR(1000),
	CONSTRAINT DEVICE_TYPE_PRIMARY PRIMARY KEY (DEVICE_TYPE_ID)
);


-- CCSP_DEVICE.DEVICE_TYPE_MANAGE_PATH definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_TYPE_MANAGE_PATH;

CREATE TABLE CCSP_DEVICE.DEVICE_TYPE_MANAGE_PATH (
	ID INT8 NOT NULL,
	DEVICE_TYPE_ID INT8 NOT NULL,
	DEVICE_PT_METHOD INT4 NOT NULL,
	DEVICE_PT_PORT INT4,
	DEVICE_PT_PATH VARCHAR(255),
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_TYPE_MANAGE_PATH_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE;

CREATE TABLE CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (
	ID INT8 NOT NULL,
	DEVICE_TYPE_ID INT8 NOT NULL,
	VALUE VARCHAR(100) NOT NULL,
	VALUE_TYPE INT4 NOT NULL,
	DEFAULT_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_TYPE_RELA_VALUE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DEVICE_TYPE_ROUTE_CONFIG definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_TYPE_ROUTE_CONFIG;

CREATE TABLE CCSP_DEVICE.DEVICE_TYPE_ROUTE_CONFIG (
	ID INT8 NOT NULL,
	DEVICE_TYPE_ID INT8 NOT NULL,
	NET_PRO INT4 NOT NULL,
	URL_START VARCHAR(1000),
	SUPPORT_ALLOCATE_APP INT4 NOT NULL,
	HAS_MANAGE_ROUTE INT4 NOT NULL,
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_TYPE_ROUTE_CONFIG_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DEVICE_VENDOR definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_VENDOR;

CREATE TABLE CCSP_DEVICE.DEVICE_VENDOR (
	VENDOR_ID INT8 NOT NULL,
	VENDOR_NAME VARCHAR(240) NOT NULL,
	VENDOR_SHORT_NAME VARCHAR(150) NOT NULL,
	LINK_MAN VARCHAR(120),
	LINK_MAN_PHONE VARCHAR(120),
	DEFAULT_FLAG INT4 NOT NULL,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_VENDOR_PRIMARY PRIMARY KEY (VENDOR_ID)
);


-- CCSP_DEVICE.DEVICE_VSM_NET_CONFIG definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DEVICE_VSM_NET_CONFIG;

CREATE TABLE CCSP_DEVICE.DEVICE_VSM_NET_CONFIG (
	ID INT8 NOT NULL,
	NET_NAME VARCHAR(150),
	NET_CARD_NAME VARCHAR(150),
	HOST_SERIALNUM VARCHAR(80),
	HOST_ID INT8,
	SUBDOMAIN VARCHAR(80),
	GATEWAY VARCHAR(80) NOT NULL,
	HCCS_ID INT8,
	MASK VARCHAR(80) NOT NULL,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DEVICE_VSM_NET_CONFIG_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE;

CREATE TABLE CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (
	ID INT8 NOT NULL,
	INTERACTION_API_ID INT8 NOT NULL,
	SORD_NUM INT4 NOT NULL,
	API_SERIAL_NUM VARCHAR(100) NOT NULL,
	API_NAME VARCHAR(300) NOT NULL,
	CONTEXT_PATH VARCHAR(100) NOT NULL,
	API_PATH VARCHAR(200) NOT NULL,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DIC_DEVICE_API_TEMPLATE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DIC_DEVICE_IMAGE_TYPE definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DIC_DEVICE_IMAGE_TYPE;

CREATE TABLE CCSP_DEVICE.DIC_DEVICE_IMAGE_TYPE (
	ID INT8 NOT NULL,
	IMAGE_NAME VARCHAR(300) NOT NULL,
	IMAGE_VALUE VARCHAR(100) NOT NULL,
	IMAGE_VERSION VARCHAR(100),
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	VENDOR_ID INT8,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DIC_DEVICE_IMAGE_TYPE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE;

CREATE TABLE CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE (
	ID INT8 NOT NULL,
	INTERACTION_NAME VARCHAR(300) NOT NULL,
	INTERACTION_SERIAL_NUMBER VARCHAR(100) NOT NULL,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	FAMILY_TYPE INT4 NOT NULL,
	CONSTRAINT DIC_DEVICE_INTERACTION_TYPE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DIC_DEVICE_MACHINE_TYPE definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DIC_DEVICE_MACHINE_TYPE;

CREATE TABLE CCSP_DEVICE.DIC_DEVICE_MACHINE_TYPE (
	ID INT8 NOT NULL,
	SERVER_NAME VARCHAR(300) NOT NULL,
	SERVER_CODE INT4 NOT NULL,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	FAMILY_TYPE INT4 NOT NULL,
	CONSTRAINT DIC_DEVICE_MACHINE_TYPE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DIC_DEVICE_MANAGEMENT_STATUS definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DIC_DEVICE_MANAGEMENT_STATUS;

CREATE TABLE CCSP_DEVICE.DIC_DEVICE_MANAGEMENT_STATUS (
	ID INT8 NOT NULL,
	STATUS_NAME VARCHAR(150) NOT NULL,
	STATUS_CODE VARCHAR(255) NOT NULL,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DIC_DEVICE_MANAGEMENT_STATUS_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS;

CREATE TABLE CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS (
	ID INT8 NOT NULL,
	OPER_NAME VARCHAR(150) NOT NULL,
	OPER_CODE INT4 NOT NULL,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DIC_DEVICE_OPERATION_STATUS_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DIC_DEVICE_VSM_RESOURCE definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DIC_DEVICE_VSM_RESOURCE;

CREATE TABLE CCSP_DEVICE.DIC_DEVICE_VSM_RESOURCE (
	ID INT8 NOT NULL,
	RESOURCE_VALUE VARCHAR(50),
	RESOURCE_NAME VARCHAR(150) NOT NULL,
	INVALID_FLAG INT4 DEFAULT 0 NOT NULL,
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30),
	CONSTRAINT DIC_DEVICE_VSM_RESOURCE_PRIMARY PRIMARY KEY (ID)
);


-- CCSP_DEVICE.DIC_SNMP_AUTHENTICATION definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DIC_SNMP_AUTHENTICATION;

CREATE TABLE CCSP_DEVICE.DIC_SNMP_AUTHENTICATION (
	ID INT8,
	SNMP_AUTHENTICATION_ID INT4,
	SNMP_AUTHENTICATION_NAME VARCHAR(255),
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30)
);


-- CCSP_DEVICE.DIC_SNMP_PRIVACY definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.DIC_SNMP_PRIVACY;

CREATE TABLE CCSP_DEVICE.DIC_SNMP_PRIVACY (
	ID INT8,
	SNMP_PRIVACY_ID INT4,
	SNMP_PRIVACY_NAME VARCHAR(255),
	REMARK VARCHAR(1000),
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATE_BY INT8,
	UPDATE_TIME VARCHAR(30)
);


-- CCSP_DEVICE.SYS_JOB definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.SYS_JOB;

CREATE TABLE CCSP_DEVICE.SYS_JOB (
	JOB_ID INT8 NOT NULL,
	JOB_NAME VARCHAR(255) NOT NULL,
	JOB_GROUP VARCHAR(255) NOT NULL,
	SERVER_ID VARCHAR(100) NOT NULL,
	METHOD_URL VARCHAR(1500),
	JSON_PARAM TEXT NOT NULL,
	CRON_EXPRESSION VARCHAR(255),
	MISFIRE_POLICY VARCHAR(20) DEFAULT '3'::CHARACTER VARYING,
	CONCURRENT VARCHAR(1) DEFAULT '1'::CHARACTER VARYING,
	JOB_STATUS VARCHAR(1) DEFAULT '0'::CHARACTER VARYING,
	CREATED_BY INT8,
	CREATE_TIME VARCHAR(30),
	UPDATED_BY INT8,
	UPDATE_TIME VARCHAR(30),
	REMARK VARCHAR(1500),
	CONSTRAINT SYS_JOB_PRIMARY PRIMARY KEY (JOB_ID)
);


-- CCSP_DEVICE.SYS_JOB_LOG definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.SYS_JOB_LOG;

CREATE TABLE CCSP_DEVICE.SYS_JOB_LOG (
	JOB_LOG_ID INT8 NOT NULL,
	JOB_ID INT8 NOT NULL,
	JOB_MESSAGE VARCHAR(1500),
	STATUS VARCHAR(1) NOT NULL,
	EXCEPTION_INFO VARCHAR(6000),
	CREATE_TIME VARCHAR(30) NOT NULL,
	TRIGGER_TIME INT8,
	CONSTRAINT SYS_JOB_LOG_PRIMARY PRIMARY KEY (JOB_LOG_ID)
);


-- CCSP_DEVICE.SYS_TASK definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.SYS_TASK;

CREATE TABLE CCSP_DEVICE.SYS_TASK (
	TASK_ID INT8 NOT NULL,
	TASK_NAME VARCHAR(255) NOT NULL,
	TASK_GROUP VARCHAR(255) NOT NULL,
	SERVER_ID VARCHAR(255) NOT NULL,
	METHOD_URL VARCHAR(1000),
	JSON_PARAM TEXT NOT NULL,
	TASK_STATUS INT4 DEFAULT 0 NOT NULL,
	CREATE_TIME VARCHAR(30),
	UPDATE_TIME VARCHAR(30),
	REMARK VARCHAR(300),
	TIMEOUT INT4,
	START_TIME VARCHAR(255),
	END_TIME VARCHAR(255),
	POLICY VARCHAR(1) DEFAULT '0'::CHARACTER VARYING NOT NULL,
	CONSTRAINT SYS_TASK_PRIMARY PRIMARY KEY (TASK_ID)
);


-- CCSP_DEVICE.SYS_TASK_LOG definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.SYS_TASK_LOG;

CREATE TABLE CCSP_DEVICE.SYS_TASK_LOG (
	TASK_LOG_ID INT8 NOT NULL,
	TASK_ID INT8 NOT NULL,
	TASK_MESSAGE VARCHAR(3000),
	STATUS VARCHAR(1) NOT NULL,
	EXCEPTION_INFO VARCHAR(6000),
	CREATE_TIME VARCHAR(30),
	TRIGGER_TIME INT8,
	CONSTRAINT SYS_TASK_LOG_PRIMARY PRIMARY KEY (TASK_LOG_ID)
);


-- CCSP_DEVICE.TENANT_KEY definition

-- Drop table

-- DROP TABLE CCSP_DEVICE.TENANT_KEY;

CREATE TABLE CCSP_DEVICE.TENANT_KEY (
	ID INT8 NOT NULL,
	TENANT_ID INT8 NOT NULL,
	"KEY_TYPE" INT4 NOT NULL,
	CONTENT VARCHAR(1000) NOT NULL,
	CREATE_BY INT8,
	CREATE_TIME VARCHAR(30),
	CONSTRAINT TENANT_KEY_PRIMARY PRIMARY KEY (ID)
);

INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363783263125507,'getVsmInfo',1,'chsm_sansec_old_0088',201,'1','/','/api/1.0/vsm',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363783464452104,'getVsmStatus',1,'chsm_sansec_old_0088',202,'2','/','/api/1.0/vsm/status',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363783665778694,'configVsmNetwork',1,'chsm_sansec_old_0088',203,'3','/','/api/1.0/vsm/network',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363783867105280,'createVsm',1,'chsm_sansec_old_0088',204,'4','/','/api/1.0/vsm',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363783967768585,'vsmOperate',1,'chsm_sansec_old_0088',205,'5','/','/api/1.0/vsm',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363784101986313,'getChsmInfo',1,'chsm_sansec_old_0088',206,'6','/','/api/1.0/chsm',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363784202649609,'getChsmStatus',1,'chsm_sansec_old_0088',207,'7','/','/api/1.0/chsm/status',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363784303312897,'configChsmPublicKey',1,'chsm_sansec_old_0088',208,'8','/','/api/1.0/chsm/authpk',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363784437530627,'clearChsmPublicKey',1,'chsm_sansec_old_0088',209,'9','/','/api/1.0/chsm/authpk',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363784571748355,'getChsmPublicKeyFinger',1,'chsm_sansec_old_0088',210,'10','/','/api/1.0/chsm/authpk',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075363784705966086,'configVsm',1,'chsm_sansec_old_0088',211,'30','/','/api/1.0/vsm/config',0,0,NULL,6071976419609675523,'2023-09-27 14:25:50',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183023638280,'getHsmInfo',6,'vsm_sansec_unified_1001',2001,'11','/pkiweb/sansecplat','/api/1.0/chsm',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183090747140,'getHsmStatus',6,'vsm_sansec_unified_1001',2002,'12','/pkiweb/sansecplat','/api/1.0/chsm/status',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183124301577,'configHsmNetwork',6,'vsm_sansec_unified_1001',2003,'13','/pkiweb/sansecplat','/api/1.0/chsm/network',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183157856009,'restartHsm',6,'vsm_sansec_unified_1001',2004,'14','/pkiweb/sansecplat','/api/1.0/chsm',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183191410441,'configHsmPublicKey',6,'vsm_sansec_unified_1001',2005,'15','/pkiweb/sansecplat','/api/1.0/chsm/authpk',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183224964870,'clearHsmPublicKey',6,'vsm_sansec_unified_1001',2006,'16','/pkiweb/sansecplat','/api/1.0/chsm/authpk',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183258519297,'getHsmPublicKeyFinger',6,'vsm_sansec_unified_1001',2007,'17','/pkiweb/sansecplat','/api/1.0/chsm/authpk',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183292073728,'createHsmLmk',6,'vsm_sansec_unified_1001',2008,'18','/pkiweb/sansecplat/hsmm','/key/masterKey/generateLMK',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183325628169,'generateHsmLMKWithComponent',6,'vsm_sansec_unified_1001',2009,'19','/pkiweb/sansecplat/hsmm','/key/masterKey/importLMK',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183359182600,'getHsmDeviceKeyPair',6,'vsm_sansec_unified_1001',2010,'20','/pkiweb/sansecplat/hsmm','/platform/key/gennerAsymmTempKey',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183359182601,'exportHsmMasterKey',6,'vsm_sansec_unified_1001',2011,'21','/pkiweb/sansecplat/hsmm','/platform/key/exportLMKByCipher',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183392737031,'importHsmMasterKey',6,'vsm_sansec_unified_1001',2012,'22','/pkiweb/sansecplat/hsmm','/platform/key/impoerLMKByCipher',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183426291459,'getDeviceInitState',6,'vsm_sansec_unified_1001',2013,'23','/pkiweb/sansecplat','/api/1.0/chsm/init/status',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183459845895,'deviceInit',6,'vsm_sansec_unified_1001',2014,'24','/pkiweb/sansecplat','/api/1.0/chsm/init',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183493400325,'importAsyncKeyPair',6,'vsm_sansec_unified_1001',2015,'25','/pkiweb/sansecplat/hsmm','/platform/key/importAsyncKeyPair',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183526954761,'importSyncKeyPair',6,'vsm_sansec_unified_1001',2016,'26','/pkiweb/sansecplat/hsmm','/platform/key/importSyncKeyPair',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183560509189,'deleteSymmetricKey',6,'vsm_sansec_unified_1001',2017,'27','/pkiweb/sansecplat/hsmm','/key/symmetricKey/deleteSymmetricKey',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183594063623,'deleteSM2Key',6,'vsm_sansec_unified_1001',2018,'28','/pkiweb/sansecplat/hsmm','/key/sm2Key/deleteSM2Key',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391183627618055,'getHsmServiceStatus',6,'vsm_sansec_unified_1001',2019,'29','/PlatformServlet','?method=getServiceState',0,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399467713562372,'getHsmInfo',11,'hsm_sansec_1002',3101,'101','/PlatformServlet','?method=getHsmInfo',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399467747116809,'getHsmStatus',11,'hsm_sansec_1002',3102,'102','/PlatformServlet','?method=getState',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399467780671241,'configHsmNetwork',11,'hsm_sansec_1002',3103,'103','/PlatformServlet','?method=network',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399467814225672,'restartHsm',11,'hsm_sansec_1002',3104,'104','/PlatformServlet','?method=restartHsm',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399467847780104,'configHsmPublicKey',11,'hsm_sansec_1002',3105,'105','/AuthServlet','?method=authPK',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399467881334535,'clearHsmPublicKey',11,'hsm_sansec_1002',3106,'106','/AuthServlet','?method=cleanPK',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399467914888968,'getHsmPublicKeyFinger',11,'hsm_sansec_1002',3107,'107','/AuthServlet','?method=getAuthPKFingerprints',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399467948443392,'createHsmLmk',11,'hsm_sansec_1002',3108,'108','/PlatformServlet','?method=generateLMK',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399467981997832,'generateHsmLMKWithComponent',11,'hsm_sansec_1002',3109,'109','/PlatformServlet','?method=importLMK',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399468015552261,'getHsmDeviceKeyPair',11,'hsm_sansec_1002',3110,'110','/PlatformServlet','?method=gennerAsymmTempKey',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399468015552262,'exportHsmMasterKey',11,'hsm_sansec_1002',3111,'111','/PlatformServlet','?method=exportLMKByCipher',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399468049106689,'importHsmMasterKey',11,'hsm_sansec_1002',3112,'112','/PlatformServlet','?method=importLMKByCipher',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399468082661126,'getDeviceInitState',11,'hsm_sansec_1002',3113,'113','/PlatformServlet','?method=initStatus',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399468116215557,'deviceInit',11,'hsm_sansec_1002',3114,'114','/PlatformServlet','?method=doHsmInit',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399468149769985,'importAsyncKeyPair',11,'hsm_sansec_1002',3115,'115','/PlatformServlet','?method=importAsyncKeyPair',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399468183324417,'importSyncKeyPair',11,'hsm_sansec_1002',3116,'116','/PlatformServlet','?method=importSymmKey',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399468216878857,'deleteSymmetricKey',11,'hsm_sansec_1002',3117,'117','/PlatformServlet','?method=delHsmSymmetricKey',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399468216878858,'deleteSM2Key',11,'hsm_sansec_1002',3118,'118','/PlatformServlet','?method=delHsmKeyPair',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075399468250433286,'getHsmServiceStatus',11,'hsm_sansec_1002',3119,'119','/PlatformServlet','?method=getServiceState',0,0,NULL,6071976419609675523,'2023-09-27 14:43:33',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568489441028,'getHsmInfo',12,'hsm_sansec_1002',3101,'101','/PlatformServlet','?method=getHsmInfo',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568522995460,'getHsmStatus',12,'hsm_sansec_1002',3102,'102','/PlatformServlet','?method=getState',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568556549894,'configHsmNetwork',12,'hsm_sansec_1002',3103,'103','/PlatformServlet','?method=network',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568590104321,'restartHsm',12,'hsm_sansec_1002',3104,'104','/PlatformServlet','?method=restartHsm',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568590104322,'configHsmPublicKey',12,'hsm_sansec_1002',3105,'105','/AuthServlet','?method=authPK',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568623658756,'clearHsmPublicKey',12,'hsm_sansec_1002',3106,'106','/AuthServlet','?method=cleanPK',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568657213191,'getHsmPublicKeyFinger',12,'hsm_sansec_1002',3107,'107','/AuthServlet','?method=getAuthPKFingerprints',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568690767622,'createHsmLmk',12,'hsm_sansec_1002',3108,'108','/PlatformServlet','?method=generateLMK',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568690767623,'generateHsmLMKWithComponent',12,'hsm_sansec_1002',3109,'109','/PlatformServlet','?method=importLMK',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568724322055,'getHsmDeviceKeyPair',12,'hsm_sansec_1002',3110,'110','/PlatformServlet','?method=gennerAsymmTempKey',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568757876483,'exportHsmMasterKey',12,'hsm_sansec_1002',3111,'111','/PlatformServlet','?method=exportLMKByCipher',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568757876484,'importHsmMasterKey',12,'hsm_sansec_1002',3112,'112','/PlatformServlet','?method=importLMKByCipher',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568791430921,'getDeviceInitState',12,'hsm_sansec_1002',3113,'113','/PlatformServlet','?method=initStatus',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568824985345,'deviceInit',12,'hsm_sansec_1002',3114,'114','/PlatformServlet','?method=doHsmInit',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568858539782,'importAsyncKeyPair',12,'hsm_sansec_1002',3115,'115','/PlatformServlet','?method=importAsyncKeyPair',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568858539783,'importSyncKeyPair',12,'hsm_sansec_1002',3116,'116','/PlatformServlet','?method=importSymmKey',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568892094214,'deleteSymmetricKey',12,'hsm_sansec_1002',3117,'117','/PlatformServlet','?method=delHsmSymmetricKey',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568925648642,'deleteSM2Key',12,'hsm_sansec_1002',3118,'118','/PlatformServlet','?method=delHsmKeyPair',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075401568959203081,'getHsmServiceStatus',12,'hsm_sansec_1002',3119,'119','/PlatformServlet','?method=getServiceState',0,0,NULL,6071976419609675523,'2023-09-27 14:44:36',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333318346500,'getHsmInfo',21,'hsm_sansec_1002',3101,'101','/PlatformServlet','?method=getHsmInfo',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333385455369,'getHsmStatus',21,'hsm_sansec_1002',3102,'102','/PlatformServlet','?method=getState',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333419009797,'configHsmNetwork',21,'hsm_sansec_1002',3103,'103','/PlatformServlet','?method=network',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333452564230,'restartHsm',21,'hsm_sansec_1002',3104,'104','/PlatformServlet','?method=restartHsm',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333486118659,'configHsmPublicKey',21,'hsm_sansec_1002',3105,'105','/AuthServlet','?method=authPK',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333519673090,'clearHsmPublicKey',21,'hsm_sansec_1002',3106,'106','/AuthServlet','?method=cleanPK',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333553227524,'getHsmPublicKeyFinger',21,'hsm_sansec_1002',3107,'107','/AuthServlet','?method=getAuthPKFingerprints',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333586781960,'createHsmLmk',21,'hsm_sansec_1002',3108,'108','/PlatformServlet','?method=generateLMK',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333620336389,'generateHsmLMKWithComponent',21,'hsm_sansec_1002',3109,'109','/PlatformServlet','?method=importLMK',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333653890820,'getHsmDeviceKeyPair',21,'hsm_sansec_1002',3110,'110','/PlatformServlet','?method=gennerAsymmTempKey',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333687445251,'exportHsmMasterKey',21,'hsm_sansec_1002',3111,'111','/PlatformServlet','?method=exportLMKByCipher',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333687445252,'importHsmMasterKey',21,'hsm_sansec_1002',3112,'112','/PlatformServlet','?method=importLMKByCipher',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333720999686,'getDeviceInitState',21,'hsm_sansec_1002',3113,'113','/PlatformServlet','?method=initStatus',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403333754554112,'deviceInit',21,'hsm_sansec_1002',3114,'114','/PlatformServlet','?method=doHsmInit',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403334022989570,'importAsyncKeyPair',21,'hsm_sansec_1002',3115,'115','/PlatformServlet','?method=importAsyncKeyPair',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403334056544000,'importSyncKeyPair',21,'hsm_sansec_1002',3116,'116','/PlatformServlet','?method=importSymmKey',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403334090098433,'deleteSymmetricKey',21,'hsm_sansec_1002',3117,'117','/PlatformServlet','?method=delHsmSymmetricKey',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403334090098434,'deleteSM2Key',21,'hsm_sansec_1002',3118,'118','/PlatformServlet','?method=delHsmKeyPair',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075403334123652868,'getHsmServiceStatus',21,'hsm_sansec_1002',3119,'119','/PlatformServlet','?method=getServiceState',0,0,NULL,6071976419609675523,'2023-09-27 14:45:28',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405747073318918,'getVsmInfo',31,'chsm_sansec_standard_0088',101,'1','/','/api/1.0/vsm',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405747241091077,'getVsmStatus',31,'chsm_sansec_standard_0088',102,'2','/','/api/1.0/vsm/status',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405747375308803,'configVsmNetwork',31,'chsm_sansec_standard_0088',103,'3','/','/api/1.0/vsm/network',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405747543080962,'createVsm',31,'chsm_sansec_standard_0088',104,'4','/','/api/1.0/vsm',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405747677298689,'vsmOperate',31,'chsm_sansec_standard_0088',105,'5','/','/api/1.0/vsm',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405747811516420,'getChsmInfo',31,'chsm_sansec_standard_0088',106,'6','/','/api/1.0/chsm',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405747945734153,'getChsmStatus',31,'chsm_sansec_standard_0088',107,'7','/','/api/1.0/chsm/status',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405748113506310,'configChsmPublicKey',31,'chsm_sansec_standard_0088',108,'8','/','/api/1.0/chsm/authpk',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405748247724036,'clearChsmPublicKey',31,'chsm_sansec_standard_0088',109,'9','/','/api/1.0/chsm/authpk',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405748381941763,'getChsmPublicKeyFinger',31,'chsm_sansec_standard_0088',110,'10','/','/api/1.0/chsm/authpk',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405748482605062,'configVsm',31,'chsm_sansec_standard_0088',111,'30','/','/api/1.0/vsm/config',0,0,NULL,6071976419609675523,'2023-09-27 14:46:40',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214002091781,'getHsmInfo',32,'vsm_sansec_1002',2101,'101','/PlatformServlet','?method=getHsmInfo',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214035646209,'getHsmStatus',32,'vsm_sansec_1002',2102,'102','/PlatformServlet','?method=getState',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214069200643,'configHsmNetwork',32,'vsm_sansec_1002',2103,'103','/PlatformServlet','?method=network',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214102755081,'restartHsm',32,'vsm_sansec_1002',2104,'104','/PlatformServlet','?method=restartHsm',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214136309512,'configHsmPublicKey',32,'vsm_sansec_1002',2105,'105','/AuthServlet','?method=authPK',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214169863940,'clearHsmPublicKey',32,'vsm_sansec_1002',2106,'106','/AuthServlet','?method=cleanPK',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214203418368,'getHsmPublicKeyFinger',32,'vsm_sansec_1002',2107,'107','/AuthServlet','?method=getAuthPKFingerprints',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214236972808,'createHsmLmk',32,'vsm_sansec_1002',2108,'108','/PlatformServlet','?method=generateLMK',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214270527234,'generateHsmLMKWithComponent',32,'vsm_sansec_1002',2109,'109','/PlatformServlet','?method=importLMK',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214304081666,'getHsmDeviceKeyPair',32,'vsm_sansec_1002',2110,'110','/PlatformServlet','?method=gennerAsymmTempKey',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214337636097,'exportHsmMasterKey',32,'vsm_sansec_1002',2111,'111','/PlatformServlet','?method=exportLMKByCipher',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214337636098,'importHsmMasterKey',32,'vsm_sansec_1002',2112,'112','/PlatformServlet','?method=importLMKByCipher',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214371190537,'getDeviceInitState',32,'vsm_sansec_1002',2113,'113','/PlatformServlet','?method=initStatus',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214404744961,'deviceInit',32,'vsm_sansec_1002',2114,'114','/PlatformServlet','?method=doHsmInit',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214438299392,'importAsyncKeyPair',32,'vsm_sansec_1002',2115,'115','/PlatformServlet','?method=importAsyncKeyPair',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214471853830,'importSyncKeyPair',32,'vsm_sansec_1002',2116,'116','/PlatformServlet','?method=importSymmKey',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214505408260,'deleteSymmetricKey',32,'vsm_sansec_1002',2117,'117','/PlatformServlet','?method=delHsmSymmetricKey',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214538962693,'deleteSM2Key',32,'vsm_sansec_1002',2118,'118','/PlatformServlet','?method=delHsmKeyPair',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_API (API_ID,API_NAME,DEVICE_TYPE_ID,INTERACTION_SERIAL_NUMBER,API_TEMPLATE_ID,API_SERIAL_NUMBER,CONTEXT_PATH,API_PATH,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412214538962694,'getHsmServiceStatus',32,'vsm_sansec_1002',2119,'119','/PlatformServlet','?method=getServiceState',0,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1,2,1,0,NULL,1,'2023-03-08 18:14:14',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2,2,2,0,NULL,1,'2023-03-08 18:14:14',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (4,2,4,0,'',1,'2023-03-08 18:14:14',NULL,'');
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5,2,5,0,'',1,'2023-03-08 18:14:14',NULL,'');
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6,2,6,0,'',1,'2023-03-08 18:14:14',NULL,'');
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (7,2,7,0,NULL,1,'2023-03-08 18:14:14',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (8,2,8,0,NULL,1,'2023-03-08 18:14:14',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (9,2,9,0,NULL,1,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (10,2,10,0,NULL,1,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (11,2,11,0,NULL,1,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (12,6,1,0,NULL,1,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (13,6,2,0,NULL,1,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (15,6,4,0,NULL,1,'2023-03-09 14:26:07',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (16,6,5,0,NULL,1,'2023-03-09 01:40:56',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (17,6,6,0,NULL,1,'2023-03-09 01:40:56',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (18,6,7,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (19,6,8,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (20,6,9,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (21,6,10,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (22,6,11,0,NULL,1,'2023-03-09 16:48:11',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (23,10,1,0,NULL,1,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (24,10,2,0,NULL,1,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (26,10,4,0,NULL,1,'2023-03-09 14:26:07',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (27,10,5,0,NULL,1,'2023-03-09 01:40:56',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (28,10,6,0,NULL,1,'2023-03-09 01:40:56',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (29,10,7,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (30,10,8,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (31,10,9,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (32,10,10,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (33,10,11,0,NULL,1,'2023-03-09 16:48:11',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (34,11,2,0,NULL,1,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (36,11,4,0,NULL,1,'2023-03-09 14:26:07',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (37,11,5,0,NULL,1,'2023-03-09 01:40:56',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (38,11,6,0,NULL,1,'2023-03-09 01:40:56',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (39,11,7,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (40,11,8,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (41,11,9,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (42,11,10,0,NULL,1,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (43,11,11,0,NULL,1,'2023-03-09 16:48:11',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (44,11,1,0,NULL,1,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (45,12,1,0,NULL,NULL,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (46,12,2,0,NULL,NULL,'2023-03-08 08:12:44',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (47,12,4,0,NULL,NULL,'2023-03-09 14:26:07',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (48,12,5,0,NULL,NULL,'2023-03-09 01:40:56',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (49,12,6,0,NULL,NULL,'2023-03-09 01:40:56',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (50,12,7,0,NULL,NULL,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (51,12,8,0,NULL,NULL,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (52,12,9,0,NULL,NULL,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (53,12,10,0,NULL,NULL,'2023-03-09 01:59:09',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (54,12,11,0,NULL,NULL,'2023-03-09 16:48:11',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875303145879242119,21,1,0,NULL,5872045327240398210,'2023-07-20 14:14:38',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875303145912796553,21,2,0,NULL,5872045327240398210,'2023-07-20 14:14:38',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875303145979905415,21,4,0,NULL,5872045327240398210,'2023-07-20 14:14:38',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875303146013459845,21,5,0,NULL,5872045327240398210,'2023-07-20 14:14:38',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875303146080568706,21,6,0,NULL,5872045327240398210,'2023-07-20 14:14:38',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875303146114123142,21,7,0,NULL,5872045327240398210,'2023-07-20 14:14:38',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875303146181232005,21,8,0,NULL,5872045327240398210,'2023-07-20 14:14:38',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875303146248340871,21,9,0,NULL,5872045327240398210,'2023-07-20 14:14:38',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875303146281895299,21,10,0,NULL,5872045327240398210,'2023-07-20 14:14:38',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875303146349004161,21,11,0,NULL,5872045327240398210,'2023-07-20 14:14:38',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875336200780252550,32,1,0,NULL,5872045327240398210,'2023-07-20 14:31:03',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875336200847361409,32,2,0,NULL,5872045327240398210,'2023-07-20 14:31:03',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875336200880915840,32,4,0,NULL,5872045327240398210,'2023-07-20 14:31:03',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875336200914470272,32,5,0,NULL,5872045327240398210,'2023-07-20 14:31:03',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875336200914470273,32,9,0,NULL,5872045327240398210,'2023-07-20 14:31:03',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875336200948024711,32,8,0,NULL,5872045327240398210,'2023-07-20 14:31:03',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875336201015133568,32,7,0,NULL,5872045327240398210,'2023-07-20 14:31:03',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875336201048688000,32,11,0,NULL,5872045327240398210,'2023-07-20 14:31:03',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875336201115796864,32,10,0,NULL,5872045327240398210,'2023-07-20 14:31:03',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5875336201149351300,32,6,0,NULL,5872045327240398210,'2023-07-20 14:31:03',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (601, 6, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1101, 11, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1201, 12, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2101, 21, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);
INSERT INTO CCSP_DEVICE.DEVICE_BUSITYPE (ID,DEVICE_TYPE_ID,BUSI_TYPE_ID,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3201, 32, 12, 0, NULL, 1, '2023-03-08 18:14:14', NULL, NULL);

-- 这两条自己测试用，提测和正式打包不需要
-- INSERT INTO CCSP_DEVICE.DEVICE_TYPE (DEVICE_TYPE_ID,DEVICE_TYPE_NAME,DEFAULT_FLAG,VENDOR_ID,FAMILY_TYPE,MACHINE_TYPE_ID,PARENT_ID,HCCS_IMAGE_ID,INTERACTION_SERIAL_NUMBER,MGT_METHOD,MGT_PORT,BUSI_PORT,CLOUD_VSM_TOTAL,SUPPORT_MAIN_KEY_FLAG,SUPPORT_SEC_MANAGE_FLAG,SUPPORT_GEN_KEY_FLAG,SUPPORT_SNMP_FLAG,TOKEN_CALL_BACK_FLAG,NEED_PASSWORD_FLAG,READ_INFO_FLAG,MGT_PUBLICKEY_FLAG,KEY_TEMPLET_IDS,DEFAULT_KEY_TEMPLET_IDS,SUPPORT_EXTERNAL_SERVICE,SUPPORT_MANAGE_PT,CONNECT_AUTH_CODE,INVALID_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) VALUES (1,'云密码机_dev',1,5466516874462629769,1,1,NULL,NULL,'chsm_sansec_old_0088',1,8083,8083,32,0,0,0,0,0,1,1,1,NULL,NULL,0,0,NULL,0,NULL,NULL,6071976419609675523,'2023-09-27 14:24:47',NULL);
-- INSERT INTO CCSP_DEVICE.DEVICE_TYPE (DEVICE_TYPE_ID,DEVICE_TYPE_NAME,DEFAULT_FLAG,VENDOR_ID,FAMILY_TYPE,MACHINE_TYPE_ID,PARENT_ID,HCCS_IMAGE_ID,INTERACTION_SERIAL_NUMBER,MGT_METHOD,MGT_PORT,BUSI_PORT,CLOUD_VSM_TOTAL,SUPPORT_MAIN_KEY_FLAG,SUPPORT_SEC_MANAGE_FLAG,SUPPORT_GEN_KEY_FLAG,SUPPORT_SNMP_FLAG,TOKEN_CALL_BACK_FLAG,NEED_PASSWORD_FLAG,READ_INFO_FLAG,MGT_PUBLICKEY_FLAG,KEY_TEMPLET_IDS,DEFAULT_KEY_TEMPLET_IDS,SUPPORT_EXTERNAL_SERVICE,SUPPORT_MANAGE_PT,CONNECT_AUTH_CODE,INVALID_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) VALUES (6,'云服务器密码机_V6',1,5466516874462629769,3,2,1,2,'vsm_sansec_unified_1001',1,19443,8008,NULL,1,1,1,0,0,1,1,1,NULL,NULL,0,0,'zM7MYRiXXu7pgCPspRhR70oMk0sJQgTU7wXIhD0e4NNz8GGRvSvd+Qw/X2NQBq2p',0,1,'2023-03-01 14:08:18',6071976419609675523,'2023-09-27 14:39:26',NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE (DEVICE_TYPE_ID,DEVICE_TYPE_NAME,DEFAULT_FLAG,VENDOR_ID,FAMILY_TYPE,MACHINE_TYPE_ID,PARENT_ID,HCCS_IMAGE_ID,INTERACTION_SERIAL_NUMBER,MGT_METHOD,MGT_PORT,BUSI_PORT,CLOUD_VSM_TOTAL,SUPPORT_MAIN_KEY_FLAG,SUPPORT_SEC_MANAGE_FLAG,SUPPORT_GEN_KEY_FLAG,SUPPORT_SNMP_FLAG,TOKEN_CALL_BACK_FLAG,NEED_PASSWORD_FLAG,READ_INFO_FLAG,MGT_PUBLICKEY_FLAG,KEY_TEMPLET_IDS,DEFAULT_KEY_TEMPLET_IDS,SUPPORT_EXTERNAL_SERVICE,SUPPORT_MANAGE_PT,CONNECT_AUTH_CODE,INVALID_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) VALUES (11,'华为服务器密码机',1,5466516874462629769,2,2,0,NULL,'hsm_sansec_1002',1,9443,8008,NULL,1,1,1,0,0,1,1,1,NULL,NULL,0,0,'W1AOdzYqNEGmEskVh5fSZE0He4pDJv+7rY9Phv41EM5EPaHE8+6U4gkzWEE2UnguPvChwM6xWVXDePuS6QjCjb47DWZVVB78p2yVB1dllXc=',0,NULL,'2023-05-05 14:14:06',6071976419609675523,'2023-09-27 14:43:33','');
INSERT INTO CCSP_DEVICE.DEVICE_TYPE (DEVICE_TYPE_ID,DEVICE_TYPE_NAME,DEFAULT_FLAG,VENDOR_ID,FAMILY_TYPE,MACHINE_TYPE_ID,PARENT_ID,HCCS_IMAGE_ID,INTERACTION_SERIAL_NUMBER,MGT_METHOD,MGT_PORT,BUSI_PORT,CLOUD_VSM_TOTAL,SUPPORT_MAIN_KEY_FLAG,SUPPORT_SEC_MANAGE_FLAG,SUPPORT_GEN_KEY_FLAG,SUPPORT_SNMP_FLAG,TOKEN_CALL_BACK_FLAG,NEED_PASSWORD_FLAG,READ_INFO_FLAG,MGT_PUBLICKEY_FLAG,KEY_TEMPLET_IDS,DEFAULT_KEY_TEMPLET_IDS,SUPPORT_EXTERNAL_SERVICE,SUPPORT_MANAGE_PT,CONNECT_AUTH_CODE,INVALID_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) VALUES (12,'华为租户服务器密码机',1,5466516874462629769,2,2,0,NULL,'hsm_sansec_1002',1,9443,8008,NULL,1,1,1,0,0,1,1,1,NULL,NULL,0,0,'W1AOdzYqNEGmEskVh5fSZE0He4pDJv+7rY9Phv41EM5EPaHE8+6U4gkzWEE2UnguPvChwM6xWVXDePuS6QjCjb47DWZVVB78p2yVB1dllXc=',0,NULL,'2023-05-05 14:14:06',6071976419609675523,'2023-09-27 14:44:36','');
INSERT INTO CCSP_DEVICE.DEVICE_TYPE (DEVICE_TYPE_ID,DEVICE_TYPE_NAME,DEFAULT_FLAG,VENDOR_ID,FAMILY_TYPE,MACHINE_TYPE_ID,PARENT_ID,HCCS_IMAGE_ID,INTERACTION_SERIAL_NUMBER,MGT_METHOD,MGT_PORT,BUSI_PORT,CLOUD_VSM_TOTAL,SUPPORT_MAIN_KEY_FLAG,SUPPORT_SEC_MANAGE_FLAG,SUPPORT_GEN_KEY_FLAG,SUPPORT_SNMP_FLAG,TOKEN_CALL_BACK_FLAG,NEED_PASSWORD_FLAG,READ_INFO_FLAG,MGT_PUBLICKEY_FLAG,KEY_TEMPLET_IDS,DEFAULT_KEY_TEMPLET_IDS,SUPPORT_EXTERNAL_SERVICE,SUPPORT_MANAGE_PT,CONNECT_AUTH_CODE,INVALID_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) VALUES (21,'服务器密码机_V5.2.5',1,5466516874462629769,2,2,0,NULL,'hsm_sansec_1002',1,443,8008,NULL,1,1,1,0,0,1,1,1,NULL,NULL,0,0,'zM7MYRiXXu7pgCPspRhR70oMk0sJQgTU7wXIhD0e4NNz8GGRvSvd+Qw/X2NQBq2p',0,5808835364836477569,'2023-07-15 14:58:43',6071976419609675523,'2023-09-27 14:45:28','');
INSERT INTO CCSP_DEVICE.DEVICE_TYPE (DEVICE_TYPE_ID,DEVICE_TYPE_NAME,DEFAULT_FLAG,VENDOR_ID,FAMILY_TYPE,MACHINE_TYPE_ID,PARENT_ID,HCCS_IMAGE_ID,INTERACTION_SERIAL_NUMBER,MGT_METHOD,MGT_PORT,BUSI_PORT,CLOUD_VSM_TOTAL,SUPPORT_MAIN_KEY_FLAG,SUPPORT_SEC_MANAGE_FLAG,SUPPORT_GEN_KEY_FLAG,SUPPORT_SNMP_FLAG,TOKEN_CALL_BACK_FLAG,NEED_PASSWORD_FLAG,READ_INFO_FLAG,MGT_PUBLICKEY_FLAG,KEY_TEMPLET_IDS,DEFAULT_KEY_TEMPLET_IDS,SUPPORT_EXTERNAL_SERVICE,SUPPORT_MANAGE_PT,CONNECT_AUTH_CODE,INVALID_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) VALUES (31,'云密码机',1,5466516874462629769,1,NULL,0,NULL,'chsm_sansec_standard_0088',1,8083,NULL,32,0,0,0,0,0,0,0,0,NULL,NULL,0,0,NULL,0,5808835364836477569,'2023-07-10 10:32:34',6071976419609675523,'2023-09-27 14:46:38','');
INSERT INTO CCSP_DEVICE.DEVICE_TYPE (DEVICE_TYPE_ID,DEVICE_TYPE_NAME,DEFAULT_FLAG,VENDOR_ID,FAMILY_TYPE,MACHINE_TYPE_ID,PARENT_ID,HCCS_IMAGE_ID,INTERACTION_SERIAL_NUMBER,MGT_METHOD,MGT_PORT,BUSI_PORT,CLOUD_VSM_TOTAL,SUPPORT_MAIN_KEY_FLAG,SUPPORT_SEC_MANAGE_FLAG,SUPPORT_GEN_KEY_FLAG,SUPPORT_SNMP_FLAG,TOKEN_CALL_BACK_FLAG,NEED_PASSWORD_FLAG,READ_INFO_FLAG,MGT_PUBLICKEY_FLAG,KEY_TEMPLET_IDS,DEFAULT_KEY_TEMPLET_IDS,SUPPORT_EXTERNAL_SERVICE,SUPPORT_MANAGE_PT,CONNECT_AUTH_CODE,INVALID_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) VALUES (32,'云服务器密码机_V5.2.5',1,5466516874462629769,3,2,31,2,'vsm_sansec_1002',1,443,8008,NULL,1,1,1,0,0,1,1,1,NULL,NULL,0,0,'zM7MYRiXXu7pgCPspRhR70oMk0sJQgTU7wXIhD0e4NNz8GGRvSvd+Qw/X2NQBq2p',0,5808835364836477569,'2023-07-10 10:33:37',6071976419609675523,'2023-09-27 14:49:53','');
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1,1,'1',1,0,NULL,6071976419609675523,'2023-09-27 11:29:37',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2,1,'2',1,0,NULL,6071976419609675523,'2023-09-27 11:29:37',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3,1,'3',1,0,NULL,6071976419609675523,'2023-09-27 11:29:37',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (4,1,'4',1,0,NULL,6071976419609675523,'2023-09-27 11:29:37',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5,1,'5',1,0,NULL,6071976419609675523,'2023-09-27 11:29:37',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6,1,'6',1,0,NULL,6071976419609675523,'2023-09-27 11:29:37',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391182620985096,6,'1',2,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391182620985097,6,'2',2,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391182620985098,6,'3',2,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391182620985099,6,'4',2,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075391182620985100,6,'5',2,0,NULL,6071976419609675523,'2023-09-27 14:39:26',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405145760990985,31,'1',1,0,NULL,6071976419609675523,'2023-09-27 14:46:22',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405145760990986,31,'2',1,0,NULL,6071976419609675523,'2023-09-27 14:46:22',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405145760990987,31,'3',1,0,NULL,6071976419609675523,'2023-09-27 14:46:22',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405145760990988,31,'4',1,0,NULL,6071976419609675523,'2023-09-27 14:46:22',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405145760990989,31,'5',1,0,NULL,6071976419609675523,'2023-09-27 14:46:22',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075405145760990990,31,'6',1,0,NULL,6071976419609675523,'2023-09-27 14:46:22',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412213599438601,32,'1',2,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412213599438602,32,'2',2,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412213599438603,32,'3',2,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412213599438604,32,'4',2,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_TYPE_RELA_VALUE (ID,DEVICE_TYPE_ID,VALUE,VALUE_TYPE,DEFAULT_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6075412213599438605,32,'5',2,0,NULL,6071976419609675523,'2023-09-27 14:49:53',NULL,NULL);
INSERT INTO CCSP_DEVICE.DEVICE_VENDOR (VENDOR_ID,VENDOR_NAME,VENDOR_SHORT_NAME,LINK_MAN,LINK_MAN_PHONE,DEFAULT_FLAG,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5466516874462629769,'三未信安科技股份有限公司','sansec',NULL,NULL,1,0,'默认厂商',1234,'2023-03-01 14:08:18',5469505260282187143,'2023-03-17 11:25:37');
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (101,1,1,'1','getVsmInfo','/','/api/1.0/vsm',0,'获取虚拟机信息',NULL,'',NULL,'');
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (102,1,2,'2','getVsmStatus','/','/api/1.0/vsm/status',0,'获取虚拟机运行状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (103,1,3,'3','configVsmNetwork','/','/api/1.0/vsm/network',0,'配置虚拟机网络信息',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (104,1,4,'4','createVsm','/','/api/1.0/vsm',0,'创建虚拟机',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (105,1,5,'5','vsmOperate','/','/api/1.0/vsm',0,'虚拟机启动、停止、重启、重置、删除请求',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (106,1,6,'6','getChsmInfo','/','/api/1.0/chsm',0,'获取宿主机信息',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (107,1,7,'7','getChsmStatus','/','/api/1.0/chsm/status',0,'获取宿主机设备运行状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (108,1,8,'8','configChsmPublicKey','/','/api/1.0/chsm/authpk',0,'配置宿主机设备公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (109,1,9,'9','clearChsmPublicKey','/','/api/1.0/chsm/authpk',0,'清楚宿主机设备公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (110,1,10,'10','getChsmPublicKeyFinger','/','/api/1.0/chsm/authpk',0,'获取宿主机公钥指纹',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (111,1,30,'30','configVsm','/','/api/1.0/vsm/config',0,'配置虚拟机信息',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (201,2,1,'1','getVsmInfo','/','/api/1.0/vsm',0,'获取虚拟机信息',NULL,'',NULL,'');
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (202,2,2,'2','getVsmStatus','/','/api/1.0/vsm/status',0,'获取虚拟机运行状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (203,2,3,'3','configVsmNetwork','/','/api/1.0/vsm/network',0,'配置虚拟机网络信息',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (204,2,4,'4','createVsm','/','/api/1.0/vsm',0,'创建虚拟机',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (205,2,5,'5','vsmOperate','/','/api/1.0/vsm',0,'虚拟机启动、停止、重启、重置、删除请求',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (206,2,6,'6','getChsmInfo','/','/api/1.0/chsm',0,'获取宿主机信息',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (207,2,7,'7','getChsmStatus','/','/api/1.0/chsm/status',0,'获取宿主机设备运行状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (208,2,8,'8','configChsmPublicKey','/','/api/1.0/chsm/authpk',0,'配置宿主机设备公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (209,2,9,'9','clearChsmPublicKey','/','/api/1.0/chsm/authpk',0,'清楚宿主机设备公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (210,2,10,'10','getChsmPublicKeyFinger','/','/api/1.0/chsm/authpk',0,'获取宿主机公钥指纹',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (211,2,30,'30','configVsm','/','/api/1.0/vsm/config',0,'配置虚拟机信息',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2001,20,11,'11','getHsmInfo','/pkiweb/sansecplat','/api/1.0/chsm',0,'获取物理机信息',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2002,20,12,'12','getHsmStatus','/pkiweb/sansecplat','/api/1.0/chsm/status',0,'获取物理机设备状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2003,20,13,'13','configHsmNetwork','/pkiweb/sansecplat','/api/1.0/chsm/network',0,'配置物理机网络',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2004,20,14,'14','restartHsm','/pkiweb/sansecplat','/api/1.0/chsm',0,'重启物理机',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2005,20,15,'15','configHsmPublicKey','/pkiweb/sansecplat','/api/1.0/chsm/authpk',0,'配置物理机公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2006,20,16,'16','clearHsmPublicKey','/pkiweb/sansecplat','/api/1.0/chsm/authpk',0,'清楚物理机公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2007,20,17,'17','getHsmPublicKeyFinger','/pkiweb/sansecplat','/api/1.0/chsm/authpk',0,'获取物理机公钥指纹',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2008,20,18,'18','createHsmLmk','/pkiweb/sansecplat/hsmm','/key/masterKey/generateLMK',0,'创建物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2009,20,19,'19','generateHsmLMKWithComponent','/pkiweb/sansecplat/hsmm','/key/masterKey/importLMK',0,'分量合成物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2010,20,20,'20','getHsmDeviceKeyPair','/pkiweb/sansecplat/hsmm','/platform/key/gennerAsymmTempKey',0,'获取物理机设备公私钥对',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2011,20,21,'21','exportHsmMasterKey','/pkiweb/sansecplat/hsmm','/platform/key/exportLMKByCipher',0,'导出物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2012,20,22,'22','importHsmMasterKey','/pkiweb/sansecplat/hsmm','/platform/key/impoerLMKByCipher',0,'导入物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2013,20,23,'23','getDeviceInitState','/pkiweb/sansecplat','/api/1.0/chsm/init/status',0,'获取设备初始化状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2014,20,24,'24','deviceInit','/pkiweb/sansecplat','/api/1.0/chsm/init',0,'设备初始化',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2015,20,25,'25','importAsyncKeyPair','/pkiweb/sansecplat/hsmm','/platform/key/importAsyncKeyPair',0,'导入非对称密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2016,20,26,'26','importSyncKeyPair','/pkiweb/sansecplat/hsmm','/platform/key/importSyncKeyPair',0,'导入对称密钥分量',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2017,20,27,'27','deleteSymmetricKey','/pkiweb/sansecplat/hsmm','/key/symmetricKey/deleteSymmetricKey',0,'删除对称密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2018,20,28,'28','deleteSM2Key','/pkiweb/sansecplat/hsmm','/key/sm2Key/deleteSM2Key',0,'删除sm2密钥对',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2019,20,29,'29','getHsmServiceStatus','/PlatformServlet','?method=getServiceState',0,'获取物理机服务状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2101,21,101,'101','getHsmInfo','/PlatformServlet','?method=getHsmInfo',0,'获取物理机信息',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2102,21,102,'102','getHsmStatus','/PlatformServlet','?method=getState',0,'获取物理机设备状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2103,21,103,'103','configHsmNetwork','/PlatformServlet','?method=network',0,'配置物理机网络',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2104,21,104,'104','restartHsm','/PlatformServlet','?method=restartHsm',0,'重启物理机',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2105,21,105,'105','configHsmPublicKey','/AuthServlet','?method=authPK',0,'配置物理机公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2106,21,106,'106','clearHsmPublicKey','/AuthServlet','?method=cleanPK',0,'清楚物理机公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2107,21,107,'107','getHsmPublicKeyFinger','/AuthServlet','?method=getAuthPKFingerprints',0,'获取物理机公钥指纹',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2108,21,108,'108','createHsmLmk','/PlatformServlet','?method=generateLMK',0,'创建物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2109,21,109,'109','generateHsmLMKWithComponent','/PlatformServlet','?method=importLMK',0,'分量合成物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2110,21,110,'110','getHsmDeviceKeyPair','/PlatformServlet','?method=gennerAsymmTempKey',0,'获取物理机设备公私钥对',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2111,21,111,'111','exportHsmMasterKey','/PlatformServlet','?method=exportLMKByCipher',0,'导出物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2112,21,112,'112','importHsmMasterKey','/PlatformServlet','?method=importLMKByCipher',0,'导入物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2113,21,113,'113','getDeviceInitState','/PlatformServlet','?method=initStatus',0,'获取设备初始化状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2114,21,114,'114','deviceInit','/PlatformServlet','?method=doHsmInit',0,'设备初始化',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2115,21,115,'115','importAsyncKeyPair','/PlatformServlet','?method=importAsyncKeyPair',0,'导入非对称密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2116,21,116,'116','importSyncKeyPair','/PlatformServlet','?method=importSymmKey',0,'导入对称密钥分量',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2117,21,117,'117','deleteSymmetricKey','/PlatformServlet','?method=delHsmSymmetricKey',0,'删除对称密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2118,21,118,'118','deleteSM2Key','/PlatformServlet','?method=delHsmKeyPair',0,'删除sm2密钥对',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2119,21,119,'119','getHsmServiceStatus','/PlatformServlet','?method=getServiceState',0,'获取物理机服务状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3001,30,11,'11','getHsmInfo','/pkiweb/sansecplat','/api/1.0/chsm',0,'获取物理机信息',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3002,30,12,'12','getHsmStatus','/pkiweb/sansecplat','/api/1.0/chsm/status',0,'获取物理机设备状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3003,30,13,'13','configHsmNetwork','/pkiweb/sansecplat','/api/1.0/chsm/network',0,'配置物理机网络',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3004,30,14,'14','restartHsm','/pkiweb/sansecplat','/api/1.0/chsm',0,'重启物理机',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3005,30,15,'15','configHsmPublicKey','/pkiweb/sansecplat','/api/1.0/chsm/authpk',0,'配置物理机公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3006,30,16,'16','clearHsmPublicKey','/pkiweb/sansecplat','/api/1.0/chsm/authpk',0,'清楚物理机公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3007,30,17,'17','getHsmPublicKeyFinger','/pkiweb/sansecplat','/api/1.0/chsm/authpk',0,'获取物理机公钥指纹',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3008,30,18,'18','createHsmLmk','/pkiweb/sansecplat/hsmm','/key/masterKey/generateLMK',0,'创建物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3009,30,19,'19','generateHsmLMKWithComponent','/pkiweb/sansecplat/hsmm','/key/masterKey/importLMK',0,'分量合成物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3010,30,20,'20','getHsmDeviceKeyPair','/pkiweb/sansecplat/hsmm','/platform/key/gennerAsymmTempKey',0,'获取物理机设备公私钥对',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3011,30,21,'21','exportHsmMasterKey','/pkiweb/sansecplat/hsmm','/platform/key/exportLMKByCipher',0,'导出物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3012,30,22,'22','importHsmMasterKey','/pkiweb/sansecplat/hsmm','/platform/key/impoerLMKByCipher',0,'导入物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3013,30,23,'23','getDeviceInitState','/pkiweb/sansecplat','/api/1.0/chsm/init/status',0,'获取设备初始化状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3014,30,24,'24','deviceInit','/pkiweb/sansecplat','/api/1.0/chsm/init',0,'设备初始化',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3015,30,25,'25','importAsyncKeyPair','/pkiweb/sansecplat/hsmm','/platform/key/importAsyncKeyPair',0,'导入非对称密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3016,30,26,'26','importSyncKeyPair','/pkiweb/sansecplat/hsmm','/platform/key/importSyncKeyPair',0,'导入对称密钥分量',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3017,30,27,'27','deleteSymmetricKey','/pkiweb/sansecplat/hsmm','/key/symmetricKey/deleteSymmetricKey',0,'删除对称密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3018,30,28,'28','deleteSM2Key','/pkiweb/sansecplat/hsmm','/key/sm2Key/deleteSM2Key',0,'删除sm2密钥对',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3019,30,29,'29','getHsmServiceStatus','/PlatformServlet','?method=getServiceState',0,'获取物理机服务状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3101,31,101,'101','getHsmInfo','/PlatformServlet','?method=getHsmInfo',0,'获取物理机信息',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3102,31,102,'102','getHsmStatus','/PlatformServlet','?method=getState',0,'获取物理机设备状态',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3103,31,103,'103','configHsmNetwork','/PlatformServlet','?method=network',0,'配置物理机网络',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3104,31,104,'104','restartHsm','/PlatformServlet','?method=restartHsm',0,'重启物理机',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3105,31,105,'105','configHsmPublicKey','/AuthServlet','?method=authPK',0,'配置物理机公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3106,31,106,'106','clearHsmPublicKey','/AuthServlet','?method=cleanPK',0,'清楚物理机公钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3107,31,107,'107','getHsmPublicKeyFinger','/AuthServlet','?method=getAuthPKFingerprints',0,'获取物理机公钥指纹',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3108,31,108,'108','createHsmLmk','/PlatformServlet','?method=generateLMK',0,'创建物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID,INTERACTION_API_ID,SORD_NUM,API_SERIAL_NUM,API_NAME,CONTEXT_PATH,API_PATH,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3109,31,109,'109','generateHsmLMKWithComponent','/PlatformServlet','?method=importLMK',0,'分量合成物理机设备主密钥',NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID, INTERACTION_API_ID, SORD_NUM, API_SERIAL_NUM, API_NAME,
                                                 CONTEXT_PATH, API_PATH, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME,
                                                 UPDATE_BY, UPDATE_TIME)
VALUES (3110, 31, 110, '110', 'getHsmDeviceKeyPair', '/PlatformServlet', '?method=gennerAsymmTempKey', 0, '获取物理机设备公私钥对',
        NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID, INTERACTION_API_ID, SORD_NUM, API_SERIAL_NUM, API_NAME,
                                                 CONTEXT_PATH, API_PATH, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME,
                                                 UPDATE_BY, UPDATE_TIME)
VALUES (3111, 31, 111, '111', 'exportHsmMasterKey', '/PlatformServlet', '?method=exportLMKByCipher', 0, '导出物理机设备主密钥',
        NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID, INTERACTION_API_ID, SORD_NUM, API_SERIAL_NUM, API_NAME,
                                                 CONTEXT_PATH, API_PATH, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME,
                                                 UPDATE_BY, UPDATE_TIME)
VALUES (3112, 31, 112, '112', 'importHsmMasterKey', '/PlatformServlet', '?method=importLMKByCipher', 0, '导入物理机设备主密钥',
        NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID, INTERACTION_API_ID, SORD_NUM, API_SERIAL_NUM, API_NAME,
                                                 CONTEXT_PATH, API_PATH, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME,
                                                 UPDATE_BY, UPDATE_TIME)
VALUES (3113, 31, 113, '113', 'getDeviceInitState', '/PlatformServlet', '?method=initStatus', 0, '获取设备初始化状态', NULL,
        NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID, INTERACTION_API_ID, SORD_NUM, API_SERIAL_NUM, API_NAME,
                                                 CONTEXT_PATH, API_PATH, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME,
                                                 UPDATE_BY, UPDATE_TIME)
VALUES (3114, 31, 114, '114', 'deviceInit', '/PlatformServlet', '?method=doHsmInit', 0, '设备初始化', NULL, NULL, NULL,
        NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID, INTERACTION_API_ID, SORD_NUM, API_SERIAL_NUM, API_NAME,
                                                 CONTEXT_PATH, API_PATH, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME,
                                                 UPDATE_BY, UPDATE_TIME)
VALUES (3115, 31, 115, '115', 'importAsyncKeyPair', '/PlatformServlet', '?method=importAsyncKeyPair', 0, '导入非对称密钥',
        NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID, INTERACTION_API_ID, SORD_NUM, API_SERIAL_NUM, API_NAME,
                                                 CONTEXT_PATH, API_PATH, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME,
                                                 UPDATE_BY, UPDATE_TIME)
VALUES (3116, 31, 116, '116', 'importSyncKeyPair', '/PlatformServlet', '?method=importSymmKey', 0, '导入对称密钥分量', NULL,
        NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID, INTERACTION_API_ID, SORD_NUM, API_SERIAL_NUM, API_NAME,
                                                 CONTEXT_PATH, API_PATH, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME,
                                                 UPDATE_BY, UPDATE_TIME)
VALUES (3117, 31, 117, '117', 'deleteSymmetricKey', '/PlatformServlet', '?method=delHsmSymmetricKey', 0, '删除对称密钥', NULL,
        NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID, INTERACTION_API_ID, SORD_NUM, API_SERIAL_NUM, API_NAME,
                                                 CONTEXT_PATH, API_PATH, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME,
                                                 UPDATE_BY, UPDATE_TIME)
VALUES (3118, 31, 118, '118', 'deleteSM2Key', '/PlatformServlet', '?method=delHsmKeyPair', 0, '删除sm2密钥对', NULL, NULL,
        NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_API_TEMPLATE (ID, INTERACTION_API_ID, SORD_NUM, API_SERIAL_NUM, API_NAME,
                                                 CONTEXT_PATH, API_PATH, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME,
                                                 UPDATE_BY, UPDATE_TIME)
VALUES (3119, 31, 119, '119', 'getHsmServiceStatus', '/PlatformServlet', '?method=getServiceState', 0, '获取物理机服务状态',
        NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_IMAGE_TYPE (ID, IMAGE_NAME, IMAGE_VALUE, IMAGE_VERSION, INVALID_FLAG, VENDOR_ID,
                                               REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (1, '密钥管理服务器', 'kms', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_IMAGE_TYPE (ID, IMAGE_NAME, IMAGE_VALUE, IMAGE_VERSION, INVALID_FLAG, VENDOR_ID,
                                               REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (2, '服务器密码机', 'hsm', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_IMAGE_TYPE (ID, IMAGE_NAME, IMAGE_VALUE, IMAGE_VERSION, INVALID_FLAG, VENDOR_ID,
                                               REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (3, '签名验签服务器', 'svs', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_IMAGE_TYPE (ID, IMAGE_NAME, IMAGE_VALUE, IMAGE_VERSION, INVALID_FLAG, VENDOR_ID,
                                               REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (4, '金融密码机', 'phsm', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_IMAGE_TYPE (ID, IMAGE_NAME, IMAGE_VALUE, IMAGE_VERSION, INVALID_FLAG, VENDOR_ID,
                                               REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (5, '动态令牌服务器', 'sms', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_IMAGE_TYPE (ID, IMAGE_NAME, IMAGE_VALUE, IMAGE_VERSION, INVALID_FLAG, VENDOR_ID,
                                               REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (6, '数字证书认证服务器', 'sca', NULL, 0, 5466516874462629769, NULL, NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE (ID, INTERACTION_NAME, INTERACTION_SERIAL_NUMBER, INVALID_FLAG,
                                                     REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME,
                                                     FAMILY_TYPE)
VALUES (1, '三未标准云密码机0088管理规范', 'chsm_sansec_standard_0088', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 1);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE (ID, INTERACTION_NAME, INTERACTION_SERIAL_NUMBER, INVALID_FLAG,
                                                     REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME,
                                                     FAMILY_TYPE)
VALUES (2, '三未旧云密码机0088管理规范', 'chsm_sansec_old_0088', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 1);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE (ID, INTERACTION_NAME, INTERACTION_SERIAL_NUMBER, INVALID_FLAG,
                                                     REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME,
                                                     FAMILY_TYPE)
VALUES (20, '三未虚拟机统一web平台管理规范', 'vsm_sansec_unified_1001', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 3);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE (ID, INTERACTION_NAME, INTERACTION_SERIAL_NUMBER, INVALID_FLAG,
                                                     REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME,
                                                     FAMILY_TYPE)
VALUES (21, '三未虚拟机V5密码机管理规范', 'vsm_sansec_1002', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 3);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE (ID, INTERACTION_NAME, INTERACTION_SERIAL_NUMBER, INVALID_FLAG,
                                                     REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME,
                                                     FAMILY_TYPE)
VALUES (22, '密服一体虚拟机管理规范', 'vsm_extra_standard_1003', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 3);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE (ID, INTERACTION_NAME, INTERACTION_SERIAL_NUMBER, INVALID_FLAG,
                                                     REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME,
                                                     FAMILY_TYPE)
VALUES (30, '三未物理机统一web平台管理规范', 'hsm_sansec_unified_1001', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 2);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE (ID, INTERACTION_NAME, INTERACTION_SERIAL_NUMBER, INVALID_FLAG,
                                                     REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME,
                                                     FAMILY_TYPE)
VALUES (31, '三未物理密码机管理规范', 'hsm_sansec_1002', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 2);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_INTERACTION_TYPE (ID, INTERACTION_NAME, INTERACTION_SERIAL_NUMBER, INVALID_FLAG,
                                                     REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME,
                                                     FAMILY_TYPE)
VALUES (32, '密服一体物理机管理规范', 'hsm_extra_standard_1003', 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 2);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_MACHINE_TYPE (ID, SERVER_NAME, SERVER_CODE, INVALID_FLAG, REMARK, CREATE_BY,
                                                 CREATE_TIME, UPDATE_BY, UPDATE_TIME, FAMILY_TYPE)
VALUES (1, '云密码机', 1, 0, '', 1, '2023-03-01 14:08:18', NULL, NULL, 1);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_MACHINE_TYPE (ID, SERVER_NAME, SERVER_CODE, INVALID_FLAG, REMARK, CREATE_BY,
                                                 CREATE_TIME, UPDATE_BY, UPDATE_TIME, FAMILY_TYPE)
VALUES (2, '服务器密码机', 2, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 0);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_MACHINE_TYPE (ID, SERVER_NAME, SERVER_CODE, INVALID_FLAG, REMARK, CREATE_BY,
                                                 CREATE_TIME, UPDATE_BY, UPDATE_TIME, FAMILY_TYPE)
VALUES (3, '签名验签服务器', 3, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 0);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_MACHINE_TYPE (ID, SERVER_NAME, SERVER_CODE, INVALID_FLAG, REMARK, CREATE_BY,
                                                 CREATE_TIME, UPDATE_BY, UPDATE_TIME, FAMILY_TYPE)
VALUES (4, '时间戳密码机', 4, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 0);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_MACHINE_TYPE (ID, SERVER_NAME, SERVER_CODE, INVALID_FLAG, REMARK, CREATE_BY,
                                                 CREATE_TIME, UPDATE_BY, UPDATE_TIME, FAMILY_TYPE)
VALUES (5, '金融密码机', 5, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 0);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_MACHINE_TYPE (ID, SERVER_NAME, SERVER_CODE, INVALID_FLAG, REMARK, CREATE_BY,
                                                 CREATE_TIME, UPDATE_BY, UPDATE_TIME, FAMILY_TYPE)
VALUES (6, '网关服务器', 6, 0, NULL, 1, '2023-03-01 14:08:18', NULL, NULL, 0);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_MANAGEMENT_STATUS (ID, STATUS_NAME, STATUS_CODE, INVALID_FLAG, REMARK, CREATE_BY,
                                                      CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (1, '标记使用', '1', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_MANAGEMENT_STATUS (ID, STATUS_NAME, STATUS_CODE, INVALID_FLAG, REMARK, CREATE_BY,
                                                      CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (2, '未使用', '2', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_MANAGEMENT_STATUS (ID, STATUS_NAME, STATUS_CODE, INVALID_FLAG, REMARK, CREATE_BY,
                                                      CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (3, '平台使用', '3', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS (ID, OPER_NAME, OPER_CODE, INVALID_FLAG, REMARK, CREATE_BY,
                                                     CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES (0, '运行中', 0, 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS (ID,OPER_NAME,OPER_CODE,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1,'创建中',1,0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS (ID,OPER_NAME,OPER_CODE,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2,'创建失败',2,0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS (ID,OPER_NAME,OPER_CODE,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3,'启动中',3,0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS (ID,OPER_NAME,OPER_CODE,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (4,'重启中',4,0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS (ID,OPER_NAME,OPER_CODE,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5,'停止中',5,0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS (ID,OPER_NAME,OPER_CODE,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (6,'删除中',6,0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_OPERATION_STATUS (ID,OPER_NAME,OPER_CODE,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (7,'删除失败',7,0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_VSM_RESOURCE (ID,RESOURCE_VALUE,RESOURCE_NAME,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1,'1','一倍虚机资源',0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_VSM_RESOURCE (ID,RESOURCE_VALUE,RESOURCE_NAME,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2,'2','二倍虚机资源',0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_VSM_RESOURCE (ID,RESOURCE_VALUE,RESOURCE_NAME,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3,'4','四倍虚机资源',0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_VSM_RESOURCE (ID,RESOURCE_VALUE,RESOURCE_NAME,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (4,'8','八倍虚机资源',0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_DEVICE_VSM_RESOURCE (ID,RESOURCE_VALUE,RESOURCE_NAME,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5,'0','动态分配',0,NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_AUTHENTICATION (ID,SNMP_AUTHENTICATION_ID,SNMP_AUTHENTICATION_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (0,0,'MD5',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_AUTHENTICATION (ID,SNMP_AUTHENTICATION_ID,SNMP_AUTHENTICATION_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1,1,'SHA',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_AUTHENTICATION (ID,SNMP_AUTHENTICATION_ID,SNMP_AUTHENTICATION_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2,2,'SHA224',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_AUTHENTICATION (ID,SNMP_AUTHENTICATION_ID,SNMP_AUTHENTICATION_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3,3,'SHA256',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_AUTHENTICATION (ID,SNMP_AUTHENTICATION_ID,SNMP_AUTHENTICATION_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (4,4,'SHA384',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_AUTHENTICATION (ID,SNMP_AUTHENTICATION_ID,SNMP_AUTHENTICATION_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (5,5,'SHA512',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_PRIVACY (ID,SNMP_PRIVACY_ID,SNMP_PRIVACY_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (0,0,'DES',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_PRIVACY (ID,SNMP_PRIVACY_ID,SNMP_PRIVACY_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (1,1,'TRIPLE_DES',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_PRIVACY (ID,SNMP_PRIVACY_ID,SNMP_PRIVACY_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (2,2,'AES',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_PRIVACY (ID,SNMP_PRIVACY_ID,SNMP_PRIVACY_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (3,3,'AES192',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.DIC_SNMP_PRIVACY (ID,SNMP_PRIVACY_ID,SNMP_PRIVACY_NAME,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) VALUES (4,4,'AES256',NULL,NULL,NULL,NULL,NULL);
INSERT INTO CCSP_DEVICE.SYS_JOB (JOB_ID,JOB_NAME,JOB_GROUP,SERVER_ID,METHOD_URL,JSON_PARAM,CRON_EXPRESSION,MISFIRE_POLICY,CONCURRENT,JOB_STATUS,CREATED_BY,CREATE_TIME,UPDATED_BY,UPDATE_TIME,REMARK) VALUES (1,'getRunStatus','runStatus','ccsp-atom-device',NULL,'runStatusCronJobImpl.getDeviceInfoStatus()','*/30 * * * * ?','3','1','0',NULL,NULL,NULL,NULL,NULL);

INSERT INTO CCSP_DEVICE.DEVICE_MONITOR_CONFIG(ID, DEVICE_TYPE_ID, MONITOR_TYPE, URL, SNMP_VERSION, SNMP_PROTO, SNMP_PORT, SAFE_LEVEL, SECURITY_NAME, AUTHENTICATION_PROTOCOL, AUTHENTICATION_AUTH_CODE, PRIVACY_PROTOCOL, PRIVACY_AUTH_CODE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118851783558825989, 6, 1, NULL, NULL, 'https', 19443, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-12 14:26:33', NULL, '2023-10-12 14:26:33');
INSERT INTO CCSP_DEVICE.DEVICE_MONITOR_CONFIG(ID, DEVICE_TYPE_ID, MONITOR_TYPE, URL, SNMP_VERSION, SNMP_PROTO, SNMP_PORT, SAFE_LEVEL, SECURITY_NAME, AUTHENTICATION_PROTOCOL, AUTHENTICATION_AUTH_CODE, PRIVACY_PROTOCOL, PRIVACY_AUTH_CODE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118878410611230722, 21, 2, NULL, 'v3', 'UDP', 161, 1, 'sansec', 0, 'A/YC4vF0axVbZJsskjNGhQ==', 2, 'A/YC4vF0axVbZJsskjNGhQ==', NULL, NULL, '2023-10-12 14:39:47', NULL, '2023-10-12 14:39:47');
INSERT INTO CCSP_DEVICE.DEVICE_MONITOR_CONFIG(ID, DEVICE_TYPE_ID, MONITOR_TYPE, URL, SNMP_VERSION, SNMP_PROTO, SNMP_PORT, SAFE_LEVEL, SECURITY_NAME, AUTHENTICATION_PROTOCOL, AUTHENTICATION_AUTH_CODE, PRIVACY_PROTOCOL, PRIVACY_AUTH_CODE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118880293417519113, 32, 2, NULL, 'v3', 'UDP', 161, 1, 'snmpuser', 5, 'DXTNvsZh9NyBEcy74FgtnQ==', 2, 'DXTNvsZh9NyBEcy74FgtnQ==', NULL, NULL, '2023-10-19 09:22:59', NULL, '2023-10-19 09:22:59');

INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6116333655350315014, 21, '1.3.6.1.4.1.2021.11.9.0', 'sansec.cpu.userCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6075546759500924936, '2023-10-11 17:35:47', 6075546759500924936, '2023-10-11 17:35:47');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6116336230317426690, 21, '1.3.6.1.4.1.2021.11.10.0', 'sansec.cpu.sysCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6075546759500924936, '2023-10-11 17:37:04', 6075546759500924936, '2023-10-11 17:37:04');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6116356515078866947, 21, '1.2.3.4.5.21.2', 'sansec.mem.percent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '(${0}-${1})/${0}', NULL, 6075546759500924936, '2023-10-11 17:47:08', 6075546759500924936, '2023-10-11 17:47:08');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6116356515078866948, 21, NULL, NULL, NULL, NULL, NULL, 6116356515078866947, 1, '1.3.6.1.2.1.25.2.2.0', NULL, NULL, NULL, 6075546759500924936, '2023-10-11 17:47:08', 6075546759500924936, '2023-10-11 17:47:08');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6116356515078866949, 21, NULL, NULL, NULL, NULL, NULL, 6116356515078866947, 2, '1.3.6.1.4.1.2021.4.6.0', NULL, NULL, NULL, 6075546759500924936, '2023-10-11 17:47:08', 6075546759500924936, '2023-10-11 17:47:08');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6116357135567423490, 21, '1.3.6.1.2.1.25.2.2.0', 'sansec.mem.total', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6075546759500924936, '2023-10-11 17:47:27', 6075546759500924936, '2023-10-11 17:47:27');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118780652053727238, 21, '1.2.3.4.5.21.1', 'sansec.cpu.id', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '${0}+${1}', NULL, 6075546759500924936, '2023-10-12 13:51:13', 6075546759500924936, '2023-10-12 13:51:13');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118780652053727239, 21, NULL, NULL, NULL, NULL, NULL, 6118780652053727238, 1, '1.3.6.1.4.1.2021.11.9.0', NULL, NULL, NULL, 6075546759500924936, '2023-10-12 13:51:13', 6075546759500924936, '2023-10-12 13:51:13');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118780652053727240, 21, NULL, NULL, NULL, NULL, NULL, 6118780652053727238, 2, '1.3.6.1.4.1.2021.11.10.0', NULL, NULL, NULL, 6075546759500924936, '2023-10-12 13:51:13', 6075546759500924936, '2023-10-12 13:51:13');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118801853656467462, 21, '1.3.6.1.4.1.2021.4.6.0', 'sansec.mem.free', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6075546759500924936, '2023-10-12 14:01:45', 6075546759500924936, '2023-10-12 14:01:45');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118869345646544904, 32, '1.2.3.4.5.32.1', 'sansec.cpu.id', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '${0}+${1}', NULL, 6075546759500924936, '2023-10-12 14:35:16', 6075546759500924936, '2023-10-12 14:35:16');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118869345646544905, 32, NULL, NULL, NULL, NULL, NULL, 6118869345646544904, 1, '1.3.6.1.4.1.2021.11.9.0', NULL, NULL, NULL, 6075546759500924936, '2023-10-12 14:35:16', 6075546759500924936, '2023-10-12 14:35:16');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118869345646544906, 32, NULL, NULL, NULL, NULL, NULL, 6118869345646544904, 2, '1.3.6.1.4.1.2021.11.10.0', NULL, NULL, NULL, 6075546759500924936, '2023-10-12 14:35:16', 6075546759500924936, '2023-10-12 14:35:16');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118870217826895880, 32, '1.3.6.1.4.1.2021.11.9.0', 'sansec.cpu.userCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6075546759500924936, '2023-10-12 14:35:42', 6075546759500924936, '2023-10-12 14:35:42');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118870603400873992, 32, '1.3.6.1.4.1.2021.11.10.0', 'sansec.cpu.sysCpu', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6075546759500924936, '2023-10-12 14:35:54', 6075546759500924936, '2023-10-12 14:35:54');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118872014230521863, 32, '1.2.3.4.5.32.2', 'sansec.mem.percent', 'Integer', 'Get', 1, NULL, NULL, NULL, 'CUSTOM', '(${0}-${1})/${0}', NULL, 6075546759500924936, '2023-10-12 14:36:36', 6075546759500924936, '2023-10-12 14:36:36');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118872014230521864, 32, NULL, NULL, NULL, NULL, NULL, 6118872014230521863, 1, '1.3.6.1.2.1.25.2.2.0', NULL, NULL, NULL, 6075546759500924936, '2023-10-12 14:36:36', 6075546759500924936, '2023-10-12 14:36:36');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118872014230521865, 32, NULL, NULL, NULL, NULL, NULL, 6118872014230521863, 2, '1.3.6.1.4.1.2021.4.6.0', NULL, NULL, NULL, 6075546759500924936, '2023-10-12 14:36:36', 6075546759500924936, '2023-10-12 14:36:36');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118872511507204105, 32, '1.3.6.1.2.1.25.2.2.0', 'sansec.mem.total', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6075546759500924936, '2023-10-12 14:36:51', 6075546759500924936, '2023-10-12 14:36:51');
INSERT INTO  CCSP_DEVICE.DEVICE_SNMP_OID_CONFIG(ID, DEVICE_TYPE_ID, OID, NAME, VALUE_TYPE, REQUEST_TYPE, COLLECTOR_TYPE, PARENT_ID, SUB_OID_KEY, SUB_OID_VALUE, COMPUTING_TYPE, CALCULATE_TEMPLATE, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (6118874040683333638, 32, '1.3.6.1.4.1.2021.4.6.0', 'sansec.mem.free', 'Integer', 'Get', 0, NULL, NULL, NULL, NULL, NULL, NULL, 6075546759500924936, '2023-10-12 14:37:36', 6075546759500924936, '2023-10-12 14:37:36');

