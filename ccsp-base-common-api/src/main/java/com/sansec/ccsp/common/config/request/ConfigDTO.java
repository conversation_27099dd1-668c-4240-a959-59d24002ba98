package com.sansec.ccsp.common.config.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>aw<PERSON>
 * @Description: 配置表;
 * @Date: 2023-2-18
 */
@Data
public class ConfigDTO {
    /**
     * 主键
     */
    private Long configId;
    /**
     * 业务服务类型id
     */
    private Integer busiServiceTypeId;
    /**
     * 配置编码
     */
    private String configCode;
    /**
     * 配置名
     */
    private String configName;
    /**
     * 配置值
     */
    private String configValue;
    /**
     * 配置类型;1明文2密文
     */
    private Integer configType;
    /**
     * 可维护标志;0：不可维护1：可维护
     */
    private Integer maintainFlag;
    /**
     * 排序
     */
    private Integer sordNum;
    /**
     * 备注
     */
    @Size(max = 100, message = "备注长度限制100")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "备注请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}
