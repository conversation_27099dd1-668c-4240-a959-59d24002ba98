CREATE TABLE "CCSP_APP"."APP_TO_BUSI_TYPE"
(
 "ID" BIGINT NOT NULL,
 "APP_ID" BIGINT NOT NULL,
 "BUSI_TYPE_ID" BIGINT NOT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_APP"."APP_REGISTER"
(
 "ID" BIGINT NOT NULL,
 "APP_ID" BIGINT NULL,
 "APP_REGISTER_STATUS" INT NOT NULL,
 "APP_CODE" VARCHAR(270) NOT NULL,
 "APP_NAME" VARCHAR(270) NOT NULL,
 "APP_SHORT" VARCHAR(270) NULL,
 "AUTH_TYPE" INT NOT NULL,
 "IS_PASS" INT NULL,
 "AUDIT_BY" BIGINT NULL,
 "AUDIT_REMARK" VARCHAR(1000) NULL,
 "TENANT_ID" BIGINT NOT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NOT NULL,
 "HMAC" VARCHAR(255) NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_APP"."APP_INFO"
(
 "APP_ID" BIGINT NOT NULL,
 "APP_CODE" VARCHAR(270) NOT NULL,
 "APP_NAME" VARCHAR(270) NOT NULL,
 "APP_SHORT" VARCHAR(270) NULL,
 "REGION_ID" BIGINT NULL,
 "AUTH_TYPE" INT NOT NULL,
 "TENANT_ID" BIGINT NOT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NOT NULL,
 "HMAC" VARCHAR(255) NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL,
 "APPLY_BY" BIGINT NULL
);

CREATE TABLE "CCSP_APP"."APP_AUTH_CODE"
(
 "ID" BIGINT NOT NULL,
 "APP_ID" BIGINT NOT NULL,
 "AUTH_CODE" VARCHAR(255) NOT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NOT NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_APP"."APP_AUTH_CERT"
(
 "ID" BIGINT NOT NULL,
 "APP_ID" BIGINT NOT NULL,
 "CERT_NAME" VARCHAR(270) NOT NULL,
 "CERT" TEXT NOT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NOT NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_APP"."APP_AUTH_AKSK"
(
 "ID" BIGINT NOT NULL,
 "TENANT_CODE" VARCHAR(64) NOT NULL,
 "APP_ID" BIGINT NOT NULL,
 "ACCESSKEY_ID" VARCHAR(200) NOT NULL,
 "SECRETKEY" VARCHAR(200) NOT NULL,
 "STATUS" INT NOT NULL,
 "REMARK" VARCHAR(300) NULL,
 "CREATE_TIME" VARCHAR(20) NULL,
 "LAST_TIME" VARCHAR(20) NULL,
 "HMAC" VARCHAR(255) NULL
);

ALTER TABLE "CCSP_APP"."APP_TO_BUSI_TYPE" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_APP"."APP_REGISTER" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_APP"."APP_INFO" ADD CONSTRAINT  PRIMARY KEY("APP_ID") ;

ALTER TABLE "CCSP_APP"."APP_AUTH_CODE" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_APP"."APP_AUTH_CERT" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_APP"."APP_AUTH_AKSK" ADD CONSTRAINT  PRIMARY KEY("ID") ;

CREATE UNIQUE INDEX "PRIMARY"
ON "CCSP_APP"."APP_TO_BUSI_TYPE"("ID");

COMMENT ON TABLE "CCSP_APP"."APP_TO_BUSI_TYPE" IS '应用和业务关联表';

COMMENT ON COLUMN "CCSP_APP"."APP_TO_BUSI_TYPE"."ID" IS '主键';

COMMENT ON COLUMN "CCSP_APP"."APP_TO_BUSI_TYPE"."APP_ID" IS '应用ID';

COMMENT ON COLUMN "CCSP_APP"."APP_TO_BUSI_TYPE"."BUSI_TYPE_ID" IS '业务类型ID';

COMMENT ON COLUMN "CCSP_APP"."APP_TO_BUSI_TYPE"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_APP"."APP_TO_BUSI_TYPE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_APP"."APP_TO_BUSI_TYPE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_APP"."APP_TO_BUSI_TYPE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_APP"."APP_TO_BUSI_TYPE"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX1380358677273200"
ON "CCSP_APP"."APP_REGISTER"("ID");

COMMENT ON TABLE "CCSP_APP"."APP_REGISTER" IS '应用申请表';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."ID" IS 'ID';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."APP_ID" IS '应用ID';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."APP_REGISTER_STATUS" IS '应用申请状态';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."APP_NAME" IS '应用名称';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."APP_SHORT" IS '应用简称';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."AUTH_TYPE" IS '授权类型';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."IS_PASS" IS '是否通过';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."AUDIT_BY" IS '审批人';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."AUDIT_REMARK" IS '审批意见';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."TENANT_ID" IS '所属租户ID';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."HMAC" IS '数据完整性';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_APP"."APP_REGISTER"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX1380358912060000"
ON "CCSP_APP"."APP_INFO"("APP_ID");

COMMENT ON TABLE "CCSP_APP"."APP_INFO" IS '应用表';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."APP_ID" IS '应用ID';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."APP_NAME" IS '应用名称';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."APP_SHORT" IS '应用简称';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."REGION_ID" IS '区域ID';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."AUTH_TYPE" IS '授权类型';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."TENANT_ID" IS '所属租户ID';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."HMAC" IS '完整性校验';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CCSP_APP"."APP_INFO"."APPLY_BY" IS '申请人';

CREATE UNIQUE INDEX "INDEX1380359106661100"
ON "CCSP_APP"."APP_AUTH_CODE"("ID");

COMMENT ON TABLE "CCSP_APP"."APP_AUTH_CODE" IS '应用登录口令';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CODE"."ID" IS 'ID';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CODE"."APP_ID" IS '应用ID';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CODE"."AUTH_CODE" IS '口令';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CODE"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CODE"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CODE"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CODE"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CODE"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CODE"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX1380359186471700"
ON "CCSP_APP"."APP_AUTH_CERT"("ID");

COMMENT ON TABLE "CCSP_APP"."APP_AUTH_CERT" IS '应用登录证书';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CERT"."ID" IS 'ID';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CERT"."APP_ID" IS '应用ID';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CERT"."CERT_NAME" IS '证书名称';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CERT"."CERT" IS '证书内容';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CERT"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CERT"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CERT"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CERT"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CERT"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_CERT"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX1380359281713700"
ON "CCSP_APP"."APP_AUTH_AKSK"("ID");

COMMENT ON TABLE "CCSP_APP"."APP_AUTH_AKSK" IS '应用aksk认证';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_AKSK"."ID" IS 'ID';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_AKSK"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_AKSK"."APP_ID" IS '应用id';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_AKSK"."ACCESSKEY_ID" IS '访问密钥id';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_AKSK"."SECRETKEY" IS '访问密钥';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_AKSK"."STATUS" IS '状态（启用、停用）';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_AKSK"."REMARK" IS '描述';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_AKSK"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_AKSK"."LAST_TIME" IS '最后使用时间';

COMMENT ON COLUMN "CCSP_APP"."APP_AUTH_AKSK"."HMAC" IS 'hmac';

