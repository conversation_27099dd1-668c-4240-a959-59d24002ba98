package com.sansec.ccsp.pt.business.servicemgt.gateway.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @Description: 路由与服务或设备关联表;
 * <AUTHOR> http://www.chiner.pro
 * @Date: 2023-2-24
 */
@TableName("ROUTE_TO_SERVICE")
@Data
public class RouteToServicePO extends BasePO{
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 路由表主键
     */
    private Long routeId;
    /**
     * 服务主键/设备主键
     */
    private Long serviceId;
    /**
     * 1服务 2设备
     */
    private Integer serviceType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}