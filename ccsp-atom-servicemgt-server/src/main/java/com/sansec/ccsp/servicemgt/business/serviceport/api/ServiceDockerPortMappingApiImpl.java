package com.sansec.ccsp.servicemgt.business.serviceport.api;

import com.sansec.ccsp.security.annotation.IgnoreToken;
import com.sansec.ccsp.servicemgt.business.serviceport.service.ServiceDockerPortMappingService;
import com.sansec.ccsp.servicemgt.serviceport.api.ServiceDockerPortMappingApi;
import com.sansec.ccsp.servicemgt.serviceport.request.ServiceDockerPortMappingDTO;
import com.sansec.ccsp.servicemgt.serviceport.request.ServiceInfoAddPortsDTO;
import com.sansec.ccsp.servicemgt.serviceport.request.ServiceInfoIdDTO;
import com.sansec.ccsp.servicemgt.serviceport.response.ServiceDockerPortMappingVO;
import com.sansec.ccsp.servicemgt.serviceport.response.ServiceGroupDockerPortMappingVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
public class ServiceDockerPortMappingApiImpl implements ServiceDockerPortMappingApi {
    @Resource
    private ServiceDockerPortMappingService serviceDockerPortMappingService;

    @IgnoreToken
    @Override
    public SecRestResponse<List<ServiceDockerPortMappingVO>> findByServiceInfoId(ServiceInfoIdDTO serviceInfoIdDTO) {
        return serviceDockerPortMappingService.findByServiceInfoId(serviceInfoIdDTO);
    }

    @IgnoreToken
    @Override
    public SecRestResponse<Object> addByServiceInfoId(ServiceInfoAddPortsDTO serviceInfoAddPortsDTO) {
        return serviceDockerPortMappingService.addByServiceInfoId(serviceInfoAddPortsDTO);
    }

    @IgnoreToken
    @Override
    public SecRestResponse<Object> deleteByServiceInfoId(ServiceInfoIdDTO serviceInfoIdDTO) {
        return serviceDockerPortMappingService.deleteByServiceInfoId(serviceInfoIdDTO);
    }

    @Override
    public SecRestResponse<List<ServiceGroupDockerPortMappingVO>> findByServiceGroupId(ServiceDockerPortMappingDTO serviceDockerPortMappingDTO) {
        return serviceDockerPortMappingService.findByServiceGroupId(serviceDockerPortMappingDTO);
    }
}
