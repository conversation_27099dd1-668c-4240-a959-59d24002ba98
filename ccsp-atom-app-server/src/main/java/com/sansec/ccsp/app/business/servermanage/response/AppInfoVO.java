package com.sansec.ccsp.app.business.servermanage.response;

import lombok.Data;

/**
* @Description: 应用表;
* <AUTHOR> x<PERSON><PERSON><PERSON><PERSON><PERSON>
* @Date: 2023-2-18
*/
@Data
public class AppInfoVO {
   /**
    * 应用ID
    */
   private Long appId;
   /**
    * 应用标识
    */
   private String appCode;
   /**
    * 应用名称
    */
   private String appName;
   /**
    * 应用简称
    */
   private String appShort;
   /**
    * 授权类型
    */
   private Integer authType;
   /**
    * 所属租户ID
    */
   private Long tenantId;

   /**
    * 所属租户ID
    */
   private Long regionId;

   /**
    * 是否作废;默认为0
    */
   private Integer invalidFlag;
   /**
    * 备注
    */
   private String remark;
   /**
    * 创建人
    */
   private Long createBy;
   /**
    * 创建时间
    */
   private String createTime;
   /**
    * 更新人
    */
   private Long updateBy;
   /**
    * 更新时间
    */
   private String updateTime;

}