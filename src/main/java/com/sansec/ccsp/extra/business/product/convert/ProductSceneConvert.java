package com.sansec.ccsp.extra.business.product.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.extra.business.product.entity.ProductScenePO;
import com.sansec.ccsp.extra.business.product.request.ProductSceneDTO;
import com.sansec.ccsp.extra.business.product.response.ProductSceneVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR> xiaojiawei
 * @description : 产品场景;(PRODUCT_SCENE)实体类转换接口
 * @date : 2023-11-21
 */
@Mapper(componentModel = "spring")
public interface ProductSceneConvert {
    /**
     * dtoToPo
     *
     * @param productSceneDTO
     * @return
     */
    @Mappings({})
    ProductScenePO dtoToPo(ProductSceneDTO productSceneDTO);

    @Mappings({})
    List<ProductScenePO> dtoToPo(List<ProductSceneDTO> list);

    /**
     * poToDto
     *
     * @param productScenePO
     * @return
     */
    ProductSceneDTO poToDto(ProductScenePO productScenePO);

    /**
     * poToDto-list
     *
     * @param list
     * @return
     */
    List<ProductSceneDTO> poToDto(List<ProductScenePO> list);

    @Mapping(source = "size", target = "pageSize")
    @Mapping(source = "current", target = "pageNum")
    @Mapping(source = "records", target = "list")
    SecPageVO<ProductSceneVO> pagePOToSecPageVOPage(IPage<ProductScenePO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<ProductSceneVO> convert(List<ProductScenePO> list);

    @Mappings({})
    ProductSceneVO convertVo(ProductScenePO request);
}