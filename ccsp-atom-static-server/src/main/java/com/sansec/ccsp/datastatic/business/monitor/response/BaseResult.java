package com.sansec.ccsp.datastatic.business.monitor.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/1/7 14:33
 */
@Data
public class BaseResult<T> {

    private String code = ErrorCode.ERROR_CODE_0X00000000.getCode();
    private String msg = ErrorCode.ERROR_CODE_0X00000000.getDesc();
    private String cn = ErrorCode.ERROR_CODE_0X00000000.getCn();
    private String en = ErrorCode.ERROR_CODE_0X00000000.getEn();

    private T data;

    public boolean checkIsSuccess(){
        return ErrorCode.ERROR_CODE_0X00000000.getCode().equals(code);
    }

}
