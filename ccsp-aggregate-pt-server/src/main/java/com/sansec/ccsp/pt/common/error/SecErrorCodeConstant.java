package com.sansec.ccsp.pt.common.error;

/**
 * @classDesc: 功能描述:()
 * @author: 肖家炜
 * @Date: 2020/4/9 15:18
 * @copyright sansec
 */
public class SecErrorCodeConstant {

    private static final String PREFIX = "0A05";

    public static final String SUCCESS = "0";
    //TODO 公共0000-0100
    //Token获取异常
    public static final String TOKEN_ERROR = PREFIX + "0000";
    public static final String GET_CONFIGAPI_CONFIG_ERROR = PREFIX + "0001";
    /**
     * 请求参数错误
     */
    public static final String REQ_PARAM_ERROR = PREFIX + "0010";
    /**
     * 获取系统配置错误
     */
    public static final String GET_SYS_OPERATION_ERROR = PREFIX + "0011";
    /**
     * 获取系统状态异常
     */
    public static final String GET_SYS_STATUS_ERROR = PREFIX + "0012";
    /**
     * 设置token有效期异常
     */
    public static final String SET_TOKEN_EXPIRE_ERROR = PREFIX + "0013";
    /**
     * 异步任务已存在
     */
    public static final String SYS_TASK_HAS_EXIST = PREFIX + "0014";
    /**
     * 无权操作该接口
     */
    public static final String NO_PERMISSION = PREFIX + "0015";

    /**
     * 内部调用异常
     */
    public static final String RPC_INVOKE_ERROR = PREFIX + "0016";


    //TODO 租户管理0101-0200
    /**
     * 租户添加异常
     */
    public static final String TENANT_ADD_ERROR = PREFIX + "0101";
    /**
     * 租户ID不可为空
     */
    public static final String TENANT_ID_NULL = PREFIX + "0102";
    /**
     * 租户不存在
     */
    public static final String TENANT_NOT_EXIST = PREFIX + "0103";
    /**
     * 租户状态不属于初始化或停服，无法删除
     */
    public static final String TENANT_STATUS_CANNOT_DELETE = PREFIX + "0104";
    /**
     * 租户编辑异常
     */
    public static final String EDIT_ERROR = PREFIX + "0105";
    /**
     * 注册租户名称不可为空
     */
    public static final String TENANT_REGISTER_NAME_EMPTY = PREFIX + "0106";
    /**
     * 注册租户标识不可为空
     */
    public static final String TENANT_REGISTER_CODE_EMPTY = PREFIX + "0107";
    /**
     * 租户标识或名称已存在
     */
    public static final String TENANT_REGISTER_CODE_NAME_REPEAT = PREFIX + "0108";
    /**
     * 注册租户ID为空
     */
    public static final String TENANT_REGISTER_ID_NULL = PREFIX + "0109";
    /**
     * 租户初始三员信息输入错误
     */
    public static final String TENANT_REGISTER_USER_SIZE_ERROR = PREFIX + "0110";
    /**
     * 注册租户数据库操作错误
     */
    public static final String TENANT_REGISTER_DB_ERROR = PREFIX + "0111";
    /**
     * 租户启动异常
     */
    public static final String RESTART_ERROR = PREFIX + "0112";
    /**
     * 租户停用异常
     */
    public static final String STOP_ERROR = PREFIX + "0113";
    /**
     * 设备组不存在
     */
    public static final String DEVICE_GROUP_NOT_EXIST = PREFIX + "0114";
    /**
     * 设备已绑定
     */
    public static final String DEVICE_HAS_EXIST = PREFIX + "0115";
    /**
     * 设备不存在
     */
    public static final String DEVICE_NOT_EXIST = PREFIX + "0116";
    /**
     * 释放设备失败
     */
    public static final String DELETE_DEVICE_ERROR = PREFIX + "0117";
    /**
     * 服务组不存在
     */
    public static final String SERVICE_GROUP_NOT_EXIST = PREFIX + "0118";
    /**
     * 服务不存在
     */
    public static final String SERVICE_NOT_EXIST = PREFIX + "0119";
    /**
     * 绑定服务失败，服务类型需为加解密服务、签名验签服务、杂凑服务或密钥管理服务
     */
    public static final String SERVICE_TYPE_ERROR = PREFIX + "0120";
    /**
     * 注册租户对象为空
     */
    public static final String TENANT_REGISTER_IS_NULL = PREFIX + "0121";
    /**
     * 租户已被审核，不可再次审核
     */
    public static final String TENANT_REGISTER_HAS_AUDITED = PREFIX + "0122";
    /**
     * 注册租户参数错误
     */
    public static final String TENANT_REGISTER_INFO_ERROR = PREFIX + "0123";
    /**
     * 租户无用户信息
     */
    public static final String USERINFO_NOT_EXIST = PREFIX + "0124";
    /**
     * 注册租户不存在
     */
    public static final String TENANT_REGISTER_NOT_EXIST = PREFIX + "0125";
    /**
     * 租户初始化失败
     */
    public static final String TENANT_INIT_ERROR = PREFIX + "0126";
    /**
     * 租户三员信息的Ukey序列号重复
     */
    public static final String TENANT_USER_UKEY_REPEAT = PREFIX + "0127";
    /**
     * 租户三员信息数据错误
     */
    public static final String TENANT_USER_ERROR = PREFIX + "0128";
    /**
     * 获取业务类型列表错误
     */
    public static final String TENANT_BUSI_LIST_ERROR = PREFIX + "0129";
    /**
     * 获取租户业务类型错误
     */
    public static final String TENANT_BUSI_TYPE_ERROR = PREFIX + "0130";
    /**
     * 设备未绑定当前租户
     */
    public static final String DEVICE_NOT_BIND_CUR_TENANT = PREFIX + "0131";
    /**
     * 服务未绑定当前租户
     */
    public static final String SERVICE_NOT_BIND_CUR_TENANT = PREFIX + "0132";

    /**
     * 服务属于服务组，请先从服务组中释放
     */
    public static final String SERVICE_TO_GROUP_ERROR = PREFIX + "0133";
    /**
     * 应用管理服务请求失败
     */
    public static final String TENANT_APP_SERVICE_ERROR = PREFIX + "0134";
    /**
     * 设备管理服务请求失败
     */
    public static final String TENANT_DEVICE_SERVICE_ERROR = PREFIX + "0135";
    /**
     * 服务管理服务请求失败
     */
    public static final String TENANT_SERVICE_SERVICE_ERROR = PREFIX + "0136";
    /**
     * 设备属于设备组，请先从设备组中释放
     */
    public static final String DEVICE_TO_GROUP_ERROR = PREFIX + "0137";
    /**
     * 租户三员信息的Ukey序列号已使用
     */
    public static final String TENANT_USER_UKEY_ISHAS = PREFIX + "0138";
    /**
     * 租户三员信息的口令被禁用，请切换口令后重试
     */
    public static final String AUTH_CODE_IS_BLACKLIST = PREFIX + "0139";
    /**
     * 设备已绑定其他租户
     */
    public static final String DEVICE_HAS_BIND_OTHER_GROUP = PREFIX + "0140";
    /**
     * 业务类型接口调用失败
     */
    public static final String BUSI_TYPE_API_ERROR = PREFIX + "0141";
    /**
     * 业务类型对象不存在
     */
    public static final String BUSI_TYPE_NOT_EXIST = PREFIX + "0142";
    /**
     * 租户状态不属于运行中或停服失败，无法停止
     */
    public static final String TENANT_NOT_MOVE = PREFIX + "0143";
    /**
     * 租户状态不属于停服中或启动失败，无法启动
     */
    public static final String TENANT_NOT_STOP = PREFIX + "0144";
    /**
     * 租户数据异常
     */
    public static final String TENANT_DATA_ERROR = PREFIX + "0145";

    /**
     * 当前用户无权操作该租户
     */
    public static final String TENANT_NO_POWER = PREFIX + "0146";
    /**
     * 获取设备组失败
     */
    public static final String GET_DEVICE_GROUP_ERROR = PREFIX + "0147";
    /**
     * 获取服务组列表失败
     */
    public static final String GET_SERVICE_GROUP_ERROR = PREFIX + "0148";
    /**
     * 租户未拥有足够业务类型
     */
    public static final String TENANA_NOT_HAS_ENOUGH_BUSITYPE = PREFIX + "0149";
    /**
     * 租户无密钥配置
     */
    public static final String TENANT_KEY_RESOURCE_NULL = PREFIX + "0150";
    /**
     * 租户包含应用，不允许删除租户
     */
    public static final String TENANT_HAS_APP_DELETE_ERROR = PREFIX + "0151";
    /**
     * 租户包含未释放的设备，不允许删除租户
     */
    public static final String TENANT_HAS_DEVICE_DELETE_ERROR = PREFIX + "0152";
    /**
     * 租户包含未释放的服务，不允许删除租户
     */
    public static final String TENANT_HAS_SERVICE_DELETE_ERROR = PREFIX + "0153";
    /**
     * 租户包含密钥信息，故无法释放密钥管理服务
     */
    public static final String TENANT_HAS_KMS_DELETE_ERROR = PREFIX + "0154";
    /**
     * 租户包含加解密、签名验签、杂凑服务，需使用密钥管理服务，故无法释放
     */
    public static final String TENANT_HAS_PKI_DELETE_ERROR = PREFIX + "0155";
    /**
     * 注册记录不可删除
     */
    public static final String TENANT_REGISTER_CANNOT_DELETE_ERROR = PREFIX + "0156";
    /**
     * 删除租户和业务类型关联关系错误
     */
    public static final String DELETE_TENANT_BUSI_TYPE_ERROR = PREFIX + "0157";
    /**
     * 删除租户三员失败
     */
    public static final String DELETE_TENANT_REGISTER_USER_ERROR = PREFIX + "0158";
    /**
     * 删除注册租户失败
     */
    public static final String DELETE_TENANT_REGISTER_ERROR = PREFIX + "0159";
    /**
     * 注册租户机构不可为空
     */
    public static final String TENANT_REGISTER_ORGAN_EMPTY = PREFIX + "0160";
    /**
     * 租户绑定设备一次最多不能超过5台
     */
    public static final String DEVICE_TO_TENANT_ABOVE_FIVE = PREFIX + "0161";
    /**
     * 用户名不可为空
     */
    public static final String REGISTER_USER_CODE_NULL_ERROR = PREFIX + "0162";
    /**
     * 用户名只能包含大小写字母、数字，且长度4-20
     */
    public static final String REGISTER_USER_CODE_ERROR = PREFIX + "0163";
    /**
     * 姓名不可为空
     */
    public static final String REGISTER_USER_NAME_NULL_ERROR = PREFIX + "0164";
    /**
     * 姓名只能包含大小写字母、数字、字符_，且长度2-15
     */
    public static final String REGISTER_USER_NAME_ERROR = PREFIX + "0165";
    /**
     * 创建业务地址失败
     */
    public static final String CREATE_BUSI_URL_ERROR = PREFIX + "0166";
    /**
     * 编辑业务地址失败
     */
    public static final String EDIT_BUSI_URL_ERROR = PREFIX + "0167";
    /**
     * 删除业务地址失败
     */
    public static final String DELETE_BUSI_URL_ERROR = PREFIX + "0168";
    /**
     * 业务地址不存在
     */
    public static final String BUSI_URL_NOT_EMPTY = PREFIX + "0169";
    /**
     * 业务地址名称重复
     */
    public static final String BUSI_URL_NAME_EXIST = PREFIX + "0170";
    /**
     * 业务地址类型不存在
     */
    public static final String BUSI_URL_TYPE_NOT_EXIST = PREFIX + "0171";
    /**
     * 无空闲设备可用
     */
    public static final String NO_FREE_DEVICE_AVAILABLE = PREFIX + "0172";

    /**
     * 服务类型对象添加设备配置信息错误
     */
    public static final String SERVICE_TYPE_INFO_ERROR = PREFIX + "0173";
    /**
     * 租户Hmac更新失败
     */
    public static final String TENANT_HMAC_UPDATE_ERROR = PREFIX + "0174";
    /**
     * 租户注册Hmac更新失败
     */
    public static final String TENANT_REGISTER_HMAC_UPDATE_ERROR = PREFIX + "0175";
    /**
     * 租户配额Hmac更新失败
     */
    public static final String TENANT_QUOTA_HMAC_UPDATE_ERROR = PREFIX + "0176";

    /**
     * 无法删除租户关联的数据库
     */
    public static final String TENANT_DATABASE_DELELTE_ERROR = PREFIX + "0177";

    /**
     * 查询租户关联的数据库失败
     */
    public static final String TENANT_DATABASE_SELECT_ERROR = PREFIX + "0178";

    /**
     * 服务组标识不可为空
     */
    public static final String SERVICE_GROUP_CODE_NOT_NULL = PREFIX + "0179";

    /**
     * 租户%s无法正常使用，请联系密服平台系统操作员处理
     */
    public static final String TENANT_STATUS_ERROR = PREFIX + "0180";
    /**
     * 租户分区信息错误
     */
    public static final String TENANT_REGION_INFO_ERROR = PREFIX + "0181";
    /**
     * 租户与设备分区不一致
     */
    public static final String TENANT_DEVICE_REGION_ERROR = PREFIX + "0182";

    /**
     * 设备组添加网关路由错误
     */
    public static final String DEVICE_GROUP_ADD_ROUTE_ERROR = PREFIX + "0183";

    /**
     * 设备组下发的服务时报错
     */
    public static final String DEVICE_GROUP_DOWN_SERVICE_ERROR = PREFIX + "0184";

    /**
     * 服务绑定设备关系入库失败
     */
    public static final String SERVICE_AUTO_BIND_DEVICE_ERROR = PREFIX + "0185";

    /**
     * 获取服务绑定设备失败
     */
    public static final String SERVICE_AUTO_BIND_DEVICE_IS_NULL = PREFIX + "0186";

    /**
     * 该设备类型设备不可被密码服务使用
     */
    public static final String DEVICE_TYPE_NOT_SERVICE = PREFIX + "0187";

    /**
     * 采集设备监控指标异常
     */
    public static final String COLLECT_DEVICE_MONITOR_ERROR =  PREFIX + "0188";

    /**
     * 租户机构信息错误
     */
    public static final String TENANT_ORGANIZATION_INFO_ERROR = PREFIX + "0189";

    /**
     * 机构已被租户绑定
     */
    public static final String TENANT_BIND_ORGANIZATION_NOT_ALLOWED_DELETE = PREFIX + "0190";

    /**
     * 租户下已存在该业务类型的独享服务，无法再次绑定共享服务
     */
    public static final String TENANT_BIND_SHARE_GROUP_HAS_SERVICE_ERROR = PREFIX + "0191";

    /**
     * 租户是否支持专享服务配置值错误
     */
    public static final String TENANT_EXCLUSIVE_ERROR = PREFIX + "0192";


    /**
     * 租户支持专享服务时审核数据库必填
     */
    public static final String EXCLUSIVE_TENANT_AUDIT_DATABASE_NOT_BE_NULL = PREFIX + "0193";

    /**
     * 租户与服务组分区不一致
     */
    public static final String TENANT_SERVICE_GROUP_REGION_ERROR = PREFIX + "0194";

    /**
     * 租户已经绑定该服务
     */
    public static final String TENANT_ALREADY_BIND_SERVICE_GROUP = PREFIX + "0195";

    /**
     * 租户单位组织信息不允许为空
     */
    public static final String TENANT_ORGANIZATION_ID_NOT_BE_NULL = PREFIX + "0196";

    /**
     * 租户和服务组的对应关系不存在
     */
    public static final String TENANT_TO_SERVICE_GROUP_NOT_EXIST = PREFIX + "0197";
    //调用认证中心注册租户失败
    public static final String AUTH_REGISTER_TENANT_ERROR = PREFIX + "0198";
    //调用认证中心删除租户失败
    public static final String AUTH_DELETE_TENANT_ERROR = PREFIX + "0199";
    //调用认证中心获取token失败
    public static final String AUTH_GET_TOKEN_ERROR = PREFIX + "0200";


    //TODO 应用管理0201-0300

    /**
     * 应用业务管理删除失败
     */
    public static final String APP_BUSI_DELETE_ERROR = PREFIX + "0201";
    /**
     * 应用业务管理查询失败
     */
    public static final String APP_BUSI_QUERY_ERROR = PREFIX + "0202";
    /**
     * 应用业务管理更新失败
     */
    public static final String APP_BUSI_UPDATE_ERROR = PREFIX + "0203";
    /**
     * 应用业务类型关联服务类型错误，只可为1：设备组；2：服务组
     */
    public static final String APP_GROUP_TYPE_ERROR = PREFIX + "0204";
    /**
     * 应用业务类型关联组不支持该业务
     */
    public static final String APP_GROUP_BUSI_TYPE_ERROR = PREFIX + "0205";
    /**
     * 应用为空
     */
    public static final String APP_INFO_EXIST = PREFIX + "0206";
    /**
     * 无权操作该应用
     */
    public static final String APP_MENU_ERROR = PREFIX + "0207";

    /**
     * 迁移设备后会导致无默认设备可用，将影响使用默认设备的应用业务正常运行
     */
    public static final String DEFAULT_DEVICE_INFLUENCE_APP_ERROR = PREFIX + "0208";
    /**
     * 迁移设备后会导致设备组无设备可用，将影响使用该设备组的应用业务正常运行
     */
    public static final String ARRAY_DEVICE_INFLUENCE_APP_ERROR = PREFIX + "0209";
    /**
     * 迁移设备后会导致无默认设备可用，将影响使用默认设备的业务服务正常运行
     */
    public static final String DEFAULT_DEVICE_INFLUENCE_SERVICE_ERROR = PREFIX + "0210";
    /**
     * 迁移设备后会导致设备组无设备可用，将影响使用该设备组的业务服务正常运行
     */
    public static final String ARRAY_DEVICE_INFLUENCE_SERVICE_ERROR = PREFIX + "0211";
    /**
     * 应用业务类型为空
     */
    public static final String APP_BUSI_IS_BLANK = PREFIX + "0212";

    /**
     * 包含应用不支持的业务类型
     */
    public static final String APP_BAND_SERVICE_ERROR = PREFIX + "0213";
    /**
     * 应用信息查询失败
     */
    public static final String APP_INFO_QUERY_ERROR = PREFIX + "0214";
    /**
     * 删除服务组将影响应用业务正常使用，请先解绑服务组和应用业务的关联关系后再删除
     */
    public static final String DELETE_SERVICE_GROUP_INFLUENCE_APP_ERROR = PREFIX + "0215";
    /**
     * 迁移服务后会导致服务组无服务可用，将影响使用该服务组的应用业务正常运行
     */
    public static final String ARRAY_SERVICE_INFLUENCE_APP_ERROR = PREFIX + "0216";
    /**
     * 迁移服务后会导致无默认服务可用，将影响使用默认服务的应用业务正常运行
     */
    public static final String DEFAULT_SERVICE_INFLUENCE_APP_ERROR = PREFIX + "0217";
    /**
     * 删除设备组将影响服务正常使用，请先解绑设备组和服务的关联关系后再删除
     */
    public static final String DELETE_DEVICE_GROUP_INFLUENCE_SERVICE_ERROR = PREFIX + "0218";
    /**
     * 应用添加失败
     */
    public static final String APP_ADD_ERROR = PREFIX + "0219";
    /**
     * 服务类型ID不可为空
     */
    public static final String APP_SERVICE_TYPE_ERROR = PREFIX + "0220";
    /**
     * 应用标识已存在
     */
    public static final String APP_CODE_REPEAT = PREFIX + "0221";
    /**
     * 应用不支持该业务类型
     */
    public static final String APP_SERVICE_TYPE_NOT_SUPPORT = PREFIX + "0222";
    /**
     * 应用标识已存在
     */
    public static final String APP_CODE_ISEXIST = PREFIX + "0223";
    /**
     * 调用密管接口异常
     */
    public static final String APP_INVOKE_KMS_ERROR = PREFIX + "0224";
    /**
     * 应用注册表查询失败
     */
    public static final String APP_REGISTER_QUERY_ERROR = PREFIX + "0225";
    /**
     * 应用aksk密钥数量获取失败
     */
    public static final String APP_AKSKNUM_GET_ERROR = PREFIX + "0226";
    /**
     * 应用aksk可添加数量达到上限
     */
    public static final String APP_AKSKNUM_ADD = PREFIX + "0227";
    /**
     * 应用所选区域和租户不一致
     */
    public static final String APP_REGION_AND_TENANT_ERROR = PREFIX + "0228";
    /**
     * 应用业务类型关联组和应用所属区域不同
     */
    public static final String APP_REGION_AND_GROUP_ERROR = PREFIX + "0229";
    /**
     * 应用修改访问密码和原访问密码相同不允许修改
     */
    public static final String APP_UPDATE_AUTH_CODE_IS_SAME = PREFIX + "0230";
    /**
     * 删除服务组将影响租户正常使用，无法删除
     */
    public static final String DELETE_SERVICE_GROUP_INFLUENCE_TENANT_ERROR = PREFIX + "0231";
    //删除设备组影响租户使用，请先解绑设备组和租户的关系
    public static final String DELETE_DEVICE_GROUP_INFLUENCE_TENANT_ERROR = PREFIX + "0232";
    //删除设备组影响业务使用，请先解绑设备组和业务的关系
    public static final String DELETE_DEVICE_GROUP_INFLUENCE_BUSI_ERROR = PREFIX + "0233";
    //设备组必须是支持业务调用的共享设备组
    public static final String DEVICE_GROUP_NOT_SUPPORT_BUSI_REST = PREFIX + "0234";
    //租户未绑定该设备组
    public static final String TENANT_NOT_BIND_DEVICE_GROUP = PREFIX + "0235";
    //服务组ID不能为空
    public static final String SERVICE_GROUP_ID_IS_BLANK = PREFIX + "0236";
    //组类型不能为空
    public static final String GROUP_TYPE_IS_BLANK = PREFIX + "0237";


    //TODO 服务管理0301-0400
    //接收设备信息接口返回值为空
    public static final String RECEIVE_DEVICE_NULL = PREFIX + "0301";
    //接收设备信息接口返回结果异常
    public static final String RECEIVE_DEVICE_ERROR = PREFIX + "0302";
    //配置公钥接口返回值为空
    public static final String CONFIG_PUBLIC_KEY_NULL = PREFIX + "0303";
    //查询公钥接口返回值为空
    public static final String GET_PUBLIC_KEY_FINGER_NULL = PREFIX + "0304";
    //清除公钥接口返回值为空
    public static final String CLEAR_PUBLIC_KEY_NULL = PREFIX + "0305";
    //配置公钥接口返回结果异常
    public static final String CONFIG_PUBLIC_KEY_ERROR = PREFIX + "0306";
    //查询公钥接口返回结果异常
    public static final String GET_PUBLIC_KEY_FINGER_ERROR = PREFIX + "0307";
    //清除公钥接口返回结果异常
    public static final String CLEAR_PUBLIC_KEY_ERROR = PREFIX + "0308";
    //获取公钥指纹异常
    public static final String GET_PT_PUBLIC_KEY_FINGER_ERROR = PREFIX + "0309";
    //配置公钥异常
    public static final String CONFIG_PUBLIC_KEY_FUN_ERROR = PREFIX + "0310";
    //接收配置信息接口返回值为空
    public static final String RECEIVE_CONFIG_NULL = PREFIX + "0311";
    //接收配置信息接口返回结果异常
    public static final String RECEIVE_CONFIG_ERROR = PREFIX + "0312";
    //启动业务服务接口返回值为空
    public static final String START_BUSI_SERVICE_NULL = PREFIX + "0313";
    //启动业务服务接口返回结果异常
    public static final String START_BUSI_SERVICE_ERROR = PREFIX + "0314";
    //停止业务服务接口返回值为空
    public static final String STOP_BUSI_SERVICE_NULL = PREFIX + "0315";
    //停止业务服务接口返回结果异常
    public static final String STOP_BUSI_SERVICE_ERROR = PREFIX + "0316";
    //初始化租户返回值为空
    public static final String INIT_TENANT_NULL = PREFIX + "0317";
    //初始化租户返回结果异常
    public static final String INIT_TENANT_ERROR = PREFIX + "0318";
    //删除租户返回值为空
    public static final String DELETE_TENANT_NULL = PREFIX + "0319";
    //删除租户返回结果异常
    public static final String DELETE_TENANT_ERROR = PREFIX + "0320";
    //服务名称不可重复
    public static final String SERVICE_NAME_EXEIT = PREFIX + "0321";
    //服务被绑定使用中不可删除
    public static final String SERVICE_CANT_DELETE_SERVICE_GROUP_EXIST = PREFIX + "0322";
    //端口重复
    public static final String SERVICE_ADD_PORT_EXIST = PREFIX + "0323";
    //更新服务状态失败
    public static final String SERVICE_UPDATE_STATUS_ERROR = PREFIX + "0326";
    //服务不存在
    public static final String SERVICE_NOT_EXEIST = PREFIX + "0327";
    //端口不能为空
    public static final String SERVICE_PORT_NOT_EXEIST = PREFIX + "0328";
    //服务运行中不可删除
    public static final String SERVICE_START_NOT_DELETE = PREFIX + "0329";
    //获取服务菜单接口返回值为空
    public static final String GET_SERVICE_MENU_NULL = PREFIX + "0330";
    //获取服务菜单接口返回结果异常
    public static final String GET_SERVICE_MENU_ERROR = PREFIX + "0331";
    //设备组下不存在设备信息
    public static final String DEVICE_GROUP_NOT_EXIST_DEVICE = PREFIX + "0332";
    //获取服务状态返回值为空
    public static final String GET_SERVICE_STATUS_NULL = PREFIX + "0333";
    //获取服务状态返回值异常
    public static final String GET_SERVICE_STATUS_ERROR = PREFIX + "0334";
    //服务类型输入错误，没有符合条件的服务类型
    public static final String GET_SERVICE_TYPE_ERROR = PREFIX + "0335";
    //租户未绑定设备
    public static final String TENANT_DEVICE_GROUP_NOT_EXIST_DEVICE = PREFIX + "0336";
    //添加监控设备返回值为空
    public static final String ADD_MONITOR_DEVICE_NULL = PREFIX + "0337";
    //添加监控设备异常
    public static final String ADD_MONITOR_DEVICE_ERROR = PREFIX + "0338";
    //删除监控设备返回值为空
    public static final String DELETE_MONITOR_DEVICE_NULL = PREFIX + "0339";
    //删除监控设备异常
    public static final String DELETE_MONITOR_DEVICE_ERROR = PREFIX + "0340";
    //服务添加异常
    public static final String SERVICE_ADD_ERROR = PREFIX + "0341";
    //更新token有效期失败
    public static final String UPDATE_TOKEN_EXPIRE_ERROR = PREFIX + "0342";
    //获取子服务操作日志失败
    public static final String GET_SERVICE_OPERATION_LOG_ERROR = PREFIX + "0343";
    //下发租户配额返回值为空
    public static final String SET_TENANT_QUOTA_NULL = PREFIX + "0344";
    //下发租户配额返回结果异常
    public static final String SET_TENANT_QUOTA_ERROR = PREFIX + "0345";
    //获取租户配额返回值为空
    public static final String GET_TENANT_QUOTA_NULL = PREFIX + "0346";
    //获取租户配额返回结果异常
    public static final String GET_TENANT_QUOTA_ERROR = PREFIX + "0347";
    //获取服务统计信息返回值为空
    public static final String GET_STATISTIC_INFO_NULL = PREFIX + "0348";
    //获取服务统计信息返回结果异常
    public static final String GET_STATISTIC_INFO_ERROR = PREFIX + "0349";
    //不能从主机改为备机
    public static final String NOT_ACTIVE_TO_STANDBY = PREFIX + "0350";
    //当前服务应用数量已达最大配额，请添加配额后重试
    public static final String QUOTA_NO_AVAILABLE = PREFIX + "0351";
    //服务ID不可为空
    public static final String SERVICE_ID_NULL = PREFIX + "0352";
    //租户ID不可为空
    public static final String SERVICE_TENANT_ID_NULL = PREFIX + "0353";
    //设备组ID不可为空
    public static final String SERVICE_DEVICE_GROUP_ID_NULL = PREFIX + "0354";
    //初始化参数异常
    public static final String INIT_ADD_INIT_SERVICE_PARAM_ERROR = PREFIX + "0355";
    //初始化启动异常
    public static final String INIT_ADD_INIT_SERVICE_START_ERROR = PREFIX + "0356";
    //已绑定服务组不可切换
    public static final String SERVICE_CANT_BIND_SERVICE_GROUP_EXIST = PREFIX + "0357";
    //服务切换服务组异常
    public static final String SERVICE_BIND_SERVICE_GROUP_ERROR = PREFIX + "0358";
    //服务组没有绑定数据库实例
    public static final String SERVICE_GROUP_NOT_BIND_DB = PREFIX + "0359";
    //数据库映射地址不能为空
    public static final String DB_MAP_IP_NOT_NULL = PREFIX + "0360";
    //服务组不支持绑定该类型服务
    public static final String SERVICE_GROUP_BINDING_BUSI_ERROR = PREFIX + "0361";
    //服务删除-释放服务异常
    public static final String SERVICE_DELETE_GROUP_UNBIND_SERVICE_ERROR = PREFIX + "0362";
    //同服务类型同ip不可重复
    public static final String SERVICE_ADD_TYPE_IP_EXIST = PREFIX + "0363";
    //平台不支持创建共享服务组
    public static final String SERVICE_GROUP_ADD_SHARE_ERROR = PREFIX + "0364";
    //当前用户无权创建共享服务组，请联系系统操作员
    public static final String SERVICE_GROUP_ADD_SHARE_NO_POWER = PREFIX + "0365";
    //创建共享服务组时，必须指定业务类型
    public static final String SERVICE_GROUP_ADD_SHARE_NO_BUSI = PREFIX + "0366";
    //该业务类型不支持创建共享服务组
    public static final String SERVICE_GROUP_ADD_SHARE_BUSI_ERROR = PREFIX + "0367";
    //创建该业务类型的服务组需选择一个共享的密钥管理服务
    public static final String SERVICE_GROUP_ADD_SHARE_KMS_ERROR = PREFIX + "0368";
    //选择的共享密钥管理服务不符合规范，请重新选择
    public static final String SERVICE_GROUP_ADD_SHARE_KMS_GROUP_ERROR = PREFIX + "0369";
    //服务组非共享服务组，无法进行绑定
    public static final String SERVICE_GROUP_NO_SHARE = PREFIX + "0370";
    //服务所属业务类型和共享服务组不一致，无法进行绑定
    public static final String SERVICE_GROUP_SHARE_BUSI_ERROR = PREFIX + "0371";
    //服务组组ID不可为空
    public static final String SERVICE_SERVICE_GROUP_ID_NULL = PREFIX + "0372";
    //无法找到运行状态正常的服务进行处理
    public static final String SERVICE_INFO_STATUS_ERROR = PREFIX + "0373";
    //服务初始化中不可重置密码
    public static final String SERVICE_INIT_ING_DONT_RESET_DB_AUTH_ERROR = PREFIX + "0374";
    //租户绑定共享服务组创建路由报错
    public static final String TENANT_BIND_SHARE_GROUP_ROUTE_ERROR = PREFIX + "0375";
    //租户解绑共享服务组处理路由报错
    public static final String TENANT_UNBIND_SHARE_GROUP_ROUTE_ERROR = PREFIX + "0376";
    //释放该服务后，将影响对应租户的使用
    public static final String SHARE_SERVICE_GROUP_UNBIND_TENANT_ERROR = PREFIX + "0377";
    //释放该服务后，将影响对应服务组的使用
    public static final String SHARE_SERVICE_GROUP_UNBIND_SERVICE_GROUP_ERROR = PREFIX + "0378";
    //是否共享不可为空
    public static final String SERVICE_GROUP_SHARE_IS_NULL = PREFIX + "0379";
    //专享服务组所属租户不可为空
    public static final String UN_SHARE_SERVICE_GROUP_TENANT_ID_NULL = PREFIX + "0380";
    //平台不支持创建独享服务组
    public static final String SERVICE_GROUP_ADD_UNSHARE_NOT_SUPPORT = PREFIX + "0381";
    //选择的共享密钥管理服务组没有服务，请重新选择
    public static final String SERVICE_GROUP_ADD_SHARE_KMS_SERVICE_ERROR = PREFIX + "0382";
    //请先释放服务，再删除该服务组
    public static final String SERVICE_GROUP_DELETE_HAVE_SERVICE_ERROR = PREFIX + "0383";
    //服务不属于该服务组
    public static final String SERVICE_GROUP_SERVICE_NOT_BELONG = PREFIX + "0384";
    //获取宿主机端口列表失败
    public static final String CONTAINER_PORT_CHECK_FAILED = PREFIX + "0385";

    //服务管理添加容器端口映射失败
    public static final String CONTAINER_PORT_MAPPING_FAILED = PREFIX + "0386";
    //服务管理添加容器端口映射失败
    public static final String SERVICE_PORT_SAME = PREFIX + "0387";
    //镜像不存在
    public static final String IMAGE_NOT_EXIST_ADD_SERVICE = "0A050388";
    public static final String HOST_STATUS_BUSY = "0A050389";
    //镜像下载和上传到宿主机错误
    public static final String IMAGE_FILE_DOWNLOAD_UPLOAD_ERROR = PREFIX + "0390";
    //专享服务组和专享设备组的租户不一致
    public static final String SINGLE_SERVICE_DEVICE_GROUP_TENANT_ERROR = PREFIX + "0391";

    //Vpn创建数量已达上限
    public static final String VPN_NUM_OVER_THE_LIMIT =  "0A050392";


    //镜像正在上传中
    public static final String IMAGE_IS_UPLOADING =  "0A050393";

    //特殊处理VPN业务服务代理端口范围获取分布式锁失败
    public static final String ADD_VPN_PORT_RANGE_LOCK_FAILED =  "0A050394";

    //宿主机分配端口
    public static final String HOST_ALLOCATE_PORT_FAILED =  "0A050395";

    //TODO 证书管理 0401-0500
    //认证方式为CRL时，CRL文件不能为空
    public static final String CA_CERT_CRL_FILE_NULL = PREFIX + "0400";
    //认证方式为OCSP时，OCSP地址不能为空
    public static final String CA_CERT_OCSP_URL_NULL = PREFIX + "0401";
    //认证方式为OCSP时，OCSP客户端证书不能为空
    public static final String CA_CERT_OCSP_FILE_NULL = PREFIX + "0402";
    //认证方式为OCSP时，OCSP认证口令不能为空
    public static final String CA_CERT_OCSP_AUTH_CODE_NULL = PREFIX + "0403";
    //解析文件失败
    public static final String CA_CERT_FILE_INFO_ERROR = PREFIX + "0404";
    //信任域证书信息为空
    public static final String CA_CERT_FILE_INFO_NULL = PREFIX + "0405";
    //添加信任域失败
    public static final String CA_CERT_ADD_ERROR = PREFIX + "0406";
    //无法构造有效的证书链
    public static final String CA_CERT_GET_CERT_PATH_ERROR = PREFIX + "0407";
    //PEM编码CRL失败
    public static final String CA_CERT_CRL_ENCODE_ERROR = PREFIX + "0408";
    //验证CRL失败
    public static final String CA_CERT_CRL_VALIDATE_ERROR = PREFIX + "0409";
    //证书标签已存在
    public static final String CA_CERT_DUPLICATE_ALIAS = PREFIX + "0410";
    //信任域证书不存在
    public static final String CA_CERT_NOT_EXIT = PREFIX + "0411";
    //口令解密失败
    public static final String CA_CERT_AUTH_CODE_DECRYPT_ERROR = PREFIX + "0412";
    //租户信息不存在
    public static final String CA_CERT_TENANT_INFO_NULL = PREFIX + "0413";
    //认证方式为OCSP时，CA证书不能为空
    public static final String CA_CERT_OCSP_CA_FILE_NULL = PREFIX + "0414";
    //认证方式为OCSP时，解析客户端证书失败
    public static final String CA_CERT_OCSP_FILE_PARSING_ERROR = PREFIX + "0415";
    //认证方式为OCSP时，解析CA端证书失败
    public static final String CA_CERT_OCSP_CA_FILE_PARSING_ERROR = PREFIX + "0416";
    //认证方式为OCSP时，解析节点证书失败
    public static final String CA_CERT_OCSP_NODE_FILE_PARSING_ERROR = PREFIX + "0417";
    //认证方式为OCSP时，解析节点不能为空
    public static final String CA_CERT_OCSP_NODE_FILE_NULL = PREFIX + "0418";
    //证书文件类型错误，仅允许上传cer或p7b格式证书文件
    public static final String CA_CERT_FILE_TYPE_ERROR = PREFIX + "0419";
    //CRL验证文件类型错误，仅允许上传crl格式证书文件
    public static final String CA_CRL_FILE_TYPE_ERROR = PREFIX + "0420";
    //OCSP客户端证书文件类型错误，证书必须为pfx文件
    public static final String CA_OCSP_CLIENT_FILE_TYPE_ERROR = PREFIX + "0421";
    //OCSP-CA证书文件类型错误，证书必须为cer或p7b文件
    public static final String CA_OCSP_CA_FILE_TYPE_ERROR = PREFIX + "0422";
    //OCSP节点证书文件类型错误，证书必须为cer或p7b文件
    public static final String CA_OCSP_NODE_FILE_TYPE_ERROR = PREFIX + "0423";

    //信任域证书文件大小不能超过20KB
    public static final String CA_CERT_FILE_SIZE_ERROR = PREFIX + "0424";
    //CRL验证文件大小不能超过20MB
    public static final String CA_CRL_FILE_SIZE_ERROR = PREFIX + "0425";
    //OCSP客户端证书文件大小不能超过20MB
    public static final String CA_OCSP_CLIENT_FILE_SIZE_ERROR = PREFIX + "0426";
    //OCSP-CA证书文件大小不能超过20KB
    public static final String CA_OCSP_CA_FILE_SIZE_ERROR = PREFIX + "0427";
    //OCSP节点证书文件大小不能超过20KB
    public static final String CA_OCSP_NODE_FILE_SIZE_ERROR = PREFIX + "0428";
    //请输入正确的OCSP地址
    public static final String CA_OCSP_URL_ERROR = PREFIX + "0429";
    //证书序列号重复
    public static final String CA_CERT_DUPLICATE_SERIALNUMBER = PREFIX + "0430";



    //TODO 公共状态配置 0501-0600
    //无法连接到密钥管理系统
    public static final String KMS_REQUEST_ERROR = PREFIX + "0501";
    //无权调用KMS
    public static final String KMS_UNAUTHORIZED_ERROR = PREFIX + "0502";
    //配置值格式错误
    public static final String CONFIG_VALUE_FORMAT_ERROR = PREFIX + "0503";


    //TODO 网关  0801-0900
    //uris不可为空  路由请求规则uris不可为空
    public static final String APISIX_URIS_NOT_EMPTY_ERROR = PREFIX + "0801";
    //路由请求规则ipPorts不可为空
    public static final String APISIX_IPPORTS_NOT_EMPTY_ERROR = PREFIX + "0802";
    //请求参数位置不符合要求，可选值：http,post_arg,cookie,arg
    public static final String APISIX_REQUEST_TYPE_ERROR = PREFIX + "0803";
    //路由ID不可为空
    public static final String APISIX_ROUTE_ID_NOT_NULL_ERROR = PREFIX + "0804";
    //路由请求服务名称不可为空
    public static final String APISIX_SERVICE_NAME_NOT_EMPTY_ERROR = PREFIX + "0805";
    //路由添加失败
    public static final String APISIX_ROUTE_ADD_EMPTY_ERROR = PREFIX + "0806";
    //附加参数varsName、varsVal、varsOper不可为空
    public static final String APISIX_ROUTE_VARS_PARAM_EMPTY_ERROR = PREFIX + "0807";
    //运算符不符合要求，可选值为：==   ~=    >    <   IN
    public static final String APISIX_ROUTE_OPERATE_ERROR = PREFIX + "0808";
    //服务类型ID不可为空
    public static final String GATEWAY_SERVICE_TYPE_NOT_EMPTY_ERROR = PREFIX + "0809";
    //租户不ID不可为空
    public static final String GATEWAY_TENANTID_NOT_EMPTY_ERROR = PREFIX + "0810";
    //租户编码不可为空
    public static final String GATEWAY_TENANTCODE_NOT_EMPTY_ERROR = PREFIX + "0811";
    //数据库主键标识ID不可为空
    public static final String GATEWAY_DB_KEY_NOT_EMPTY_ERROR = PREFIX + "0812";
    //应用ID不可为空
    public static final String GATEWAY_APP_ID_NOT_EMPTY_ERROR = PREFIX + "0813";
    //应用编码不可为空
    public static final String GATEWAY_APP_CODE_NOT_EMPTY_ERROR = PREFIX + "0814";
    //应用绑定服务选择服务组，服务不属于:pki、svs、digist、kms
    public static final String GATEWAY_APP_BAND_SERVICE_ERROR = PREFIX + "0815";
    //暂时限制网关只能添加一条
    public static final String GATEWAY_NOT_REPEAT_ADD = PREFIX + "0816";
    //网关不存在
    public static final String GATEWAY_NOT_EXIST = PREFIX + "0817";
    //路由更新失败
    public static final String APISIX_ROUTE_EDIT_EMPTY_ERROR = PREFIX + "0818";
    //路由删除失败
    public static final String APISIX_ROUTE_DELETE_EMPTY_ERROR = PREFIX + "0819";
    public static final String APISIX_ROUTE_FIND_EMPTY_ERROR = PREFIX + "0820";
    //路由类型不能为空
    public static final String APISIX_ROUTE_TYPE_EMPTY_ERROR = PREFIX + "0821";
    //TCP端口不足
    public static final String APISIX_ROUTE_TCP_PORT_EMPTY = PREFIX + "0822";
    //TCP路由更新失败
    public static final String APISIX_TCP_ROUTE_EDIT_EMPTY_ERROR = PREFIX + "0823";
    //网关下绑定服务不允许删除
    public static final String GATEWAY_EXIST_SERVICE_CANT_DELETE = PREFIX + "0824";
    //网关名称重复
    public static final String GATEWAY_EXIST_GATEWAY_NAME = PREFIX + "0825";
    //网关组件标识重复
    public static final String GATEWAY_EXIST_GATEWAY_CODE = PREFIX + "0826";
    //网关IP重复
    public static final String GATEWAY_EXIST_GATEWAY_IP = PREFIX + "0827";
    //系统中不存在管理类型网关，请先添加网关
    public static final String GATEWAY_MGT_NOT_EXIST = PREFIX + "0828";
    ////系统中不存在业务类型网关，请先添加网关
    public static final String GATEWAY_BUSI_NOT_EXIST = PREFIX + "0829";
    //网关无法删除，同一网关类型下至少保留一个网关
    public static final String GATEWAY_TYPE_NOT_EXIST_ONE = PREFIX + "0830";
    //组ID不可为空
    public static final String GATEWAY_GROUP_ID_NOT_NULL = PREFIX + "0831";
    //组标识不可为空
    public static final String GATEWAY_GROUP_GROUP_NOT_NULL = PREFIX + "0832";
    //区域id不可为空
    public static final String GATEWAY_REGION_ID_NOT_NULL = PREFIX + "0833";
    //网关无法删除，同一网关类型下至少保留一个网关
    public static final String GATEWAY_TYPE_NOT_EXIST_ONE_REGION = PREFIX + "0834";
    //系统不存在可用网关
    public static final String GATEWAY_LIVE_NOT_EXIST = PREFIX + "0835";
    //网关信息错误，网关IP不存在或重复
    public static final String GATEWAY_IP_REPEAT = PREFIX + "0836";
    //区域下只允许添加业务网关
    public static final String GATEWAY_REGION_NOT_ALLOW_MGT = PREFIX + "0837";
    //区域模式不允许添加管理网关
    public static final String CANNOT_ADD_MGT_GATEWAY_IN_REGION = PREFIX + "0838";
    //至少保留一个管理网关
    public static final String KEEP_THE_LEAST_ONE_MGT_GATEWAY = PREFIX + "0839";
    //无法删除已绑定服务的网关
    public static final String CANNOT_DELETE_GATEWAY_BEEN_BOUND = PREFIX + "0840";
    //区域下不存在可用网关
    public static final String REGION_NOT_EXIST_GATEWAY = PREFIX + "0841";
    //系统种不存在可用网关
    public static final String SYSTEM_NOT_EXIST_GATEWAY = PREFIX + "0842";
    //区域下不存在可用的网关SDK端口
    public static final String REGION_NOT_EXIST_GATEWAY_SDK_PORT = PREFIX + "0843";
    //系统中不存在可用的网关SDK端口
    public static final String SYSTEM_NOT_EXIST_GATEWAY_SDK_PORT = PREFIX + "0844";
    //不允许删除管理网关
    public static final String CANNOT_DELETE_MGT_GATEWAY = PREFIX + "0845";

    //密码服务代理已存在
    public static final String REGION_ADD_PROXY_FAILED_EXIST = PREFIX + "0846";
    //网关不可使用，请确认网关状态后添加
    public static final String GATEWAY_NOT_USE = PREFIX + "0847";
    //业务网关所属区域不可为空
    public static final String GATEWAY_BUSI_REGION_NOT_NULL = PREFIX + "0848";
    //网关IP,API端口重复
    public static final String GATEWAY_IP_API_PORT_REPEAT = PREFIX + "0849";


    //TODO 日志管理  0901-0920
    //一次导出操作日志不能大于5000条
    public static final String LOG_EXPORT_EXCEED_MAX_SIZE = PREFIX + "0901";
    //没有需要导出的日志数据
    public static final String LOG_EXPORT_NOT_EXIST = PREFIX + "0902";
    //此服务类型没有对应的下载文档
    public static final String DOC_DOWNLOAD_NOT_EXIST = PREFIX + "0903";

    //TODO 配额管理  0921-0990
    //配额信息不存在
    public static final String QUOTA_NOT_EXIST = PREFIX + "0921";
    //配额字典表中信息不存在
    public static final String QUOTA_DIC_NOT_EXIST = PREFIX + "0922";
    //配额值不能为0
    public static final String QUOTA_VALUE_IS_ZERO = PREFIX + "0923";
    //配额值小于允许设置的最小配额值
    public static final String QUOTA_VALUE_LESS_THAN_ALLOW = PREFIX + "0924";
    //配额值大于允许设置的最大配额值
    public static final String QUOTA_VALUE_GREATER_THAN_ALLOW = PREFIX + "0925";

    //TODO 主密钥备份恢复  0991-1000
    //请上传主密钥备份文件
    public static final String MASTER_KEY_FILE_NULL = PREFIX + "0991";
    //主密钥备份文件内容为空或文件被损坏
    public static final String MASTER_KEY_FILE_CONTENT_NULL = PREFIX + "0992";
    //请上传密钥备份文件
    public static final String INNER_KEY_FILE_NULL = PREFIX + "0993";
    //密钥备份文件内容为空或文件被损坏
    public static final String INNER_KEY_FILE_CONTENT_NULL = PREFIX + "0994";
    //上传文件必须是.bk文件
    public static final String MASTER_KEY_FILE_TYPE_ERROR = PREFIX + "0995";


    //TODO 许可证管理1001-1200
    //参数%s错误！
    public static final String LICENSE_PARAM_ERROR = PREFIX + "1001";
    //申请许可证记录不存在
    public static final String LICENSE_APPLY_NOT_EXIST = PREFIX + "1101";
    //申请许可证不允许下载
    public static final String LICENSE_APPLY_FORBID_DOWNLOAD = PREFIX + "1102";
    //申请许可证数据被非法纂改
    public static final String LICENSE_APPLY_VALUE_DISTORT = PREFIX + "1103";
    //申请许可证已使用，不允许重复导入
    public static final String LICENSE_APPLY_APPLYING = PREFIX + "1104";
    //许可证数据与原申请数据不匹配，不允许导入
    public static final String LICENSE_APPLY_DATA_ERROR = PREFIX + "1105";
    //不支持的许可类型
    public static final String LICENSE_APPLY_DISALLOW = PREFIX + "1106";
    //申请一次永久许平台许可证时数量不允许超过1个
    public static final String LICENSE_APPLY_DISALLOW_ONE = PREFIX + "1107";
    //许可证无效
    public static final String LICENSE_INVALID = PREFIX + "1005";
    //许可证已使用
    public static final String LICENSE_USED = PREFIX + "1006";
    //许可证数据被非法纂改
    public static final String LICENSE_VALUE_DISTORT = PREFIX + "1007";
    //许可证使用中
    public static final String LICENSE_USING = PREFIX + "1008";
    //许可证不存在
    public static final String LICENSE_NOT_EXIST = PREFIX + "1009";
    //许可证绑定服务实例不存在
    public static final String LICENSE_SERVER_NOT_EXIST = PREFIX + "1010";
    //许可证失效
    public static final String LICENSE_EXPIRED = PREFIX + "1011";
    //永久许可证不支持续约
    public static final String LICENSE_NOT_RENEW = PREFIX + "1012";
    //许可证未使用，无法释放资源
    public static final String LICENSE_NO_USING = PREFIX + "1013";
    //服务实例已绑定许可证，无法重复绑定
    public static final String LICENSE_SERVER_USING = PREFIX + "1014";
    //不支持业务服务许可证
    public static final String LICENSE_BUSINESS_UNSUPPORTED = PREFIX + "1015";
    //无可用的许可证
    public static final String LICENSE_NOT_AVAILABLE = PREFIX + "1016";
    //业务服务存在续约许可证，不支持释放
    public static final String LICENSE_NOT_UNBIND = PREFIX + "1017";
    //许可证内部异常，请联系管理员！
    public static final String LICENSE_ERROR = PREFIX + "1099";

    //监控管理1201-1250
    //监控地址必须是服务所在服务器地址
    public static final String SERVICE_IP_NOT_EXEIST = PREFIX + "1201";
    //大屏指标模板数据不允许为空，请联系管理员！
    public static final String SCREEN_INDEX_TEMPLATE_IS_NULL = PREFIX + "1202";
    //字段不允许非空
    public static final String FIELD_NOT_ALLOW_NULL = PREFIX + "1203";

    //管控服务1251-1300
    //初始化参数接口异常
    public static final String REMOTE_INIT_PARAM = PREFIX + "1251";
    //初始化配置接口异常
    public static final String REMOTE_INIT_CONFIG = PREFIX + "1252";
    //初始化设备接口异常
    public static final String REMOTE_INIT_DEVICE = PREFIX + "1253";
    //初始化启动接口异常
    public static final String REMOTE_INIT_START = PREFIX + "1254";
    //初始化修改自启参数接口异常
    public static final String REMOTE_INIT_EDIT_START_PARAM = PREFIX + "1255";
    //查询端口可用情况接口异常
    public static final String REMOTE_PORTS_CHECK = PREFIX + "1256";
    //TODO 系统初始化1301-1310
    //系统初始化信息失败
    public static final String SYS_INIT_ERROR = PREFIX + "1301";
    //解析jdbc  url失败
    public static final String JDBC_ANALYSIS_ERROR = PREFIX + "1302";
    //数据初始化添加数据库服务失败
    public static final String SYS_INIT_ADD_DB_ERROR = PREFIX + "1303";
    //数据初始化添加数据库实例失败
    public static final String SYS_INIT_ADD_DBUNIT_ERROR = PREFIX + "1304";
    //数据初始化添加数据库实例与服务组关系失败
    public static final String SYS_INIT_ADD_DBANDGROUP_ERROR = PREFIX + "1305";
    //系统初始化获取初始化人员失败
    public static final String SYS_INIT_GET_USER_ERROR = PREFIX + "1306";


    // TODO SIM盾管理1320-1360
    // SIM盾证书未申请
    public static final String SIM_CERT_NOT_APPLY = PREFIX + "1321";
    // 申请证书异常
    public static final String SIM_CERT_APPLY_ERROR = PREFIX + "1322";
    // 更新证书异常
    public static final String SIM_CERT_UPDATE_ERROR = PREFIX + "1323";
    // 撤销证书异常
    public static final String SIM_CERT_REVERT_ERROR = PREFIX + "1324";
    // 重置PIN码异常
    public static final String SIM_PIN_RESET_ERROR = PREFIX + "1325";
    // 设置PIN码异常
    public static final String SIM_PIN_SET_ERROR = PREFIX + "1326";
    // 修改PIN码异常
    public static final String SIM_PIN_MODIFY_ERROR = PREFIX + "1327";
    // SIM流水日志不存在
    public static final String SIM_FLOW_DATA_NOT_EXIST = PREFIX + "1328";
    // SIM盾用户状态不合法
    public static final String SIM_USER_STATUS_ERROR = PREFIX + "1329";
    // 手机号已被其他用户占用
    public static final String PHONE_HAS_BEAN_USED = PREFIX + "1330";
    // SIM盾证书数据库操作失败
    public static final String SIM_SHIELD_CERT_DB_ERROR = PREFIX + "1331";
    // 登录失败，未开通SIM盾
    public static final String SIM_SHIELD_LOGIN_ERROR = PREFIX + "1332";
    // 请求已过期
    public static final String SIM_SYN_REQUEST_EXPIRED = PREFIX + "1333";
    // 请求处理异常
    public static final String SIM_SYN_REQUEST_ERROR = PREFIX + "1334";
    // 证书已申请成功，请勿重复申请
    public static final String SIM_SHIELD_CERT_HAS_APPLY = PREFIX + "1335";
    // 回调接口处理证书请求异常
    public static final String SIM_SHIELD_EXTERNAL_CERT_ERROR = PREFIX + "1336";
    // 回调接口签名验签异常
    public static final String SIM_SHIELD_EXTERNAL_SIGN_ERROR = PREFIX + "1337";
    // 证书状态不合法
    public static final String SIM_SHIELD_CERT_STATUS_ERROR = PREFIX + "1338";
    // Sim盾用户不存在
    public static final String SIMSHIELD_USER_NOT_EXIST = PREFIX + "1339";
    // 签名值、方法和状态信息不可为空
    public static final String SIM_SIGN_INFO_CANT_EMPTY = PREFIX + "1340";
    // SIM签名请求失败
    public static final String SIM_SIGN_REQUEST_ERROR = PREFIX + "1341";
    // 手机号格式错误
    public static final String PHONE_FORMAT_ERROR = PREFIX + "1342";
    // SIM盾异步请求错误：%s
    public static final String SIM_SHIELD_SYN_REQUEST_FAILED = PREFIX + "1343";
    // SIM登录检查失败：%s
    public static final String SIM_SHIELD_LOGIN_CHECK_FAILED = PREFIX + "1344";
    // 会话keyId不可为空
    public static final String SESSION_KEY_ID_NOT_NULL = PREFIX + "1345";
    // SIM验签失败
    public static final String SIM_VERIFY_SIGNATURE_ERROR = PREFIX + "1346";

    // TODO SIMKey管理1361-1400
    //SimKey用户不存在
    public static final String SIMKEY_USER_NOT_EXIST = PREFIX + "1361";
    //SimKey用户状态不合法
    public static final String SIMKEY_USER_STATUS_ERROR = PREFIX + "1362";
    //SimKey用户权限错误
    public static final String SIMKEY_USER_PERMISSION_ERROR = PREFIX + "1363";
    //绑定或解绑的目标用户不存在
    public static final String BIND_USER_NOT_EXIST = PREFIX + "1364";
    //绑定或解绑的目标SimKey用户不存在
    public static final String BIND_SIMKEY_USER_NOT_EXIST = PREFIX + "1365";
    //不可绑定已经绑定了的SIMKEY用户
    public static final String SIMKEY_USER_ALREADY_BIND = PREFIX + "1366";
    //SIMKEY用户未与此用户绑定，不可解绑
    public static final String SIMKEY_USER_ALREADY_UNBIND = PREFIX + "1367";
    //手机号已存在白名单中
    public static final String SIMKEY_WHITE_LIST_PHONE_ALREADY_EXIST = PREFIX + "1368";
    //SIMKEY白名单项不存在
    public static final String SIMKEY_WHITE_LIST_NOT_EXIST = PREFIX + "1369";
    //SIMKEY批量导入白名单，Excel文件格式错误，请检查第%s行数据的格式
    public static final String SIMKEY_WHITE_LIST_EXCEL_FORMAT_ERROR = PREFIX + "1370";
    //SIMKEY批量导入白名单，Excel文件手机号码重复或已在白名单中，请检查第%s行手机号码
    public static final String SIMKEY_WHITE_LIST_EXCEL_PHONE_ALREADY_EXIST = PREFIX + "1371";
    //SIMKEY批量导入白名单，Excel文件解析错误
    public static final String SIMKEY_WHITE_LIST_EXCEL_PARSE_ERROR = PREFIX + "1372";
    //SIMKEY批量导入白名单，Excel文件格式错误，请上传%s格式的文件
    public static final String SIMKEY_WHITE_LIST_EXCEL_MIME_ERROR = PREFIX + "1373";
    //SIMKEY批量导入白名单，Excel大小超出限制，请上传2M以内的文件
    public static final String SIMKEY_WHITE_LIST_EXCEL_OVERSIZE = PREFIX + "1374";
    //SIMKEY批量导入白名单，Excel文件与模板文件不匹配，请下载模板文件填写
    public static final String SIMKEY_WHITE_LIST_EXCEL_NOT_MATCH = PREFIX + "1375";
    //手机号已注册SIMKEY
    public static final String SIMKEY_PHONE_ALREADY_EXIST = PREFIX + "1376";
    //SIMKEY用户已开通业务，不可重复开通
    public static final String SIMKEY_USER_ALREADY_OPEN = PREFIX + "1377";
    //SIMKEY用户被禁用，不可开通SIMKEY
    public static final String SIMKEY_PHONE_IS_DISENABLE = PREFIX + "1378";
    //无开通权限，请联系管理员添加白名单
    public static final String SIMKEY_PHONE_NOT_IN_WHITELIST = PREFIX + "1379";


    /**
     * 生成登录验证码失败
     */
    public static final String SIMKEY_GEN_QRCODE_ERROR = PREFIX + "1380";

    /**
     * 登录请求已过期
     */
    public static final String THE_LOGIN_REQUEST_HAS_EXPIRED = PREFIX + "1381";

    /**
     * SIMKEY用户未绑定系统用户
     */
    public static final String SIMKEY_USER_UNBIND_CCSP_USER = PREFIX + "1382";

    /**
     * 手机号不在白名单
     */
    public static final String THE_PHONE_NUMBER_IS_NOT_WHITELISTED = PREFIX + "1383";

    /**
     * SIMkey用户被禁用
     */
    public static final String SIMKEY_USER_WAS_DISABLED = PREFIX + "1384";

    /**
     * SIMkey用户未开通
     */
    public static final String SIMKEY_USER_NOT_OPENED = PREFIX + "1385";

    /**
     * SIMkey用户证书未申请
     */
    public static final String SIMKEY_USER_CERT_NOT_APPLY = PREFIX + "1386";

    /**
     * 获取SIMKEY证书错误，SIMKEY证书无效
     */
    public static final String GET_SIMKEY_CERT_ERROR = PREFIX + "1387";

    /**
     * SIMKEY登录验证签名值失败
     */
    public static final String FAILED_TO_VERIFY_THE_SIGNATURE_VALUE = PREFIX + "1388";

    /**
     * SIMKEY证书接口操作类型错误
     */
    public static final String SIMKEY_CERT_OPER_CODE_ERROR = PREFIX + "1389";

    /**
     * SIMKEY用户当前有正常使用证书，不需要申请
     */
    public static final String SIMKEY_CERT_ALREADY_EXIST = PREFIX + "1390";

    /**
     * SIMKEY用户没有启用状态的证书
     */
    public static final String SIMKEY_CERT_ENABLE_NOT_EXIST = PREFIX + "1391";
    /**
     * SIMKEY用户未申请证书或不存在已启用的证书，无法更新证书
     */
    public static final String SIMKEY_USER_NOT_VALID_CERT = PREFIX + "1392";
    /**
     * SIMKEY用户登录 用户名或密码错误
     */
    public static final String SIMKEY_USER_LOGIN_ERROR = PREFIX + "1393";
    /**
     * SIMKEY获取证书信息失败
     */
    public static final String SIMKEY_CERT_PARSE_ERROR = PREFIX + "1394";
    /**
     * SIMKEY登录状态检测错误：%s
     */
    public static final String SIM_KEY_LOGIN_CHECK_FAILED = PREFIX + "1395";
    /**
     * SIMKEY用户未申请证书
     */
    public static final String SIM_KEY_USER_NOT_CERT = PREFIX + "1396";



     // todo 区域1401-1450
    //区域不存在
    public static final String REGION_NOT_EXIST = PREFIX + "1401";
    //区域标识或名称已存在
    public static final String REGION_CODE_NAME_REPEAT = PREFIX + "1402";
    //添加区域数据库操作错误
    public static final String REGION_DB_ERROR = PREFIX + "1403";
    //区域名称重复
    public static final String REGION_NAME_DUPLICATE = PREFIX + "1404";
    //区域下包含租户，无法删除
    public static final String REGION_INCLUDE_TENANT = PREFIX + "1405";
    //区域下包含网关，无法删除
    public static final String REGION_INCLUDE_SERVICEGATEWAY = PREFIX + "1406";
    //区域下包含网关路由，无法删除
    public static final String REGION_INCLUDE_SERVICEGATEWAYROUTE = PREFIX + "1407";
    //区域下包含密码设备，无法删除
    public static final String REGION_INCLUDE_DEVICE = PREFIX + "1408";
    //区域下包含密码服务，无法删除
    public static final String REGION_INCLUDE_SERVICE = PREFIX + "1409";
    // 区域ID不可为空
    public static final String REGION_ID_NOT_NULL = PREFIX + "1410";
    //区域下包含数据库服务，无法删除
    public static final String REGION_INCLUDE_DATABASE = PREFIX + "1411";
    //区域下包含应用，无法删除
    public static final String REGION_INCLUDE_APP = PREFIX + "1412";
    //区域下包含设备组，无法删除
    public static final String REGION_INCLUDE_DEVICEGROUP = PREFIX + "1413";
    //区域下包含服务组，无法删除
    public static final String REGION_INCLUDE_SERVICEGROUP = PREFIX + "1414";
    //区域ID和所属租户区域ID不一致
    public static final String REGION_ID_NOT_EQUAL_TENANT_ID = PREFIX + "1415";
    //数据库所属区域和区域ID不一致
    public static final String REGION_ID_NOT_EQUAL_DB_REGION_ID = PREFIX + "1416";

    //区域下包含密码设备，无法删除
    //业务地址对象IP和端口重复
    public static final String BUSI_URL_IP_PORT_DUPLICATED = PREFIX + "1417";

    //存在正在使用的区域，区域模式无法修改
    public static final String REGION_MODE_CANNOT_REMOVE = PREFIX + "1418";
    //服务云标识已存在
    public static final String SERVICE_CLOUD_CODE_REPEAT = PREFIX + "1419";


    // 校验密码1451-1460
    //获取redis中的密钥值出错
    public static final String UPGRADE_CODE_REDISID_ERROR = PREFIX + "1451";
    //输入口令解密出错
    public static final String UPGRADE_CODE_DECRYPT_ERROR = PREFIX + "1452";
    //升级密码错误
    public static final String UPGRADE_CODE_CHECK_ERROR = PREFIX + "1453";

    /**
     * 告警1461-1470
     */
    // 告警源已过期
    public static final String ALARM_SOURCE_HAS_EXPIRED = PREFIX + "1461";
    // 区域告警信息查询失败
    public static final String REGION_ALARM_SEARCH_ERROR = PREFIX + "1462";

    //管控服务1471-1480
    //管控服务已存在
    public static final String REMOTE_SERVER_IS_EXIST = PREFIX + "1471";
    //管控服务不存在
    public static final String REMOTE_SERVER_IS_NOT_EXIST = PREFIX + "1472";
    //管控服务状态异常
    public static final String REMOTE_SERVER_NOT_LIVE = PREFIX + "1473";
    //管控服务入库失败
    public static final String INSERT_REMOTE_SERVER_DB_ERROR = PREFIX + "1474";
    //当前区域下不存在管控服务
    public static final String THE_CURRENT_NOT_EXIST_REMOTE_SERVER = PREFIX + "1475";
    //管控服务删除失败
    public static final String REMOTE_SERVER_DELETE_FAILURE = PREFIX + "1476";
    //管控服务删除失败
    public static final String PRIMARY_REMOTE_SERVER_IS_EXIST = PREFIX + "1477";

    //************认证服务管理1480-1500************************//
    /**
     * 认证服务不可用
     */
    public static final String AUTH_NOT_SURVIVE = PREFIX + "1480";

    /**
     * 认证服务不存在
     */
    public static final String AUTH_SERVICE_NOT_EXIST = PREFIX + "1481";

    /**
     * 认证服务地址已存在
     */
    public static final String AUTH_SERVICE_ADDRESS_REPEAT = PREFIX + "1482";

    /**
     * 认证服务区域不可变更
     */
    public static final String AUTH_SERVICE_REGION_MODIFY = PREFIX + "1483";

    /**
     * 认证服务名称已存在
     */
    public static final String AUTH_SERVICE_NAME_REPEAT = PREFIX + "1484";

    /**
     * 认证服务ip不可变更
     */
    public static final String AUTH_SERVICE_IP_MODIFY = PREFIX + "1485";

    /**
     * 认证服务端口不可变更
     */
    public static final String AUTH_SERVICE_PORT_MODIFY = PREFIX + "1486";

    /**
     * 区域下存在应用
     */
    public static final String GATEWAY_EXIST_APP = PREFIX + "1487";


    // 监控服务管理1501-1520
    // 监控服务禁止删除
    public static final String MONITOR_SERVICE_DELETE_FORBIDDEN = PREFIX + "1501";
    // 监控服务ID不存在
    public static final String MONITOR_SERVICE_OBJ_NOT_EXIST = PREFIX + "1502";
    // 监控服务ip和端口已添加
    public static final String MONITOR_SERVICE_PORT_IP_EXIST = PREFIX + "1503";
    // 删除监控服务失败
    public static final String MONITOR_SERVICE_DELETE_FAILED = PREFIX + "1504";
    // 查询监控服务区域ID为空
    public static final String MONITOR_SERVICE_REGION_ID_EMPTY = PREFIX + "1505";
    // 区域下不存在可用网关
    public static final String MONITOR_SERVICE_REGION_GATEWAY_NOT_USABLE = PREFIX + "1506";
    // 区域下不存在监控服务
    public static final String MONITOR_SERVICE_NOT_EXIST_BY_REGION = PREFIX + "1507";
    // 监控服务状态异常
    public static final String MONITOR_SERVICE_CHECK_STATUS_ERROR = PREFIX + "1508";
    // 监控服务添加失败
    public static final String MONITOR_SERVICE_ADD_FAILED = PREFIX + "1509";


    // 网关统计组件1521-1550
    //网关统计组件标识已存在
    public static final String SERVICE_COUNT_MODULE_CODE_REPEAT = PREFIX + "1521";
    ///网关统计组件不存在
    public static final String SERVICE_COUNT_MODULE_NOT_EXIST = PREFIX + "1522";
    //区域下最后一个统计组件不可删除
    public static final String LAST_SERVICE_COUNT_MODULE_IN_REGION = PREFIX + "1523";
    //删除网关统计组件失败
    public static final String DELETE_SERVICE_COUNT_MODULE_ERROR = PREFIX + "1524";
    //当前区域下不存在该网关
    public static final String GATEWAY_NOT_EXIST_IN_REGION = PREFIX + "1525";


    // todo 实时统计1600-1700
    //服务标识不存在
    public static final String REAL_TIME_SERVICE_CODE_NOT_ERROR = PREFIX + "1601";
    //应用标识不存在
    public static final String REAL_TIME_APP_CODE_NOT_ERROR = PREFIX + "1602";
    //租户标识不存在
    public static final String REAL_TIME_TENANT_CODE_NOT_ERROR = PREFIX + "1603";
    //采集时间格式化小时失败
    public static final String REAL_TIME_COLLECT_TIME_FORMAT_HOUR_ERROR = PREFIX + "1604";
    //采集数据目标ip端口错误
    public static final String REAL_TIME_COLLECT_DATA_IPPORT_ERROR = PREFIX + "1605";
    //秒级采集时间不存在
    public static final String REAL_TIME_COLLECT_DATA_TIME_ERROR = PREFIX + "1606";
    //统计分析参数查询不存在
    public static final String REAL_TIME_FRONT_QUERY_PARAM_ERROR = PREFIX + "1607";
    //查询类型不存在
    public static final String REAL_TIME_FRONT_QUERY_TYPE_ERROR = PREFIX + "1608";
    //查询时间格式不正确
    public static final String REAL_TIME_FRONT_QUERY_TIME_ERROR = PREFIX + "1609";

    //应用不存在
    public static final String REAL_TIME_APP_NOT_EXISTS_ERROR = PREFIX + "1610";

    //应用所属租户信息不存在,%s
    public static final String REAL_TIME_APP_TENANT_NOT_EXISTS_ERROR = PREFIX + "1611";

    //请求统计模块失败
    public static final String REQUEST_STATIC_MODULE_ERROR = PREFIX + "1612";

    //权限错误：请选择您当前单位下的租户
    public static final String ORGANIZATION_PERMISSION_ERROR = PREFIX + "1613";


    //************************* hadoop错误码 1701-1720 *****************************************//
    //hdfs未知异常
    public static final String HDFS_UNKNOWN_ERROR = PREFIX + "1700";

    //本地文件不存在
    public static final String HDFS_LOCAL_FILE_PATH_NOT_EXIST = PREFIX + "1701";

    //hdfs文件已存在
    public static final String HDFS_FILE_PATH_IS_EXIST = PREFIX + "1702";


    //************************* 镜像管理错误码 1721-1750 *****************************************//

    //文件hash读取异常
    public static final String FILE_HASH_READ_ERROR = PREFIX + "1721";

    //文件hash不匹配
    public static final String FILE_HASH_NOT_MATCH = PREFIX + "1722";

    //超过上传镜像文件最大总和限制
    public static final String EXCEED_IMAGE_FILE_MAX_SIZE_LIMIT = PREFIX + "1723";

    //镜像文件异常
    public static final String UPLOAD_IMAGE_ERROR = PREFIX + "1725";

    //已存在相同镜像
    public static final String IMAGE_EXIST = PREFIX + "1726";

    //镜像不存在
    public static final String IMAGE_NOT_EXIST = PREFIX + "1727";

    //镜像已启用
    public static final String IMAGE_ENABLED = PREFIX + "1728";

    //镜像上传被中断
    public static final String UPLOAD_IMAGE_INTERRUPT = PREFIX + "1729";

    //镜像不可修改
    public static final String IMAGE_MODIFY_VALID_ERROR = PREFIX + "1729";

    //镜像初始化中
    public static final String IMAGE_UPLOADING = PREFIX + "1730";

    //************************* 文件管理错误码 1751-1800 *****************************************//
    /**
     * 文件不存在
     */
    public static final String FILE_NOT_EXIST = PREFIX + "1751";

    /**
     * 文件同步异常
     */
    public static final String FILE_SYNC_ERROR = PREFIX + "1752";



    public static final String SERVER_PRODUCT_STATUS_ERROR = PREFIX + "1801";

    /**
     * 云算力对接异常
     */

    public static final String DCQC_BATCH_INSERT_DATA_ERROR = PREFIX + "1901";

    public static final String KAFKA_PUSH_ERROR = PREFIX + "1902";

    public static final String TENANT_PRODUCT_NUM_NOT_EXIST = PREFIX + "1903";
    /**
     * 云算力推送记录表Hmac更新失败
     */
    public static final String KAFKA_PUSH_DATA_HMAC_UPDATE_ERROR = PREFIX + "1904";

    // 监控统计报错  2771-2800

    // 查询监控数据异常：%s
    public static final String MONITOR_DATA_QUERY_ERROR = PREFIX + "2771";

    // 查询监控数据，时间段非法
    public static final String MONITOR_DATA_TIME_INVALID = PREFIX + "2772";

    // 查询监控数据，查询对象类型不存在
    public static final String MONITOR_DATA_OBJ_TYPE_NOT_EXIST = PREFIX + "2773";

    // 查询监控数据，数据统计类型不存在
    public static final String MONITOR_DATA_STAT_TYPE_NOT_EXIST = PREFIX + "2774";
}
