package com.sansec.ccsp.device.business.devicetype.controller;

import com.sansec.ccsp.device.business.devicetype.service.DeviceTypeService;
import com.sansec.ccsp.device.devicetype.request.DeviceTypeDTO;
import com.sansec.ccsp.device.devicetype.request.DeviceTypeEditDTO;
import com.sansec.ccsp.device.devicetype.request.DeviceTypeIdDTO;
import com.sansec.ccsp.device.devicetype.request.DeviceTypePageDTO;
import com.sansec.ccsp.device.devicetype.response.DeviceTypeVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

 /**
 * @Description: 设备类型;(DEVICE_TYPE)表控制层
 * <AUTHOR> xia<PERSON><PERSON>aw<PERSON>
 * @Date: 2023-2-18
 */
@RestController
@RequestMapping("/deviceType/v1")
@Validated
public class DeviceTypeController{
    @Resource
    private DeviceTypeService deviceTypeService;
    
    /** 
     * 分页查询
     *
     * @param deviceTypePageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    public SecRestResponse<SecPageVO<DeviceTypeVO>> find(@RequestBody DeviceTypePageDTO deviceTypePageDTO){
        return deviceTypeService.find(deviceTypePageDTO);
    }


    
    /** 
     * 新增数据
     *
     * @param deviceTypeDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    public SecRestResponse<Object> add(@RequestBody DeviceTypeDTO deviceTypeDTO){
        return deviceTypeService.add(deviceTypeDTO);
    }
    
    /** 
     * 更新数据
     *
     * @param deviceTypeEditDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/edit")
    public SecRestResponse<Object> edit(@RequestBody DeviceTypeEditDTO deviceTypeEditDTO){
        return deviceTypeService.edit(deviceTypeEditDTO);
    }
    
    /** 
     * 通过主键删除数据
     *
     * @param deviceTypeDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/deleteById")
    public SecRestResponse<Object> deleteById(@RequestBody DeviceTypeIdDTO deviceTypeDTO){
        return deviceTypeService.deleteById(deviceTypeDTO);
    }
}