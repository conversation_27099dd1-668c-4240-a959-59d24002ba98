package com.sansec.ai.extra.dify.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.sansec.ai.extra.dify.constant.DifyConfigEnum;
import com.sansec.ai.extra.dify.entity.ModelConfigParam;
import com.sansec.ai.extra.dify.request.DifyInitRequest;
import com.sansec.ai.extra.dify.request.DifyInsInfo;
import com.sansec.ai.extra.dify.request.DifyInstanceProperties;
import com.sansec.ai.extra.dify.result.DifyInstanceStatusVo;
import com.sansec.ai.extra.dify.result.ProcessResultVo;
import com.sansec.ai.extra.dify.service.DifyDeployService;
import com.sansec.ai.extra.dify.service.ProcessService;
import com.sansec.ai.extra.dify.util.DifyTokenUtil;
import com.sansec.ai.extra.dify.util.ModelConfigParser;
import com.sansec.ai.extra.dify.util.TemplateUtils;
import com.sansec.ai.portal.constant.PortalErrorCode;
import com.sansec.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.UnixDomainSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Dify部署相关接口
 *
 * <AUTHOR>
 * @since 2025/5/12 18:51
 */
@Slf4j
@Service
public class DifyDeployServiceImpl extends BaseDifyService implements DifyDeployService {

	@Autowired
	private JdbcTemplate jdbcTemplate;
	@Autowired
	public DifyTokenUtil difyTokenUtil;
	@Autowired
	public ModelConfigParser modelConfigParser;
	@Autowired
	private ProcessService processService;

	@Value("${ai.dify.instance_ip}")
	private String dbIP;
	@Value("${spring.datasource.username}")
	private String dbUser;
	@Value("${spring.datasource.password}")
	private String dbPwd;


	/**
	 * 根据门户标识，初始化专用数据库
	 *
	 * @param portalCode 门户标识
	 * @throws BusinessException
	 */
	@Override
	public void initDatabase(String portalCode) throws BusinessException {
		log.info("初始化专用数据库-开始[portalCode={}]", portalCode);
		//NOTE 1-从系统配置中，获取模板数据库名称，一共两个模板：业务数据库、插件数据库
		//业务数据库模板
		String dbTemplateOfDify = getConfigOrDefault(DifyConfigEnum.TEMPLATE_DATABASE_NAME_DIFY, "dify");
		//插件数据库模板
		String dbTemplateOfDifyPlugin = getConfigOrDefault(DifyConfigEnum.TEMPLATE_DATABASE_NAME_DIFY_PLUGIN, "dify_plugin");
		//NOTE 2-根据门户标识，定义数据库的名称：模板数据库名称_门户标识
		//业务数据库
		String dbOfDify = getDatabaseName(portalCode);
		//插件数据库
		String dbOfDifyPlugin = getPluginDatabaseName(portalCode);
		//先释放连接，防止创建时数据库正在被访问
		jdbcTemplate.execute(String.format("SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = '%s' AND pid <> pg_backend_pid();", dbOfDify));
		jdbcTemplate.execute(String.format("SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = '%s' AND pid <> pg_backend_pid();", dbOfDifyPlugin));
		//如果数据库已存在，需要先删除
		jdbcTemplate.execute(String.format("DROP DATABASE IF EXISTS \"%s\";", dbOfDify));
		jdbcTemplate.execute(String.format("DROP DATABASE IF EXISTS \"%s\";", dbOfDifyPlugin));
		//使用模板数据库来创建新的数据库
		jdbcTemplate.execute(String.format("CREATE DATABASE \"%s\" WITH TEMPLATE \"%s\";", dbOfDify, dbTemplateOfDify));
		jdbcTemplate.execute(String.format("CREATE DATABASE \"%s\" WITH TEMPLATE \"%s\";", dbOfDifyPlugin, dbTemplateOfDifyPlugin));
		//NOTE 3-检查数据库是否成功生成
		boolean difyExist = dbOfDify.equals(jdbcTemplate.queryForObject(String.format("SELECT datname FROM pg_database WHERE datname = '%s';", dbOfDify), String.class));
		boolean difyPluginExist = dbOfDifyPlugin.equals(jdbcTemplate.queryForObject(String.format("SELECT datname FROM pg_database WHERE datname = '%s';", dbOfDifyPlugin), String.class));
		if (difyExist && difyPluginExist) {
			log.info("初始化专用数据库-成功[portalCode={}]", portalCode);
			return;
		}
		log.error("初始化专用数据库-数据库创建失败，开始回滚[portalCode={}, difyExist={}, difyPluginExist={}]", portalCode, difyExist, difyPluginExist);
		//NOTE 4-如果数据库创建失败，则需要回滚，把已创建的数据库删除
		if (difyExist) {
			jdbcTemplate.execute(String.format("DROP DATABASE \"%s\";", dbOfDify));
		}
		if (difyPluginExist) {
			jdbcTemplate.execute(String.format("DROP DATABASE \"%s\";", dbOfDifyPlugin));
		}
		log.error("初始化专用数据库-回滚完成[portalCode={}]", portalCode);
		throw new BusinessException(PortalErrorCode.PORTAL_INIT_FAILED_S, "初始化专用数据库失败");
	}

	/**
	 * 根据门户标识+实例配置，初始化专用配置
	 *
	 * @param portalCode 门户标识
	 * @param properties 实例配置
	 * @throws BusinessException
	 */
	@Override
	public void initConfig(String portalCode, DifyInstanceProperties properties) throws BusinessException {
		log.info("初始化专用配置-开始[portalCode={}]", portalCode);
		//NOTE 1-初始化部署目录
		//模板目录名称
		String templateDirName = ".template";
		//根据模板目录，创建实例部署目录
		ProcessResultVo result = processService.execCommandWithResult(getRootDir(), 10000L, String.format("cp -r %s %s", templateDirName, portalCode));
		if (!result.getSuccess()) {
			log.error("初始化专用配置-创建实例部署目录失败[portalCode={}, result={}]", portalCode, JSON.toJSONString(result));
			throw new BusinessException(PortalErrorCode.PORTAL_INIT_FAILED_S, "创建实例部署目录失败");
		}
		//根据模板目录，创建知识库目录
		result = processService.execCommandWithResult(getAppStorageDir(null), 10000L, String.format("cp -r %s %s", templateDirName, portalCode));
		if (!result.getSuccess()) {
			log.error("初始化专用配置-创建知识库目录失败[portalCode={}, result={}]", portalCode, JSON.toJSONString(result));
			throw new BusinessException(PortalErrorCode.PORTAL_INIT_FAILED_S, "创建知识库目录失败");
		}
		//NOTE 2-初始化配置文件
		//获取env文件的配置项
		// key = env文件中的占位符key
		// value = 对应{@link com.sansec.ai.extra.dify.request.DifyInstanceProperties}中的field
		Map<String, String> configMap = getConfigMap(DifyConfigEnum.TEMPLATE_ENV_CONFIG_MAP);
		Map<String, Object> params = new HashMap<>();
		JSONObject propertiesJson = JSON.parseObject(JSON.toJSONString(properties));
		for (Map.Entry<String, String> entry : configMap.entrySet()) {
			params.put(entry.getKey(), propertiesJson.getString(entry.getValue()));
		}
		//.env文件路径
		String envFilePath = getDeployDir(portalCode) + File.separator + ".env";
		try {
			TemplateUtils.composeFile(envFilePath, params);
		} catch (IOException e) {
			log.error("初始化专用配置-初始化配置文件失败[portalCode={}, envFilePath={}, params={}]", portalCode, envFilePath, JSON.toJSONString(params));
			//删除已创建的部署目录
			processService.execCommandWithResult(getRootDir(), 10000L, String.format("rm -rf %s %s", templateDirName, portalCode));
			//删除已创建的知识库目录
			processService.execCommandWithResult(getRootDir(), 10000L, String.format("rm -rf %s %s", templateDirName, getAppStorageDir(portalCode)));
			throw new BusinessException(PortalErrorCode.PORTAL_INIT_FAILED_S, "初始化配置文件失败");
		}
		log.info("初始化专用配置-成功[portalCode={}]", portalCode);
	}

	/**
	 * 根据门户标识，创建Dify实例对应的容器
	 *
	 * @param portalCode 门户标识
	 * @throws BusinessException
	 */
	@Override
	public void createContainer(String portalCode) throws BusinessException {
		ProcessResultVo result = processService.execCommandWithResult(getDeployDir(portalCode), 60000L, "docker-compose up -d");
		if (!result.getSuccess()) {
			log.error("初始化专用配置-创建Dify实例对应的容器失败[portalCode={}, result={}]", portalCode, JSON.toJSONString(result));
			throw new BusinessException(PortalErrorCode.PORTAL_INIT_FAILED_S, "启动门户相关服务失败");
		}
	}

	/**
	 * 初始化Dify实例
	 */
	@Override
	public void initInstance(DifyInsInfo difyInsInfo, DifyInitRequest request) throws BusinessException {
		String baseUrl = "http://" + difyInsInfo.getIp() + ":" + difyInsInfo.getPort();
		log.info("初始化Dify实例-开始[baseUrl={}]", baseUrl);

		// 1. 系统初始化
		// 1.1 设置管理员
		String setupUrl = baseUrl + "/console/api/setup";
		JSONObject setupBody = new JSONObject();
		setupBody.put("email", request.getEmail());
		setupBody.put("name", request.getUsername());
		setupBody.put("password", request.getPassword());
		log.debug("设置管理员-请求URL: {}, 请求体: {}", setupUrl, setupBody.toJSONString());

		JSONObject setupResponse = requestPost(setupUrl, setupBody.toJSONString(), null);
		if (setupResponse == null || !"success".equals(setupResponse.getString("result"))) {
			log.error("设置管理员失败，响应内容：{}", setupResponse);
			throw new BusinessException("设置管理员失败");
		}

		// 1.2 登录管理员
		String token = difyTokenUtil.getNewToken(difyInsInfo);
		log.info("获取管理员token成功");

		// 1.3 设置语言和时区
		String setTimeZoneUrl = baseUrl + "/console/api/account/timezone";
		requestPost(setTimeZoneUrl, "{\"timezone\":\"Asia/Shanghai\"}", token);
		log.info("设置时区成功");
		String setLanguageUrl = baseUrl + "/console/api/account/interface-language";
		requestPost(setLanguageUrl, "{\"interface_language\":\"zh-Hans\"}", token);
		log.info("设置语言成功");

		// 2 插件数据库设置租户id
		// 2.1 获取租户id
		String difyDbUrl = "jdbc:postgresql://" + dbIP + "/dify_" + request.getPortalCode();
		String queryTenantIdSql = "SELECT id FROM tenants LIMIT 1";
		String newTenantId;

		// 从dify数据库获取租户ID
		try (Connection difyConn = DriverManager.getConnection(difyDbUrl, dbUser, dbPwd);
		     PreparedStatement queryStmt = difyConn.prepareStatement(queryTenantIdSql);
		     ResultSet rs = queryStmt.executeQuery()) {

			if (rs.next()) {
				newTenantId = rs.getString("id");
				log.info("成功获取租户ID [portalCode={}, tenantId={}]", request.getPortalCode(), newTenantId);
			} else {
				log.error("未找到租户记录 [portalCode={}]", request.getPortalCode());
				throw new BusinessException("未找到租户记录");
			}
		} catch (SQLException e) {
			log.error("获取租户ID失败 [portalCode={}]: {}", request.getPortalCode(), e.getMessage(), e);
			throw new BusinessException("获取租户ID失败: " + e.getMessage());
		}

		// 2.2 更新插件数据库中的租户ID
		String difyPluginDbUrl = "jdbc:postgresql://" + dbIP + "/dify_plugin_" + request.getPortalCode();
		List<String> difyPluginTableNames = Arrays.asList("agent_strategy_installations.tenant_id", "ai_model_installations.tenant_id", "plugin_installations.tenant_id", "tool_installations.tenant_id");

		this.updateTenantIdByTableName(difyPluginDbUrl, difyPluginTableNames, newTenantId);


		// 3. 添加自有模型
		// 3.1 查询模型配置文件
		List<ModelConfigParam> modelConfigParamList = modelConfigParser.loadModelConfig();
		String addModelUrl = baseUrl + "/console/api/workspaces/current/model-providers/langgenius/openai_api_compatible/openai_api_compatible/models";
		log.info("查询模型配置文件成功，模型数量: {}", modelConfigParamList.size());

		// 3.2 遍历模型配置，添加模型
		for (ModelConfigParam modelConfigParam : modelConfigParamList) {
			// 组装请求体
			JSONObject credentials = new JSONObject();
			credentials.put("mode", modelConfigParam.getCompletionMode());
			credentials.put("context_size", modelConfigParam.getContextSize());
			credentials.put("max_tokens_to_sample", modelConfigParam.getMaxTokensToSample());
			credentials.put("agent_though_support", "not_supported");
			credentials.put("function_calling_type", "no_call");
			credentials.put("stream_function_calling", "not_supported");
			credentials.put("vision_support", modelConfigParam.getVisionSupport());
			credentials.put("stream_mode_delimiter", "\n\n");
			credentials.put("voices", "alloy");
			credentials.put("display_name", modelConfigParam.getDisplayName());
			credentials.put("api_key", modelConfigParam.getApiKey());
			credentials.put("endpoint_url", modelConfigParam.getEndpointUrl());
			JSONObject addModelBody = new JSONObject();
			addModelBody.put("model", modelConfigParam.getName());
			addModelBody.put("model_type", modelConfigParam.getModelType());
			addModelBody.put("credentials", credentials);
			log.info("添加模型-请求URL: {}, 请求体: {}", addModelUrl, addModelBody.toJSONString());

			JSONObject addModelResponse = requestPost(addModelUrl, addModelBody.toJSONString(), token);
			if (addModelResponse == null || !"success".equals(addModelResponse.getString("result"))) {
				log.error("添加模型失败，响应内容：{}", addModelResponse);
				throw new BusinessException("添加模型失败");
			}
		}

		// 4.配置系统模型
		Map<String, String> systemModelMap = modelConfigParser.loadSystemModelConfig();
		String setSystemModelUrl = baseUrl + "/console/api/workspaces/current/default-model";
		List<JSONObject> modelSettings = new ArrayList<>(5);
		String llmModelName = "";
		List<String> modelTypeList = Arrays.asList("llm", "text-embedding", "rerank", "speech2text", "tts");
		for (String modelType : modelTypeList) {
			JSONObject model = new JSONObject();
			model.put("model_type", modelType);
			if (systemModelMap.containsKey(modelType) && StringUtils.isNotBlank(systemModelMap.get(modelType))) {
				model.put("provider", "langgenius/openai_api_compatible/openai_api_compatible");
				model.put("model", systemModelMap.get(modelType));
				if (modelType.equals("llm")) {
					llmModelName = systemModelMap.get(modelType);
				}
			}
			modelSettings.add(model);
		}

		JSONObject setSystemModelBody = new JSONObject();
		setSystemModelBody.put("model_settings", modelSettings);
		JSONObject setSystemModelResponse = requestPost(setSystemModelUrl, setSystemModelBody.toJSONString(), token);
		if (setSystemModelResponse == null || !"success".equals(setSystemModelResponse.getString("result"))) {
			log.error("设置系统模型失败，响应内容：{}", setSystemModelResponse);
			throw new BusinessException("设置系统模型失败");
		}

		// 5. 应用配置

		// 5.1 更新业务数据库中的租户ID
		List<String> difyTableNames = Arrays.asList("api_based_extensions.tenant_id", "apps.tenant_id", "installed_apps.tenant_id", "installed_apps.app_owner_tenant_id", "upload_files.tenant_id", "workflows.tenant_id", "tags.tenant_id", "tag_bindings.tenant_id");

		this.updateTenantIdByTableName(difyDbUrl, difyTableNames, newTenantId);

		// 5.2 模型名称替换
		List<String> replaceTableName = Arrays.asList("app_model_configs.model", "workflows.graph");
		try (Connection pluginConn = DriverManager.getConnection(difyDbUrl, dbUser, dbPwd)) {
			// 开启事务
			pluginConn.setAutoCommit(false);
			try {
				// 遍历所有需要更新的表
				for (String item : replaceTableName) {
					String[] split = item.split("\\.");
					String tableName = split[0];
					String colName = split[1];
					String updateSql = "UPDATE " + tableName + " SET " + colName + " = replace(" + colName + ", 'LLM_NAME', '" + llmModelName + "');";
					try (PreparedStatement updateStmt = pluginConn.prepareStatement(updateSql)) {
						int updatedRows = updateStmt.executeUpdate();
						log.info("更新模型名称 - 表: {}, 更新记录数: {}", tableName, updatedRows);
					} catch (SQLException e) {
						log.error("更新表 {} 失败: {},sql:{}", tableName, e.getMessage(), updateSql, e);
						throw e;
					}
				}
				// 提交事务
				pluginConn.commit();
				log.info("更新模型名称完成 [dburl={}, llmModelName={}]", difyDbUrl, llmModelName);

			} catch (SQLException e) {
				// 回滚事务
				pluginConn.rollback();
				log.error("更新模型名称更新失败，已回滚事务 [dburl={}]: {}", difyDbUrl, e.getMessage(), e);
				throw e;
			}
		} catch (SQLException e) {
			log.error("数据库连接失败，无法更新模型名称 [dburl={}]: {}", difyDbUrl, e.getMessage(), e);
			throw new BusinessException("数据库模型名称更新失败: " + e.getMessage());
		}

		// 5.3 额外特殊处理
		try (Connection pluginConn = DriverManager.getConnection(difyDbUrl, dbUser, dbPwd)) {
			// 开启事务
			pluginConn.setAutoCommit(false);
			try {
				StringBuilder updateSql = new StringBuilder();
				updateSql.append("UPDATE upload_files SET key = replace(key, '11111111-1111-1111-1111-111111111111', '" + newTenantId + "');");
				updateSql.append("UPDATE workflows SET environment_variables = replace(environment_variables, 'PORTAL_CODE_REPLACE', '" + request.getPortalCode() + "');");
				try (PreparedStatement updateStmt = pluginConn.prepareStatement(updateSql.toString())) {
					int updatedRows = updateStmt.executeUpdate();
					log.info("执行sql完成，sql:{}, updatedRows:{}", updateSql, updatedRows);
				} catch (SQLException e) {
					throw e;
				}
				// 提交事务
				pluginConn.commit();
				log.info("额外特殊处理完成 [dburl={}]", difyDbUrl);

			} catch (SQLException e) {
				// 回滚事务
				pluginConn.rollback();
				log.error("额外特殊处理失败，已回滚事务 [dburl={}]: {}", difyDbUrl, e.getMessage(), e);
				throw e;
			}
		} catch (SQLException e) {
			log.error("额外特殊处理失败, [dburl={}]: {}", difyDbUrl, e.getMessage(), e);
			throw new BusinessException("额外特殊处理失败: " + e.getMessage());
		}

		// 6. 更改upload_files目录名称为当前租户id
		String uploadFileDir = getUploadFileDir(request.getPortalCode());
		String commond = "mv 11111111-1111-1111-1111-111111111111 " + newTenantId;
		ProcessResultVo processResultVo = processService.execCommandWithResult(uploadFileDir, 10000L, commond);
		if (!processResultVo.getSuccess()) {
			log.error("更改upload_files目录名称失败[workDir={}, commond={}, result={}]", uploadFileDir, commond, JSON.toJSONString(processResultVo));
			throw new BusinessException("更改upload_files目录名称失败");
		}

		log.info("初始化Dify实例-成功[baseUrl={}]", baseUrl);
	}

	private void updateTenantIdByTableName(String dbUrl, List<String> tableNames, String newTenantId) {
		try (Connection pluginConn = DriverManager.getConnection(dbUrl, dbUser, dbPwd)) {
			// 开启事务
			pluginConn.setAutoCommit(false);

			try {
				// 遍历所有需要更新的表
				for (String item : tableNames) {
					String[] split = item.split("\\.");
					String tableName = split[0];
					String colName = split[1];
					String updateSql = "UPDATE " + tableName + " SET " + colName + " = CAST(? AS UUID)";

					try (PreparedStatement updateStmt = pluginConn.prepareStatement(updateSql)) {
						updateStmt.setString(1, newTenantId);
						int updatedRows = updateStmt.executeUpdate();
						log.info("数据库租户ID更新 - 表: {}, 更新记录数: {}", tableName, updatedRows);
					} catch (SQLException e) {
						log.error("更新表 {} 失败: {}", tableName, e.getMessage(), e);
						throw e;
					}
				}

				// 提交事务
				pluginConn.commit();
				log.info("数据库租户ID更新成功完成 [dburl={}, tenantId={}]", dbUrl, newTenantId);

			} catch (SQLException e) {
				// 回滚事务
				pluginConn.rollback();
				log.error("数据库租户ID更新失败，已回滚事务 [dburl={}]: {}", dbUrl, e.getMessage(), e);
				throw e;
			}
		} catch (SQLException e) {
			log.error("数据库连接失败，无法更新租户ID [dburl={}]: {}", dbUrl, e.getMessage(), e);
			throw new BusinessException("数据库租户ID更新失败: " + e.getMessage());
		}
	}

	/**
	 * 根据门户标识，删除Dify实例所有数据
	 *
	 * @param portalCode
	 * @throws BusinessException
	 */
	@Override
	public void deleteAllInfo(String portalCode) throws BusinessException {
		//知识库根路径
		//删除知识库目录
		if (Files.exists(Path.of(getAppStorageDir(portalCode)))) {
			String appStorageRootDir = getRootDir() + File.separator + "app_storage";
			ProcessResultVo result = processService.execCommandWithResult(appStorageRootDir, 10000L, "rm -rf " + portalCode);
			if (!result.getSuccess()) {
				log.error("删除Dify实例所有数据-失败[portalCode={}, result={}]", portalCode, JSON.toJSONString(result));
				throw new BusinessException(PortalErrorCode.PORTAL_INIT_FAILED_S, "删除知识库目录失败");
			}
		}
		//删除实例部署目录
		if (Files.exists(Path.of(getDeployDir(portalCode)))) {
			ProcessResultVo result = processService.execCommandWithResult(getRootDir(), 10000L, "rm -rf " + portalCode);
			if (!result.getSuccess()) {
				log.error("删除Dify实例所有数据-失败[portalCode={}, result={}]", portalCode, JSON.toJSONString(result));
				throw new BusinessException(PortalErrorCode.PORTAL_INIT_FAILED_S, "删除部署目录失败");
			}
		}
		//先释放连接，防止删除时数据库正在被访问
		jdbcTemplate.execute(String.format("SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = '%s';", getDatabaseName(portalCode)));
		jdbcTemplate.execute(String.format("SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = '%s';", getPluginDatabaseName(portalCode)));
		//删除业务数据库
		jdbcTemplate.execute(String.format("DROP DATABASE IF EXISTS \"%s\";", getDatabaseName(portalCode)));
		//删除插件数据库
		jdbcTemplate.execute(String.format("DROP DATABASE IF EXISTS \"%s\";", getPluginDatabaseName(portalCode)));
	}

	/**
	 * 根据门户标识，检查实例状态
	 *
	 * @param portalCode 门户标识
	 * @return 实例状态信息
	 */
	@Override
	public DifyInstanceStatusVo checkInstanceStatus(String portalCode) {
		// 检查该Dify实例的9个容器状态是否正常
		log.info("检查Dify实例状态-开始[portalCode={}]", portalCode);

		for (String container : getContainerNameList(portalCode)) {
			// 根据容器名称检查容器状态
			if (checkContainerStatus(container)) {
				continue;
			}
			// 如果有容器状态异常，则立即中断检查，并返回错误信息
			log.info("检查Dify实例状态-容器异常[portalCode={}, container={}]", portalCode, container);
			return new DifyInstanceStatusVo(false, String.format("容器 %s 状态异常", container));
		}
		log.info("检查Dify实例状态-结束[portalCode={}, status={}]", portalCode, true);
		return new DifyInstanceStatusVo(true, "所有容器状态正常");
	}

	/**
	 * 检查容器状态
	 * <p>
	 * 通过Docker API的UNIX套接字检查容器状态，等同于执行：
	 * curl --unix-socket /var/run/docker.sock http://localhost/containers/containerName/json
	 * </p>
	 *
	 * @param containerName 容器名称
	 * @return 容器是否运行正常
	 */
	private boolean checkContainerStatus(String containerName) {
		log.info("开始检查容器状态: {}", containerName);
		String socketPath = "/var/run/docker.sock";
		File socketFile = new File(socketPath);

		if (!socketFile.exists()) {
			log.error("Docker套接字文件不存在: {}", socketPath);
			return false;
		}

		try {
			// 创建UnixDomainSocketAddress（Java 16+特性）
			// 如果使用的Java版本低于16，需要使用其他方式如jnr-unixsocket库
			UnixDomainSocketAddress socketAddress = UnixDomainSocketAddress.of(socketPath);

			// 创建套接字通道
			try (SocketChannel channel = SocketChannel.open(socketAddress)) {
				// 构造HTTP GET请求
				String request = String.format(
						"GET /containers/%s/json HTTP/1.1\r\n" +
								"Host: localhost\r\n" +
								"Accept: application/json\r\n" +
								"Connection: close\r\n\r\n",
						containerName);

				// 发送请求
				ByteBuffer buffer = ByteBuffer.wrap(request.getBytes(StandardCharsets.UTF_8));
				while (buffer.hasRemaining()) {
					channel.write(buffer);
				}

				// 读取响应
				ByteBuffer responseBuffer = ByteBuffer.allocate(16384); // 16KB缓冲区
				StringBuilder response = new StringBuilder();

				while (channel.read(responseBuffer) > 0) {
					responseBuffer.flip();
					response.append(StandardCharsets.UTF_8.decode(responseBuffer));
					responseBuffer.clear();
				}

				// 解析响应
				String responseStr = response.toString();
//				log.debug("收到Docker API响应: {}", responseStr);

				// 分离HTTP头和响应体
				int bodyStart = responseStr.indexOf("\r\n\r\n") + 4;
				if (bodyStart > 4) {
					String body = responseStr.substring(bodyStart).trim();

					// 使用正则表达式匹配容器状态
					// 匹配模式: "State":{"Status":"running"     "Health":{"Status": "healthy"
					Matcher stateMatcher = Pattern.compile("\"State\":[{]\"Status\":\\s*\"([^\"]+)\"").matcher(body);
					Matcher healthMatcher = Pattern.compile("\"Health\":[{]\"Status\":\\s*\"([^\"]+)\"").matcher(body);

					if (stateMatcher.find() && healthMatcher.find()) {
						String stateStatus = stateMatcher.group(1);
						String healthStatus = healthMatcher.group(1);
						log.info("容器 {} 状态: {}, 健康状态: {}", containerName, stateStatus, healthStatus);

						// 只有状态为 "running" "healthy" 时才认为容器健康
						boolean isHealthy = "running".equals(stateStatus) && "healthy".equals(healthStatus);
						if (!isHealthy) {
							log.error("容器 {} 状态异常: {}, 健康状态: {}", containerName, stateStatus, healthStatus);
						}
						return isHealthy;
					} else {
						log.error("无法从响应中匹配到容器状态信息: {}", body);
					}
				} else {
					log.error("无法解析HTTP响应: {}", responseStr);
				}
			}
		} catch (IOException e) {
			log.error("访问Docker API失败: {}", e.getMessage(), e);
		} catch (Exception e) {
			log.error("检查容器状态时发生异常: {}", e.getMessage(), e);
		}

		return false;
	}

	/**
	 * 停止某个门户的Dify实例
	 *
	 * @param portalCode 门户标识
	 * @throws BusinessException
	 */
	@Override
	public void stopInstance(String portalCode) throws BusinessException {
		ProcessResultVo result = processService.execCommandWithResult(getDeployDir(portalCode), 20000L, "docker-compose stop");
		if (!result.getSuccess()) {
			log.error("停止Dify实例-失败[portalCode={}, result={}]", portalCode, JSON.toJSONString(result));
			throw new BusinessException(PortalErrorCode.PORTAL_INIT_FAILED_S, "执行停止门户指令失败，请重试");
		}
	}

	/**
	 * 启动某个门户的Dify实例
	 *
	 * @param portalCode 门户标识
	 * @throws BusinessException
	 */
	@Override
	public void startInstance(String portalCode) throws BusinessException {
		ProcessResultVo result = processService.execCommandWithResult(getDeployDir(portalCode), 10000L, "docker-compose start");
		if (!result.getSuccess()) {
			log.error("启动Dify实例-失败[portalCode={}, result={}]", portalCode, JSON.toJSONString(result));
			throw new BusinessException(PortalErrorCode.PORTAL_START_FAILED_S, "执行启动门户指令失败，请重试");
		}
	}

	/**
	 * 删除某个门户的Dify实例。
	 *
	 * @param portalCode 门户标识
	 * @throws BusinessException
	 */
	@Override
	public void removeInstance(String portalCode) throws BusinessException {
		if (Files.exists(Path.of(getDeployDir(portalCode)))) {
			ProcessResultVo result = processService.execCommandWithResult(getDeployDir(portalCode), 30000L, "docker-compose down");
			if (!result.getSuccess()) {
				log.error("删除Dify实例-失败[portalCode={}, result={}]", portalCode, JSON.toJSONString(result));
				throw new BusinessException(PortalErrorCode.PORTAL_DELETE_FAILED_S, "执行删除门户指令失败，请重试");
			}
		}
	}

	/**
	 * 发送POST请求
	 *
	 * @param url  请求URL
	 * @param body 请求体内容（JSON格式字符串）
	 * @return 响应的JSON对象
	 */
	private JSONObject requestPost(String url, String body, String token) {
		log.info("Executing POST request to URL: {}", url);
		log.info("Request body: {}", body);
		try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
			HttpPost request = new HttpPost(url);
			request.setHeader("Content-Type", "application/json");
			if (StringUtils.isNotBlank(token)) {
				request.setHeader("Authorization", "Bearer " + token);
			}

			// 设置请求体
			if (StringUtils.isNotBlank(body)) {
				StringEntity entity = new StringEntity(body, "UTF-8");
				request.setEntity(entity);
			}

			try (CloseableHttpResponse response = httpClient.execute(request)) {
				int statusCode = response.getStatusLine().getStatusCode();
				log.info("POST request returned status code: {}", statusCode);
				HttpEntity entity = response.getEntity();
				if (entity != null) {
					String responseString = EntityUtils.toString(entity);
					log.info("Response content: {}", responseString);
					return JSONObject.parseObject(responseString);
				}
			}
		} catch (IOException e) {
			log.error("Error occurred while executing POST request: {}", e.getMessage(), e);
		}
		return null;
	}


}
