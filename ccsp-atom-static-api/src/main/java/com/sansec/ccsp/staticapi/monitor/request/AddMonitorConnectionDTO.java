package com.sansec.ccsp.staticapi.monitor.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * @Description: 添加监控链接
 * @CreateTime: 2023-05-19
 * @Author: wang<PERSON>jie
 */
@Data
public class AddMonitorConnectionDTO {

    /**
     * 连接名称
     */
    @NotBlank(message = "连接名称不可为空")
    @Length(max = 50, message = "连接名称长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "连接名称只能包含中文、英文、数字、特殊字符 - _")
    private String connectionName;
    /**
     * 租户标识
     */
    private String tenantCode;
    /**
     * 监控传输方式
     */
    private String monitorTransmissionType;
    /**
     * ip
     */
    @NotBlank(message = "ip不可为空")
    @Pattern(regexp = CommonPattern.COMMON_IP, message = "ip格式错误")
    private String ip;
    /**
     * port
     */
    @Min(value = 0, message = "端口范围0-65535")
    @Max(value = 65535, message = "端口范围0-65535")
    @NotNull(message = "端口不可为空")
    private Integer port;

    private Long regionId;

    private String gatewayApiUrl;
    private Boolean proxyRoute;
}
