package com.sansec.ccsp.pt.business.app.group.request;

import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Size;

 /**
 * @Description: 应用业务和设备组/服务组关联表;
 * <AUTHOR> x<PERSON><PERSON><PERSON>aw<PERSON>
 * @Date: 2023-2-18
 */
@Data
public class AppBusiToGroupPageDTO extends SecPageDTO{
   /**
     * 应用名
     */
    @Size(max = 50, message = "应用名称长度限制50")
    private String name;
}