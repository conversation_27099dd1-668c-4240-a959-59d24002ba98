package com.sansec.ai.portal.listener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sansec.ai.common.base.entity.BaseEntity;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.common.base.utils.EncryptionUtil;
import com.sansec.ai.common.service.SysConfigService;
import com.sansec.ai.extra.dify.constant.DifyCommonConstant;
import com.sansec.ai.extra.dify.constant.DifyConfigEnum;
import com.sansec.ai.extra.dify.request.DifyInitRequest;
import com.sansec.ai.extra.dify.request.DifyInsInfo;
import com.sansec.ai.extra.dify.request.DifyInstanceProperties;
import com.sansec.ai.extra.dify.result.DifyInstanceStatusVo;
import com.sansec.ai.extra.dify.service.DifyDeployService;
import com.sansec.ai.portal.constant.PortalStatusEnum;
import com.sansec.ai.portal.entity.po.DifyInstanceInfo;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.service.DifyInstanceInfoService;
import com.sansec.ai.portal.service.PortalInfoService;
import com.sansec.ai.user.dto.GatewayAddDTO;
import com.sansec.ai.user.dto.PortalManagerAddDTO;
import com.sansec.ai.user.enums.PortalUserTypeEnum;
import com.sansec.ai.user.service.GatewayService;
import com.sansec.ai.user.service.PortalManagerService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.utils.SM3Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.Base64;
import java.util.Date;

/**
 * 创建门户事件
 *
 * <AUTHOR>
 * @since 2025/5/10 10:16
 */
@Slf4j
@Component
public class PortalCreateEventListener implements ApplicationListener<PortalCreateEvent> {

	/**
	 * 宿主机IP
	 */
	@Value("${ai.dify.instance_ip}")
	private String machineIp;
	/**
	 * 网关地址
	 */
	@Value("${ai.dify.url}")
	private String gatewayUrl;


	@Autowired
	private PortalInfoService portalInfoService;
	@Autowired
	private DifyInstanceInfoService difyInstanceInfoService;
	@Autowired
	private DifyDeployService difyDeployService;
	@Autowired
	private PortalManagerService portalManagerService;
	@Autowired
	private SysConfigService sysConfigService;
	@Autowired
	private GatewayService gatewayService;

	@Async
	@Override
	public void onApplicationEvent(PortalCreateEvent event) {
		log.info("异步部署Dify实例-开始[eventId={}]", event.getSource());
		//NOTE 1-创建实例
		DifyInstanceInfo instanceInfo = createInstance(event);
		log.info("已创建Dify实例[eventId={}, instance={}]", event.getSource(), JSON.toJSONString(instanceInfo));
		//NOTE 2-部署实例
		//NOTE 2.1-初始化专用数据库
		try {
			difyDeployService.initDatabase(event.portalInfo.getPortalCode());
		} catch (Exception e) {
			log.error(String.format("初始化专用配置异常[eventId=%s]", event.getSource()), e);
			updatePortalOfError(event, e instanceof BusinessException ? e.getLocalizedMessage() : "初始化专用数据库异常");
			return;
		}
		//NOTE 2.2-初始化专用配置
		try {
			DifyInstanceProperties properties = new DifyInstanceProperties();
			properties.setInstanceCode(instanceInfo.getPortalCode());
			properties.setGatewayUrl(gatewayUrl);
			properties.setSecretKey(instanceInfo.getSecretKey());
			properties.setServicePort(instanceInfo.getInstancePort());
			difyDeployService.initConfig(event.portalInfo.getPortalCode(), properties);
		} catch (Exception e) {
			log.error(String.format("初始化专用配置异常[eventId=%s]", event.getSource()), e);
			updatePortalOfError(event, e instanceof BusinessException ? e.getLocalizedMessage() : "初始化专用配置异常");
			return;
		}
		//NOTE 2.3-创建门户实例专用容器
		try {
			difyDeployService.createContainer(event.portalInfo.getPortalCode());
		} catch (Exception e) {
			log.error(String.format("创建门户实例专用容器异常[eventId=%s]", event.getSource()), e);
			updatePortalOfError(event, e instanceof BusinessException ? e.getLocalizedMessage() : "创建门户实例专用容器异常");
			// 异常时停止实例
			stopInstanceWhenError(event);
			return;
		}

		//NOTE 2.4-检查容器创建状态
		{
			boolean isHealthy = false;
			for (int i = 0; i < 3; i++) {
				try {
					Thread.sleep(20000L);
				} catch (InterruptedException e) {
					throw new RuntimeException(e);
				}
				DifyInstanceStatusVo statusVo = difyDeployService.checkInstanceStatus(event.portalInfo.getPortalCode());
				if (!statusVo.getHealth()) {
					log.error("当前门户状态检查异常，等待重试[portalCode={}, 检查次数={}]", event.portalInfo.getPortalCode(), i + 1);
					continue;
				}
				isHealthy = true;
				break;
			}
			if (!isHealthy) {
				log.error("检查门户创建状态失败[eventId={}]", event.getSource());
				updatePortalOfError(event, "检查门户创建状态失败");
				// 异常时停止实例
				stopInstanceWhenError(event);
				return;
			}
		}
		// 2.4.5-等待一段时间再进行下一步操作
		{
			try {
				Thread.sleep(20000L);
			} catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
		}

		//NOTE 2.5-初始化门户实例
		try {
			log.info("开始初始化门户实例[eventId={}]", event.getSource());
			DifyInsInfo difyInsInfo = new DifyInsInfo(instanceInfo.getInstanceIp(), instanceInfo.getInstancePort());
			DifyInitRequest request = new DifyInitRequest();
			request.setEmail(event.request.getUserAccount() + DifyCommonConstant.EMAIL_SUFFIX);
			request.setUsername(event.request.getUserAccount());
			request.setPassword(event.request.getUserPassword());
			request.setPortalCode(event.portalInfo.getPortalCode());
			difyDeployService.initInstance(difyInsInfo, request);
		} catch (Exception e) {
			log.error(String.format("初始化门户实例异常[eventId=%s]", event.getSource()), e);
			updatePortalOfError(event, e instanceof BusinessException ? e.getLocalizedMessage() : "初始化门户实例异常");
			// 异常时停止实例
			stopInstanceWhenError(event);
			return;
		}

		//NOTE 2.6-创建门户管理员
		{
			log.info(String.format("开始创建门户管理员[eventId=%s]", event.getSource()));
			PortalManagerAddDTO dto = new PortalManagerAddDTO();
			dto.setPortalId(event.portalInfo.getId());
			if (event.request.getUserId() == null) {
				dto.setAddNewUser(true);
			} else {
				dto.setAddNewUser(false);
				dto.setUserId(String.valueOf(event.request.getUserId()));
			}
			dto.setUserName(event.request.getUserAccount());
			//明文密码处理
			{
				String digestWithBase64 = SM3Util.digestWithHex(event.request.getUserPassword()).toLowerCase();
				dto.setUserPwd(digestWithBase64);
			}
			dto.setUserType(PortalUserTypeEnum.MANAGER.getType());
			dto.setSecToken(event.secToken);
			try {
				log.debug("新建门户管理员[pwd={}, dto={}]", event.request.getUserPassword(), JSON.toJSONString(dto));
				portalManagerService.add(dto);
			} catch (Exception e) {
				log.error(String.format("创建门户管理员失败[eventId=%s]", event.getSource()), e);
				updatePortalOfError(event, e instanceof BusinessException ? e.getLocalizedMessage() : "创建门户管理员失败");
				// 异常时停止实例
				stopInstanceWhenError(event);
				return;
			}
		}

		//NOTE 2.7-添加路由
		{
			GatewayAddDTO dto = new GatewayAddDTO();
			dto.setRouteId(String.valueOf(instanceInfo.getPortalId()));
			dto.setUri(String.format("http://%s:%s", instanceInfo.getInstanceIp(), instanceInfo.getInstancePort()));
			dto.setCookieValue(instanceInfo.getPortalCode());
			log.info("添加门户路由[routeId={}, uri={}, cookieValue={}]", dto.getRouteId(), dto.getUri(), dto.getCookieValue());
			try {
				gatewayService.add(dto);
			} catch (Exception e) {
				log.error(String.format("添加门户路由失败[eventId=%s]", event.getSource()), e);
				updatePortalOfError(event, e instanceof BusinessException ? e.getLocalizedMessage() : "添加门户路由失败");
				// 异常时停止实例
				stopInstanceWhenError(event);
				return;
			}
		}

		//NOTE 3-更新门户状态为已部署
		checkPortalOfSuccess(event.portalInfo);
		log.info("门户实例创建成功[eventId={}]", event.getSource());
	}

	/**
	 * 创建实例
	 */
	private DifyInstanceInfo createInstance(PortalCreateEvent event) {
		//NOTE 2-保存实例信息
		DifyInstanceInfo entity = new DifyInstanceInfo();
		// ID
		entity.setId(IdGenerator.ins().generator());
		// 门户ID
		entity.setPortalId(event.portalInfo.getId());
		// 门户编码/URL前缀/实例前缀
		entity.setPortalCode(event.request.getPortalCode());
		// 实例访问IP
		entity.setInstanceIp(machineIp);
		// 实例访问端口
		{
			// 先查询有哪些端口被使用过了
			DifyInstanceInfo difyInstanceInfo = difyInstanceInfoService.getOne(new LambdaQueryWrapper<DifyInstanceInfo>()
					.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
					.orderByDesc(DifyInstanceInfo::getInstancePort)
					.last("LIMIT 1"));
			if (difyInstanceInfo == null) {
				//获取Dify实例默认端口
				String port = sysConfigService.getConfigValue(DifyConfigEnum.DEFAULT_PORT);
				entity.setInstancePort(Integer.valueOf(port));
			} else {
				entity.setInstancePort(difyInstanceInfo.getInstancePort() + 1);
			}
		}
		// 实例初始化秘钥
		{
			// 生成 42 字节的随机数据（openssl rand -base64 42 生成 42 字节，Base64 编码后变长）
			byte[] randomBytes = new byte[42];
			SecureRandom secureRandom = new SecureRandom();
			secureRandom.nextBytes(randomBytes);
			// Base64 编码（无填充，类似于 openssl 的 -base64）
			String base64Key = Base64.getEncoder().withoutPadding().encodeToString(randomBytes);
			entity.setSecretKey(base64Key);
		}
		// 实例管理员账号
		entity.setUserAccount(event.request.getUserAccount());
		// 实例管理员密码，加密存储
		{
			entity.setUserPassword(EncryptionUtil.encode(event.request.getUserPassword()));
		}
		difyInstanceInfoService.save(entity);
		return entity;
	}

	/**
	 * 初始化失败，更新门户状态
	 *
	 * @param event
	 * @param message
	 */
	private void updatePortalOfError(PortalCreateEvent event, String message) {
		portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
				.eq(PortalInfo::getId, event.portalInfo.getId())
				.set(PortalInfo::getStatus, PortalStatusEnum.INITIAL_ERROR.code)
				.set(PortalInfo::getStatusDesc, message)
				.set(BaseEntity::getUpdateTime, new Date()));
	}

	/**
	 * 初始化成功，检查门户状态
	 *
	 * @param portalInfo
	 */
	private void checkPortalOfSuccess(PortalInfo portalInfo) {
		boolean isHealthy = false;
		for (int i = 0; i < 3; i++) {
			try {
				Thread.sleep(20000L);
			} catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
			DifyInstanceStatusVo statusVo = difyDeployService.checkInstanceStatus(portalInfo.getPortalCode());
			if (!statusVo.getHealth()) {
				log.error("当前门户状态检查异常，等待重试[portalCode={}]", portalInfo.getPortalCode());
				continue;
			}
			isHealthy = true;
			break;
		}
		if (isHealthy) {
			portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
					.eq(PortalInfo::getId, portalInfo.getId())
					.set(PortalInfo::getStatus, PortalStatusEnum.RUNNING.code)
					.set(PortalInfo::getStatusDesc, " ")
					.set(BaseEntity::getUpdateTime, new Date()));
		} else {
			portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
					.eq(PortalInfo::getId, portalInfo.getId())
					.set(PortalInfo::getStatus, PortalStatusEnum.ABNORMAL.code)
					.set(PortalInfo::getStatusDesc, "当前门户状态检查异常")
					.set(BaseEntity::getUpdateTime, new Date()));
		}
	}


	/**
	 * 当初始化失败时，关闭容器，防止资源浪费
	 */
	private void stopInstanceWhenError(PortalCreateEvent event) {
		try {
			difyDeployService.stopInstance(event.portalInfo.getPortalCode());
		} catch (Exception e) {
			log.error(String.format("停止实例失败[eventId=%s]", event.getSource()), e);
		}
	}

}
