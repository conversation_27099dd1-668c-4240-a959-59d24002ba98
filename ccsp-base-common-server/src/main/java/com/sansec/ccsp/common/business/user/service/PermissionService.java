package com.sansec.ccsp.common.business.user.service;

import com.sansec.ccsp.common.user.request.HasAnyPermissionsDTO;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.PostConstruct;

public interface PermissionService {

	@PostConstruct
	void initLocalCache();

	/**
	 * 鉴权信息校验
	 * @param hasAnyPermissionsDTO 鉴权输入信息
	 * @return 是否具有权限
	 */
	Boolean hasAnyPermissions(@RequestBody HasAnyPermissionsDTO hasAnyPermissionsDTO);
}
