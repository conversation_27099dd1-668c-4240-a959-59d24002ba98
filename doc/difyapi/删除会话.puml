@startuml
title 删除会话

participant "前端" as web
participant "统一门户\n后端服务" as service
participant "Dify api" as difyapi
participant "Dify实例" as dify

autonumber "<b>1-0</b>"

group 删除会话
  web -> service:调用删除会话接口
    note left
      user_id：当前登录用户id，必须
      app_id: 应用id，必须
      conversation_id: 会话id，必须
    end note
  service -> service: 根据用户获取所属门户
  service -> difyapi: 调用删除会话接口
    note left
      dify实例真实端口
      app_id
      conversation_id
    end note
  group 单例模式获取dify实例token
  difyapi -> difyapi: 查询内存中是否包含该门户的token
  difyapi -> difyapi: token是否快过期
    group 更新token
      difyapi -> dify: 登录管理员账户获取token
      dify -> difyapi: 新的token
      difyapi -> difyapi: 更新内存中的token
    end
  end
  group 获取应用的api密钥
    difyapi -> dify: 根据应用id获取应用的api密钥
    dify -> difyapi: api密钥列表
    alt api密钥列表空
      difyapi -> dify: 创建api密钥
      dify -> difyapi: api密钥内容
    end
  end

  difyapi -> dify: 调用删除会话接口
  dify -> difyapi: 执行结果
  difyapi -> service: 执行结果
  service -> web: 执行结果
@enduml