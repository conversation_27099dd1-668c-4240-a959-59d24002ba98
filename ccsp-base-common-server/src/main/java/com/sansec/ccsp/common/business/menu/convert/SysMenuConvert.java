package com.sansec.ccsp.common.business.menu.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.common.business.menu.entity.SysMenuPO;
import com.sansec.ccsp.common.menu.request.AddSysMenuDTO;
import com.sansec.ccsp.common.menu.request.SysMenuDTO;
import com.sansec.ccsp.common.menu.response.SysMenuVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

 /**
 * @Description: 菜单表;(SYS_MENU)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Mapper(componentModel = "spring")
public interface SysMenuConvert{
    /**
     * dtoToPo
     * @param sysMenuDTO
     * @return
     */
    @Mappings({})
    SysMenuPO dtoToPo(SysMenuDTO sysMenuDTO);

     /**
      * addDtoToPo
      * @param addSysMenuDTO
      * @return
      */
    @Mappings({})
    SysMenuPO addDtoToPo(AddSysMenuDTO addSysMenuDTO);
    /**
     * poToDto
     * @param sysMenuPO
     * @return
     */
    SysMenuDTO poToDto(SysMenuPO sysMenuPO);

    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<SysMenuDTO> poToDto(List<SysMenuPO> list);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<SysMenuVO> pagePOToSecPageVOPage(IPage<SysMenuPO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<SysMenuVO> convert(List<SysMenuPO> list);

    @Mappings({})
    SysMenuVO convertVo(SysMenuPO request);
}
