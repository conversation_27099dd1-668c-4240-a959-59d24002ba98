package com.sansec.ccsp.extra.business.product.request;

import com.sansec.ccsp.extra.business.constant.CommonPattern;
import lombok.Data;

import javax.validation.constraints.*;

@Data
public class TenantToProductOpenDTO {
    /**
     * 租户id
     */
    @NotNull(message = "租户id不可为空")
    private Long tenantId;

    /**
     * 产品id
     */
    @NotNull(message = "产品id不可为空")
    private Long productId;

    /**
     * 服务组id
     */
    private Long serviceGroupId;

    /**
     * 开通数量
     */
    @NotNull(message = "开通数量不可为空")
    @Min(value = 1, message = "开通数量不可小于1")
    @Max(value = 100000, message = "开通数量不可大于100000")
    private Integer hasNum;

    /**
     * 到期时间
     */
    @Pattern(regexp = CommonPattern.COMMON_DATE, message = "到期时间格式必须为yyyy-MM-dd hh:mm:ss的格式")
    private String expirationTime;

    /**
     * 备注
     */
    @Size(max = 100, message = "备注长度最大100")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "备注请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符 ")
    private String remark;

    /**
     * 是否支持软算法
     */
    private Integer softAlgFlag;
}
