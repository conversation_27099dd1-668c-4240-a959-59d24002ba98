package com.sansec.ccsp.pt.business.servicemgt.gateway.convert;

import com.sansec.ccsp.pt.business.servicemgt.gateway.entity.ServiceGatewayPO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.request.ServiceGatewayEditDTO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import com.sansec.ccsp.pt.business.servicemgt.gateway.request.ServiceGatewayDTO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.response.ServiceGatewayVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.common.param.response.SecPageVO;

import java.util.ArrayList;
import java.util.List;
import com.alibaba.fastjson.JSON;
import com.sansec.ccsp.pt.business.servicemgt.gateway.request.IpPorts;
import org.springframework.util.CollectionUtils;

/**
 * @Description: API网关表;(SERVICE_GATEWAY)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Mapper(componentModel = "spring")
public interface ServiceGatewayConvert{
    /**
     * dtoToPo
     * @param serviceGatewayDTO
     * @return
     */
    @Mapping(source = "proxyMgtNodes", target = "proxyMgtNodes" , qualifiedByName = "listProxyMgtNodesToString")
    ServiceGatewayPO dtoToPo(ServiceGatewayDTO serviceGatewayDTO);
    
    /**
     * poToDto
     * @param serviceGatewayPO
     * @return
     */
    @Mapping(source = "proxyMgtNodes", target = "proxyMgtNodes" , qualifiedByName = "stringProxyMgtNodesToList")
    ServiceGatewayDTO poToDto(ServiceGatewayPO serviceGatewayPO);
    
    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<ServiceGatewayDTO> poToDto(List<ServiceGatewayPO> list);
     
    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<ServiceGatewayVO> pagePOToSecPageVOPage(IPage<ServiceGatewayPO> iPage);
    
    @InheritConfiguration(name = "convertVo")
    List<ServiceGatewayVO> convert(List<ServiceGatewayPO> list);

    @Mapping(source = "proxyMgtNodes", target = "proxyMgtNodes" , qualifiedByName = "stringProxyMgtNodesToList")
    ServiceGatewayVO convertVo(ServiceGatewayPO request);

     ServiceGatewayPO dtoEditToPo(ServiceGatewayEditDTO serviceGatewayEditDTO);

     @Named("stringProxyMgtNodesToList")
     default List<IpPorts> stringProxyMgtNodesToList(String proxyMgtNodes) {
         if( StringUtils.isEmpty(proxyMgtNodes) ){
             return new ArrayList<>();
         }
         return JSON.parseArray(proxyMgtNodes,IpPorts.class);
     }

     @Named("listProxyMgtNodesToString")
     default String listProxyMgtNodesToString(List<IpPorts> proxyMgtNodes) {
         if( CollectionUtils.isEmpty(proxyMgtNodes) ){
             return null;
         }
         return JSON.toJSONString(proxyMgtNodes);
     }
}