package com.sansec.ccsp.device.devicemasterkey.request;

import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class DeviceMasterKeyPageDTO extends SecPageDTO {
    private Long tenantId;

    /**
     * 恢复记录类型 1-主密钥，2-内部密钥
     */
    @NotNull(message = "密钥恢复记录类型不可为空")
    private Integer coverType;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 恢复状态0-恢复成功，1-恢复中，2-恢复失败
     */
    private Integer coverStatus;

    /**
     * 恢复时间
     */
    private String coverTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * HMAC
     */
    private String hmac;
}
