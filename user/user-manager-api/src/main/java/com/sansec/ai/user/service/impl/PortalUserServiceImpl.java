package com.sansec.ai.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.benmanes.caffeine.cache.Cache;
import com.sansec.ai.common.base.entity.ImageCode;
import com.sansec.ai.common.base.enums.TokenTypeEnum;
import com.sansec.ai.common.base.utils.TokenUtil;
import com.sansec.ai.portal.entity.vo.DifyInstanceInfoVo;
import com.sansec.ai.portal.service.IPortalService;
import com.sansec.ai.user.dto.CaptchaGetDTO;
import com.sansec.ai.user.dto.PortalUserLoginDTO;
import com.sansec.ai.user.dto.PortalUserModifyDTO;
import com.sansec.ai.user.entities.PortalUser;
import com.sansec.ai.user.entities.PortalUserRelation;
import com.sansec.ai.user.enums.EnableEnum;
import com.sansec.ai.user.enums.PortalLoginResEnum;
import com.sansec.ai.user.enums.PortalUserTypeEnum;
import com.sansec.ai.user.enums.UserTypeEnum;
import com.sansec.ai.user.mapper.PortalUserMapper;
import com.sansec.ai.user.mapper.PortalUserRelationMapper;
import com.sansec.ai.user.service.PortalUserLockService;
import com.sansec.ai.user.service.PortalUserService;
import com.sansec.ai.user.vo.CaptchaGetVO;
import com.sansec.ai.user.vo.PortalUserVO;
import com.sansec.ai.user.vo.UserStatusVO;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.plat.base.utils.Pbkdf2Tools;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseCookie;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;

import static com.sansec.ai.user.constants.UserModuleErrorCodeConstants.*;

/**
 * @Author: WanJun
 * @Date: 2025/5/17 15:15
 * @Description:
 */
@Service
@Slf4j
public class PortalUserServiceImpl extends ServiceImpl<PortalUserMapper, PortalUser> implements PortalUserService {

    @Resource
    private PortalUserRelationMapper portalUserRelationMapper;

    @Resource
    Cache<String, ImageCode> portalVerifyCodeCache;

    @Resource
    private PortalImageCodeGenerator portalImageCodeGenerator;


    @Resource
    private Cache<String, Object> portalUserCache;

    @Resource
    private PortalUserLockService portalUserLockService;

    @Resource
    private PortalUserMapper portalUserMapper;
	@Resource
	private IPortalService portalService;

    @Value("${ai.portal.cookie-name}")
    private String cookieName;

    /**
     * 获取门户用户信息
     *
     * @param userId 用户id
     * @return 用户信息
     */
    @Override
    public PortalUserVO get(Long userId) {
        PortalUser portalUser = this.getById(userId);
        if (portalUser == null) {
            throw new BusinessException(USER_NOT_EXISTS);
        }

        PortalUserVO portalUserVO = new PortalUserVO();
        portalUserVO.setId(portalUser.getId());
        portalUserVO.setUserName(portalUser.getUserName());
        portalUserVO.setLoginDate(portalUser.getLoginDate());
        PortalUserRelation portalUserRelation = portalUserRelationMapper.selectOne(Wrappers.lambdaQuery(PortalUserRelation.class).eq(PortalUserRelation::getUserId, userId));
        if (portalUserRelation != null) {
            portalUserVO.setPortalId(portalUserRelation.getPortalId());
        }
        return portalUserVO;
    }

    /**
     * 根据门户id解绑所有门户用户
     *
     * @param portalId 门户id
     */
    @Override
    public void unbindByPortalId(Long portalId) {
        portalUserRelationMapper.delete(Wrappers.lambdaQuery(PortalUserRelation.class)
                .eq(PortalUserRelation::getPortalId, portalId)
                .eq(PortalUserRelation::getUserType, PortalUserTypeEnum.NORMAL.getType()));
    }

    /**
     * 生成图片验证码
     *
     * @return 返回图片验证码id
     */
    @Override
    public SecRestResponse<CaptchaGetVO> generateCaptcha() {
        String key = IdUtil.getSnowflakeNextIdStr();
        ImageCode imageCode = portalImageCodeGenerator.generate();
        portalVerifyCodeCache.put(key, imageCode);
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ImageIO.write(imageCode.getImage(), "png", baos); // 可以改为需要的图片格式，如"jpg"
            byte[] imageBytes = baos.toByteArray();
            return SecRestResponse.success(CaptchaGetVO.builder().captchaId(key)
                    .captchaBase64(Base64.encodeBase64String(imageBytes)).build());
        } catch (Exception e) {
            throw new BusinessException(USER_CAPTCHA_CODE_WRITE_TO_RESPONSE_ERROR);
        }
    }

    /**
     * 根据验证码id获取验证码
     *
     * @param captchaGetDTO 验证码id
     * @return 图片验证码写入响应
     */
    @Override
    public SecRestResponse<String> getCaptcha(CaptchaGetDTO captchaGetDTO, HttpServletResponse response) {
        // 1.参数校验
        if (captchaGetDTO == null || StringUtils.isEmpty(captchaGetDTO.getCaptchaId())) {
            throw new BusinessException(USER_CAPTCHA_ID_NOT_BE_BLANK);
        }
        // 2.从缓存中获取验证码
        ImageCode imageCode = portalVerifyCodeCache.getIfPresent(captchaGetDTO.getCaptchaId());
        if (imageCode == null) {
            throw new BusinessException(USER_CAPTCHA_CODE_EXPIRED);
        }

        // 3.验证码写入响应，并返回
        response.setContentType(MediaType.IMAGE_PNG_VALUE);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            ImageIO.write(imageCode.getImage(), "png", outputStream);
        } catch (IOException e) {
            log.error("verifyCodeCache error", e);
            throw new BusinessException(USER_CAPTCHA_CODE_WRITE_TO_RESPONSE_ERROR);
        }

        return SecRestResponse.success();
    }

    @Override
    public SecRestResponse<String> login(PortalUserLoginDTO dto,HttpServletResponse response) {
        // 1、参数校验
        if(portalUserLockService.isImageCodeDisplay(dto.getUserName()) || StringUtils.isNotBlank(dto.getCaptchaId())){
            ImageCode imageCode1 = portalVerifyCodeCache.getIfPresent(dto.getCaptchaId());
            if (imageCode1 == null) {
                throw new BusinessException(USER_CAPTCHA_CODE_EXPIRED);
            }

            if (!imageCode1.getCode().equalsIgnoreCase(dto.getCaptcha())) {
                portalVerifyCodeCache.invalidate(dto.getCaptchaId());
                throw new BusinessException(USER_CAPTCHA_CODE_VERIFY_ERROR);
            }
            portalVerifyCodeCache.invalidate(dto.getCaptchaId());
        }


        // 2、检查用户是否存在
        PortalUser portalUser = portalUserMapper.selectOne(Wrappers.lambdaQuery(PortalUser.class).eq(PortalUser::getUserName, dto.getUserName()));
        if (portalUser == null) {
            throw new BusinessException(USER_NOT_EXISTS);
        }
        portalUserLockService.getFlagLock(portalUser.getUserName());
        // 3、用户名和密码是否正确
        boolean authCodeCorrect = Pbkdf2Tools.validateAuch(dto.getUserPwd(), portalUser.getUserPwd());
        if (!authCodeCorrect) {
            portalUserLockService.lockUser(portalUser.getUserName());
            throw new BusinessException(USER_AUTH_CODE_NOT_CORRECT_ERROR);
        }

        List<PortalUserRelation> userRelations = portalUserRelationMapper.selectList(Wrappers.lambdaQuery(PortalUserRelation.class).eq(PortalUserRelation::getUserId, portalUser.getId()));
        if(CollUtil.isEmpty(userRelations)){
            throw new BusinessException(USER_NOT_BIND_PORTAL);
        }
	    DifyInstanceInfoVo portalInfo = portalService.getDifyInstanceInfoByPortalId(userRelations.get(0).getPortalId());
	    if(portalInfo == null){
		    throw new BusinessException(USER_NOT_BIND_PORTAL);
	    }

        // 4、生成token并放入缓存
        String token = TokenUtil.createToken(portalUser.getId(), String.valueOf(UserTypeEnum.PORTAL_USER.getCode()), portalInfo.getPortalCode());
        portalUserCache.put(token, portalUser.getId());
        portalUserLockService.release(portalUser.getUserName());
        this.update(Wrappers.lambdaUpdate(PortalUser.class).eq(PortalUser::getId, portalUser.getId())
                .set(PortalUser::getLoginDate, new Date()));
        ResponseCookie responseCookie = ResponseCookie.from(cookieName, portalInfo.getPortalCode())
                .secure(true)
                .path("/")
                .maxAge(-1) // Session cookie
                .build();
        response.addHeader("Set-Cookie", responseCookie.toString());
        // 5、返回信息
        return SecRestResponse.success(token);
    }

    @Override
    public SecRestResponse<String> logout() {
        String token = TokenUtil.getTokenFromHeader(TokenTypeEnum.AI_PORTAL_TOKEN);
        if (StringUtils.isNotBlank(token)) {
            portalUserCache.invalidate(token);
        }
        return SecRestResponse.success();
    }

    @Override
    public PortalLoginResEnum checkToken(String token, String requestUri) {
        //校验token
        log.info("requestUti:{},token:{}", requestUri, token);
        if (StringUtils.isBlank(token)) {
            log.info("requestUti:{},token is null", requestUri);
            return PortalLoginResEnum.TOKENERROR;
        }
        String userId = String.valueOf(TokenUtil.getUserId(TokenTypeEnum.AI_PORTAL_TOKEN));
        if (StringUtils.isBlank(userId)) {
            log.info("token parsing error");
            return PortalLoginResEnum.TOKENERROR;
        }

        //检查token是否失效
        String userIdInCache = (String) portalUserCache.getIfPresent(token);
        if (StringUtils.isBlank(userIdInCache)) {
            log.info("token expiration");
            return PortalLoginResEnum.TOKENERROR;
        }

        //检查是否为用户伪造的token
        if (!userId.equals(userIdInCache)) {
            log.info("token invalid");
            return PortalLoginResEnum.TOKENERROR;
        }

        //重新更新内存，可以刷新用户token的过期时间
        portalUserCache.put(token, userIdInCache);
        return PortalLoginResEnum.SUCCESS;
    }

    @Override
    public boolean checkUserNameExist(String userName) {
        return this.exists(Wrappers.lambdaQuery(PortalUser.class).eq(PortalUser::getUserName, userName));
    }

    @Override
    public SecRestResponse<UserStatusVO> getUserStatus(String userName) {
        UserStatusVO userStatusVO = new UserStatusVO();
        userStatusVO.setCodeDisplay(EnableEnum.DISABLE.getCode());
        if(StringUtils.isNotBlank(userName)){
            boolean imageCodeDisplay = portalUserLockService.isImageCodeDisplay(userName);
            if(imageCodeDisplay){
                userStatusVO.setCodeDisplay(EnableEnum.ENABLE.getCode());
            }
        }
        return SecRestResponse.success(userStatusVO);
    }

    @Override
    public void modify(PortalUserModifyDTO dto) {
        // 1、参数校验

        // 2、查询用户是否存在
        PortalUser portalUser = this.getById(dto.getUserId());
        if (portalUser == null) {
            throw new BusinessException(USER_NOT_EXISTS);
        }
        if(StringUtils.isNotBlank(dto.getOldUserPwd())){
            boolean authCodeCorrect = Pbkdf2Tools.validateAuch(dto.getOldUserPwd(), portalUser.getUserPwd());
            if (!authCodeCorrect) {
                throw new BusinessException(OLD_USER_AUTH_CODE_NOT_CORRECT_ERROR);
            }
            boolean newPwdSameWithOld = Pbkdf2Tools.validateAuch(dto.getUserPwd(), portalUser.getUserPwd());
            if(newPwdSameWithOld){
                throw new BusinessException(NEW_USER_AUTH_CODE_SAME_WITH_OLD_ERROR);
            }
        }

        // 3、修改用户密码
        String userPwdEnc = Pbkdf2Tools.generateEncryptedStr(dto.getUserPwd());
        portalUser.setUserPwd(userPwdEnc);

        this.update(Wrappers.lambdaUpdate(PortalUser.class).eq(PortalUser::getId, dto.getUserId())
                .set(PortalUser::getUserPwd, userPwdEnc));

    }
}