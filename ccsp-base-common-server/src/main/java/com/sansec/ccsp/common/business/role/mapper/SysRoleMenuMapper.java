package com.sansec.ccsp.common.business.role.mapper;

import com.sansec.ccsp.common.business.role.entity.SysRoleMenuPO;
import com.sansec.db.mapper.SansecBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 角色菜单对应关系表;(SYS_ROLE_MENU)表数据库访问层
 * <AUTHOR> xia<PERSON>jiawei
 * @Date: 2023-2-18
 */
@Mapper
public interface SysRoleMenuMapper extends SansecBaseMapper<SysRoleMenuPO> {


    /**
     * 批量新增角色菜单信息
     *
     * @param list 角色菜单列表
     * @return 结果
     */
    int batchRoleMenu(List<SysRoleMenuPO> list);


    /**
     * 通过角色查询所有注解
     * @param roleId 角色id
     * @return 权限列表
     */
    List<String> getPermsByRoleId(Long roleId);

}
