package com.sansec.ccsp.device.devicetype.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 设备类型;
 * @Date: 2023-2-18
 */
@Data
public class DeviceTypePageDTO extends SecPageDTO {

    @Size(max = 50, message = "设备组名称长度限制50")
    private String name;

    @Size(max = 50, message = "厂商名称长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_NAME,
            message = "厂商名称只能包含中文、英文、数字或特殊字符 -_")
    private String vendorName;

    @Size(max = 50, message = "设备类型名称长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_NAME,
            message = "设备类型名称只能包含中文、英文、数字或特殊字符 -_")
    private String deviceTypeName;


}