package com.sansec.ccsp.pt.business.region.convert;

import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import com.sansec.ccsp.pt.business.region.request.RegionDTO;
import com.sansec.ccsp.pt.business.region.request.RegionEditDTO;
import com.sansec.ccsp.pt.business.region.response.RegionVO;
import com.sansec.ccsp.pt.business.region.entity.RegionPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.common.param.response.SecPageVO;
import java.util.List;

/**
 * @description : 区域表(1030);(REGION)实体类转换接口
 * <AUTHOR> xiaojiaw<PERSON>
 * @date : 2023-9-18
 */
@Mapper(componentModel = "spring")
public interface RegionConvert{
    /**
     * dtoToPo
     * @param regionDTO
     * @return
     */
    @Mappings({})
    RegionPO dtoToPo(RegionDTO regionDTO);

    @Mappings({})
    RegionPO dtoToPoEdit(RegionEditDTO regionEditDTO);

    /**
     * poToDto
     * @param regionPO
     * @return
     */
    RegionDTO poToDto(RegionPO regionPO);

    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<RegionDTO> poToDto(List<RegionPO> list);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<RegionVO> pagePOToSecPageVOPage(IPage<RegionPO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<RegionVO> convert(List<RegionPO> list);

    @Mappings({})
    RegionVO convertVo(RegionPO request);

}