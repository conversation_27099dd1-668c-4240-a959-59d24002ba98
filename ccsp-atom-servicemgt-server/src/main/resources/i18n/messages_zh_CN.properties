0=操作成功！
## 通用和字典类错误 0000-0050
0000FFFF=请求错误
0A100001=token信息不存在
0A110002=token校验失败或者已过期
0A100026=数据库操作错误
0A100027=参数错误！
0A100028=参数%s错误！
0A100029=业务类型接口调用失败
0A100030=业务类型对象不存在
0A100031=业务类型不可为空
0A100051=服务状态更新失败
0A100052=服务ip端口重复
0A100101=服务类型名称重复
0A100102=服务类型无对应的业务类型
0A100103=服务选择设备的方式 配置1不能改成配置2或配置3
0A100104=服务选择设备的方式 配置2和配置3不能改成配置1
0A100151=服务组名称重复
0A100152=不允许创建该类型服务组
0A100153=无权操作
0A100154=服务组名称不可为空
0A100155=服务组不存在
0A100156=类型为1的服务组不存在
0A100157=租户下资源服务组已存在
0A100158=租户信息错误
0A100159=服务信息完整性操作失败
0A100160=服务组标识重复
0A100171=服务组对象不存在，不能绑定服务
0A100172=服务对象不存在，不能绑定服务
0A100173=服务组所属租户和当前租户ID必须相同
0A100174=服务对象所属服务组对象必须为该租户下的服务组类型为1（资源组）的服务组
0A100175=服务已被其他服务组绑定
0A100176=需要解绑的服务不在该服务组下
0A100177=服务组ID不可为null
0A100178=服务组不存在
0A100179=未传入租户的情况下，服务组对象不存在
0A100180=未传入租户的情况下，服务组ID不可为空
0A100181=服务组下未绑定服务
0A100182=服务组不存在或无服务
0A100183=服务已被当前服务组绑定
0A100184=服务信息不存在
0A100185=默认服务组不存在
0A100186=服务组未关联数据库
0A100187=单业务类型服务组的业务类型异常
0A100188=服务组无业务类型异常
0A100189=服务组无绑定密钥管理服务组信息
0A100190=删除服务组绑定密钥管理服务组信息失败
0A100191=根据pki和svs的服务组标识查询远程kms的服务组标识不存在
0A100300=数据库实例名称不存在
0A100301=数据库名称已经存在
0A100302=数据库ip已经存在
0A100303=数据库实例已经存在
0A100304=数据库存在实例/模式不可删除
0A100305=数据库实例/模式关联服务组不可删除
0A100306=数据库实例名称已经存在
0A100307=数据库信息完整性操作失败
0A100308=数据库最小单元完整性操作失败
0A100309=数据库信息不存在
0A100310=数据库ID不可为空
0A100311=新增数据为空
0A100312=数据库类型不可为空
0A100313=数据库账号密码长度超长,限制1-50
0A100314=数据库ip和端口的个数，限制不超过5
0A100315=映射数据库ip已经存在
0A100316=映射数据库实例已经存在
0A100317=数据库实例不存在
0A100318=当前数据库正在备份或恢复中，无法操作
0A100319=数据库还未成功备份，无法进行还原操作




0A100342=新数据库密码不存在
0A100343=新数据库修改密码参数不存在
0A100344=原数据库密码不存在
0A100345=加密公钥不存在
0A100346=原数据库密码输入不正确
0A100347=数据库ip端口数据错误
0A100348=服务类型不存在



0A100401=创建数据库实例接口异常:%s
0A100402=创建数据库模式接口异常:%s
0A100403=执行数据库脚本接口异常:%s
0A100404=检测数据库实例状态接口异常:%s
0A100405=删除数据库实例接口异常:%s
0A100406=删除数据库模式接口异常:%s
0A100407=检测数据库脚本状态接口异常:%s
0A100408=检测数据库状态接口异常:%s
0A100409=数据库备份恢复接口异常:%s
0A100410=获取数据库备份恢复状态接口异常:%s
0A100411=当前区域下不存在可用的管控服务

