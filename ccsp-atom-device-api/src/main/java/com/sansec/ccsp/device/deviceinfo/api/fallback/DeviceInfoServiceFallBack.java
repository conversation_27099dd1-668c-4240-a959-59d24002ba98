package com.sansec.ccsp.device.deviceinfo.api.fallback;

import com.sansec.ccsp.device.deviceinfo.api.DeviceInfoServiceApi;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdsDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoPageDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoStatusDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.FreeDeviceByTenantDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.DeviceInfoByRegionIdDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.DeviceInfoListDTO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoInGroupVO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoVO;
import com.sansec.ccsp.device.deviceinfo.response.DevicePushDataInfo;
import com.sansec.ccsp.device.deviceinfo.response.DeviceStatusVO;
import com.sansec.ccsp.device.deviceinfo.response.vsmhost.DeviceStatusChartVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @author: zhandaojian
 * @date: 2023/2/21 16:37
 * @Description: 设备管理-物理机管理-Feign熔断
 */
@Slf4j
@Component
public class DeviceInfoServiceFallBack implements FallbackFactory<DeviceInfoServiceApi> {
    @Override
    public DeviceInfoServiceApi create(Throwable cause) {
        return new DeviceInfoServiceApi() {
            @Override
            public SecRestResponse<SecPageVO<DeviceInfoVO>> find(DeviceInfoPageDTO deviceInfoPageDTO) {
                log.error("获取设备列表异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<List<DeviceInfoVO>> list(DeviceInfoListDTO deviceInfoListDTO) {
                log.error("获取设备列表异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<SecPageVO<DeviceInfoVO>> findOnlySelf(DeviceInfoPageDTO deviceInfoPageDTO) {
                log.error("查询设别信息列表异常");
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<List<DeviceInfoInGroupVO>> getDeviceInfoByFree(FreeDeviceByTenantDTO freeDeviceByTenantDTO) {
                log.error("获取空闲设备列表异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<Object> delete(DeviceIdDTO deviceIdDTO) {
                log.error("删除设备信息异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<DeviceInfoVO> info(DeviceIdDTO deviceIdDTO) {
                log.error("获取物理机基本信息异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<DeviceStatusChartVO> getDeviceNumOfSystemOperations() {
                log.error("获取平台设备数量异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<DeviceStatusChartVO> getDeviceNumOfTenantOperations(Long tenantId) {
                log.error("获取租户设备数量异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<Map<Long, Integer>> getDeviceNumOfTenantList(List<Long> tenantIdList) {
                log.error("获取多个租户设备数量异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            /**
             * @param deviceGroupId
             * @return
             * @Description: 获取租户设备总数
             */
            @Override
            public SecRestResponse<DeviceStatusChartVO> getDeviceNumOfDeviceGroupOperations(Long deviceGroupId) {
                log.error("获取设备组设备数量异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            /**
             * @param deviceGroupIdList
             * @return
             * @Description: 获取多个租户设备总数
             */
            @Override
            public SecRestResponse<Map<Long, Integer>> getDeviceNumOfDeviceGroupList(List<Long> deviceGroupIdList) {
                log.error("获取设备组设备数量异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<DeviceStatusChartVO> getDeviceStatusOfSystemOperations() {
                log.error("获取平台设备状态异常：", cause);
                return SecRestResponse.fallback(cause);
            }

//            @Override
//            public SecRestResponse<DeviceStatusChartVO> getDeviceStatusOfTenantOperations(Long tenantId) {
//                log.error("获取租户设备状态异常：", cause);
//                return SecRestResponse.fallback(cause);
//            }

            @Override
            public SecRestResponse<DeviceStatusChartVO> getDeviceStatusOfDeviceGroup(List<Long> deviceGroupIdList) {
                log.error("根据设备组获取租户设备状态异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<List<DeviceStatusVO>> getDeviceStatusList(DeviceIdsDTO deviceIdsDTO) {
                log.error("获取多个设备状态异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<DeviceInfoInGroupVO> getDeviceInfoInGroupVO(Long deviceId) {
                log.error("根据设备ID获取设备信息包含设备组信息异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<List<DeviceInfoInGroupVO>> getDeviceInfoInGroupVOList(List<Long> deviceIdList) {
                log.error("根据设备ID列表获取设备列表异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<List<DeviceInfoVO>> getDeviceInfoListByIds(List<Long> deviceIdList) {
                log.error("getDeviceInfoListByIds根据设备ID列表获取设备列表异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<List<DeviceInfoVO>> getDeviceListByGroupId(Long deviceGroupId) {
                log.error("getDeviceListByGroupId根据设备组ID获取设备列表：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public SecRestResponse<List<DeviceInfoVO>> getDeviceInfoListByRegionId(DeviceInfoByRegionIdDTO deviceInfoByRegionIdDTO) {
                log.error("根据区域ID获取设备列表异常：", cause);
                return SecRestResponse.fallback(cause);
            }

            @Override
            public void getDeviceInfoStatus(DeviceInfoStatusDTO deviceInfoStatusDTO) {
                log.error("定时获取设备状态失败：", cause);
            }

            @Override
            public SecRestResponse<List<DevicePushDataInfo>> getDevicePushData() {
                log.error("获取待推送设备信息失败", cause);
                return SecRestResponse.fallback(cause);
            }
        };
    }
}
