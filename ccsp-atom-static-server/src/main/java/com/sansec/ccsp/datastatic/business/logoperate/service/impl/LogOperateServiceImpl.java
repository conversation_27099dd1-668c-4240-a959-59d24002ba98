package com.sansec.ccsp.datastatic.business.logoperate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sansec.ccsp.datastatic.business.logoperate.convert.LogOperateConvert;
import com.sansec.ccsp.datastatic.business.logoperate.entity.LogOperatePO;
import com.sansec.ccsp.datastatic.business.logoperate.mapper.LogOperateMapper;
import com.sansec.ccsp.datastatic.business.logoperate.service.LogOperateService;
import com.sansec.ccsp.datastatic.common.constant.LogOperateConstant;
import com.sansec.ccsp.datastatic.common.enums.ServiceTypeEnum;
import com.sansec.ccsp.datastatic.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.staticapi.logoperate.request.LogOperateAddDTO;
import com.sansec.ccsp.staticapi.logoperate.request.LogOperateAuditDTO;
import com.sansec.ccsp.staticapi.logoperate.request.LogOperatePageDTO;
import com.sansec.ccsp.staticapi.logoperate.request.LogOperateTenantPageDTO;
import com.sansec.ccsp.staticapi.logoperate.response.LogOperateVo;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import com.sansec.component.algorithm.utill.ComponentSynthesisEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class LogOperateServiceImpl extends ServiceImpl<LogOperateMapper, LogOperatePO> implements LogOperateService {

    @Resource
    LogOperateConvert logOperateConvert;

    /**
     * 批量入库的大小
     */
    @Value("${logOperate.batchInsert.size:500}")
    private int batchSize;

    @Override
    public void ptAdd(LogOperateAddDTO logOperateAddDTO) {
        log.info("LogOperateServiceImpl.ptAdd, logOperateAddDTO:{}", logOperateAddDTO);
        try {
            LogOperatePO operatePO = logOperateConvert.addDtoToPo(logOperateAddDTO);
            if(operatePO.getResult() != null){
                operatePO.setAuditStatus(1);
                operatePO.setLogType(1);
                Long id = IdGenerator.ins().generator();
                operatePO.setId(id);
                baseMapper.insert(operatePO);

                String hmac = logOperateHmacById(id);
                LambdaUpdateWrapper<LogOperatePO> updateWrapper=new LambdaUpdateWrapper<>();
                updateWrapper.eq(LogOperatePO::getId,id);
                updateWrapper.set(LogOperatePO::getHmac,hmac);
                baseMapper.update(null,updateWrapper);
            }
        }catch (Exception e){
            log.error("接受管理平台侧操作日志入库失败，dto={},error={}",logOperateAddDTO,e);
        }

    }

    @Override
    public void serviceBatchAdd(List<LogOperateAddDTO> addDTOList) {
        List<LogOperatePO> poList=new ArrayList<>(addDTOList.size());
        for(int i=0;i<addDTOList.size();i++){
            LogOperatePO operatePO = logOperateConvert.addDtoToPo(addDTOList.get(i));
            operatePO.setAuditStatus(1);
            operatePO.setLogType(2);
            operatePO.setId(IdGenerator.ins().generator());
            Long busiTypeId = operatePO.getBusiTypeId();
            if(busiTypeId != null){
                ServiceTypeEnum serviceTypeEnum = ServiceTypeEnum.byId(busiTypeId);
                if(serviceTypeEnum != null){
                    operatePO.setModuleName(serviceTypeEnum.getName());
                }
            }
            //result只能为0-成功，1-失败
            String result = operatePO.getResult();
            if("0".equals(result) || "1".equals(result)){
                /**
                 * 请求服务得到的日志1是成功0是失败
                 */
                if(result.equals("0")){
                    operatePO.setResult("1");
                }
                else if(result.equals("1")){
                    operatePO.setResult("0");
                }
                String hmac = logOperateHmacByPo(operatePO);
                operatePO.setHmac(hmac);

                poList.add(operatePO);
            }
            else{
                log.error("入库失败:{}",operatePO);
            }

        }

        List<List<LogOperatePO>> lists = Lists.partition(poList, batchSize);
        for(int i=0;i<lists.size();i++){
            List<LogOperatePO> poBatchList = lists.get(i);
            try {
                this.saveBatch(poBatchList,batchSize);
            }catch (Exception e){
                log.error("服务日志批量入库失败",e);
                log.error("服务日志批量入库失败所在组,batchList:{}",poBatchList);
            }
        }
    }

    public String logOperateHmacById(long logOperateId){
        LogOperatePO po = baseMapper.selectById(logOperateId);
        if(po==null){
            return null;
        }
        StringBuilder sb=new StringBuilder();
        sb.append(po.getId())
                .append(po.getTenantId())
                .append(po.getResult())
                .append(po.getBusiTypeId())
                .append(po.getCreateTime())
                .append(po.getOperIp())
                .append(po.getOperName())
                .append(po.getModuleName())
                .append(po.getOperContent())
                .append(po.getAuditStatus())
                .append(po.getErrorMsg())
                .append(po.getLogType())
                .append(po.getExtend1())
                .append(po.getExtend2());

        String digest = ComponentSynthesisEncryptionUtil.sm3Hmac(sb.toString());

        return digest;
    }

    public String logOperateHmacByPo(LogOperatePO po){

        StringBuilder sb=new StringBuilder();
        sb.append(po.getId())
                .append(po.getTenantId())
                .append(po.getResult())
                .append(po.getBusiTypeId())
                .append(po.getCreateTime())
                .append(po.getOperIp())
                .append(po.getOperName())
                .append(po.getModuleName())
                .append(po.getOperContent())
                .append(po.getAuditStatus())
                .append(po.getErrorMsg())
                .append(po.getLogType())
                .append(po.getExtend1())
                .append(po.getExtend2());

        String digest = ComponentSynthesisEncryptionUtil.sm3Hmac(sb.toString());

        return digest;

    }

    public String logOperateHmacByVo(LogOperateVo po){

        StringBuilder sb=new StringBuilder();
        sb.append(po.getId())
                .append(po.getTenantId())
                .append(po.getResult())
                .append(po.getBusiTypeId())
                .append(po.getCreateTime())
                .append(po.getOperIp())
                .append(po.getOperName())
                .append(po.getModuleName())
                .append(po.getOperContent())
                .append(po.getAuditStatus())
                .append(po.getErrorMsg())
                .append(po.getLogType())
                .append(po.getExtend1())
                .append(po.getExtend2());

        String digest = ComponentSynthesisEncryptionUtil.sm3Hmac(sb.toString());

        return digest;

    }

    @Override
    public SecRestResponse<SecPageVO<LogOperateVo>> find(LogOperatePageDTO pageDTO) {
        LambdaQueryWrapper<LogOperatePO> queryWrapper=new LambdaQueryWrapper<>();

        queryWrapper.eq(LogOperatePO::getTenantId,pageDTO.getTenantId());

        if(StringUtils.isNotBlank(pageDTO.getOperName())){
            queryWrapper.like(LogOperatePO::getOperName,pageDTO.getOperName());
        }

        if(StringUtils.isNotBlank(pageDTO.getOperIp())){
            queryWrapper.like(LogOperatePO::getOperIp,pageDTO.getOperIp());
        }

        if(pageDTO.getLogType() != null){
            queryWrapper.eq(LogOperatePO::getLogType,pageDTO.getLogType());
        }

        if(StringUtils.isNotBlank(pageDTO.getModuleName())){
            queryWrapper.like(LogOperatePO::getModuleName,pageDTO.getModuleName());
        }

        if(StringUtils.isNotBlank(pageDTO.getOperContent())){
            queryWrapper.like(LogOperatePO::getOperContent,pageDTO.getOperContent());
        }

        if(StringUtils.isNotBlank(pageDTO.getResult())){
            queryWrapper.eq(LogOperatePO::getResult,pageDTO.getResult());
        }

        if(StringUtils.isNotBlank(pageDTO.getStartTime())){
            queryWrapper.ge(LogOperatePO::getCreateTime,pageDTO.getStartTime());
        }

        if(StringUtils.isNotBlank(pageDTO.getEndTime())){
            queryWrapper.le(LogOperatePO::getCreateTime,pageDTO.getEndTime());
        }

        queryWrapper.orderByDesc(LogOperatePO::getCreateTime);
        IPage<LogOperatePO> page=new Page<>(pageDTO.getPageNum(),pageDTO.getPageSize());
        IPage<LogOperatePO> operatePOIPage = baseMapper.selectPage(page, queryWrapper);

        SecPageVO<LogOperateVo> secPageVo = logOperateConvert.pagePoToSecPageVo(operatePOIPage);
        List<LogOperateVo> voList = secPageVo.getList();
        for(int i=0;i<voList.size();i++){
            LogOperateVo logOperateVo = voList.get(i);
            String hmac = logOperateHmacByVo(logOperateVo);
            if(hmac.equals(logOperateVo.getHmac())){
                voList.get(i).setIsCheck(LogOperateConstant.CHECK_SUCCESS);
            }
            else{
                voList.get(i).setIsCheck(LogOperateConstant.CHECK_FAIL);
            }
        }

        secPageVo.setList(voList);

        return ResultUtil.ok(secPageVo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SecRestResponse<Object> audit(LogOperateAuditDTO auditDTO) {
        List<Long> ids = auditDTO.getIds();

        LambdaQueryWrapper<LogOperatePO> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(LogOperatePO::getTenantId,auditDTO.getTenantId());
        queryWrapper.in(LogOperatePO::getId,ids);

        Long selectCount = baseMapper.selectCount(queryWrapper);
        if(selectCount != ids.size()){
            throw new BusinessException(SecErrorCodeConstant.LOG_OPER_NOT_ALL_EXIST);
        }

//        queryWrapper.clear();
//        queryWrapper.eq(LogOperatePO::getAuditStatus,0);
//        queryWrapper.in(LogOperatePO::getId,ids);
//        Long auditCount = baseMapper.selectCount(queryWrapper);
//        if(auditCount > 0){
//            throw new BusinessException(SecErrorCodeConstant.LOG_OPER_ALREADY_AUDIT);
//        }

        for(Long id:ids){
            LambdaUpdateWrapper<LogOperatePO> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.eq(LogOperatePO::getId,id).set(LogOperatePO::getAuditStatus,0);
            baseMapper.update(null,updateWrapper);

            String hmac = logOperateHmacById(id);
            updateWrapper.clear();
            updateWrapper.eq(LogOperatePO::getId,id).set(LogOperatePO::getHmac,hmac);
            baseMapper.update(null,updateWrapper);
        }
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Long> getCount(LogOperatePageDTO pageDTO) {
        LambdaQueryWrapper<LogOperatePO> queryWrapper=new LambdaQueryWrapper<>();

        queryWrapper.eq(LogOperatePO::getTenantId,pageDTO.getTenantId());
        if(StringUtils.isNotBlank(pageDTO.getOperName())){
            queryWrapper.like(LogOperatePO::getOperName,pageDTO.getOperName());
        }

        if(StringUtils.isNotBlank(pageDTO.getOperIp())){
            queryWrapper.like(LogOperatePO::getOperIp,pageDTO.getOperIp());
        }

        if(pageDTO.getLogType() != null){
            queryWrapper.eq(LogOperatePO::getLogType,pageDTO.getLogType());
        }

        if(StringUtils.isNotBlank(pageDTO.getModuleName())){
            queryWrapper.like(LogOperatePO::getModuleName,pageDTO.getModuleName());
        }

        if(StringUtils.isNotBlank(pageDTO.getOperContent())){
            queryWrapper.like(LogOperatePO::getOperContent,pageDTO.getOperContent());
        }

        if(StringUtils.isNotBlank(pageDTO.getResult())){
            queryWrapper.eq(LogOperatePO::getResult,pageDTO.getResult());
        }

        if(StringUtils.isNotBlank(pageDTO.getStartTime())){
            queryWrapper.ge(LogOperatePO::getCreateTime,pageDTO.getStartTime());
        }

        if(StringUtils.isNotBlank(pageDTO.getEndTime())){
            queryWrapper.le(LogOperatePO::getCreateTime,pageDTO.getEndTime());
        }

        Long selectCount = baseMapper.selectCount(queryWrapper);
        return ResultUtil.ok(selectCount);
    }

    @Override
    public SecRestResponse<SecPageVO<LogOperateVo>> findByTenant(LogOperateTenantPageDTO pageDTO) {
        LambdaQueryWrapper<LogOperatePO> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(LogOperatePO::getTenantId,pageDTO.getTenantId());

        if(StringUtils.isNotBlank(pageDTO.getOperName())){
            queryWrapper.like(LogOperatePO::getOperName,pageDTO.getOperName());
        }

        if(StringUtils.isNotBlank(pageDTO.getOperIp())){
            queryWrapper.like(LogOperatePO::getOperIp,pageDTO.getOperIp());
        }

        if(pageDTO.getLogType() != null){
            queryWrapper.eq(LogOperatePO::getLogType,pageDTO.getLogType());
        }

        if(StringUtils.isNotBlank(pageDTO.getModuleName())){
            queryWrapper.like(LogOperatePO::getModuleName,pageDTO.getModuleName());
        }

        if(StringUtils.isNotBlank(pageDTO.getOperContent())){
            queryWrapper.like(LogOperatePO::getOperContent,pageDTO.getOperContent());
        }

        if(StringUtils.isNotBlank(pageDTO.getResult())){
            queryWrapper.eq(LogOperatePO::getResult,pageDTO.getResult());
        }

        if(StringUtils.isNotBlank(pageDTO.getStartTime())){
            queryWrapper.ge(LogOperatePO::getCreateTime,pageDTO.getStartTime());
        }

        if(StringUtils.isNotBlank(pageDTO.getEndTime())){
            queryWrapper.le(LogOperatePO::getCreateTime,pageDTO.getEndTime());
        }

        queryWrapper.orderByDesc(LogOperatePO::getCreateTime);
        IPage<LogOperatePO> page=new Page<>(pageDTO.getPageNum(),pageDTO.getPageSize());
        IPage<LogOperatePO> operatePOIPage = baseMapper.selectPage(page, queryWrapper);

        SecPageVO<LogOperateVo> secPageVo = logOperateConvert.pagePoToSecPageVo(operatePOIPage);
        List<LogOperateVo> voList = secPageVo.getList();
        for(int i=0;i<voList.size();i++){
            LogOperateVo logOperateVo = voList.get(i);
            String hmac = logOperateHmacByVo(logOperateVo);
            if(hmac.equals(logOperateVo.getHmac())){
                voList.get(i).setIsCheck(LogOperateConstant.CHECK_SUCCESS);
            }
            else{
                voList.get(i).setIsCheck(LogOperateConstant.CHECK_FAIL);
            }
        }

        secPageVo.setList(voList);

        return ResultUtil.ok(secPageVo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SecRestResponse<Object> auditByTenant(LogOperateAuditDTO auditDTO) {
        List<Long> ids = auditDTO.getIds();
        LambdaQueryWrapper<LogOperatePO> tenantQueryWrapper = new LambdaQueryWrapper<>();
        tenantQueryWrapper.eq(LogOperatePO::getTenantId, auditDTO.getTenantId());
        tenantQueryWrapper.in(LogOperatePO::getId, ids);

        Long selectCount = baseMapper.selectCount(tenantQueryWrapper);
        if (selectCount != ids.size()) {
            throw new BusinessException(SecErrorCodeConstant.LOG_OPER_NOT_ALL_EXIST);
        }

//        queryWrapper.clear();
//        queryWrapper.eq(LogOperatePO::getAuditStatus,0);
//        queryWrapper.in(LogOperatePO::getId,ids);
//        Long auditCount = baseMapper.selectCount(queryWrapper);
//        if(auditCount > 0){
//            throw new BusinessException(SecErrorCodeConstant.LOG_OPER_ALREADY_AUDIT);
//        }

        for(Long id:ids){
            LambdaUpdateWrapper<LogOperatePO> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.eq(LogOperatePO::getId,id).set(LogOperatePO::getAuditStatus,0);
            baseMapper.update(null,updateWrapper);

            String hmac = logOperateHmacById(id);
            updateWrapper.clear();
            updateWrapper.eq(LogOperatePO::getId,id).set(LogOperatePO::getHmac,hmac);
            baseMapper.update(null,updateWrapper);
        }
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Long> getCountByTenant(LogOperateTenantPageDTO pageDTO) {
        LambdaQueryWrapper<LogOperatePO> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(LogOperatePO::getTenantId,pageDTO.getTenantId());

        if(StringUtils.isNotBlank(pageDTO.getOperName())){
            queryWrapper.like(LogOperatePO::getOperName,pageDTO.getOperName());
        }

        if(StringUtils.isNotBlank(pageDTO.getOperIp())){
            queryWrapper.like(LogOperatePO::getOperIp,pageDTO.getOperIp());
        }

        if(pageDTO.getLogType() != null){
            queryWrapper.eq(LogOperatePO::getLogType,pageDTO.getLogType());
        }

        if(StringUtils.isNotBlank(pageDTO.getModuleName())){
            queryWrapper.like(LogOperatePO::getModuleName,pageDTO.getModuleName());
        }

        if(StringUtils.isNotBlank(pageDTO.getOperContent())){
            queryWrapper.like(LogOperatePO::getOperContent,pageDTO.getOperContent());
        }

        if(StringUtils.isNotBlank(pageDTO.getResult())){
            queryWrapper.eq(LogOperatePO::getResult,pageDTO.getResult());
        }

        if(StringUtils.isNotBlank(pageDTO.getStartTime())){
            queryWrapper.ge(LogOperatePO::getCreateTime,pageDTO.getStartTime());
        }

        if(StringUtils.isNotBlank(pageDTO.getEndTime())){
            queryWrapper.le(LogOperatePO::getCreateTime,pageDTO.getEndTime());
        }

        Long selectCount = baseMapper.selectCount(queryWrapper);
        return ResultUtil.ok(selectCount);
    }
}
