package com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.response;

import lombok.Data;

/**
 * @Description: 服务操作日志
 * @CreateTime: 2023-04-23
 * @Author: wangjunjie
 */
@Data
public class ServiceInterfaceApiRecordVO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 主键
     */
    private Long serviceId;
    /**
     * 接口名称
     */
    private String apiName;
    /**
     * ip
     */
    private String ip;
    /**
     * 端口
     */
    private Integer port;
    /**
     * 请求头
     */
    private String headers;
    /**
     * 请求体
     */
    private String requestInfo;
    /**
     * 响应体
     */
    private String responseInfo;
    /**
     * 创建时间
     */
    private String createTime;
}
