package com.sansec.ccsp.device.devicetype.response;

import lombok.Data;

@Data
public class DeviceTypeRouteConfigVO{
    /**
     * 主键
     */
    private Long id;
    /**
     * 设备类型ID
     */
    private Long deviceTypeId;
    /**
     * 请求协议
     */
    private Integer netPro;
    /**
     * URL前缀
     */
    private String urlStart;
    /**
     * 是否有管理路由;0：否，1：是
     */
    private Integer hasManageRoute;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}
