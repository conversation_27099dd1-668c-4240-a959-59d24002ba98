package com.sansec.ccsp.security.util;

import com.sansec.ccsp.key.KeyUtil;
import com.sansec.ccsp.sm4.SM4Util;
import com.sansec.ccsp.utils.BytesUtil;
import com.sansec.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.*;

/**
 * 使用jce进行加解密操作
 * wangjianing
 * 2022/4/16
 */
@Slf4j
public class JceCryptoUtil {
	public static final String PROVIDER = "SwxaJCE";

	/**
	 * 外部三分量，默认密钥
	 */
	private static byte[] SM_4_KEY = null;

	/**
	 * 外部三分量，默认IV
	 */
	private static final IvParameterSpec IV_DEFAULT = new IvParameterSpec(new byte[16]);


	private synchronized static void init() {
		if(SM_4_KEY != null){
			return;
		}
		byte[] lmk1 = Base64.decodeBase64(System.getProperty("key.lmk1.value"));
		String lmk2Path = System.getProperty("key.lmk2.path");

		byte[] lmk2;
		try {
			lmk2 = BytesUtil.getFileValue(lmk2Path);
		} catch (IOException var6) {
			log.error("JceCryptoUtil init lmk2 error = {}", var6.getMessage());
			throw new RuntimeException(String.format("lmk2Path = %s", lmk2Path), var6);
		}

		byte[] lmk3 = Base64.decodeBase64("qtSRA+zF6foUjSTkyeAzkw==");

		try {
			SM_4_KEY = KeyUtil.getKey(lmk1, lmk2, lmk3);
		} catch (Exception var5) {
			log.error("JceCryptoUtil init SM_4_KEY error = {}", var5.getMessage());
			throw new RuntimeException("init SM_4_KEY error", var5);
		}
	}

	/**
	 * 对多个字段进行加密，并按照字段顺序返回加密后的字段
	 * @param values
	 * @return  当某个字段加密失败时，对应位置的返回值为null
	 */
	public static String[] encrypt(String... values){
		String[] result = new String[values.length];
		for (int i = 0; i < values.length; i++) {
			if(values[i] == null){
				result[i] = null;
				continue;
			}
			byte[] encrypt = cipher_sm4(Cipher.ENCRYPT_MODE, values[i].getBytes(StandardCharsets.UTF_8));
			if(encrypt == null){
				result[i] = null;
			}
			else{
				result[i] = Base64.encodeBase64String(encrypt);
			}
		}
		return result;
	}

	/**
	 * 使用外部三分量加解密
	 * @param cipherMode 加解密模式，Cipher.ENCRYPT_MODE加密，Cipher.DECRYPT_MODE解密
	 * @param data  待加密数据
	 * @return
	 */
	public static byte[] cipher_sm4(int cipherMode, byte[] data) {
		if(SM_4_KEY == null){
			init();
		}
		try {
			//组装加密算法
			Cipher cipher = Cipher.getInstance(getCipherAlg("SM4"), PROVIDER);
			SecretKeySpec secretKey = new SecretKeySpec(SM_4_KEY, "SM4");
			if(cipherMode != Cipher.ENCRYPT_MODE && cipherMode != Cipher.DECRYPT_MODE){
				log.error("JceCryptoUtil 不支持的加解密模式[cipherMode={}]", cipher);
				return null;
			}
			//加密模式
			cipher.init(cipherMode, secretKey, IV_DEFAULT);
			return cipher.doFinal(data);
		} catch (Exception e) {
			log.error("JceCryptoUtil jce调用密码机-使用外部三分量加解密出错：",e);
			return null;
		}
	}

	/**
	 * @return 加解密结果
	 * <AUTHOR>
	 * @Description 内部密钥加解密
	 * @Date 2022/4/16
	 * @Param index 密钥索引,非对称真实索引为index/2,奇数表示签名用户，偶数表示加密用途
	 * @Param alg 算法:SM2/SM4/RSA/AES/3DES
	 * @Param cipherMode 加解密模式，Cipher.ENCRYPT_MODE加密，Cipher.DECRYPT_MODE解密
	 * @Param data 数据
	 */
	public static byte[] cipher(int index, String alg, int cipherMode, byte[] data) {
		Cipher cipher;
		try {
			//组装加密算法
			String cipherAlg = getCipherAlg(alg);
			cipher = Cipher.getInstance(cipherAlg, PROVIDER);
			Key key;
			if ("RSA".equals(alg) || "SM2".equals(alg)) {
				if (cipherMode == Cipher.ENCRYPT_MODE) {
					key = getPubKey(index, alg);
				} else {
					key = getPriKey(index, alg);
				}
			} else {
				key = getSymmetricKey(index, alg);
			}
			//加密模式
			cipher.init(cipherMode, key);

			return cipher.doFinal(data);
		} catch (Exception e) {
			//logger.error("jce调用密码机内部密钥加密出错：",e);
			return null;
		}
	}

	/**
	 * @return
	 * <AUTHOR>
	 * @Description 内部密钥hmac
	 * @Date 2022/4/18
	 * @Param index 密钥索引,非对称密钥真实索引为index/2,奇数表示签名用户，偶数表示加密用途
	 * @Param hmacAlg HMAC算法 HMACSM3 HMACSHA1 HMACSHA224 HMACSHA256 HMACSHA384 HMACSHA512
	 * @Param keyAlg 密钥算法
	 * @Param data 数据
	 */
	public static byte[] hMac(int index, String hmacAlg, String keyAlg, byte[] data) {
		try {
			Mac mac = Mac.getInstance(hmacAlg, PROVIDER);
			SecretKey key = (SecretKey) getSymmetricKey(index, keyAlg);
			mac.init(key);
			mac.update(data);
			return mac.doFinal();

		} catch (Exception e) {
			//logger.error("jce调用密码机内部密钥hmac出错：",e);
			throw new BusinessException(String.format("jce调用密码机内部密钥加密出错：%s", e.getMessage()));
		}
	}

	public static byte[] sign(PrivateKey privateKey, byte[] data, String alg) {
		try {
			Signature sign = Signature.getInstance(alg, PROVIDER);
			sign.initSign(privateKey);
			sign.update(data);
			return sign.sign();
		} catch (NoSuchAlgorithmException | NoSuchProviderException e) {
			//logger.error("jce error: {}",e.getMessage());
			throw new BusinessException("jce加载异常： " + e.getMessage());
		} catch (InvalidKeyException e) {
			//logger.error("generate pubKey error: ",e);
			throw new BusinessException("转换私钥对象异常：" + e.getMessage());
		} catch (SignatureException e) {
			//logger.error("sign data error : ",e);
			throw new BusinessException("签名异常: " + e.getMessage());
		}
	}

	public static boolean verify(PublicKey pubKey, byte[] data, byte[] signData, String alg) {
		try {
			Signature sign = Signature.getInstance(alg, PROVIDER);
			sign.initVerify(pubKey);
			sign.update(data);
			return sign.verify(signData);
		} catch (NoSuchAlgorithmException | NoSuchProviderException e) {
			//logger.error("jce error: {}",e.getMessage());
			throw new BusinessException("jce加载异常： " + e.getMessage());
		} catch (SignatureException e) {
			//logger.error("verify data error : ",e);
			throw new BusinessException("验签异常: " + e.getMessage());
		} catch (InvalidKeyException i) {
			//logger.error("generate pubKey error: ",i);
			throw new BusinessException("转换公钥对象异常：" + i.getMessage());
		}

	}

	private static Key getSymmetricKey(int index, String alg) throws NoSuchAlgorithmException, NoSuchProviderException {

		KeyGenerator kg = KeyGenerator.getInstance(alg, PROVIDER);
		kg.init(index << 16);
		return kg.generateKey();
	}

	public static Key getPubKey(int index, String alg) throws NoSuchAlgorithmException, NoSuchProviderException {
		KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(alg, PROVIDER);
		keyPairGenerator.initialize(index << 16);
		return keyPairGenerator.generateKeyPair().getPublic();
	}

	private static Key getPriKey(int index, String alg) throws NoSuchAlgorithmException, NoSuchProviderException {
		KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(alg, PROVIDER);
		keyPairGenerator.initialize(index << 16);
		return keyPairGenerator.generateKeyPair().getPrivate();
	}

	/**
	 * @return byte[] 随机数
	 * <AUTHOR>
	 * @Description 生成随机数
	 * @Date 2022/4/16
	 * @Param length 随机数长度
	 */
	public static byte[] generateRandom(int length) {
		try {
			SecureRandom secureRandom = SecureRandom.getInstance("RND", PROVIDER);
			byte[] seed = secureRandom.generateSeed(length);
			secureRandom.setSeed(seed);
			byte[] random = new byte[length];
			secureRandom.nextBytes(random);
			return random;
		} catch (NoSuchAlgorithmException | NoSuchProviderException e) {
			//logger.error("JCE调用失败：",e);
			throw new BusinessException("生成随机数失败");
		}
	}

	private static String getCipherAlg(String alg) {
		switch (alg) {
			case "SM2":
				return alg;
			case "RSA":
				return "RSA/ECB/PKCS1Padding";
			case "SM4":
				return "SM4/CBC/PKCS5PADDING";
			default:
				return alg + "/CBC/PKCS5PADDING";
		}
	}

}
