package com.sansec.ccsp.datastatic.business.common.service.impl;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.sansec.ccsp.datastatic.business.common.response.StatusVO;
import com.sansec.ccsp.datastatic.business.common.service.StatusService;
import com.sansec.ccsp.datastatic.business.statistic.mapper.StatisticIncreCalMapper;
import com.sansec.common.constants.SecBaseErrorCodeConstant;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import com.sansec.redis.utils.RedisUtill;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 密钥接口
 * <AUTHOR>
 * @date: 2021/9/6 18:07
 */
@Service
@Slf4j
public class StatusServiceImpl implements StatusService {
    @Value("${spring.cloud.nacos.server-addr}")
    private String serverAddr;

    @Resource
    private RedisUtill redisUtill;
    @Resource
    private StatisticIncreCalMapper statisticMapper;
    private static final String PER_TOKEN_USER = "permission_token_user";
    private static final String FALSE = "false";
    private static NamingService namingService ;

    private void createNacos() throws NacosException {
        if(namingService==null){
            namingService=NacosFactory.createNamingService(serverAddr);
        }
    }
    @Override
    public SecRestResponse<StatusVO> getStatus() {
        StatusVO statusVO = new StatusVO();

        try {
            this.createNacos();
            String nacosStatus = namingService.getServerStatus();
            log.info("nacos 状态,nacosStatus={}",nacosStatus);
            if (StringUtils.isBlank(nacosStatus) || "DOWN".equals(nacosStatus)){
                statusVO.setNacos(FALSE);
            }
        } catch (Exception e) {
            log.error("检测nacos异常",e);
            statusVO.setNacos(FALSE);
        }

        try {
            redisUtill.strGet(PER_TOKEN_USER);
        } catch (Exception e) {
            log.error("检测redis异常",e);
            statusVO.setRedis(FALSE);
        }

        try {
            statisticMapper.selectById(1L);
        } catch (Exception e) {
            log.error("检测db异常",e);
            statusVO.setDb(FALSE);
        }

        if (FALSE.equals(statusVO.getRedis()) || FALSE.equals(statusVO.getNacos()) || FALSE.equals(statusVO.getDb())){
            return ResultUtil.error(SecBaseErrorCodeConstant.UNKNOWN_ERROR,statusVO);
        }

        return ResultUtil.ok(statusVO);
    }
}

