package com.sansec.ccsp.pt.business.region.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @description : 区域表(1030);
 * <AUTHOR> <PERSON><PERSON><PERSON>jiawei
 * @date : 2023-9-18
 */
@Data
public class RegionPageDTO extends SecPageDTO{
    /**
     * 区域名称
     */
    @Size(max = 50, message = "区域名称长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "区域名称只能包含中文、英文、数字或特殊字符 - _")
    private String regionName;

    /**
     * 区域标识
     */
    @Size(max = 50, message = "区域标识长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_CODE, message = "区域标识必须以大小写字母或者数字开头并只能包含大小写字母、数字、特殊字符-_")
    private String regionCode;


}