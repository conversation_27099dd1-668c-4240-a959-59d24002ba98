package com.sansec.ccsp.extra.business.workorder.request;

import com.sansec.ccsp.extra.business.constant.CommonPattern;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class WorkOrderAddDTO {

    /**
     * 工单类型id
     */
    Integer orderTypeId;

    /**
     * 申请密码产品id
     */
    Long productId;

    /**
     * 申请服务数量
     */
    Integer quantity;

    /**
     * 组织单位
     */
    @Size(max = 50, message = "组织单位长度最大50")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "组织单位只能包含中文、英文、数字、特殊字符 - _ ")
    String organization;


    /**
     * 联系人
     */
    @Size(max = 20, message = "联系人长度最大20")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "联系人只能包含中文、英文、数字、特殊字符 - _ ")
    String personName;


    /**
     * 手机号
     */
    @Pattern(regexp = "^$|^1\\d{10}$", message = "手机号格式校验错误")
    String phone;


    /**
     * 邮箱
     */
    @Pattern(regexp = CommonPattern.EMAIL_RULE, message = "邮箱格式校验错误,只允许英文字母、数字、下划线、英文句号、以及中划线组成")
   private String email;

    /**
     * 问题描述
     */
    @NotBlank(message = "问题描述不可为空")
    @Size(max = 200, message = "问题描述长度最大200")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "问题描述请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
    String description;

    /**
     * 列表<附件id>
     */
    List<Long> attachmentList;
}
