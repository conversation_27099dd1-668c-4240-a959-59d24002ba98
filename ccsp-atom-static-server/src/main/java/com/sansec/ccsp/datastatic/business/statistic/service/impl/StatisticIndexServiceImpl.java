package com.sansec.ccsp.datastatic.business.statistic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sansec.ccsp.common.dic.api.DicStatisticApi;
import com.sansec.ccsp.common.dic.response.DicStatisticVO;
import com.sansec.ccsp.datastatic.business.monitor.entity.*;
import com.sansec.ccsp.datastatic.business.monitor.mapper.DicKeyStatusMapper;
import com.sansec.ccsp.datastatic.business.monitor.mapper.DicSealStatusMapper;
import com.sansec.ccsp.datastatic.business.statistic.convert.MultiLineStatisticConvert;
import com.sansec.ccsp.datastatic.business.statistic.convert.StatisticDealConvert;
import com.sansec.ccsp.datastatic.business.statistic.mapper.StatisticIncreCalMapper;
import com.sansec.ccsp.datastatic.business.statistic.mapper.StatisticIncreRecordMapper;
import com.sansec.ccsp.datastatic.business.statistic.mapper.StatisticIncreTotalCalMapper;
import com.sansec.ccsp.datastatic.business.statistic.mapper.StatisticUnincreCalMapper;
import com.sansec.ccsp.datastatic.business.statistic.service.StatisticIndexService;
import com.sansec.ccsp.datastatic.common.enums.ServiceTypeEnum;
import com.sansec.ccsp.datastatic.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.datastatic.common.utils.DateDetail;
import com.sansec.ccsp.datastatic.common.utils.DateUtil;
import com.sansec.ccsp.security.util.LoginUserUtil;
import com.sansec.ccsp.staticapi.monitor.request.MonitorIndexPeriodDTO;
import com.sansec.ccsp.staticapi.monitor.request.ServiceRandom;
import com.sansec.ccsp.staticapi.monitor.request.StatisticIndexDTO;
import com.sansec.ccsp.staticapi.monitor.request.StatisticInsertListDTO;
import com.sansec.ccsp.staticapi.monitor.response.*;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 统计首页接口实现类
 * @CreateTime: 2023-05-13
 * @Author: wangjunjie
 */
@Service
@Slf4j
public class StatisticIndexServiceImpl implements StatisticIndexService {

    // 增量类
    private static final Integer INCRE_STATISTIC_TYPE = 1;
    // 非增量计算类
    private static final Integer UNINCRE_UNCOMPUTE_STATISTIC_TYPE = 2;
    // 非增量非计算类
    private static final Integer UNINCRE_COMPUTE_STATISTIC_TYPE = 3;
    // 分布状态有效
    private static final Integer IS_AVAILABLE = 1;

    @Resource
    private StatisticIncreTotalCalMapper increTotalCalMapper;
    @Resource
    private StatisticUnincreCalMapper unincreCalMapper;
    @Resource
    private StatisticIncreCalMapper statisticIncreCalMapper;
    @Resource
    private StatisticIncreRecordMapper statisticIncreRecordMapper;
    @Resource
    private DicStatisticApi dicStatisticApi;
    @Resource
    private MultiLineStatisticConvert multiLineStatisticConvert;
    @Resource
    private DicKeyStatusMapper dicKeyStatusMapper;
    @Resource
    private DicSealStatusMapper dicSealStatusMapper;
    @Resource
    private StatisticIncreTotalCalMapper statisticIncreTotalCalMapper;

    /**
     * 指标字典 名称->实体
     */
    public Map<String, DicStatisticVO> getStatisticNameTypeMap() {
        List<DicStatisticVO> statisticVOList = dicStatisticApi.getAll();
        return statisticVOList.stream().collect(Collectors.toMap(DicStatisticVO::getStatisticName, item -> item));
    }

    /**
     * 饼图状态分布字典
     * 指标名称 -> (状态分布值 —> 状态分布名)
     */
    public Map<String, Map<String, String>> getPieStatusDic() {
        Map<String, Map<String, String>> pieStatusDic = new HashMap<>();
        // 密钥状态分布字典
        LambdaQueryWrapper<DicKeyStatusPO> keyStatusQueryWrapper = new LambdaQueryWrapper<>();
        keyStatusQueryWrapper.eq(DicKeyStatusPO::getIsAvailable, IS_AVAILABLE);
        List<DicKeyStatusPO> keyStatusList = dicKeyStatusMapper.selectList(new QueryWrapper<>());
        pieStatusDic.put("kmsKeyStateStatistic", keyStatusList.stream().collect(Collectors.toMap(DicKeyStatusPO::getDValue, DicKeyStatusPO::getDName)));
        // 签章状态分布字典
        LambdaQueryWrapper<DicSealStatusPO> sealStatusQueryWrapper = new LambdaQueryWrapper<>();
        sealStatusQueryWrapper.eq(DicSealStatusPO::getIsAvailable, IS_AVAILABLE);
        List<DicSealStatusPO> sealStatusList = dicSealStatusMapper.selectList(new QueryWrapper<>());
        pieStatusDic.put("sealStateStatistic", sealStatusList.stream().collect(Collectors.toMap(DicSealStatusPO::getDValue, DicSealStatusPO::getDName)));
        return pieStatusDic;
    }

    @Override
    public SecRestResponse<List<NumStatisticVO>> getStatisticNum(StatisticIndexDTO statisticIndexDTO) {
        // 查询结果列表
        List<NumStatisticVO> numStatisticVOList = new ArrayList<>();
        // 指标字典
        Map<String, DicStatisticVO> statisticNameDicMap = getStatisticNameTypeMap();
        // 为null表示非组织审计员
        List<String> tenantCodeList = statisticIndexDTO.getTenantCodeList();
        if (tenantCodeList == null) {
            tenantCodeList = new ArrayList<>();
            if (StringUtils.isNotBlank(statisticIndexDTO.getTenantCode())) {
                tenantCodeList.add(statisticIndexDTO.getTenantCode());
            }
        } else {
            // 组织审计员的租户列表为空时，添加空字符串，防止无法拼接租户条件导致查询到所有租户
            if (CollectionUtils.isEmpty(tenantCodeList)) {
                tenantCodeList.add("");
            }
        }

        // 查询请求中的所有指标
        for (String statisticName : statisticIndexDTO.getStatisticNames()) {
            // 增量类型
            DicStatisticVO dicStatisticVO = statisticNameDicMap.get(statisticName);
            if (dicStatisticVO == null || !dicStatisticVO.getIsAvailable().equals(IS_AVAILABLE)) {
                log.error("统计指标不存在或无效，统计指标字典查询为空，statisticName:{}", statisticName);
                throw new BusinessException(SecErrorCodeConstant.STATISTIC_CAL_NOT_EXIST);
            }
            Integer statisticType = dicStatisticVO.getStatisticType();
            if (INCRE_STATISTIC_TYPE.equals(statisticType)) {
                List<StatisticIncreTotalCalPO> increTotalCalPOList = increTotalCalMapper.selectNum(statisticIndexDTO.getTenantCodeList(), statisticName);
                for (StatisticIncreTotalCalPO increTotalCalPO : increTotalCalPOList) {
                    NumStatisticVO numStatisticVO = new NumStatisticVO();
                    numStatisticVO.setStatisticName(increTotalCalPO.getStatisticName());
                    numStatisticVO.setValue(increTotalCalPO.getTotalStatistic());
                    numStatisticVO.setUnit(statisticNameDicMap.get(increTotalCalPO.getStatisticName()).getUnit());
                    numStatisticVOList.add(numStatisticVO);
                }
                // 非增量指标类型
            } else if (UNINCRE_UNCOMPUTE_STATISTIC_TYPE.equals(statisticType) || UNINCRE_COMPUTE_STATISTIC_TYPE.equals(statisticType)) {
                List<StatisticUnincreCalPO> unIncreTotalCalPOList = unincreCalMapper.selectUnincreCal(statisticIndexDTO.getTenantCodeList(), statisticName);
                for (StatisticUnincreCalPO unIncreTotalCalPO : unIncreTotalCalPOList) {
                    NumStatisticVO numStatisticVO = new NumStatisticVO();
                    numStatisticVO.setStatisticName(unIncreTotalCalPO.getStatisticName());
                    numStatisticVO.setSubStatisticName(unIncreTotalCalPO.getSubStatisticName());
                    numStatisticVO.setValue(unIncreTotalCalPO.getSubStatisticValue());
                    numStatisticVO.setUnit(statisticNameDicMap.get(unIncreTotalCalPO.getStatisticName()).getUnit());
                    numStatisticVOList.add(numStatisticVO);
                }
            }
        }

        return ResultUtil.ok(numStatisticVOList);
    }

    @Override
    public SecRestResponse<List<NumStatisticVO>> getTenantStatisticNum(StatisticIndexDTO statisticIndexDTO) {
        statisticIndexDTO.setTenantCode(LoginUserUtil.getTenantCode());
        return getStatisticNum(statisticIndexDTO);
    }

    /**
     * 平台管理员 tenantCodeList为空，查询所有租户
     * 租户审计员 tenantCodeList包含多个租户，查询组织下租户
     * 租户操作员 tenantCodeList只有一个租户，查询对应租户
     *
     * @param statisticIndexDTO
     * @return
     */
    @Override
    public SecRestResponse<List<PieStatisticVO>> getStatisticPie(StatisticIndexDTO statisticIndexDTO) {
        // 查询结果
        Map<String, PieStatisticVO> pieStatisticVOMap = new HashMap<>();
        // 指标名称字典
        Map<String, DicStatisticVO> statisticNameDicMap = getStatisticNameTypeMap();
        // 饼图状态分布字典
        Map<String, Map<String, String>> pieStatusDic = getPieStatusDic();
        // 租户条件
        List<String> tenantCodeList = statisticIndexDTO.getTenantCodeList();
        // 为null表示非组织审计员
        if (tenantCodeList == null) {
            tenantCodeList = new ArrayList<>();
            if (StringUtils.isNotBlank(statisticIndexDTO.getTenantCode())) {
                tenantCodeList.add(statisticIndexDTO.getTenantCode());
            }
        } else {
            // 组织审计员的租户列表为空时，添加空字符串，防止无法拼接租户条件导致查询到所有租户
            if (CollectionUtils.isEmpty(tenantCodeList)) {
                tenantCodeList.add("");
            }
        }
        // 查询请求中的所有指标
        for (String statisticName : statisticIndexDTO.getStatisticNames()) {
            // 饼图指标类型
            if (pieStatusDic.containsKey(statisticName)) {
                List<StatisticUnincreCalPO> unIncreTotalCalPOList = unincreCalMapper.selectUnincreCal(tenantCodeList, statisticName);
                for (StatisticUnincreCalPO unIncreTotalCalPO : unIncreTotalCalPOList) {
                    PieStatisticVO pieStatisticVO = pieStatisticVOMap.getOrDefault(unIncreTotalCalPO.getStatisticName(), new PieStatisticVO());
                    pieStatisticVO.setStatisticName(unIncreTotalCalPO.getStatisticName());
                    pieStatisticVO.setUnit(statisticNameDicMap.get(unIncreTotalCalPO.getStatisticName()).getUnit());
                    PieData pieData = new PieData();
                    pieData.setName(pieStatusDic.get(statisticName).get(unIncreTotalCalPO.getSubStatisticName()));
                    pieData.setValue(unIncreTotalCalPO.getSubStatisticValue());
                    if (CollectionUtils.isEmpty(pieStatisticVO.getPieDataList())) {
                        pieStatisticVO.setPieDataList(new ArrayList<>());
                    }
                    pieStatisticVO.getPieDataList().add(pieData);
                    pieStatisticVOMap.put(unIncreTotalCalPO.getStatisticName(), pieStatisticVO);
                }
            }
        }
        return ResultUtil.ok(new ArrayList<>(pieStatisticVOMap.values()));
    }

    @Override
    public SecRestResponse<List<PieStatisticVO>> getTenantStatisticPie(StatisticIndexDTO statisticIndexDTO) {
        statisticIndexDTO.setTenantCode(LoginUserUtil.getTenantCode());
        return getStatisticPie(statisticIndexDTO);
    }

    @Override
    public SecRestResponse<MultiLineStatisticVO> getServiceMultiLine(MonitorIndexPeriodDTO monitorIndexPeriodDTO) {
        // 根据周期计算开始时间
        Integer period = monitorIndexPeriodDTO.getPeriod();

        String startTime;
        Long startTimeL = null;
        String endTime;
        if (period == null || period == 0) {
            //todo  暂定30个，5秒
            startTimeL = StatisticDealConvert.getStartTimeBySecond(30, 60);
            startTime = DateUtil.dateToStr(startTimeL);
        } else {
            startTime = DateUtil.dateToStr(StatisticDealConvert.getStartTimeByPeriod(period));
        }

        endTime = DateUtil.dateToStr(DateUtil.getCurrentDateTime());


        // 从statistic_incre_cal表中统计各业务数据
        List<StatisticIncreCalPO> statisticIncreCalPOList = null;
        StatisticTenantServiceDO statisticTenantServiceDO = new StatisticTenantServiceDO();
        statisticTenantServiceDO.setEndTime(endTime);
        statisticTenantServiceDO.setStartTime(startTime);
        String tenantCode = monitorIndexPeriodDTO.getTenantCode();
        if (StringUtils.isNotBlank(tenantCode)) {
            statisticTenantServiceDO.setTenantCode(tenantCode);
        }
        if (period == null || period == 0) {
            List<StatisticIncreRecordPO> statisticIncreRecordPOList = statisticIncreRecordMapper.statisticPeriodServiceSecond(statisticTenantServiceDO, monitorIndexPeriodDTO.getStatisticNameList());
            statisticIncreCalPOList = multiLineStatisticConvert.calRecordToCalList(statisticIncreRecordPOList);
        } else if (period == 1) {
            if (CollectionUtils.isEmpty(monitorIndexPeriodDTO.getStatisticNameList())) {
                statisticIncreCalPOList = statisticIncreCalMapper.statisticPeriodServiceHour(statisticTenantServiceDO, monitorIndexPeriodDTO.getStatisticNameList());
            } else {
                statisticIncreCalPOList = statisticIncreCalMapper.statisticPeriodServiceHourByStatisticName(statisticTenantServiceDO, monitorIndexPeriodDTO.getStatisticNameList());
            }
        } else {

            if (CollectionUtils.isEmpty(monitorIndexPeriodDTO.getStatisticNameList())) {
                statisticIncreCalPOList = statisticIncreCalMapper.statisticPeriodServiceDay(statisticTenantServiceDO, monitorIndexPeriodDTO.getStatisticNameList());
            } else {
                statisticIncreCalPOList = statisticIncreCalMapper.statisticPeriodServiceDayByStatisticName(statisticTenantServiceDO, monitorIndexPeriodDTO.getStatisticNameList());
            }
        }
        // 根据统计结果回填legend
        // 根据统计结果封装MultiLineStatisticDO对象
        MultiLineStatisticDO multiLineStatisticDO = fillMultiLineByStatisticResult(statisticIncreCalPOList, monitorIndexPeriodDTO, startTimeL);
        MultiLineStatisticVO multiLineStatisticVO = multiLineStatisticConvert.convertVo(multiLineStatisticDO);
        return ResultUtil.ok(multiLineStatisticVO);
    }

    /**
     * 租户首页根据租户和周期统计密码服务
     */
    @Override
    public SecRestResponse<MultiLineStatisticVO> getTenantServiceMultiLine(MonitorIndexPeriodDTO monitorIndexPeriodDTO) {
        return getServiceMultiLine(monitorIndexPeriodDTO);
    }

    @Override
    public SecRestResponse<Long> getTenantAllStatistic(StatisticIndexDTO statisticIndexDTO) {

        String tenantCode = null;
        if (StringUtils.isNotBlank(statisticIndexDTO.getTenantCode())) {
            tenantCode = statisticIndexDTO.getTenantCode();
        }

        Long num = increTotalCalMapper.selectAllStatistic(tenantCode);
        return ResultUtil.ok(num);
    }

    /**
     * @param statisticIncreCalPOList
     * @param monitorIndexPeriodDTO
     * @return
     */
    private MultiLineStatisticDO fillMultiLineByStatisticResult(List<StatisticIncreCalPO> statisticIncreCalPOList, MonitorIndexPeriodDTO monitorIndexPeriodDTO, Long startTimeL) {
        MultiLineStatisticDO multiLineStatisticDO = new MultiLineStatisticDO();
        List<DicStatisticVO> dicStatisticVOList = dicStatisticApi.getAll();
        Map<String, DicStatisticVO> statisticMap = dicStatisticVOList.stream().collect(Collectors.toMap(DicStatisticVO::getStatisticName, item -> item));

        // 获取横轴坐标
        List<String> xAxis;
        if (monitorIndexPeriodDTO.getPeriod() == null || monitorIndexPeriodDTO.getPeriod() == 0) {
            //todo  暂定30个，5秒
            xAxis = StatisticDealConvert.getXAxisBySeconds(startTimeL, 30, 60);
        } else {
            xAxis = StatisticDealConvert.getXAxisByPeriod(monitorIndexPeriodDTO.getPeriod());
        }

        // 图例集合
        List<String> legend = constructLengend(statisticIncreCalPOList, monitorIndexPeriodDTO, statisticMap);

        List<String> statisticNameList = new ArrayList<>();
        if (CollectionUtils.isEmpty(monitorIndexPeriodDTO.getStatisticNameList())) {
//            if (statisticIncreCalPOList.isEmpty()) {
//                return multiLineStatisticDO;
//            }
            legend.add("");
            statisticNameList.add("");
        } else {
            statisticNameList = monitorIndexPeriodDTO.getStatisticNameList();
        }

        // 按服务类型封装数据集合
        List<ServiceDataVO> serviceDataVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(statisticIncreCalPOList)) {
            serviceDataVOList = getEmptyServiceDataList(statisticNameList, xAxis, statisticMap);
        } else {
            serviceDataVOList = constructServiceData(statisticIncreCalPOList, monitorIndexPeriodDTO, xAxis, statisticMap);
        }

        multiLineStatisticDO.setServiceDataVOList(serviceDataVOList);
        multiLineStatisticDO.setLegend(legend);
        multiLineStatisticDO.setxAxis(xAxis);
        return multiLineStatisticDO;
    }

    /**
     * @param statisticIncreCalPOList
     * @return
     * @Description: 根据返回结果构造图例集合
     */
    private List<String> constructLengend(List<StatisticIncreCalPO> statisticIncreCalPOList,
                                          MonitorIndexPeriodDTO monitorIndexPeriodDTO,
                                          Map<String, DicStatisticVO> statisticMap) {
        //指标集合为空根据服务类型分组,指标集合不为空根据指标分组
        if (org.springframework.util.CollectionUtils.isEmpty(monitorIndexPeriodDTO.getStatisticNameList())) {
            // 列表中sevice转set去重
            Set<String> legendSet = statisticIncreCalPOList.stream().map(StatisticIncreCalPO::getServiceCode).collect(Collectors.toSet());
            // set通过枚举获取服务名称
            return legendSet.stream().map(serviceCode -> ServiceTypeEnum.byCode(serviceCode) == null ? serviceCode : ServiceTypeEnum.byCode(serviceCode).getName()).collect(Collectors.toList());
        } else {
            return monitorIndexPeriodDTO.getStatisticNameList().stream()
                    .map(statisticName -> statisticMap.containsKey(statisticName) ?
                            statisticMap.get(statisticName).getStatisticShowName()
                            : statisticName).collect(Collectors.toList());
        }
    }

    /**
     * @param statisticIncreCalPOList
     * @param monitorIndexPeriodDTO
     * @return
     * @Description: 封装多曲线数据集合
     */
    private List<ServiceDataVO> constructServiceData(List<StatisticIncreCalPO> statisticIncreCalPOList,
                                                     MonitorIndexPeriodDTO monitorIndexPeriodDTO, List<String> xAxis,
                                                     Map<String, DicStatisticVO> statisticMap) {
        List<ServiceDataVO> serviceDataVOList = new ArrayList<>();

        // 按服务编号分组查分为多个组
        List<List<StatisticIncreCalPO>> serviceGroupList = new ArrayList<>();
        //指标集合为空根据服务类型分组,指标集合不为空根据指标分组
        if (org.springframework.util.CollectionUtils.isEmpty(monitorIndexPeriodDTO.getStatisticNameList())) {

            //合并重复数据
            Map<String, StatisticIncreCalPO> statisticIncreCalPOMap = new LinkedHashMap<>();
            for (StatisticIncreCalPO statisticIncreCalPO : statisticIncreCalPOList) {
                StringBuilder key = new StringBuilder();
                key.append(statisticIncreCalPO.getServiceCode());
                key.append(statisticIncreCalPO.getStatisticYear());
                key.append(statisticIncreCalPO.getStatisticMonth());
                key.append(statisticIncreCalPO.getStatisticDay());
                if (monitorIndexPeriodDTO.getPeriod() == 1) {
                    key.append(statisticIncreCalPO.getStatisticHour());
                } else if (monitorIndexPeriodDTO.getPeriod() == null || monitorIndexPeriodDTO.getPeriod() == 0) {
                    key.append(statisticIncreCalPO.getStatisticHour());
                    key.append(statisticIncreCalPO.getStatisticMinute());
                    key.append(statisticIncreCalPO.getStatisticSecond());
                }

                String keyStr = key.toString();

                log.info("constructServiceData : keyStr={};", keyStr);

                if (statisticIncreCalPOMap.containsKey(keyStr)) {
                    StatisticIncreCalPO item = statisticIncreCalPOMap.get(keyStr);

                    Long total = item.getTotalStatistic() == null ? 0 : item.getTotalStatistic();
                    if (statisticIncreCalPO.getTotalStatistic() != null) {
                        total += statisticIncreCalPO.getTotalStatistic();
                    }
                    item.setTotalStatistic(total);

                    Long success = item.getSuccessStatistic() == null ? 0 : item.getSuccessStatistic();
                    if (statisticIncreCalPO.getSuccessStatistic() != null) {
                        success += statisticIncreCalPO.getSuccessStatistic();
                    }
                    item.setSuccessStatistic(success);

                    Long error = item.getErrorStatistic() == null ? 0 : item.getErrorStatistic();
                    if (statisticIncreCalPO.getErrorStatistic() != null) {
                        error += statisticIncreCalPO.getErrorStatistic();
                    }
                    item.setErrorStatistic(error);
                } else {
                    statisticIncreCalPOMap.put(keyStr, statisticIncreCalPO);
                }
            }

            List<StatisticIncreCalPO> statisticIncreCalPOS = new ArrayList<>(statisticIncreCalPOMap.values());

            statisticIncreCalPOS.stream().collect(Collectors.groupingBy(StatisticIncreCalPO::getServiceCode, Collectors.toList()))
                    .forEach((serviceCode, serviceDataList) -> serviceGroupList.add(serviceDataList));
        } else {
            statisticIncreCalPOList.stream().collect(Collectors.groupingBy(StatisticIncreCalPO::getStatisticName, Collectors.toList()))
                    .forEach((statisticName, serviceDataList) -> serviceGroupList.add(serviceDataList));
        }

        // 循环组封装对象
        for (List<StatisticIncreCalPO> statisticIncreCalPOListByService : serviceGroupList) {
            ServiceDataVO serviceDataVO = new ServiceDataVO();
            //指标集合为空根据服务类型取出name,指标集合不为空name为指标名称
            if (org.springframework.util.CollectionUtils.isEmpty(monitorIndexPeriodDTO.getStatisticNameList())) {
                String serviceCode = statisticIncreCalPOListByService.get(0).getServiceCode();
                String serviceName = ServiceTypeEnum.byCode(serviceCode) == null ? serviceCode : ServiceTypeEnum.byCode(serviceCode).getName();
                serviceDataVO.setName(serviceName);
            } else {
                String statisticName = statisticIncreCalPOListByService.get(0).getStatisticName();
                if (statisticMap.containsKey(statisticName)) {
                    serviceDataVO.setName(statisticMap.get(statisticName).getStatisticShowName());
                } else {
                    serviceDataVO.setName(statisticName);
                }
            }
            List<Long> data = new ArrayList<>();
            for (int n = 0; n < xAxis.size(); n++) {
                // 初始化全0
                data.add(0L);
            }
            int m = 0;
            for (int i = 0; i < statisticIncreCalPOListByService.size(); i++) {
                StatisticIncreCalPO statisticIncreCalPO = statisticIncreCalPOListByService.get(i);
                String xAxisLabel = getXAxisLabel(statisticIncreCalPO, monitorIndexPeriodDTO.getPeriod());
                // 判断与横轴值是否同，若不同，代表缺值，不全该值为0
                if (m >= xAxis.size()) {
                    log.info("constructServiceData: 数据越界了 m:{}, xAxisSize:{}", m, xAxis.size());
                    continue;
                }
                String curXAxis = xAxis.size() > i ? xAxis.get(m) : Strings.EMPTY;
                if (xAxisLabel.equals(curXAxis)) {
                    data.set(m, statisticIncreCalPO.getTotalStatistic());
                    m++;
                } else {
                    log.info("统计缺失横坐标值，自动补全，statisticIncreCalPOListByService：{},横坐标：{}", statisticIncreCalPOListByService, curXAxis);
                    m++;
                    // 循环将空缺补全，直至与当前值相符
                    for (; m < xAxis.size(); m++) {
                        curXAxis = xAxis.get(m);
                        if (xAxisLabel.equals(curXAxis)) {
                            data.set(m, statisticIncreCalPO.getTotalStatistic());
                            m++;
                            break;
                        }
                    }
                    if (m == xAxis.size()) {
                        break;
                    }
                }
            }
            serviceDataVO.setData(data);
            serviceDataVOList.add(serviceDataVO);
        }
        return serviceDataVOList;
    }

    private List<ServiceDataVO> getEmptyServiceDataList(List<String> statisticNameList, List<String> xAxis,
                                                        Map<String, DicStatisticVO> statisticMap) {
        List<ServiceDataVO> serviceDataVOList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(statisticNameList)) {
            statisticNameList.forEach(statisticName -> {
                ServiceDataVO serviceDataVO = new ServiceDataVO();

                if (StringUtils.isNotBlank(statisticName) && statisticMap.containsKey(statisticName)) {
                    serviceDataVO.setName(statisticMap.get(statisticName).getStatisticShowName());
                } else {
                    serviceDataVO.setName(statisticName);
                }

                List<Long> data = new ArrayList<>();
                for (int n = 0; n < xAxis.size(); n++) {
                    // 初始化全0
                    data.add(0L);
                }

                serviceDataVO.setData(data);
                serviceDataVOList.add(serviceDataVO);
            });

            return serviceDataVOList;
        }

        return serviceDataVOList;
    }

    /**
     * @param statisticIncreCalPO
     * @param period
     * @return
     * @Description: 获取当前横坐标标签
     */
    private String getXAxisLabel(StatisticIncreCalPO statisticIncreCalPO, Integer period) {
        if (period == null || 0 == period) {
            //todo 暂定间隔为5S
            int second = statisticIncreCalPO.getStatisticSecond() - (statisticIncreCalPO.getStatisticSecond() % 5);
            String secondStr = second < 10 ? ":0" + second : ":" + second;
            return statisticIncreCalPO.getStatisticHour() + ":" + statisticIncreCalPO.getStatisticMinute() + secondStr;
        } else if (1 == period) {
            return statisticIncreCalPO.getStatisticHour() + ":00";
        } else {
            return statisticIncreCalPO.getStatisticMonth() + "-" + statisticIncreCalPO.getStatisticDay();
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecRestResponse<Integer> insertStatisticIncreCalList(StatisticInsertListDTO statisticInsertListDTO) {
        String tenantCode = statisticInsertListDTO.getTenantCode();
        String appCode = statisticInsertListDTO.getAppCode();
        Long serviceGroupId = statisticInsertListDTO.getServiceGroupId();
        String startTime = statisticInsertListDTO.getStartTime();
        Integer dayNum = statisticInsertListDTO.getDayNum();
        List<ServiceRandom> serviceRandomList = statisticInsertListDTO.getServiceRandomList();

        boolean isClear = statisticInsertListDTO.isClear();

        if (isClear) {
            statisticIncreCalMapper.delete(new QueryWrapper<>());
            statisticIncreTotalCalMapper.delete(new QueryWrapper<>());
        }

        if (serviceRandomList == null) {
            serviceRandomList = new ArrayList<>();
            ServiceRandom kmsDerviceRandom = new ServiceRandom();
            kmsDerviceRandom.setServiceId(123456L);
            kmsDerviceRandom.setServiceCode("kms");
            kmsDerviceRandom.setStatisticName("kmsBusiStatistic");
            kmsDerviceRandom.setStartNum(40);
            kmsDerviceRandom.setEndNum(100);
            serviceRandomList.add(kmsDerviceRandom);

            ServiceRandom pkiServiceRandom = new ServiceRandom();
            pkiServiceRandom.setServiceId(123456L);
            pkiServiceRandom.setServiceCode("pki");
            pkiServiceRandom.setStatisticName("encBusiStatistic");
            pkiServiceRandom.setStartNum(40);
            pkiServiceRandom.setEndNum(100);
            serviceRandomList.add(pkiServiceRandom);

            ServiceRandom svsServiceRandom = new ServiceRandom();
            svsServiceRandom.setServiceId(123456L);
            svsServiceRandom.setServiceCode("svs");
            svsServiceRandom.setStatisticName("signBusiStatistic");
            svsServiceRandom.setStartNum(40);
            svsServiceRandom.setEndNum(100);
            serviceRandomList.add(svsServiceRandom);

            ServiceRandom tsaServiceRandom = new ServiceRandom();
            tsaServiceRandom.setServiceId(123456L);
            tsaServiceRandom.setServiceCode("tsa");
            tsaServiceRandom.setStatisticName("timeBusiStatistic");
            tsaServiceRandom.setStartNum(40);
            tsaServiceRandom.setEndNum(100);
            serviceRandomList.add(tsaServiceRandom);

            ServiceRandom smsServiceRandom = new ServiceRandom();
            smsServiceRandom.setServiceId(123456L);
            smsServiceRandom.setServiceCode("sms");
            smsServiceRandom.setStatisticName("splitBusiStatistic");
            smsServiceRandom.setStartNum(40);
            smsServiceRandom.setEndNum(100);
            serviceRandomList.add(smsServiceRandom);

            ServiceRandom secauthServiceRandom = new ServiceRandom();
            secauthServiceRandom.setServiceId(123456L);
            secauthServiceRandom.setServiceCode("secauth");
            secauthServiceRandom.setStatisticName("secauthBusiStatistic");
            secauthServiceRandom.setStartNum(40);
            secauthServiceRandom.setEndNum(100);
            serviceRandomList.add(secauthServiceRandom);

            ServiceRandom tscServiceRandom = new ServiceRandom();
            tscServiceRandom.setServiceId(123456L);
            tscServiceRandom.setServiceCode("tsc");
            tscServiceRandom.setStatisticName("sealSignBusiStatistic");
            tscServiceRandom.setStartNum(40);
            tscServiceRandom.setEndNum(100);
            serviceRandomList.add(tscServiceRandom);

            ServiceRandom tsc1ServiceRandom = new ServiceRandom();
            tsc1ServiceRandom.setServiceId(123456L);
            tsc1ServiceRandom.setServiceCode("tsc");
            tsc1ServiceRandom.setStatisticName("sealVerifyBusiStatistic");
            tsc1ServiceRandom.setStartNum(40);
            tsc1ServiceRandom.setEndNum(100);
            serviceRandomList.add(tsc1ServiceRandom);
        }

        Map<String, ServiceRandom> serviceRandomMap = serviceRandomList.stream().collect(Collectors.toMap(ServiceRandom::getStatisticName, item -> item));

        Timestamp ts = Timestamp.valueOf(startTime);
        long timestamp = ts.getTime();

        List<String> serviceCodeList = new ArrayList<>();
        serviceCodeList.add("kms");
        serviceCodeList.add("pki");
        serviceCodeList.add("svs");
        serviceCodeList.add("tsa");
        serviceCodeList.add("sms");
        serviceCodeList.add("secauth");
        serviceCodeList.add("tsc");
        serviceCodeList.add("tsc");
        List<String> statisticNameList = new ArrayList<>();
        statisticNameList.add("kmsBusiStatistic");
        statisticNameList.add("encBusiStatistic");
        statisticNameList.add("signBusiStatistic");
        statisticNameList.add("timeBusiStatistic");
        statisticNameList.add("splitBusiStatistic");
        statisticNameList.add("secauthBusiStatistic");
        statisticNameList.add("sealSignBusiStatistic");
        statisticNameList.add("sealVerifyBusiStatistic");

        SecureRandom random = new SecureRandom();
        Integer a = 0;
        Map<String, StatisticIncreTotalCalPO> statisticIncreTotalCalPOMap = new HashMap<>();
        for (int i = 0; i < dayNum * 24; i++) {
            long time = timestamp + 3600L * 1000 * i;
            DateDetail detail = DateUtil.splitDatetime(time);
            for (int j = 0; j < serviceCodeList.size(); j++) {
                StatisticIncreCalPO statisticIncreCalPO = new StatisticIncreCalPO();
                statisticIncreCalPO.setId(IdGenerator.ins().generator());
                statisticIncreCalPO.setTenantCode(tenantCode);
                statisticIncreCalPO.setAppCode(appCode);
                statisticIncreCalPO.setServiceCode(serviceCodeList.get(j));
                statisticIncreCalPO.setServiceGroupId(serviceGroupId);
                statisticIncreCalPO.setStatisticName(statisticNameList.get(j));

                ServiceRandom serviceRandom = serviceRandomMap.get(statisticNameList.get(j));
                int randomNum = random.nextInt(serviceRandom.getEndNum() - serviceRandom.getStartNum());
                statisticIncreCalPO.setTotalStatistic((long) (serviceRandom.getStartNum() + randomNum));
                statisticIncreCalPO.setSuccessStatistic((long) (serviceRandom.getStartNum() + randomNum));
                statisticIncreCalPO.setErrorStatistic(0L);
                statisticIncreCalPO.setServiceId(serviceRandom.getServiceId());
                statisticIncreCalPO.setStatisticYear(detail.getYear());
                statisticIncreCalPO.setStatisticMonth(detail.getMonth());
                statisticIncreCalPO.setStatisticDay(detail.getDay());
                statisticIncreCalPO.setStatisticHour(detail.getHour());
                statisticIncreCalPO.setStatisticMinute(detail.getMinute());
                statisticIncreCalPO.setStatisticSecond(21);
                statisticIncreCalPO.setCreateTime(DateUtil.dateToStr(time));
                Integer num = statisticIncreCalMapper.insert(statisticIncreCalPO);
                a += num;

                StatisticIncreTotalCalPO statisticIncreTotalCalPO = null;
                if (statisticIncreTotalCalPOMap.containsKey(statisticIncreCalPO.getStatisticName())) {
                    statisticIncreTotalCalPO = statisticIncreTotalCalPOMap.get(statisticIncreCalPO.getStatisticName());
                } else {
                    statisticIncreTotalCalPO = new StatisticIncreTotalCalPO();
                    statisticIncreTotalCalPO.setId(IdGenerator.ins().generator());
                    statisticIncreTotalCalPO.setTenantCode(tenantCode);
                    statisticIncreTotalCalPO.setAppCode(appCode);
                    statisticIncreTotalCalPO.setServiceCode(statisticIncreCalPO.getServiceCode());
                    statisticIncreTotalCalPO.setServiceGroupId(serviceGroupId);
                    statisticIncreTotalCalPO.setStatisticName(statisticIncreCalPO.getStatisticName());
                    statisticIncreTotalCalPO.setTotalStatistic(0L);
                    statisticIncreTotalCalPO.setSuccessStatistic(0L);
                    statisticIncreTotalCalPO.setErrorStatistic(0L);
                }

                statisticIncreTotalCalPO.setTotalStatistic(statisticIncreTotalCalPO.getTotalStatistic() + statisticIncreCalPO.getTotalStatistic());
                statisticIncreTotalCalPO.setSuccessStatistic(statisticIncreTotalCalPO.getSuccessStatistic() + statisticIncreCalPO.getSuccessStatistic());
                statisticIncreTotalCalPO.setErrorStatistic(statisticIncreTotalCalPO.getErrorStatistic() + statisticIncreCalPO.getErrorStatistic());
                statisticIncreTotalCalPO.setStatisticYear(statisticIncreCalPO.getStatisticYear());
                statisticIncreTotalCalPO.setStatisticMonth(statisticIncreCalPO.getStatisticMonth());
                statisticIncreTotalCalPO.setStatisticDay(statisticIncreCalPO.getStatisticDay());
                statisticIncreTotalCalPO.setStatisticHour(statisticIncreCalPO.getStatisticHour());
                statisticIncreTotalCalPO.setStatisticMinute(statisticIncreCalPO.getStatisticMinute());
                statisticIncreTotalCalPO.setStatisticSecond(statisticIncreCalPO.getStatisticSecond());

                statisticIncreTotalCalPOMap.put(statisticIncreCalPO.getStatisticName(), statisticIncreTotalCalPO);
            }
        }

        List<StatisticIncreTotalCalPO> statisticIncreTotalCalPOList = new ArrayList<>(statisticIncreTotalCalPOMap.values());
        insertStatisticTotalCalList(statisticIncreTotalCalPOList);

        return ResultUtil.ok(a);
    }

    @Override
    public SecRestResponse<Integer> insertStatisticTotalCalList(StatisticInsertListDTO statisticInsertListDTO) {
        return null;
    }

    public SecRestResponse<Integer> insertStatisticTotalCalList(List<StatisticIncreTotalCalPO> statisticIncreTotalCalPOList) {
        Integer a = 0;
        for (StatisticIncreTotalCalPO item : statisticIncreTotalCalPOList) {

            QueryWrapper<StatisticIncreTotalCalPO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StatisticIncreTotalCalPO::getTenantCode, item.getTenantCode())
                    .eq(StatisticIncreTotalCalPO::getServiceCode, item.getServiceCode())
                    .eq(StatisticIncreTotalCalPO::getStatisticName, item.getStatisticName());

            StatisticIncreTotalCalPO statisticIncreTotalCalPO = statisticIncreTotalCalMapper.selectOne(queryWrapper);

            if (statisticIncreTotalCalPO == null) {
                Integer num = statisticIncreTotalCalMapper.insert(item);
                a += num;
            } else {
                statisticIncreTotalCalPO.setTotalStatistic(statisticIncreTotalCalPO.getTotalStatistic() + item.getTotalStatistic());
                statisticIncreTotalCalPO.setSuccessStatistic(statisticIncreTotalCalPO.getSuccessStatistic() + item.getSuccessStatistic());
                statisticIncreTotalCalPO.setErrorStatistic(statisticIncreTotalCalPO.getErrorStatistic() + item.getErrorStatistic());
                statisticIncreTotalCalPO.setStatisticYear(item.getStatisticYear());
                statisticIncreTotalCalPO.setStatisticMonth(item.getStatisticMonth());
                statisticIncreTotalCalPO.setStatisticDay(item.getStatisticDay());
                statisticIncreTotalCalPO.setStatisticHour(item.getStatisticHour());
                statisticIncreTotalCalPO.setStatisticMinute(item.getStatisticMinute());
                statisticIncreTotalCalPO.setStatisticSecond(item.getStatisticSecond());

                Integer num = statisticIncreTotalCalMapper.updateById(statisticIncreTotalCalPO);
                a += num;
            }
        }
        return ResultUtil.ok(a);
    }

    @Override
    public SecRestResponse<Integer> insertStatisticUnincreCalList(StatisticInsertListDTO statisticInsertListDTO) {
        return null;
    }

}
