package com.sansec.ccsp.extra.business.product.request;

import lombok.Data;

 /**
 * @description : 密码产品附件表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>aw<PERSON>
 * @date : 2023-11-22
 */
@Data
public class ProductAttachmentDTO{
    /**
     * ID
     */
    private Long id;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 存储路径
     */
    private String storagePath;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
}