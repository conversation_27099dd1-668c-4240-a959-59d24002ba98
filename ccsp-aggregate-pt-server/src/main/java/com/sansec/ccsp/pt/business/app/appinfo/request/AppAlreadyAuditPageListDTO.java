package com.sansec.ccsp.pt.business.app.appinfo.request;

import com.sansec.ccsp.app.common.enums.EnumSelectCondition;
import com.sansec.ccsp.app.common.wrapperutil.SelectCondition;
import com.sansec.ccsp.common.pattern.CommonPattern;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
public class AppAlreadyAuditPageListDTO extends SecPageDTO {
    /**
     * 应用标识，模糊搜索
     */
    @Pattern(regexp = CommonPattern.COMMON_CODE, message = "应用标识必须以大小写字母或者数字开头并只能包含大小写字母、数字、特殊字符-_")
    @SelectCondition(EnumSelectCondition.LIKE)
    @Size(max = 50, message = "应用标识长度限制50")
    private String appCode;
    /**
     * 应用名称，模糊搜索
     */
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "应用名称只能包含中文、英文、数字或特殊字符 - _")
    @SelectCondition(EnumSelectCondition.LIKE)
    @Size(max = 50, message = "应用名称长度限制50")
    private String appName;
    /**
     * 应用简称，模糊搜索
     */
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "应用简称只能包含中文、英文、数字或特殊字符 - _")
    @SelectCondition(EnumSelectCondition.LIKE)
    @Size(max = 50, message = "应用简称长度限制50")
    private String appShort;
}
