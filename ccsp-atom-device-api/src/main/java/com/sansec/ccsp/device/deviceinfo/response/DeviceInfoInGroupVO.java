package com.sansec.ccsp.device.deviceinfo.response;

import lombok.Data;


/**
 *
 * @Description: 获取设备组下的设备信息;
 * <AUTHOR>
 * @since 2023/2/21 19:41
 */
@Data
public class DeviceInfoInGroupVO {
   /**
    * 设备ID
    */
   private Long deviceId;
   /**
    * 设备名称
    */
   private String deviceName;
   /**
    * 厂商ID
    */
   private Long vendorId;
   /**
    * 厂商名称
    */
   private String vendorName;
   /**
    * 设备类型ID
    */
   private Long deviceTypeId;
   /**
    * 设备类型名称
    */
   private String deviceTypeName;

   /**
    * 设备组ID
    */
   private Long deviceGroupId;
   /**
    * 授权码
    */
   private String authCode;
   /**
    * 管理IP
    */
   private String mgtIp;
   /**
    * 管理端口
    */
   private Integer mgtPort;
    /**
     * 服务IP
     */
    private String busiIp;
    /**
     * 服务端口
     */
    private Integer busiPort;
    /**
     * 扩展端口
     */
    private Integer busiPortExtend;
    /**
     * 服务连接密码
     */
    private String connectAuthCode;
    /**
     * 是否支持生成主密钥0不支持 1支持
     */
    private Integer supportMainKeyFlag;
    /**
     * 是否生成主密钥(1是，0否);默认0
     */
    private Integer masterKeyFlag;
    /**
     * 是否支持管理接口公钥验签（0：否；1：是）
     */
    private Integer mgtPublickeyFlag;
    /**
     * 是否支持安全管理，0不支持 1支持
     */
    private Integer supportSecManageFlag;
    /**
     * 是否支持对外服务配置，0不支持 1支持
     */
    private Integer supportExternalService;
    /**
     * 设备物理类型；1：云密码机；2：物理机；3：虚拟机
     */
    private Integer familyType;
    /**
     * 区域ID
     */
    private Long regionId;
    /**
     * 运行状态
     */
    private Integer runStatus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
   private String createTime;
   /**
    * 更新人
    */
   private Long updateBy;
   /**
    * 更新时间
    */
   private String updateTime;

}