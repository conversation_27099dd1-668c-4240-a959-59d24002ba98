package com.sansec.ccsp.extra.business.license.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @description : 许可授权记录表（520）;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-4-16
 */
@TableName("LICENSE_RECORD")
@Data
public class LicenseRecordPO extends BasePO{
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 许可类型;1永久授权2按年授权3按月授权
     */
    private Long licenseType;
    /**
     * 申请许可内容
     */
    private String applyLicenseInfo;
    /**
     * 授权许可内容
     */
    private String authLicenseInfo;
    /**
     * 许可签发时间
     */
    private String issueTime;
    /**
     * 完整性校验
     */
    private String hmac;
    /**
     * 序列号
     */
    private String serialId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}