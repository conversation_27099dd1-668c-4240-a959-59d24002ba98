package com.sansec.ccsp.common.business.menu.service;

import com.sansec.ccsp.common.business.menu.entity.SysMenuPO;
import com.sansec.ccsp.common.business.menu.entity.TreeSelect;
import com.sansec.ccsp.common.menu.request.AddSysMenuDTO;
import com.sansec.ccsp.common.menu.request.SysMenuDTO;
import com.sansec.ccsp.common.menu.request.SysMenuPageDTO;
import com.sansec.ccsp.common.menu.response.RouterVO;
import com.sansec.ccsp.common.menu.response.SysMenuVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * @Description: 菜单表;(SYS_MENU)表服务接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
public interface SysMenuService{

    /**
     * 获取有效的菜单信息
     * @return
     */
    List<SysMenuPO> selectList();

    /**
     * 获取有效的菜单信息
     * @return
     */
    List<SysMenuPO> selectListByRemark(String remark);

    /**
     * 分页查询
     *
     * @param sysMenuPageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<List<SysMenuVO>> find(SysMenuPageDTO sysMenuPageDTO);

     /**
     * 新增数据
     *
     * @param sysMenuDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(AddSysMenuDTO sysMenuDTO);
    /**
     * 更新数据
     *
     * @param sysMenuDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(SysMenuDTO sysMenuDTO);

    void editName(Long id, String name);
    /**
     * 通过主键删除数据
     *
     * @param id
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(Long id);
     /**
      * 校验菜单名称是否唯一
      *
      * @param menu 菜单信息
      * @return 结果
      */
      String checkMenuNameUnique(SysMenuPO menu);

     /**
      * 是否存在子节点
      * @param menuId
      * @return 结果 true 存在 false 不存在
      */
     Long hasChildByMenuId(Long menuId);
     /**
      * 查询菜单是否存在角色
      * @param menuId 菜单ID
      * @return 结果 true 存在 false 不存在
      */
     boolean checkMenuExistRole(Long menuId);

     /**
      * 通过id获取菜单信息
      * @param menuId
      * @return
      */
     SysMenuVO getById(Long menuId);

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    List<SysMenuPO> buildMenuTree(List<SysMenuPO> menus);

    /**
     * 设置用户权限
     *
     * @param roleId 角色ID
     * @return 树结构列表
     */
    Integer setMenu(Long roleId);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    List<TreeSelect> buildMenuTreeSelect(List<SysMenuPO> menus);

    /**
     * 构建前端所需要下拉树结构
     * 角色管理界面使用，需要包含按钮
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    List<TreeSelect> buildMenuTreeSelectWithBtn(List<SysMenuPO> menus);

    /**
     * 根据用户查询系统菜单列表
     *
     * @return 菜单列表
     */
     List<SysMenuPO> selectMenuList();
    /**
     * 根据用户ID查询菜单树信息
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
     List<SysMenuPO> selectMenuTreeByUserId(Long userId);
    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
     List<RouterVO> buildMenus(List<SysMenuPO> menus);

    /**
     * 更新菜单权限状态
     */
    SecRestResponse<Object> setStatus(SysMenuDTO sysMenuDTO);
}
