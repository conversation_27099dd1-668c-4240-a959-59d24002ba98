CREATE TABLE "CCSP_STATISTIC"."SYS_TASK_LOG"
(
 "TASK_LOG_ID" BIGINT NOT NULL,
 "TASK_ID" BIGINT NOT NULL,
 "TASK_MESSAGE" VARCHAR(3000) NULL,
 "STATUS" VARCHAR(1) NOT NULL,
 "EXCEPTION_INFO" VARCHAR(6000) NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "TRIGGER_TIME" BIGINT NULL
);

CREATE TABLE "CCSP_STATISTIC"."SYS_TASK"
(
 "TASK_ID" BIGINT NOT NULL,
 "TASK_NAME" VARCHAR(255) NOT NULL,
 "TASK_GROUP" VARCHAR(255) NOT NULL,
 "SERVER_ID" VARCHAR(255) NOT NULL,
 "METHOD_URL" VARCHAR(1000) NULL,
 "JSON_PARAM" TEXT NOT NULL,
 "TASK_STATUS" INT DEFAULT 0
 NOT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_TIME" VARCHAR(30) NULL,
 "REMARK" VARCHAR(300) NULL,
 "TIMEOUT" INT NULL,
 "START_TIME" VARCHAR(255) NULL,
 "END_TIME" VARCHAR(255) NULL,
 "POLICY" VARCHAR(1) DEFAULT '0'
 NOT NULL
);

CREATE TABLE "CCSP_STATISTIC"."SYS_JOB_LOG"
(
 "JOB_LOG_ID" BIGINT NOT NULL,
 "JOB_ID" BIGINT NOT NULL,
 "JOB_MESSAGE" VARCHAR(1500) NULL,
 "STATUS" VARCHAR(1) NOT NULL,
 "EXCEPTION_INFO" VARCHAR(6000) NULL,
 "CREATE_TIME" VARCHAR(30) NOT NULL,
 "TRIGGER_TIME" BIGINT NULL
);

CREATE TABLE "CCSP_STATISTIC"."SYS_JOB"
(
 "JOB_ID" BIGINT NOT NULL,
 "JOB_NAME" VARCHAR(255) NOT NULL,
 "JOB_GROUP" VARCHAR(255) NOT NULL,
 "SERVER_ID" VARCHAR(100) NOT NULL,
 "METHOD_URL" VARCHAR(1500) NULL,
 "JSON_PARAM" TEXT NOT NULL,
 "CRON_EXPRESSION" VARCHAR(255) NULL,
 "MISFIRE_POLICY" VARCHAR(20) DEFAULT '3'
 NULL,
 "CONCURRENT" VARCHAR(1) DEFAULT '1'
 NULL,
 "JOB_STATUS" VARCHAR(1) DEFAULT '0'
 NULL,
 "CREATED_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATED_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL,
 "REMARK" VARCHAR(1500) NULL
);

CREATE TABLE "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"
(
 "ID" BIGINT NOT NULL,
 "REGION_ID" BIGINT NULL,
 "TENANT_CODE" VARCHAR(64) NOT NULL,
 "APP_CODE" VARCHAR(90) NULL,
 "SERVICE_TYPE_ID" BIGINT NOT NULL,
 "SERVICE_GROUP_ID" BIGINT NULL,
 "SERVICE_ID" BIGINT NULL,
 "STATISTIC_NAME" VARCHAR(150) NOT NULL,
 "SUB_STATISTIC_NAME" VARCHAR(150) NOT NULL,
 "SUB_STATISTIC_VALUE" BIGINT NOT NULL,
 "STATISTIC_YEAR" INT NULL,
 "STATISTIC_MONTH" INT NULL,
 "STATISTIC_DAY" INT NULL,
 "STATISTIC_HOUR" INT NULL,
 "STATISTIC_MINUTE" INT NULL,
 "STATISTIC_SECOND" INT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"
(
 "ID" BIGINT NOT NULL,
 "COLLECT_DATE_TIME" VARCHAR(30) NOT NULL,
 "COLLECT_TIME" VARCHAR(30) NOT NULL,
 "CALL_NUM" BIGINT NOT NULL,
 "REGION_CODE" VARCHAR(200) NULL,
 "TENANT_CODE" VARCHAR(100) NOT NULL,
 "APP_CODE" VARCHAR(100) NOT NULL,
 "SERVICE_IP" VARCHAR(100) NOT NULL,
 "SERVICE_PORT" INT NULL,
 "SERVICE_TYPE_CODE" VARCHAR(100) NOT NULL,
 "SERVICE_URL" VARCHAR(200) NULL,
 "AVG_COST_TIME" BIGINT NULL,
 "MAX_COST_TIME" BIGINT NULL,
 "SERVICE_FLOW_NUM" BIGINT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NOT NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"
(
 "ID" BIGINT NOT NULL,
 "COLLECT_DATE_TIME" VARCHAR(90) NOT NULL,
 "COLLECT_TIME" VARCHAR(90) NOT NULL,
 "CALL_NUM" BIGINT NOT NULL,
 "REGION_CODE" VARCHAR(200) NULL,
 "TENANT_CODE" VARCHAR(100) NOT NULL,
 "APP_CODE" VARCHAR(100) NOT NULL,
 "SERVICE_IP" VARCHAR(100) NOT NULL,
 "SERVICE_PORT" INT NULL,
 "SERVICE_TYPE_CODE" VARCHAR(100) NOT NULL,
 "SERVICE_URL" VARCHAR(200) NULL,
 "AVG_COST_TIME" BIGINT NULL,
 "MAX_COST_TIME" BIGINT NULL,
 "SERVICE_FLOW_NUM" BIGINT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NOT NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"
(
 "ID" BIGINT NOT NULL,
 "REGION_ID" BIGINT NULL,
 "TENANT_CODE" VARCHAR(64) NOT NULL,
 "APP_CODE" VARCHAR(90) NULL,
 "SERVICE_CODE" VARCHAR(50) NOT NULL,
 "SERVICE_GROUP_ID" BIGINT NULL,
 "STATISTIC_NAME" VARCHAR(150) NOT NULL,
 "TOTAL_STATISTIC" BIGINT NOT NULL,
 "SUCCESS_STATISTIC" BIGINT NULL,
 "ERROR_STATISTIC" BIGINT NULL,
 "STATISTIC_YEAR" INT NOT NULL,
 "STATISTIC_MONTH" INT NOT NULL,
 "STATISTIC_DAY" INT NOT NULL,
 "STATISTIC_HOUR" INT NOT NULL,
 "STATISTIC_MINUTE" INT NULL,
 "STATISTIC_SECOND" INT NULL,
 "HMAC" VARCHAR(255) NULL,
 "CREATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"
(
 "ID" BIGINT NOT NULL,
 "REGION_ID" BIGINT NULL,
 "TENANT_CODE" VARCHAR(64) NOT NULL,
 "APP_CODE" VARCHAR(90) NULL,
 "SERVICE_CODE" VARCHAR(50) NOT NULL,
 "SERVICE_GROUP_ID" BIGINT NULL,
 "SERVICE_ID" BIGINT NULL,
 "STATISTIC_NAME" VARCHAR(150) NOT NULL,
 "TOTAL_STATISTIC" BIGINT NOT NULL,
 "SUCCESS_STATISTIC" BIGINT NULL,
 "ERROR_STATISTIC" BIGINT NULL,
 "STATISTIC_YEAR" INT NOT NULL,
 "STATISTIC_MONTH" INT NOT NULL,
 "STATISTIC_DAY" INT NOT NULL,
 "STATISTIC_HOUR" INT NOT NULL,
 "STATISTIC_MINUTE" INT NULL,
 "STATISTIC_SECOND" INT NULL,
 "HMAC" VARCHAR(255) NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "TIME_STAMP" BIGINT NULL
);

CREATE TABLE "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"
(
 "ID" DECIMAL NOT NULL,
 "REGION_ID" BIGINT NULL,
 "TENANT_CODE" VARCHAR(64) NOT NULL,
 "APP_CODE" VARCHAR(90) NULL,
 "SERVICE_CODE" VARCHAR(50) NOT NULL,
 "SERVICE_GROUP_ID" BIGINT NULL,
 "SERVICE_ID" BIGINT NULL,
 "STATISTIC_NAME" VARCHAR(150) NOT NULL,
 "TOTAL_STATISTIC" BIGINT NOT NULL,
 "SUCCESS_STATISTIC" BIGINT NULL,
 "ERROR_STATISTIC" BIGINT NULL,
 "STATISTIC_YEAR" INT NOT NULL,
 "STATISTIC_MONTH" INT NOT NULL,
 "STATISTIC_DAY" INT NOT NULL,
 "STATISTIC_HOUR" INT NOT NULL,
 "STATISTIC_MINUTE" INT NULL,
 "STATISTIC_SECOND" INT NULL,
 "HMAC" VARCHAR(255) NULL,
 "CREATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"
(
 "ID" BIGINT NOT NULL,
 "COLLECT_DATE_TIME" VARCHAR(90) NOT NULL,
 "COLLECT_TIME" VARCHAR(90) NOT NULL,
 "CALL_NUM" BIGINT NOT NULL,
 "REGION_CODE" VARCHAR(200) NULL,
 "TENANT_CODE" VARCHAR(100) NOT NULL,
 "APP_CODE" VARCHAR(100) NOT NULL,
 "SERVICE_IP" VARCHAR(100) NOT NULL,
 "SERVICE_PORT" INT NULL,
 "SERVICE_TYPE_CODE" VARCHAR(100) NOT NULL,
 "SERVICE_URL" VARCHAR(200) NULL,
 "AVG_COST_TIME" BIGINT NULL,
 "MAX_COST_TIME" BIGINT NULL,
 "SERVICE_FLOW_NUM" BIGINT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NOT NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."STATISTIC_DAY_DATA"
(
 "ID" BIGINT NOT NULL,
 "COLLECT_DATE_TIME" VARCHAR(90) NULL,
 "COLLECT_TIME" VARCHAR(90) NOT NULL,
 "CALL_NUM" BIGINT NOT NULL,
 "REGION_CODE" VARCHAR(200) NULL,
 "TENANT_CODE" VARCHAR(100) NOT NULL,
 "APP_CODE" VARCHAR(100) NOT NULL,
 "SERVICE_IP" VARCHAR(100) NOT NULL,
 "SERVICE_PORT" INT NULL,
 "SERVICE_TYPE_CODE" VARCHAR(100) NOT NULL,
 "SERVICE_URL" VARCHAR(200) NULL,
 "AVG_COST_TIME" BIGINT NULL,
 "MAX_COST_TIME" BIGINT NULL,
 "SERVICE_FLOW_NUM" BIGINT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NOT NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"
(
 "ID" BIGINT NOT NULL,
 "REGION_CODE" VARCHAR(200) NULL,
 "TENANT_CODE" VARCHAR(100) NULL,
 "APP_CODE" VARCHAR(100) NULL,
 "SERVICE_TYPE_CODE" VARCHAR(100) NULL,
 "TARGET_CODE" VARCHAR(200) NOT NULL,
 "TARGET_CHILD_CODE" VARCHAR(200) NOT NULL,
 "MULTIPLE_VAL" BIGINT NOT NULL,
 "MAX_VAL" BIGINT NULL,
 "FLOAT_VAL" BIGINT DEFAULT 0
 NULL
);

CREATE TABLE "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"
(
 "ID" BIGINT NOT NULL,
 "CALL_NUM" BIGINT NOT NULL,
 "COLLECT_NUM" BIGINT NOT NULL,
 "REGION_CODE" VARCHAR(200) NULL,
 "TENANT_CODE" VARCHAR(100) NOT NULL,
 "APP_CODE" VARCHAR(100) NOT NULL,
 "SERVICE_IP" VARCHAR(100) NOT NULL,
 "SERVICE_PORT" INT NULL,
 "SERVICE_TYPE_CODE" VARCHAR(100) NOT NULL,
 "SERVICE_URL" VARCHAR(200) NULL,
 "AVG_COST_TIME" BIGINT NULL,
 "MAX_COST_TIME" BIGINT NULL,
 "SERVICE_FLOW_NUM" BIGINT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NOT NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"
(
 "ID" BIGINT NOT NULL,
 "TEMPLATE_CODE" VARCHAR(255) NOT NULL,
 "SCREEN_NAME" VARCHAR(255) NOT NULL,
 "SCREEN_URL" VARCHAR(600) NOT NULL,
 "VALID_FLAG" INT NOT NULL,
 "EXTEND" VARCHAR(255) NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"
(
 "ID" BIGINT NOT NULL,
 "TEMPLATE_NAME" VARCHAR(255) NOT NULL,
 "TEMPLATE_CODE" VARCHAR(255) NOT NULL,
 "INDEX_ID" BIGINT NOT NULL,
 "INDEX_TYPE" VARCHAR(255) NOT NULL,
 "INVALID_FLAG" INT NOT NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL,
 "REMARK" VARCHAR(1000) NULL
);

CREATE TABLE "CCSP_STATISTIC"."SCREEN_STATISTIC"
(
 "ID" BIGINT NOT NULL,
 "INDEX_TYPE" VARCHAR(255) NOT NULL,
 "STATISTIC_TYPE" INT NULL,
 "STATISTIC_NAME" VARCHAR(150) NULL,
 "STATISTIC_FIRST" VARCHAR(255) NULL,
 "STATISTIC_SECOND" VARCHAR(255) NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."SCREEN_INDEX"
(
 "ID" BIGINT NOT NULL,
 "NAME" VARCHAR(255) NOT NULL,
 "TEMPLATE" TEXT NOT NULL,
 "INVALID_FLAG" INT DEFAULT 0
 NOT NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL,
 "REMARK" VARCHAR(1000) NULL
);

CREATE TABLE "CCSP_STATISTIC"."MONITOR_CONNECTION"
(
 "ID" BIGINT NOT NULL,
 "SOURCE_TYPE" INT NOT NULL,
 "DEVICE_TYPE" INT NOT NULL,
 "CONNECTION_NAME" VARCHAR(300) NOT NULL,
 "CONNECTION_STATUS" INT NOT NULL,
 "TENANT_CODE" VARCHAR(64) NULL,
 "IP" VARCHAR(20) NOT NULL,
 "PORT" INT NOT NULL,
 "REGION_ID" BIGINT NULL,
 "REMARK" VARCHAR(1000) NULL,
 "CREATE_BY" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

CREATE TABLE "CCSP_STATISTIC"."LOG_OPERATE"
(
 "ID" BIGINT NOT NULL,
 "TENANT_ID" BIGINT NOT NULL,
 "RESULT" VARCHAR(10) NOT NULL,
 "BUSI_TYPE_ID" BIGINT NULL,
 "CREATE_TIME" VARCHAR(30) NOT NULL,
 "OPER_IP" VARCHAR(60) NULL,
 "OPER_NAME" VARCHAR(150) NULL,
 "MODULE_NAME" VARCHAR(150) NULL,
 "OPER_CONTENT" VARCHAR(600) NULL,
 "HMAC" VARCHAR(255) NULL,
 "AUDIT_STATUS" INT NULL,
 "ERROR_MSG" VARCHAR(1000) NULL,
 "LOG_TYPE" INT NULL,
 "EXTEND1" VARCHAR(100) NULL,
 "EXTEND2" VARCHAR(100) NULL
);

CREATE TABLE "CCSP_STATISTIC"."DIC_SEAL_STATUS"
(
 "ID" BIGINT NOT NULL,
 "D_NAME" VARCHAR(150) NOT NULL,
 "D_VALUE" VARCHAR(50) NOT NULL,
 "IS_AVAILABLE" INT NOT NULL
);

CREATE TABLE "CCSP_STATISTIC"."DIC_KEY_STATUS"
(
 "ID" BIGINT NOT NULL,
 "D_NAME" VARCHAR(150) NOT NULL,
 "D_VALUE" VARCHAR(50) NOT NULL,
 "IS_AVAILABLE" INT NOT NULL
);

CREATE TABLE "CCSP_STATISTIC"."DIC_ALARM_TYPE"
(
 "ID" BIGINT NOT NULL,
 "CODE" VARCHAR(255) NOT NULL,
 "NAME" VARCHAR(255) NOT NULL,
 "LEVEL" INT NOT NULL,
 "TENANT_VISIBLE" INT NOT NULL
);

CREATE TABLE "CCSP_STATISTIC"."ALARM_OID"
(
 "ID" BIGINT NOT NULL,
 "ALARM_CODE" VARCHAR(50) NULL,
 "OID" VARCHAR(50) NULL,
 "NAME" VARCHAR(50) NULL,
 "DESC" VARCHAR(50) NULL
);

CREATE TABLE "CCSP_STATISTIC"."ALARM_INFO"
(
 "ID" BIGINT NOT NULL,
 "TENANT_ID" BIGINT NULL,
 "REGION_ID" BIGINT NULL,
 "ALARM_CODE" VARCHAR(255) NOT NULL,
 "SOURCE_ID" BIGINT NULL,
 "SOURCE_IP" VARCHAR(20) NULL,
 "CONTENT" VARCHAR(255) NULL,
 "TIMES" INT NULL,
 "FIRST_TIME" VARCHAR(30) NULL,
 "LAST_TIME" VARCHAR(30) NULL,
 "STATUS" INT NULL
);

CREATE TABLE "CCSP_STATISTIC"."ALARM_HISTORY"
(
 "ID" BIGINT NOT NULL,
 "TENANT_ID" BIGINT NULL,
 "TENANT_NAME" VARCHAR(255) NULL,
 "REGION_ID" BIGINT NULL,
 "REGION_NAME" VARCHAR(255) NULL,
 "ALARM_CODE" VARCHAR(255) NOT NULL,
 "SOURCE_IP" VARCHAR(20) NULL,
 "SOURCE_NAME" VARCHAR(255) NULL,
 "CONTENT" VARCHAR(255) NOT NULL,
 "TIMES" INT NULL,
 "FIRST_TIME" VARCHAR(30) NULL,
 "LAST_TIME" VARCHAR(30) NULL,
 "STATUS" INT NULL,
 "REMARK" VARCHAR(1000) NULL,
 "UPDATE_BY" BIGINT NULL,
 "UPDATE_TIME" VARCHAR(30) NULL
);

INSERT INTO "CCSP_STATISTIC"."ALARM_OID"("ID","ALARM_CODE","OID","NAME","DESC") VALUES('1','service_status','.*******.4.1.746379.*******','sansec.ccsp.service.status','服务状态');
INSERT INTO "CCSP_STATISTIC"."ALARM_OID"("ID","ALARM_CODE","OID","NAME","DESC") VALUES('2','service_status','.*******.4.1.746379.*******','sansec.ccsp.service.id','服务ID');
INSERT INTO "CCSP_STATISTIC"."ALARM_OID"("ID","ALARM_CODE","OID","NAME","DESC") VALUES('3','device_status','.*******.4.1.746379.*******','sansec.ccsp.device.status','设备状态');
INSERT INTO "CCSP_STATISTIC"."ALARM_OID"("ID","ALARM_CODE","OID","NAME","DESC") VALUES('4','device_status','.*******.4.1.746379.*******','sansec.ccsp.device.id','设备ID');
INSERT INTO "CCSP_STATISTIC"."ALARM_OID"("ID","ALARM_CODE","OID","NAME","DESC") VALUES('5','permission','.*******.4.1.746379.3.2.1.1','sansec.ccsp.permission.time','许可剩余时间');
INSERT INTO "CCSP_STATISTIC"."ALARM_OID"("ID","ALARM_CODE","OID","NAME","DESC") VALUES('6','permission','.*******.4.1.746379.3.2.1.2','sansec.ccsp.permission.id','服务ID');
INSERT INTO "CCSP_STATISTIC"."ALARM_OID"("ID","ALARM_CODE","OID","NAME","DESC") VALUES('7','product_quota','.*******.4.1.746379.4.2.1.1','sansec.ccsp.product.time','配额剩余时间');
INSERT INTO "CCSP_STATISTIC"."ALARM_OID"("ID","ALARM_CODE","OID","NAME","DESC") VALUES('8','product_quota','.*******.4.1.746379.4.2.1.2','sansec.ccsp.product.id','配额ID');

INSERT INTO "CCSP_STATISTIC"."DIC_ALARM_TYPE"("ID","CODE","NAME","LEVEL","TENANT_VISIBLE") VALUES('1','cpu','CPU使用率',5,0);
INSERT INTO "CCSP_STATISTIC"."DIC_ALARM_TYPE"("ID","CODE","NAME","LEVEL","TENANT_VISIBLE") VALUES('2','memory','内存使用率',5,0);
INSERT INTO "CCSP_STATISTIC"."DIC_ALARM_TYPE"("ID","CODE","NAME","LEVEL","TENANT_VISIBLE") VALUES('3','disk','磁盘使用率',5,0);
INSERT INTO "CCSP_STATISTIC"."DIC_ALARM_TYPE"("ID","CODE","NAME","LEVEL","TENANT_VISIBLE") VALUES('4','service_status','服务状态',3,1);
INSERT INTO "CCSP_STATISTIC"."DIC_ALARM_TYPE"("ID","CODE","NAME","LEVEL","TENANT_VISIBLE") VALUES('5','device_status','设备状态',4,0);
INSERT INTO "CCSP_STATISTIC"."DIC_ALARM_TYPE"("ID","CODE","NAME","LEVEL","TENANT_VISIBLE") VALUES('6','permission','许可有效期',1,1);
INSERT INTO "CCSP_STATISTIC"."DIC_ALARM_TYPE"("ID","CODE","NAME","LEVEL","TENANT_VISIBLE") VALUES('7','product_quota','配额有效期',2,1);

INSERT INTO "CCSP_STATISTIC"."DIC_KEY_STATUS"("ID","D_NAME","D_VALUE","IS_AVAILABLE") VALUES('1','预激活','1',1);
INSERT INTO "CCSP_STATISTIC"."DIC_KEY_STATUS"("ID","D_NAME","D_VALUE","IS_AVAILABLE") VALUES('2','激活','2',1);
INSERT INTO "CCSP_STATISTIC"."DIC_KEY_STATUS"("ID","D_NAME","D_VALUE","IS_AVAILABLE") VALUES('3','注销','3',1);
INSERT INTO "CCSP_STATISTIC"."DIC_KEY_STATUS"("ID","D_NAME","D_VALUE","IS_AVAILABLE") VALUES('4','销毁','5',1);

INSERT INTO "CCSP_STATISTIC"."DIC_SEAL_STATUS"("ID","D_NAME","D_VALUE","IS_AVAILABLE") VALUES('1','停用','0',1);
INSERT INTO "CCSP_STATISTIC"."DIC_SEAL_STATUS"("ID","D_NAME","D_VALUE","IS_AVAILABLE") VALUES('2','启用','1',1);
INSERT INTO "CCSP_STATISTIC"."DIC_SEAL_STATUS"("ID","D_NAME","D_VALUE","IS_AVAILABLE") VALUES('3','删除','2',1);

INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('1','kms-密钥管理模板','{"type":"pie1","title":"密钥管理","updateFrequency":10,"elements":[[{"chartOption":{"type":"pie"},"unit":"个","name":"预激活","key":"yujihuo""title":"密钥总数"},{"chartOption":{"type":"pie"},"unit":"个","name":"激活","key":"jihuo""title":"密钥总数"},{"chartOption":{"type":"pie"},"unit":"个","name":"注销","key":"zhuxiao""title":"密钥总数"},{"chartOption":{"type":"pie"},"unit":"个","name":"销毁","key":"xiaohui""title":"密钥总数"}]],"valueList":[[{"key":"yujihuo","value":"${kmsKeyStateStatistic_1}"},{"key":"jihuo","value":"${kmsKeyStateStatistic_2}"},{"key":"zhuxiao","value":"${kmsKeyStateStatistic_3}"},{"key":"xiaohui","value":"${kmsKeyStateStatistic_5}"}]]}',0,null,null,null,null,'kmsBusiStatistic_totalError 密钥调用错误次数');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('2','secauth-动态令牌模板','{"type":"pie2","title":"动态令牌","updateFrequency":10,"elements":[[{"chartOption":{"type":"pie"},"unit":"次","name":"成功次数","key":"success","title":"总认证次数"},{"chartOption":{"type":"pie"},"unit":"次","name":"失败次数","key":"fail","title":"总认证次数"}]],"valueList":[[{"key":"success","value":"${secauthBusiStatistic_totalNumSuccess}"},{"key":"fail","value":"${secauthBusiStatistic_totalError}"}]]}',0,null,null,null,null,'secauth');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('3','pki-数据加解密模板','{"type":"iconGroup","title":"数据加解密","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"密钥数量","value":"${encKeyNumStatistic_keyNums}","colorType":"blue","key":"keyNum","unit":"个"}]},{"elementType":"hexagon1","layout":"row","elementConfig":[{"name":"总调用次数","value":"${encBusiStatistic_totals}","icon":"lock","colorType":"blue","key":"busiNum","unit":"次"}]}]}',0,null,null,null,null,'pki');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('4','secdb-数据库加密模板','{"type":"iconGroup","title":"数据库加密","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"密钥数量","value":"${secDbKeyNumStatistic_keyNums}","colorType":"blue","key":"keyNum","unit":"个"}]},{"elementType":"hexagon1","layout":"row","elementConfig":[{"name":"加密数据库","value":"${dbStatistic_dbNum}","icon":"database","colorType":"blue","key":"dbNum","unit":"个"},{"colorType":"gold","name":"加密表","value":"${dbStatistic_dbTableNum}","icon":"search","key":"dbTableNum","unit":"个"}]}]}',0,null,null,null,null,'secdb');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('5','secstorage-文件加密模板','{"type":"iconGroup","title":"文件加密","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"NAS服务器","value":"${serverStatistic_nasServerNum}","colorType":"blue","key":"signAppCertNum","unit":"个"}]},{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"文件服务器","value":"${serverStatistic_fileServerNum}","colorType":"purple","key":"signUserCertStatistic","unit":"个"}]}]}',0,null,null,null,null,'secstorage');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('6','vpn-VPN服务模板','{"type":"iconGroup","title":"VPN服务","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"并发连接数","value":"${vpnConcurrentConnectNumStatistic_vpnConcurrentConnectNumStatistic}","colorType":"blue","key":"ConcurrentConnectNum","unit":"个"},{"name":"新建连接数","value":"${vpnNewConnectNumStatistic_vpnNewConnectNumStatistic}","colorType":"purple","key":"NewConnectNum","unit":"个"}]},{"elementType":"noIcon","layout":"column","elementConfig":[{"name":"吞吐量-收","value":"${vpnThroughputRNumStatistic_vpnThroughputRNumStatistic}","colorType":"blue","key":"RNum","unit":"Kbps"},{"name":"吞吐量-发","value":"${vpnThroughputSNumStatistic_vpnThroughputSNumStatistic}","colorType":"gold","key":"SNum","unit":"Kbps"}]}]}',0,null,null,null,null,'vpn');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('7','svs-签名验签模板','{"type":"iconGroup","title":"签名验签","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"应用证书数量","value":"${signAppCertStatistic_certNumbers}","colorType":"blue","key":"signAppCertNum","unit":"个"}]},{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"用户证书数量","value":"${signUserCertStatistic_certNumbers}","colorType":"purple","key":"signUserCertStatistic","unit":"个"}]}]}',0,null,null,null,null,'svs');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('8','tsc-电子签章模板','{"type":"iconGroup","title":"电子签章","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"签章数量","value":"${sealNumStatistic_sealNum}","colorType":"blue","key":"sealNum","unit":"个"}]},{"elementType":"hexagon1","layout":"row","elementConfig":[{"name":"总签署次数","value":"${sealSignBusiStatistic_totals}","icon":"draft","colorType":"blue","key":"sealSignNum","unit":"次"},{"name":"总验签次数","value":"${sealVerifyBusiStatistic_totals}","icon":"search","colorType":"gold","key":"sealVerifyNum","unit":"次"}]}]}',0,null,null,null,null,'tsc');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('9','sms-协同签名模板','{"type":"iconGroup","title":"协同签名","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"用户数量","value":"${userNumStatistic_userNum}","colorType":"blue","key":"userNum","unit":"个"}]},{"elementType":"hexagon1","layout":"row","elementConfig":[{"name":"总调用次数","value":"${splitBusiStatistic_totals}","icon":"terminal","colorType":"blue","key":"splitNum","unit":"次"}]}]}',0,null,null,null,null,'sms');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('10','tsa-时间戳模板','{"type":"iconGroup","title":"时间戳","updateFrequency":10,"elements":[{"elementType":"diamond1","layout":"row","elementConfig":[{"name":"总调用次数","value":"${timeBusiStatistic_totals}","colorType":"blue","key":"timeBusiStatistic","unit":"次"}]}]}',0,null,null,null,null,'tsa');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('11','center-全租户模板','{"type":"centerPanel","title":"密码服务管理平台","updateFrequency":5,"elements":[{"name":"设备概况","value":"${device_sum}","icon":"device","key":"device_sum","unit":"台","children":[{"name":"已使用","value":"${device_used}","icon":"device","unit":"台"},{"name":"空闲","value":"${device_unused}","icon":"device","unit":"台"},{"name":"异常","value":"${device_error}","icon":"device","unit":"台"}]},{"name":"服务概况","value":"${service_sum}","icon":"service","key":"service_sum","unit":"个","children":[{"name":"运行中","value":"${service_run}","icon":"device","unit":"个"},{"name":"初始化中","value":"${service_start}","icon":"device","unit":"个"},{"name":"异常","value":"${service_error}","icon":"device","unit":"个"}]},{"name":"租户数量","value":"${tenant_num}","icon":"user","key":"tenant_num","unit":"个"},{"name":"许可证数量","value":"${license_num}","icon":"license","key":"license_num","unit":"个"}],"enableAlarm":true}',0,null,null,null,null,'center');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('12','center-tenant-单个租户模板','{"type":"centerPanel","title":"密码服务管理平台","updateFrequency":5,"elements":[{"name":"设备概况","value":"${device_sum}","icon":"device","key":"device_sum","unit":"台","children":[{"name":"已使用","value":"${device_used}","icon":"device","unit":"台"},{"name":"空闲","value":"${device_unused}","icon":"device","unit":"台"},{"name":"异常","value":"${device_error}","icon":"device","unit":"台"}]},{"name":"服务概况","value":"${service_sum}","icon":"service","key":"service_sum","unit":"个","children":[{"name":"运行中","value":"${service_run}","icon":"device","unit":"个"},{"name":"初始化中","value":"${service_start}","icon":"device","unit":"个"},{"name":"异常","value":"${service_error}","icon":"device","unit":"个"}]}],"enableAlarm":true}',0,null,null,null,null,'center-tenant');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('51','kms-密钥管理模板','{"title":"密钥管理服务","data":[{"unit":"个","name":"密钥数量","value":"${kmsKeyNumStatistic_keyNums}","data":[{"unit":"个","name":"预激活","value":"${kmsKeyStateStatistic_1}"},{"unit":"个","name":"激活","value":"${kmsKeyStateStatistic_2}"},{"unit":"个","name":"注销","value":"${kmsKeyStateStatistic_3}"},{"unit":"个","name":"销毁","value":"${kmsKeyStateStatistic_5}"}]}]}',0,null,null,null,null,'kmsBusiStatistic_totalError 密钥调用错误次数');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('52','secauth-动态令牌模板','{"title":"动态令牌服务","data":[{"unit":"次","name":"动态令牌总次数","value":"${secauthBusiStatistic_totals}"}]}',0,null,null,null,null,'secauth');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('53','pki-数据加解密模板','{"title":"加解密服务","data":[{"name":"密钥数量","value":"${encKeyNumStatistic_keyNums}","unit":"个"},{"name":"总调用次数","value":"${encBusiStatistic_totals}","unit":"次"}]}',0,null,null,null,null,'pki');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('54','secdb-数据库加密模板','{"title":"数据库加密服务","data":[{"name":"密钥数量","value":"${secDbKeyNumStatistic_keyNums}","unit":"个","data":[{"name":"加密数据库","value":"${dbStatistic_dbNum}","unit":"个"},{"name":"加密表","value":"${dbStatistic_dbTableNum}","unit":"个"}]}]}',0,null,null,null,null,'secdb');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('55','secstorage-文件加密模板','{"title":"文件加密服务","data":[{"name":"NAS服务器","value":"${serverStatistic_nasServerNum}","unit":"个"},{"name":"文件服务器","value":"${serverStatistic_fileServerNum}","unit":"个"}]}',0,null,null,null,null,'secstorage');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('56','vpn-VPN服务模板','{"title":"SSLVPN加密通道服务","data":[{"name":"并发连接数","value":"${vpnConcurrentConnectNumStatistic_vpnConcurrentConnectNumStatistic}","unit":"个"},{"name":"新建连接数","value":"${vpnNewConnectNumStatistic_vpnNewConnectNumStatistic}","unit":"个"}]}',0,null,null,null,null,'vpn');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('57','svs-签名验签模板','{"title":"签名验签服务","data":[{"name":"应用证书数量","value":"${signAppCertStatistic_certNumbers}","unit":"个"},{"name":"用户证书数量","value":"${signUserCertStatistic_certNumbers}","unit":"个"}]}',0,null,null,null,null,'svs');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('58','tsc-电子签章模板','{"title":"电子签章服务","data":[{"name":"签章数量","value":"${sealNumStatistic_sealNum}","unit":"个","data":[{"name":"总签署次数","value":"${sealSignBusiStatistic_totals}","unit":"次"},{"name":"总验签次数","value":"${sealVerifyBusiStatistic_totals}","unit":"次"}]}]}',0,null,null,null,null,'tsc');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('59','sms-协同签名模板','{"title":"协同签名服务","data":[{"name":"用户数量","value":"${userNumStatistic_userNum}","unit":"个"},{"name":"总调用次数","value":"${splitBusiStatistic_totals}","unit":"次"}]}',0,null,null,null,null,'sms');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('60','tsa-时间戳模板','{"title":"时间戳服务","data":[{"name":"总调用次数","value":"${timeBusiStatistic_totals}","unit":"次"}]}',0,null,null,null,null,'tsa');
INSERT INTO "CCSP_STATISTIC"."SCREEN_INDEX"("ID","NAME","TEMPLATE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('61','ca-数字证书认证模板','{"title":"数字证书认证服务","data":[{"name":"用户证书数量","value":"${caUserCertStatistic_certNums}","unit":"个"}]}',0,null,null,null,null,'ca');

INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('1','kms',1,'密钥调用总次数','kmsBusiStatistic','totals',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('2','kms',1,'密钥调用成功次数','kmsBusiStatistic','totalNumSuccess',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('3','kms',1,'密钥调用错误次数','kmsBusiStatistic','totalError',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('4','kms',2,'密钥总数统计','kmsKeyNumStatistic','keyNums',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('5','kms',2,'密钥状态分布-预创建','kmsKeyStateStatistic','1',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('6','kms',2,'密钥状态分布-创建','kmsKeyStateStatistic','2',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('7','kms',2,'密钥状态分布-注销','kmsKeyStateStatistic','3',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('8','kms',2,'密钥状态分布-销毁','kmsKeyStateStatistic','5',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('11','secauth',1,'动态令牌总次数','secauthBusiStatistic','totals',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('12','secauth',1,'动态令牌成功总次数','secauthBusiStatistic','totalNumSuccess',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('13','secauth',1,'动态令牌失败总次数','secauthBusiStatistic','totalError',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('21','pki',1,'数据加解密总次数','encBusiStatistic','totals',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('22','pki',1,'数据加解密成功总次数','encBusiStatistic','totalNumSuccess',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('23','pki',1,'数据加解密错误总次数','encBusiStatistic','totalError',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('24','pki',2,'数据加解密密钥总数','encKeyNumStatistic','keyNums',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('31','svs',1,'签名验签调用总次数','signBusiStatistic','totals',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('32','svs',1,'签名验签调用成功总次数','signBusiStatistic','totalNumSuccess',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('33','svs',1,'签名验签调用失败总次数','signBusiStatistic','totalError',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('34','svs',2,'签名验签应用证书数量','signAppCertStatistic','certNumbers',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('35','svs',2,'签名验签用户证书数量','signUserCertStatistic','certNumbers',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('41','tsa',1,'时间戳服务调用总次数','timeBusiStatistic','totals',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('42','tsa',1,'时间戳服务调用成功总次数','timeBusiStatistic','totalNumSuccess',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('43','tsa',1,'时间戳服务调用失败总次数','timeBusiStatistic','totalError',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('51','sms',1,'协同签名总签名总次数','splitBusiStatistic','totals',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('52','sms',1,'协同签名总签名成功总次数','splitBusiStatistic','totalNumSuccess',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('53','sms',1,'协同签名总签名失败总次数','splitBusiStatistic','totalError',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('54','sms',2,'协同签名用户数','userNumStatistic','userNum',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('61','secdb',2,'数据库加密加密数据库个数','dbStatistic','dbNum',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('62','secdb',2,'数据库加密加密表个数','dbStatistic','dbTableNum',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('63','secdb',2,'数据库加密加密字段个数','dbStatistic','dbColumnNum',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('64','secdb',2,'数据库加密加密钥总数','secDbKeyNumStatistic','keyNums',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('71','secstorage',2,'文件加密服务器个数','serverStatistic','fileServerNum',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('72','secstorage',2,'NAS加密服务器个数','serverStatistic','nasServerNum',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('81','vpn',3,'vpn 新建连接数','vpnNewConnectNumStatistic','vpnNewConnectNumStatistic',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('82','vpn',3,'吞吐量(收)','vpnThroughputRNumStatistic','vpnThroughputRNumStatistic',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('83','vpn',3,'吞吐量(发)','vpnThroughputSNumStatistic','vpnThroughputSNumStatistic',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('84','vpn',3,'并发连接数','vpnConcurrentConnectNumStatistic','vpnConcurrentConnectNumStatistic',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('91','tsc',1,'电子签章签署次数','sealSignBusiStatistic','totals',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('92','tsc',1,'电子签章签署成功次数','sealSignBusiStatistic','totalNumSuccess',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('93','tsc',1,'电子签章签署失败次数','sealSignBusiStatistic','totalError',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('94','tsc',1,'电子签章验签次数','sealVerifyBusiStatistic','totals',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('95','tsc',1,'电子签章验签次数','sealVerifyBusiStatistic','totalNumSuccess',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('96','tsc',1,'电子签章验签失败次数','sealVerifyBusiStatistic','totalError',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('97','tsc',2,'电子签章印章个数','sealNumStatistic','sealNum',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('98','tsc',2,'电子签章停用状态总数','sealStateStatistic','1',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('99','tsc',2,'电子签章启用状态总数','sealStateStatistic','2',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('100','tsc',2,'电子签章删除状态总数','sealStateStatistic','3',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('101','center',1,'设备运行状态','device_used','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('102','center',1,'设备异常状态','device_error','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('103','center',1,'设备空闲状态','device_unused','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('104','center',1,'服务运行状态','service_run','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('105','center',1,'服务异常状态','service_error','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('106','center',1,'服务启动状态','service_start','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('107','center',1,'服务停止状态','service_stop','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('108','center',1,'租户总数','tenant_num','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('109','center',1,'许可总数','license_num','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('110','center',1,'设备总数量','device_sum','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('111','center',1,'服务总数量','service_sum','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('121','center-tenant',1,'设备运行状态','device_used','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('122','center-tenant',1,'设备异常状态','device_error','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('123','center-tenant',1,'设备空闲状态','device_unused','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('124','center-tenant',1,'服务运行状态','service_run','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('125','center-tenant',1,'服务异常状态','service_error','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('126','center-tenant',1,'服务启动状态','service_start','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('127','center-tenant',1,'服务停止状态','service_stop','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('128','center-tenant',1,'设备总数量','device_sum','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('129','center-tenant',1,'服务总数量','service_sum','',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID","INDEX_TYPE","STATISTIC_TYPE","STATISTIC_NAME","STATISTIC_FIRST","STATISTIC_SECOND","INVALID_FLAG","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('130','ca',2,'用户证书数量','caUserCertStatistic','certNums',0,null,null,null,null,null);

INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('1','默认模板','default','1','kms',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('2','默认模板','default','2','secauth',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('3','默认模板','default','3','pki',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('4','默认模板','default','4','secdb',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('5','默认模板','default','5','secstorage',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('6','默认模板','default','6','vpn',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('7','默认模板','default','7','svs',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('8','默认模板','default','8','tsc',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('9','默认模板','default','9','sms',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('10','默认模板','default','10','tsa',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('11','默认模板','default','11','center',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('12','默认模板','default','12','center-tenant',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('51','首页服务统计卡片形式','card','51','kms',0,null,null,null,null,'');
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('52','首页服务统计卡片形式','card','52','secauth',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('53','首页服务统计卡片形式','card','53','pki',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('54','首页服务统计卡片形式','card','54','secdb',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('55','首页服务统计卡片形式','card','55','secstorage',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('56','首页服务统计卡片形式','card','56','vpn',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('57','首页服务统计卡片形式','card','57','svs',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('58','首页服务统计卡片形式','card','58','tsc',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('59','首页服务统计卡片形式','card','59','sms',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('60','首页服务统计卡片形式','card','60','tsa',0,null,null,null,null,null);
INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID","TEMPLATE_NAME","TEMPLATE_CODE","INDEX_ID","INDEX_TYPE","INVALID_FLAG","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME","REMARK") VALUES('61','首页服务统计卡片形式','card','61','ca',0,null,null,null,null,null);

INSERT INTO "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"("ID","TEMPLATE_CODE","SCREEN_NAME","SCREEN_URL","VALID_FLAG","EXTEND","REMARK","CREATE_BY","CREATE_TIME","UPDATE_BY","UPDATE_TIME") VALUES('1','default','大屏','/swmonitor/front/screen?viewName=bigpanel-ccsp001&isShowScreen=1&target=_blank',0,null,null,null,null,null,null);

INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('1','clear_incre_record','statistic','ccsp-atom-static',null,'statisticHandlerServiceImpl.increRecordClear()','0 20 0 * * ?','3','0','0',null,null,null,null,'每天00:20清理记录表');
INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('2','clear_incre_cal','statistic','ccsp-atom-static',null,'statisticHandlerServiceImpl.increCalClear()','0 20 1 * * ?','3','0','0',null,null,null,null,'每天01:20清理计算表');
INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('3','clear_unincre_cal','statistic','ccsp-atom-static',null,'statisticHandlerServiceImpl.unIncreCalClear()','0 3 * * * ?','3','0','0',null,null,null,null,'每小时第三分钟清理非增量表');
INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('4','compute_incre_cal','statistic','ccsp-atom-static',null,'statisticHandlerServiceImpl.increCalCompute()','0 5 * * * ?','3','0','0',null,null,null,null,'每小时第五分钟统计记录表到计算表');
INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('5','compute_incre_total_cal','statistic','ccsp-atom-static',null,'statisticHandlerServiceImpl.increTotalCompute()','0 * * * * ?','3','0','0',null,null,null,null,'每分钟统计记录表到总量表');


INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('6','analyzeDataByMonth','statistic','ccsp-atom-static',null,'statisticRealTimeAnalyzeDataTaskApiImpl.analyzeDataByMonth()','0 0 1 1 * ?','3','1','0',null,null,null,null,'每月1号的凌晨一点执行,按月拆分天数据');
INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('7','analyzeDataByDay','statistic','ccsp-atom-static',null,'statisticRealTimeAnalyzeDataTaskApiImpl.analyzeDataByDay()','0 30 0 * * ?','3','1','0',null,null,null,null,'每天00:30执行一次,按天拆分小时数据');
INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('8','analyze_data_by_hour','statistic','ccsp-atom-static',null,'statisticRealTimeAnalyzeDataTaskApiImpl.analyzeDataByHour()','0 0 */1 * * ?','3','0','0',null,null,null,null,'每小时执行一次,按小时拆分实时数据');
INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('9','clear_real_time_data','statistic','ccsp-atom-static',null,'statisticRealTimeAnalyzeDataTaskApiImpl.clearRealTimeData()','0 30 0 * * ?','3','1','0',null,null,null,null,'每天00:30清理实时数据，保留两天的数据');
INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('10','clear_data_by_hour','statistic','ccsp-atom-static',null,'statisticRealTimeAnalyzeDataTaskApiImpl.clearDataByHour()','0 0 0 * * ?','3','1','0',null,null,null,null,'每天00:00清理小时数据,保留7天的数据');
INSERT INTO "CCSP_STATISTIC"."SYS_JOB"("JOB_ID","JOB_NAME","JOB_GROUP","SERVER_ID","METHOD_URL","JSON_PARAM","CRON_EXPRESSION","MISFIRE_POLICY","CONCURRENT","JOB_STATUS","CREATED_BY","CREATE_TIME","UPDATED_BY","UPDATE_TIME","REMARK") VALUES('11','clear_data_by_day','statistic','ccsp-atom-static',null,'statisticRealTimeAnalyzeDataTaskApiImpl.clearDataByDay()','0 0 2 1 * ?','3','1','0',null,null,null,null,'每月1号的凌晨2点执行,保留一个月的数据');





ALTER TABLE "CCSP_STATISTIC"."SYS_TASK_LOG" ADD CONSTRAINT  PRIMARY KEY("TASK_LOG_ID") ;

ALTER TABLE "CCSP_STATISTIC"."SYS_TASK" ADD CONSTRAINT  PRIMARY KEY("TASK_ID") ;

ALTER TABLE "CCSP_STATISTIC"."SYS_JOB_LOG" ADD CONSTRAINT  PRIMARY KEY("JOB_LOG_ID") ;

ALTER TABLE "CCSP_STATISTIC"."SYS_JOB" ADD CONSTRAINT  PRIMARY KEY("JOB_ID") ;

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_MONTH_DATA" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_INCRE_CAL" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_HOUR_DATA" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_DAY_DATA" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."SCREEN_STATISTIC" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."SCREEN_INDEX" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."MONITOR_CONNECTION" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."LOG_OPERATE" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."DIC_SEAL_STATUS" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."DIC_KEY_STATUS" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."DIC_ALARM_TYPE" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."ALARM_OID" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."ALARM_INFO" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "CCSP_STATISTIC"."ALARM_HISTORY" ADD CONSTRAINT  PRIMARY KEY("ID") ;

CREATE UNIQUE INDEX "PRIMARY"
ON "CCSP_STATISTIC"."SYS_TASK_LOG"("TASK_LOG_ID");

COMMENT ON TABLE "CCSP_STATISTIC"."SYS_TASK_LOG" IS '异步任务执行日志表';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK_LOG"."TASK_LOG_ID" IS '任务日志ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK_LOG"."TASK_ID" IS '任务ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK_LOG"."TASK_MESSAGE" IS '日志信息';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK_LOG"."STATUS" IS '执行状态（0失败 1正常）';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK_LOG"."EXCEPTION_INFO" IS '异常信息';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK_LOG"."CREATE_TIME" IS '创建时间;单位毫秒';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK_LOG"."TRIGGER_TIME" IS '触发时间;任务服务上送';

CREATE UNIQUE INDEX "INDEX29439427416600"
ON "CCSP_STATISTIC"."SYS_TASK"("TASK_ID");

COMMENT ON TABLE "CCSP_STATISTIC"."SYS_TASK" IS '异步任务表';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."TASK_ID" IS '任务号';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."TASK_NAME" IS '任务名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."TASK_GROUP" IS '任务组名;执行任务串行';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."SERVER_ID" IS '服务模块';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."METHOD_URL" IS '调用接口';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."JSON_PARAM" IS 'json格式参数';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."TASK_STATUS" IS '状态(0-未执行 1-执行中 2-成功 3-异常 4-超时);状态(0-未执行 1-执行中 2-成功 3-异常 4-超时)';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."TIMEOUT" IS '超时时间;单位秒';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."START_TIME" IS '开始时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."END_TIME" IS '结束时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_TASK"."POLICY" IS '是否允许重复执行;0-不允许，1允许';

CREATE UNIQUE INDEX "INDEX29439500595200"
ON "CCSP_STATISTIC"."SYS_JOB_LOG"("JOB_LOG_ID");

COMMENT ON TABLE "CCSP_STATISTIC"."SYS_JOB_LOG" IS '定时任务执行日志表';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB_LOG"."JOB_LOG_ID" IS '任务日志ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB_LOG"."JOB_ID" IS '任务ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB_LOG"."JOB_MESSAGE" IS '日志信息';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB_LOG"."STATUS" IS '执行状态（0失败 1正常）';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB_LOG"."EXCEPTION_INFO" IS '异常信息';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB_LOG"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB_LOG"."TRIGGER_TIME" IS '触发时间';

CREATE UNIQUE INDEX "INDEX29439542363500"
ON "CCSP_STATISTIC"."SYS_JOB"("JOB_ID");

COMMENT ON TABLE "CCSP_STATISTIC"."SYS_JOB" IS '定时任务表';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."JOB_ID" IS '任务号';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."JOB_NAME" IS '任务名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."JOB_GROUP" IS '任务组名';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."SERVER_ID" IS '服务模块';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."METHOD_URL" IS '调用接口';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."JSON_PARAM" IS 'json格式参数';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."CRON_EXPRESSION" IS 'CRON执行表达式';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."MISFIRE_POLICY" IS '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."CONCURRENT" IS '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."JOB_STATUS" IS '状态（0正常 1暂停）';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."CREATED_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."UPDATED_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SYS_JOB"."REMARK" IS '备注';

CREATE UNIQUE INDEX "INDEX29439610653200"
ON "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL" IS '非增量类统计采集计算表';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."ID" IS 'id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."REGION_ID" IS '区域ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."SERVICE_TYPE_ID" IS '服务类型';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."SERVICE_GROUP_ID" IS '服务组id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."SERVICE_ID" IS '服务id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."STATISTIC_NAME" IS '统计指标名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."SUB_STATISTIC_NAME" IS '子统计指标名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."SUB_STATISTIC_VALUE" IS '子统计指标值';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."STATISTIC_YEAR" IS '年';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."STATISTIC_MONTH" IS '月';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."STATISTIC_DAY" IS '日';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."STATISTIC_HOUR" IS '时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."STATISTIC_MINUTE" IS '分';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."STATISTIC_SECOND" IS '秒';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_UNINCRE_CAL"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX29439692560100"
ON "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA" IS '调用次数实时数据（330）';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."ID" IS 'ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."COLLECT_DATE_TIME" IS '数据采集时间所在的小时时间格式';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."COLLECT_TIME" IS '数据采集时间(字符串格式);数据采集时间(字符串格式)';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."CALL_NUM" IS '调用次数';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."REGION_CODE" IS '区域标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."SERVICE_IP" IS '服务IP';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."SERVICE_PORT" IS '服务端口';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."SERVICE_TYPE_CODE" IS '服务标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."SERVICE_URL" IS '服务URL';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."AVG_COST_TIME" IS '平均耗时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."MAX_COST_TIME" IS '最大耗时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."SERVICE_FLOW_NUM" IS '流量';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_REAL_TIME_DATA"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX29439784932500"
ON "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."STATISTIC_MONTH_DATA" IS '按照月拆分实时数据（330）';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."ID" IS 'ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."COLLECT_DATE_TIME" IS '拆分数据月时间所表示的年时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."COLLECT_TIME" IS '拆分数据月时间单位';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."CALL_NUM" IS '调用次数';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."REGION_CODE" IS '区域标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."SERVICE_IP" IS '服务IP';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."SERVICE_PORT" IS '服务端口';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."SERVICE_TYPE_CODE" IS '服务标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."SERVICE_URL" IS '服务URL';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."AVG_COST_TIME" IS '平均耗时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."MAX_COST_TIME" IS '最大耗时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."SERVICE_FLOW_NUM" IS '流量';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_MONTH_DATA"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX29439893783900"
ON "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL" IS '增量类统计采集总量计算表';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."ID" IS 'id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."REGION_ID" IS '区域ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."SERVICE_CODE" IS '服务简称';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."SERVICE_GROUP_ID" IS '服务组id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."STATISTIC_NAME" IS '统计指标名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."TOTAL_STATISTIC" IS '总量统计';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."SUCCESS_STATISTIC" IS '成功量统计';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."ERROR_STATISTIC" IS '失败量统计';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."STATISTIC_YEAR" IS '年';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."STATISTIC_MONTH" IS '月';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."STATISTIC_DAY" IS '日';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."STATISTIC_HOUR" IS '时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."STATISTIC_MINUTE" IS '分';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."STATISTIC_SECOND" IS '秒';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."HMAC" IS '完整性校验值';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_TOTAL_CAL"."CREATE_TIME" IS '创建时间';

CREATE UNIQUE INDEX "INDEX29439982412000"
ON "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD" IS '增量类统计采集记录表';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."ID" IS 'id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."REGION_ID" IS '区域ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."SERVICE_CODE" IS '服务简称';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."SERVICE_GROUP_ID" IS '服务组id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."SERVICE_ID" IS '服务id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."STATISTIC_NAME" IS '统计指标名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."TOTAL_STATISTIC" IS '总量统计';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."SUCCESS_STATISTIC" IS '成功量统计';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."ERROR_STATISTIC" IS '失败量统计';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."STATISTIC_YEAR" IS '年';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."STATISTIC_MONTH" IS '月';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."STATISTIC_DAY" IS '日';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."STATISTIC_HOUR" IS '时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."STATISTIC_MINUTE" IS '分';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."STATISTIC_SECOND" IS '秒';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."HMAC" IS '完整性校验值';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_RECORD"."TIME_STAMP" IS '插入时间戳';

ALTER TABLE "CCSP_STATISTIC"."STATISTIC_INCRE_CAL" ADD CHECK ("ID" >= 0) ENABLE ;

CREATE UNIQUE INDEX "INDEX29440077652500"
ON "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."STATISTIC_INCRE_CAL" IS '增量类统计采集计算表';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."ID" IS 'id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."REGION_ID" IS '区域ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."SERVICE_CODE" IS '服务简称';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."SERVICE_GROUP_ID" IS '服务组id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."SERVICE_ID" IS '服务id';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."STATISTIC_NAME" IS '统计指标名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."TOTAL_STATISTIC" IS '总量统计';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."SUCCESS_STATISTIC" IS '成功量统计';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."ERROR_STATISTIC" IS '失败量统计';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."STATISTIC_YEAR" IS '年';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."STATISTIC_MONTH" IS '月';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."STATISTIC_DAY" IS '日';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."STATISTIC_HOUR" IS '时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."STATISTIC_MINUTE" IS '分';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."STATISTIC_SECOND" IS '秒';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."HMAC" IS '完整性校验值';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_INCRE_CAL"."CREATE_TIME" IS '创建时间';

CREATE UNIQUE INDEX "INDEX29440168662500"
ON "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."STATISTIC_HOUR_DATA" IS '按照小时拆分实时数据（330）';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."ID" IS 'ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."COLLECT_DATE_TIME" IS '拆分数据小时时间所在的天时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."COLLECT_TIME" IS '拆分数据小时时间;采集时间：字符串格式';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."CALL_NUM" IS '调用次数';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."REGION_CODE" IS '区域标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."SERVICE_IP" IS '服务IP';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."SERVICE_PORT" IS '服务端口';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."SERVICE_TYPE_CODE" IS '服务标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."SERVICE_URL" IS '服务URL';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."AVG_COST_TIME" IS '平均耗时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."MAX_COST_TIME" IS '最大耗时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."SERVICE_FLOW_NUM" IS '流量';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_HOUR_DATA"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX29440278216900"
ON "CCSP_STATISTIC"."STATISTIC_DAY_DATA"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."STATISTIC_DAY_DATA" IS '按照天拆分实时数据（330）';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."ID" IS 'ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."COLLECT_DATE_TIME" IS '拆分数据天所在的月时间格式';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."COLLECT_TIME" IS '拆分数据天时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."CALL_NUM" IS '调用次数';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."REGION_CODE" IS '区域标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."SERVICE_IP" IS '服务IP';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."SERVICE_PORT" IS '服务端口';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."SERVICE_TYPE_CODE" IS '服务标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."SERVICE_URL" IS '服务URL';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."AVG_COST_TIME" IS '平均耗时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."MAX_COST_TIME" IS '最大耗时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."SERVICE_FLOW_NUM" IS '流量';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_DAY_DATA"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX29440364359300"
ON "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM" IS '统计配置参数（330）';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"."ID" IS 'ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"."REGION_CODE" IS '区域标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"."SERVICE_TYPE_CODE" IS '服务标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"."TARGET_CODE" IS '指标名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"."TARGET_CHILD_CODE" IS '子指标名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"."MULTIPLE_VAL" IS '倍数';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"."MAX_VAL" IS '最大值';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_CONFIG_PARAM"."FLOAT_VAL" IS '浮动值;默认为0';

CREATE UNIQUE INDEX "INDEX29440416359200"
ON "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA" IS '实时数据汇总拆分（330）';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."ID" IS 'ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."CALL_NUM" IS '调用次数';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."COLLECT_NUM" IS '采集数量;此条汇总结果来自于多少数量的数据';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."REGION_CODE" IS '区域标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."APP_CODE" IS '应用标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."SERVICE_IP" IS '服务IP';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."SERVICE_PORT" IS '服务端口';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."SERVICE_TYPE_CODE" IS '服务标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."SERVICE_URL" IS '服务URL';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."AVG_COST_TIME" IS '平均耗时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."MAX_COST_TIME" IS '最大耗时';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."SERVICE_FLOW_NUM" IS '流量';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."STATISTIC_ALL_COLLECT_DATA"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX29440498882500"
ON "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL" IS 'SCREEN_TEMPLATE_URL_REL';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."ID" IS '主键';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."TEMPLATE_CODE" IS '模板编码';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."SCREEN_NAME" IS '大屏名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."SCREEN_URL" IS '大屏URL地址';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."VALID_FLAG" IS '是否有效 0：有效，1：无效';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."EXTEND" IS '扩展字段';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_URL_REL"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX29440556702500"
ON "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL" IS '大屏模板表';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."ID" IS '主键';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."TEMPLATE_NAME" IS '大屏模板名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."TEMPLATE_CODE" IS '大屏模板编码';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."INDEX_ID" IS '模板ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."INDEX_TYPE" IS '指标类型;pki、vpn、secstorage、secdb、svs、tsc、sms、tsa、kms、secauth';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_TEMPLATE_REL"."REMARK" IS '备注';

CREATE UNIQUE INDEX "INDEX29440613326200"
ON "CCSP_STATISTIC"."SCREEN_STATISTIC"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."SCREEN_STATISTIC" IS '大屏统计指标字典表';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."ID" IS '主键';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."INDEX_TYPE" IS '指标类型;1增量 2非增量数量计算类 3非增量非计算类';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."STATISTIC_TYPE" IS '统计类型';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."STATISTIC_NAME" IS '统计名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."STATISTIC_FIRST" IS '一级指标';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."STATISTIC_SECOND" IS '二级指标';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."INVALID_FLAG" IS '是否作废;是否作废;默认为0';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_STATISTIC"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX29440723394500"
ON "CCSP_STATISTIC"."SCREEN_INDEX"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."SCREEN_INDEX" IS '大屏模板指标表';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_INDEX"."ID" IS '主键';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_INDEX"."NAME" IS '指标名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_INDEX"."TEMPLATE" IS '模板';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_INDEX"."INVALID_FLAG" IS '是否作废;默认为0';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_INDEX"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_INDEX"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_INDEX"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_INDEX"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."SCREEN_INDEX"."REMARK" IS '备注';

CREATE UNIQUE INDEX "INDEX29440776687800"
ON "CCSP_STATISTIC"."MONITOR_CONNECTION"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."MONITOR_CONNECTION" IS '监控连接信息表';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."ID" IS 'id';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."SOURCE_TYPE" IS '来源 1 服务 2 设备';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."DEVICE_TYPE" IS '设备类型';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."CONNECTION_NAME" IS '连接名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."CONNECTION_STATUS" IS '连接状态 1已连接 2未连接';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."TENANT_CODE" IS '租户标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."IP" IS '设备IP';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."PORT" IS '设备端口';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "CCSP_STATISTIC"."MONITOR_CONNECTION"."UPDATE_TIME" IS '更新时间';

CREATE UNIQUE INDEX "INDEX29440842738200"
ON "CCSP_STATISTIC"."LOG_OPERATE"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."LOG_OPERATE" IS '操作日志表';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."ID" IS '主键';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."TENANT_ID" IS '租户ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."RESULT" IS '操作结果，0-成功，1-失败，2-获取操作结果失败';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."BUSI_TYPE_ID" IS '业务类型ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."CREATE_TIME" IS '保存时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."OPER_IP" IS '请求IP';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."OPER_NAME" IS '操作人';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."MODULE_NAME" IS '模块名';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."OPER_CONTENT" IS '操作内容';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."HMAC" IS '完整性保护字段';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."AUDIT_STATUS" IS '审计状态;0-审计，1-未审计';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."ERROR_MSG" IS '错误信息';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."LOG_TYPE" IS '日志类型，1-平台侧日志，2-服务侧日志';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."EXTEND1" IS '备用1';

COMMENT ON COLUMN "CCSP_STATISTIC"."LOG_OPERATE"."EXTEND2" IS '备用2';

CREATE UNIQUE INDEX "INDEX29440912669300"
ON "CCSP_STATISTIC"."DIC_SEAL_STATUS"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."DIC_SEAL_STATUS" IS '签章状态字典表';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_SEAL_STATUS"."D_NAME" IS '名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_SEAL_STATUS"."D_VALUE" IS '值';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_SEAL_STATUS"."IS_AVAILABLE" IS '是否启用 1启用 0停用';

CREATE UNIQUE INDEX "INDEX29440940132300"
ON "CCSP_STATISTIC"."DIC_KEY_STATUS"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."DIC_KEY_STATUS" IS '密钥状态字典表';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_KEY_STATUS"."D_NAME" IS '名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_KEY_STATUS"."D_VALUE" IS '值';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_KEY_STATUS"."IS_AVAILABLE" IS '是否启用 1启用 0停用';

CREATE UNIQUE INDEX "INDEX29440967213400"
ON "CCSP_STATISTIC"."DIC_ALARM_TYPE"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."DIC_ALARM_TYPE" IS '告警类型字典';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_ALARM_TYPE"."ID" IS '主键';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_ALARM_TYPE"."CODE" IS '标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_ALARM_TYPE"."NAME" IS '名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_ALARM_TYPE"."LEVEL" IS '等级;1为最高等级，随递增级别减弱';

COMMENT ON COLUMN "CCSP_STATISTIC"."DIC_ALARM_TYPE"."TENANT_VISIBLE" IS '租户可见;0租户不可见，1租户可见';

CREATE UNIQUE INDEX "INDEX29441001268100"
ON "CCSP_STATISTIC"."ALARM_OID"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."ALARM_OID" IS '告警指标';

CREATE UNIQUE INDEX "INDEX29441017046300"
ON "CCSP_STATISTIC"."ALARM_INFO"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."ALARM_INFO" IS '告警信息表';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."ID" IS '主键';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."TENANT_ID" IS '租户ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."REGION_ID" IS '区域ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."ALARM_CODE" IS '告警标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."SOURCE_ID" IS '告警源ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."SOURCE_IP" IS '告警源IP';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."CONTENT" IS '告警内容';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."TIMES" IS '告警次数';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."FIRST_TIME" IS '首次告警时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."LAST_TIME" IS '末次告警时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_INFO"."STATUS" IS '告警状态;1告警中2已处置3已恢复';

CREATE UNIQUE INDEX "INDEX29441071733800"
ON "CCSP_STATISTIC"."ALARM_HISTORY"("ID");

COMMENT ON TABLE "CCSP_STATISTIC"."ALARM_HISTORY" IS '告警历史表';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."ID" IS '主键';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."TENANT_ID" IS '租户ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."TENANT_NAME" IS '租户名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."REGION_ID" IS '区域ID';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."REGION_NAME" IS '区域名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."ALARM_CODE" IS '告警标识';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."SOURCE_IP" IS '告警源IP';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."SOURCE_NAME" IS '告警源名称';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."CONTENT" IS '告警内容';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."TIMES" IS '告警次数';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."FIRST_TIME" IS '首次告警时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."LAST_TIME" IS '末次告警时间';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."STATUS" IS '告警状态;1告警中2已处置3已恢复';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."REMARK" IS '备注';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."UPDATE_BY" IS '处置人';

COMMENT ON COLUMN "CCSP_STATISTIC"."ALARM_HISTORY"."UPDATE_TIME" IS '处置时间';

