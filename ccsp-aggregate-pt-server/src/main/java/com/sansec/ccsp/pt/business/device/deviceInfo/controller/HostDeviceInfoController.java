package com.sansec.ccsp.pt.business.device.deviceInfo.controller;

import com.sansec.ccsp.device.deviceinfo.request.DeviceIdDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdsDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoConfigNetworkDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoPageDTO;
import com.sansec.ccsp.device.deviceinfo.request.host.*;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoVO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceStatusVO;
import com.sansec.ccsp.device.deviceinfo.response.host.HostDeviceInfoVO;
import com.sansec.ccsp.pt.business.device.deviceInfo.service.HostDeviceInfoService;
import com.sansec.ccsp.pt.business.statistic.log.aspect.OperateManageLog;
import com.sansec.ccsp.security.annotation.HasAnyPermissions;
import com.sansec.ccsp.security.annotation.IgnoreToken;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/deviceinfo/host/v1")
@Validated
public class HostDeviceInfoController {
	@Resource
	private HostDeviceInfoService hostDeviceInfoService;
	/**
	 * 分页查询
	 *
	 * @param deviceInfoPageDTO 筛选条件
	 * @return 查询结果
	 */
	@HasAnyPermissions("device:hsm:list")
	@PostMapping("/find")
	public SecRestResponse<SecPageVO<DeviceInfoVO>> find(@Validated @RequestBody DeviceInfoPageDTO deviceInfoPageDTO){
		return hostDeviceInfoService.find(deviceInfoPageDTO);
	}

	/**
	 * 添加物理机
	 *
	 * @param hostDeviceInfoAddDTO 实例对象
	 * @return 实例对象
	 */
	@OperateManageLog(module = "物理机管理",desc = "添加物理机")
	@HasAnyPermissions("device:hsm:add")
	@PostMapping("add")
	public SecRestResponse<Long> add(@Validated @RequestBody HostDeviceInfoAddDTO hostDeviceInfoAddDTO){
		return hostDeviceInfoService.add(hostDeviceInfoAddDTO);
	}

	/**
	 * 重启物理机
	 *
	 * @param deviceIdDTO
	 * @return
	 */
	@OperateManageLog(module = "物理机管理",desc = "重启物理机")
	@HasAnyPermissions("device:hsm:restart")
	@PostMapping("restart")
	public SecRestResponse<Long> restart(@Validated @RequestBody DeviceIdDTO deviceIdDTO){
		return hostDeviceInfoService.restart(deviceIdDTO);
	}

	/**
	 * 删除物理机
	 *
	 * @param deviceIdDTO 设备ID
	 * @return 实例对象
	 */
	@OperateManageLog(module = "物理机管理",desc = "删除物理机")
	@HasAnyPermissions("device:hsm:delete")
	@PostMapping("delete")
	public SecRestResponse<Long> delete(@Validated @RequestBody DeviceIdDTO deviceIdDTO){
		return hostDeviceInfoService.delete(deviceIdDTO);
	}

	@OperateManageLog(module = "物理机管理",desc = "强制删除物理机")
	@HasAnyPermissions("device:hsm:forceDelete")
	@PostMapping("forceDelete")
	public SecRestResponse<Long> forceDeleteforceDelete(@Validated @RequestBody DeviceIdDTO deviceIdDTO){
		return hostDeviceInfoService.forceDelete(deviceIdDTO);
	}

	/**
	 * 配置物理机网络
	 *
	 * @param hostDeviceConfigNetworkDTO
	 * @return
	 */
	@OperateManageLog(module = "物理机管理",desc = "配置物理机网络")
	@HasAnyPermissions("device:hsm:netConfig")
	@PostMapping("/network")
	public SecRestResponse<Long> network(@Validated @RequestBody DeviceInfoConfigNetworkDTO hostDeviceConfigNetworkDTO){
		return hostDeviceInfoService.ConfigureNetwork(hostDeviceConfigNetworkDTO);
	}

	/**
	 * 获取物理机设备状态
	 *
	 * @param deviceIdsDTO
	 * @return
	 */
	@HasAnyPermissions("device:hsm:list")
	@PostMapping("status")
	@IgnoreToken
	public SecRestResponse<List<DeviceStatusVO>> status(@Validated @RequestBody DeviceIdsDTO deviceIdsDTO){
		return hostDeviceInfoService.status(deviceIdsDTO);
	}

	/**
	 * 获取物理机内部详情
	 *
	 * @param deviceIdDTO
	 * @return
	 */
	@HasAnyPermissions("device:hsm:detail")
	@PostMapping("/hsminfo")
	public SecRestResponse<HostDeviceInfoVO> hsminfo(@Validated @RequestBody DeviceIdDTO deviceIdDTO){
		return hostDeviceInfoService.hsminfo(deviceIdDTO);
	}

	/**
	 * 获取物理机基本信息
	 *
	 * @param deviceIdDTO
	 * @return
	 */
	@HasAnyPermissions("device:hsm:detail")
	@PostMapping("/info")
	public SecRestResponse<DeviceInfoVO> info(@Validated @RequestBody DeviceIdDTO deviceIdDTO){
		return hostDeviceInfoService.info(deviceIdDTO);
	}

	/**
	 * 编辑设备信息
	 *
	 * @param hostDeviceInfoEditDTO 实例对象
	 * @return 实例对象
	 */
	@OperateManageLog(module = "物理机管理",desc = "编辑物理机信息")
	@HasAnyPermissions("device:hsm:edit")
	@PostMapping("/edit")
	public SecRestResponse<Long> edit(@Validated @RequestBody HostDeviceInfoEditDTO hostDeviceInfoEditDTO){
		return hostDeviceInfoService.edit(hostDeviceInfoEditDTO);
	}

	/**
	 *备份主密钥
	 * @param backUpPublicKeyDTO
	 * @return
	 */
	@OperateManageLog(module = "密钥管理",desc = "备份主密钥")
	@PostMapping("/backUpPubKey")
	@HasAnyPermissions("common:masterKey:backup")
	public void backUpPublicKey(@Validated @RequestBody BackUpPublicKeyDTO backUpPublicKeyDTO, HttpServletResponse response){
        hostDeviceInfoService.backUpPublicKey(backUpPublicKeyDTO,response);
	}

	/**
	 * 恢复主密钥
	 * @param recoverPublicKeyDTO
	 * @return
	 */
	@OperateManageLog(module = "密钥管理",desc = "恢复主密钥")
	@PostMapping("/recoverPubKey")
	@HasAnyPermissions("common:masterKey:recover")
	public SecRestResponse<Object> recoverPublicKey(RecoverPublicKeyDTO recoverPublicKeyDTO,MultipartFile keyFile){
        return hostDeviceInfoService.recoverPublicKey(recoverPublicKeyDTO,keyFile);
	}


	@OperateManageLog(module = "密钥管理",desc = "备份内部密钥")
	@PostMapping("/backUpInnerKey")
	@HasAnyPermissions("common:masterKey:backup")
	public void backUpInnerKey(@Validated @RequestBody BackUpInnerKeyDTO backUpInnerKeyDTO, HttpServletResponse response){
		hostDeviceInfoService.backUpInnerKey(backUpInnerKeyDTO,response);
	}

	@OperateManageLog(module = "密钥管理",desc = "恢复内部密钥")
	@PostMapping("/recoverInnerKey")
	@HasAnyPermissions("common:masterKey:recover")
	public SecRestResponse<Object> recoverInnerKey(RecoverInnerKeyDTO recoverInnerKeyDTO,MultipartFile keyFile){
		return hostDeviceInfoService.recoverInnerKey(recoverInnerKeyDTO,keyFile);
	}
}
