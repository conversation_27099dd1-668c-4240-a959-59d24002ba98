package com.sansec.ccsp.servicemgt.serviceinfo.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @Author: wangjunjie
 * @Date: 2023-02-28
 * @Description:
 */
@Data
public class ServiceListByTenantIdPageDTO extends SecPageDTO {
    /**
     * 租户Id
     **/
    private Long tenantId;

    private List<Long> serviceGroupIdList;

    @Size(max = 50, message = "服务名称长度限制50")
    @Pattern(regexp = CommonPattern.COMMON_NAME, message = "服务名称只能包含中文、英文、数字或特殊字符-_")
    private String serviceName;

    private Long serviceGroupId;

    private Long serviceTypeId;

    //过滤备机
    private Integer isActiveStandby;

    // 多借口共用，使用check，不使用注解
    public void check() {
        if (this.tenantId == null) {
            throw new BusinessException("租户Id不可为空");
        }
    }

}
