package com.sansec.ccsp.pt.business.servicemgt.serviceinfo.convert;

import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoInGroupVO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request.DeviceInvokeDTO;
import com.sansec.ccsp.servicemgt.serviceinfo.request.ServiceInfoDTO;
import com.sansec.ccsp.servicemgt.serviceinfo.request.ServiceInfoEditDTO;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceInfoVO;
import com.sansec.ccsp.servicemgt.servicetype.request.ServiceTypeDTO;
import com.sansec.ccsp.servicemgt.servicetype.response.ServiceTypeVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface InterInvokerConvert {

    @Mappings({
            @Mapping(source = "busiIp", target = "ip"),
            @Mapping(source = "busiPortExtend", target = "port"),
            @Mapping(source = "connectAuthCode", target = "deviceAuthCode")
    })
    DeviceInvokeDTO convertVo(DeviceInfoInGroupVO request);

    @Mappings({
            @Mapping(source = "busiIp", target = "ip"),
            @Mapping(source = "busiPort", target = "port"),
            @Mapping(source = "connectAuthCode", target = "deviceAuthCode")
    })
    DeviceInvokeDTO convertBusiVo(DeviceInfoInGroupVO request);

    @Mappings({})
    ServiceInfoVO dtoToVo(ServiceInfoDTO dto);

    @Mappings({})
    ServiceInfoDTO voToDto(ServiceInfoVO vo);

    @Mappings({})
    ServiceInfoVO dtoEditToVo(ServiceInfoEditDTO dto);

    @Mappings({})
    ServiceInfoEditDTO dtoToEdit(ServiceInfoDTO dto);

    @Mappings({})
    ServiceTypeDTO serviceTypeVoToDto(ServiceTypeVO vo);
}
