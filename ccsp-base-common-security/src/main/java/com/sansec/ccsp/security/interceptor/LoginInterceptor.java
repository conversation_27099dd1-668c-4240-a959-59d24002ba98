package com.sansec.ccsp.security.interceptor;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.sansec.ccsp.common.config.api.CommonConfigServiceApi;
import com.sansec.ccsp.security.annotation.HasAnyPermissions;
import com.sansec.ccsp.security.annotation.IgnoreToken;
import com.sansec.ccsp.security.core.context.TokenContext;
import com.sansec.ccsp.security.entity.LoginUser;
import com.sansec.ccsp.security.entity.jwt.Payload;
import com.sansec.ccsp.security.error.SecErrorCodeConstant;
import com.sansec.ccsp.security.filter.FeignFilter;
import com.sansec.ccsp.security.service.SecurityFrameworkService;
import com.sansec.ccsp.security.util.LoginUserUtil;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.SM3Util;
import com.sansec.engine.sm.SM2Utils;
import com.sansec.redis.utils.RedisUtill;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Import;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * jwt信息拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Import(SecurityFrameworkService.class)
public class LoginInterceptor implements HandlerInterceptor {

    private static final String TENANT_CODE_HEADER = "X-SW-Authorization-TenantCode";

    private static final String AUTH_HEADER = "X-SW-Authorization-Token";

    private static final int DEFAULT_TOKEN_EXPIRE_TIME = 30;

    @Value("${spring.profiles.active}")
    private String profiles;

    @Value("${task.ignoreToken:false}")
    private Boolean taskIgnoreToken;

    @Value("${key.jwt.publicKey}")
    private String publicKey;

    @Resource
    private SecurityFrameworkService securityFrameworkService;

    @Resource
    private RedisUtill redisUtill;

    @Resource
    private CommonConfigServiceApi commonConfigServiceApi;

    private static final String UNKNOWN_PARAM = "unknown";


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        //避免静态资源拦截
        boolean instance = handler instanceof HandlerMethod;
        if ( ! instance ) {
            return true;
        }
        response.setContentType("application/json;charset=utf-8");
        //如果接口或者类上有@IgnoreToken注解，意思该接口不需要token就能访问，需要放行
        HandlerMethod handlerMethod = ( HandlerMethod )handler;
        Method method = handlerMethod.getMethod();
        //先从类上获取该注解，判断类上是否加了IgnoreToken ，代表不需要token，直接放行
        boolean ignoreToken = false;
        IgnoreToken annotation = handlerMethod.getBeanType().getAnnotation(IgnoreToken.class);
        if ( annotation == null && method.isAnnotationPresent(IgnoreToken.class) ) {
            annotation = method.getAnnotation(IgnoreToken.class);
            log.debug("请求方法 {} 上有注解 {} ", method.getName(), annotation);
        }
        //不需要登录的接口直接返回 ture
        if ( annotation != null ) {
            ignoreToken = true;
        }

        LoginUser loginUser = null;
        //获取请求头信息
        Map<String, String> headerMap = getHeaders(request);
        //header中存在Ignore且值为true时允许访问

        //获取token
        String token = headerMap.get(LoginInterceptor.AUTH_HEADER.toLowerCase(Locale.ROOT));
        if ( StringUtils.isNotEmpty(token) ) {
            TokenContext.addCurrentToken(token);
        }

        //避免feign调用重复鉴权
        String loginUserInfoStr = headerMap.get(LoginUserUtil.LOGIN_USER_KEY.toLowerCase(Locale.ROOT));
        if ( StringUtils.isNotEmpty(loginUserInfoStr) ) {
            loginUserInfoStr = new String(java.util.Base64.getDecoder().decode(loginUserInfoStr), StandardCharsets.UTF_8);
            loginUser = JSON.parseObject(loginUserInfoStr, LoginUser.class);
        } else if ( StringUtils.isNotEmpty(token) ) {
            String hmac = SM3Util.digestWithHex(token);
            String key = String.format("ccsp:login:token:hmac:%s", hmac);
            Payload payload = redisUtill.strGet(key, Payload.class);
            if ( payload == null ) {
                if ( ignoreToken ) {
                    return true;
                }
                throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
            } else {
                //token续期
                redisUtill.strSet(key, payload, getWebTokenExpireTime(), TimeUnit.MINUTES);
            }
            loginUser = payload;
        } else if ( taskIgnoreToken && Boolean.TRUE.toString().equalsIgnoreCase(headerMap.get(FeignFilter.IGNORE_TOKEN_HEADER)) ) {
            return true;
        } else if ( ignoreToken ) {
            return true;
        } else {
            log.info("token信息不存在 {},request={} ", method.getName(), request);
            throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
        }
        //设置ip
        if ( StringUtils.isEmpty(loginUser.getIp()) ) {
            loginUser.setIp(getIP(request));
        }
        LoginUserUtil.addCurrentLoginUser(loginUser);
        //权限校验
        if ( method.isAnnotationPresent(HasAnyPermissions.class) ) {
            HasAnyPermissions hasAnyPermissions = method.getAnnotation(HasAnyPermissions.class);
            if ( hasAnyPermissions != null && CollUtil.isNotEmpty(Arrays.asList(hasAnyPermissions.value())) ) {
                boolean hasPermissions = securityFrameworkService.hasAnyPermissions(hasAnyPermissions.value());
                if ( ! hasPermissions ) {
                    throw new BusinessException(SecErrorCodeConstant.NO_PERMISSION);
                }
                return true;
            }
        }
        return true;
    }

    public Integer getWebTokenExpireTime() {
        int webTokenExpireTime;
        SecRestResponse<String> restResponse = commonConfigServiceApi.getConfigValueByConfigCode("web_token_expire");
        if ( restResponse.isSuccess() ) {
            webTokenExpireTime = Integer.parseInt(restResponse.getResult());
        } else {
            webTokenExpireTime = DEFAULT_TOKEN_EXPIRE_TIME;
            log.error("get web_token_expire error, use default value: 30, code:{}, message:{}", restResponse.getCode(), restResponse.getMessage());
        }
        return webTokenExpireTime;
    }

    /*
     * 模拟用户
     */
    public LoginUser mockUser() {
        LoginUser mockUser = new LoginUser();
        mockUser.setUserId(1L);
        mockUser.setUserName("mockUser");
        mockUser.setRoleId(1L);
        mockUser.setTenantId(1L);
        mockUser.setUserType(2);
        mockUser.setTenantCode("CCSP_TENANT");
        return mockUser;
    }

    /**
     * 避免内存泄露
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        LoginUserUtil.removeCurrentLoginUser();
        TokenContext.removeCurrentToken();
    }

    private Map<String, String> getHeaders(HttpServletRequest request) {
        Map<String, String> headerMap = new HashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        while ( enumeration.hasMoreElements() ) {
            String name = enumeration.nextElement().toLowerCase(Locale.ROOT);
            String value = request.getHeader(name);
            headerMap.put(name, value);
        }
        return headerMap;
    }

    /**
     * 获取应用信息
     *
     * @param token
     * @return
     */
    public LoginUser verifyGetLoginUser(String token) {
        String[] jwtArray = token.split("\\.");
        if ( jwtArray.length != 3 ) {
            throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
        }
        Payload payload;
        try {
            String payloadStr = Base64Decoder.decodeStr(jwtArray[1]);
            payload = JSON.parseObject(payloadStr, Payload.class);
            if ( payload.getExp() < DateUtil.currentSeconds() ) {
                throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
            }
            if ( payload.getUserId() == null ) {
                throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
            }

            String sourceDataStr2 = jwtArray[0] + "." + jwtArray[1];
            boolean verify = SM2Utils.verifySign(Base64.decodeBase64(publicKey), sourceDataStr2.getBytes(StandardCharsets.UTF_8), Base64.decodeBase64(jwtArray[2]));
            if ( ! verify ) {
                throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
            }
        } catch ( Exception e ) {
            log.debug("parse jwt error: {}", e.getMessage());
            throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
        }
        return payload;
    }


    /***
     * 获取客户端ip地址
     * @param request
     */
    public static String getIP(final HttpServletRequest request) throws Exception {
        if ( request == null ) {
            return null;
        }

        String ipStr = request.getHeader("x-forwarded-for");
//        log.info("x-forwarded-for is {}",request.getHeader("x-forwarded-for"));
//        log.info("Proxy-Client-IP is {}",request.getHeader("Proxy-Client-IP"));
//        log.info("WL-Proxy-Client-IP is {}",request.getHeader("WL-Proxy-Client-IP"));
//        log.info("request.getRemoteAddr is {}",request.getRemoteAddr());
        if ( StringUtils.isBlank(ipStr) || UNKNOWN_PARAM.equalsIgnoreCase(ipStr) ) {
            ipStr = request.getHeader("Proxy-Client-IP");
        }
        if ( StringUtils.isBlank(ipStr) || UNKNOWN_PARAM.equalsIgnoreCase(ipStr) ) {
            ipStr = request.getHeader("WL-Proxy-Client-IP");
        }
        if ( StringUtils.isBlank(ipStr) || UNKNOWN_PARAM.equalsIgnoreCase(ipStr) ) {
            ipStr = request.getRemoteAddr();
        }

        // 多个路由时，取第一个非unknown的ip
        final String[] arr = ipStr.split(",");
        for ( final String str : arr ) {
            if ( ! UNKNOWN_PARAM.equalsIgnoreCase(str) ) {
                ipStr = str;
                break;
            }
        }
        //目的是将localhost访问对应的ip 0:0:0:0:0:0:0:1 转成 127.0.0.1。
        return ipStr.equals("0:0:0:0:0:0:0:1") ? "127.0.0.1" : ipStr;
    }
}

