0=操作成功！
0000FFFF=请求错误
## 通用和字典类错误 0000-0050
0A110001=token信息不存在
0A110002=token校验失败或者已过期
0A110003=无权访问
0A110010=请求参数错误:%s
0A050010=请求参数错误:%s
0A110011s=获取系统配置错误
0A110027=参数错误！
0A110028=参数%s错误！
0A050000=获取Token异常
0A050001=获取配置异常
0A050012=获取系统状态异常
0A050013=设置token有效期异常
0A050014=异步任务已存在
0A050015=无权操作该接口
0A050016=内部服务调用异常
0A050101=租户添加异常
0A050102=租户ID不可为空
0A050103=租户不存在
0A050104=租户状态不属于初始化或停服，无法删除
0A050105=租户编辑异常
0A050106=注册租户名称不可为空
0A050107=注册租户标识不可为空
0A050108=租户标识或名称已存在
0A050109=注册租户ID为空
0A050110=租户初始三员信息输入错误
0A050111=注册租户数据库操作错误
0A050112=租户启动异常
0A050113=租户停用异常
0A050114=设备组不存在
0A050115=设备已绑定
0A050116=设备不存在
0A050117=释放设备失败
0A050118=服务组不存在
0A050119=服务不存在
0A050120=绑定服务失败，服务不属于:pki、svs、digist、kms、tsa、sms、otp、tsa
0A050121=注册租户对象为空
0A050122=租户已被审核，不可再次审核
0A050123=注册租户参数错误
0A050124=租户无用户信息
0A050125=注册租户不存在
0A050126=租户初始化失败
0A050127=租户三员的Ukey已被其他用户绑定
0A050128=租户三员信息数据错误
0A050129=获取业务类型列表错误
0A050130=获取租户业务类型错误
0A050131=设备未绑定当前租户
0A050132=服务未绑定当前租户
0A050133=服务属于服务组，请先从服务组中释放
0A050134=应用管理服务请求失败
0A050135=设备管理服务请求失败
0A050136=服务管理服务请求失败
0A050137=设备属于设备组，请先从设备组中释放
0A050138=租户三员信息的Ukey序列号已被使用
0A050139=租户三员信息的口令被禁用，请切换口令后重试
0A050140=设备已绑定其他租户
0A050141=业务类型接口调用失败
0A050142=业务类型对象不存在
0A050143=租户状态不属于运行中或停服失败，无法停服
0A050144=租户状态不属于停服中或启动失败，无法启动
0A050145=租户数据异常
0A050146=当前用户无权操作该租户
0A050147=获取设备组失败
0A050148=获取服务组列表失败
0A050149=租户未拥有足够业务类型
0A050150=租户无密钥配置
0A050151=租户包含应用，不允许删除租户
0A050152=租户包含未释放的设备，不允许删除租户
0A050153=租户包含未释放的服务，不允许删除租户
0A050154=租户包含密钥信息，故无法释放密钥管理服务
0A050155=租户包含加解密、签名验签、杂凑服务，需使用密钥管理服务，故无法释放
0A050156=注册记录不可删除
0A050157=删除租户和业务类型关联关系错误
0A050158=删除租户三员失败
0A050159=删除注册租户失败
0A050160=注册租户机构不可为空
0A050161=租户绑定设备一次最多不能超过5台
0A050162=用户名不可为空
0A050163=用户名只能包含大小写字母、数字，且长度4-20
0A050164=姓名不可为空
0A050165=姓名只能包含大小写字母、数字、字符_，且长度2-15
0A050166=创建业务地址失败
0A050167=编辑业务地址失败
0A050168=删除业务地址失败
0A050169=业务地址不存在
0A050170=业务地址名称重复
0A050171=业务地址类型不存在
0A050172=无空闲设备可用
0A050173=服务类型对象添加设备配置信息错误
0A050174=租户Hmac更新失败
0A050175=租户注册Hmac更新失败
0A050176=租户配额Hmac更新失败
0A050177=删除租户已经关联的数据库失败
0A050178=查询租户关联的数据库失败
0A050179=服务组标识不可为空
0A050180=租户%s无法正常使用，请联系密服平台系统操作员处理
0A050181=租户分区信息错误
0A050182=租户与设备分区不一致
0A050183=设备组添加网关路由错误
0A050184=设备组下发的服务时报错
0A050185=服务绑定设备关系入库失败
0A050186=获取服务绑定设备失败
0A050187=该设备类型设备不可被密码服务使用
0A050188=采集设备监控指标异常
0A050189=租户绑定组织机构/单位信息错误
0A050190=组织机构/单位已被租户绑定不允许删除
0A050191=租户下已存在该业务类型的独享服务，无法再次绑定共享服务
0A050192=租户是否支持专享服务配置值错误
0A050193=租户支持专享服务时数据库必选
0A050194=租户和服务组的分区不一致
0A050195=服务组已被租户绑定，不允许重复绑定
0A050196=租户绑定的单位/组织机构信息不允许为空
0A050197=租户和服务组的对应关系不存在
0A050198=调用认证中心注册租户失败
0A050199=调用认证中心删除租户失败
0A050200=调用认证中心获取token失败:%s


0A050201=应用业务管理删除失败
0A050202=应用业务管理查询失败
0A050203=应用业务管理更新失败
0A050204=应用业务类型关联组类型错误，只可为1：设备组；2：服务组
0A050205=应用业务类型关联组不支持该业务
0A050206=应用对象不存在
0A050207=应用服务网络错误
0A050208=迁移设备后会导致无默认设备可用，将影响使用默认设备的应用业务正常运行
0A050209=迁移设备后会导致设备组无设备可用，将影响使用该设备组的应用业务正常运行
0A050210=迁移设备后会导致无默认设备可用，将影响使用默认设备的业务服务正常运行
0A050211=迁移设备后会导致设备组无设备可用，将影响使用该设备组的业务服务正常运行
0A050212=应用业务类型为空
0A050213=包含应用不支持的业务类型，应用业务类型不属于:pki、svs、digist、kms、tsa、sms、otp、tsa
0A050214=应用信息查询失败
0A050215=删除服务组将影响应用业务正常使用，请先解绑服务组和应用业务的关联关系后再删除
0A050216=迁移服务后会导致服务组无服务可用，将影响使用该服务组的应用业务正常运行
0A050217=迁移服务后会导致无默认服务可用，将影响使用默认服务的应用业务正常运行
0A050218=删除设备组将影响服务正常使用，请先解绑设备组和服务的关联关系后再删除
0A050219=应用添加失败
0A050220=服务类型ID不可为空
0A050221=应用标识已存在
0A050222=应用不支持该业务类型
0A050223=应用标识已存在
0A050224=调用密管接口异常:%s
0A050225=应用注册表查询失败
0A050226=应用获取aksk数量失败
0A050227=应用aksk可添加数量达到上限
0A050228=应用所选区域和租户不一致
0A050229=应用业务类型关联组和应用所属区域不同
0A050230=应用修改访问密码和原访问密码相同不允许修改
0A050231=删除服务组将影响租户正常使用，无法删除
0A050232=删除设备组影响租户使用，请先解绑设备组和租户的关系
0A050233=删除设备组影响业务使用，请先解绑设备组和业务的关系
0A050234=设备组必须是支持业务调用的共享设备组
0A050235=租户未绑定该设备组
0A050236=服务组ID不能为空
0A050237=组类型不能为空


0A050301=接收设备信息接口返回值为空
0A050302=接收设备信息接口返回结果异常
0A050303=配置公钥接口返回值为空
0A050304=查询公钥指纹接口返回值为空
0A050305=清除公钥接口返回值为空
0A050306=配置公钥接口返回结果异常
0A050307=查询公钥指纹接口返回结果异常
0A050308=清除公钥接口返回结果异常
0A050309=获取本地公钥指纹异常
0A050310=配置公钥异常
0A050311=接收配置信息接口返回值为空
0A050312=接收配置信息接口返回结果异常
0A050313=启动业务服务接口返回值为空
0A050314=启动业务服务接口返回结果异常
0A050315=停止业务服务接口返回值为空
0A050316=停止业务服务接口返回结果异常
0A050317=初始化租户返回值为空
0A050318=初始化租户返回结果异常
0A050319=删除租户返回值为空
0A050320=删除租户返回结果异常
0A050321=服务名称不可重复
0A050322=服务被绑定使用中不可删除
0A050323=IP端口重复
0A050326=更新服务状态失败
0A050327=服务不存在
0A050328=端口不能为空
0A050329=服务运行中不可删除
0A050330=获取服务菜单接口返回值为空
0A050331=获取服务菜单接口返回结果异常
0A050332=设备组下不存在设备信息
0A050333=获取服务状态返回值为空
0A050334=获取服务状态返回值异常
0A050335=服务类型输入错误，没有符合条件的服务类型
0A050336=租户未绑定设备
0A050337=添加监控设备返回值为空
0A050338=添加监控设备异常
0A050339=删除监控设备返回值为空
0A050340=删除监控设备异常
0A050341=添加服务报错:%s
0A050342=更新token有效期失败
0A050343=获取子服务操作日志失败
0A050344=下发租户配额返回值为空
0A050345=下发租户配额返回结果异常
0A050346=获取租户配额返回值为空
0A050347=获取租户配额返回结果异常
0A050348=获取服务统计信息返回值为空
0A050349=获取服务统计信息返回结果异常
0A050350=不能从主机改为备机
0A050351=当前服务应用数量已达最大配额，请添加配额后重试
0A050352=服务ID不可为空
0A050353=租户ID不可为空
0A050354=设备组ID不可为空
0A050355=初始化参数异常:%s
0A050356=初始化启动异常:%s
0A050357=已绑定服务组不可切换
0A050358=服务切换服务组异常:%s
0A050359=服务组没有绑定数据库实例
0A050360=数据库映射地址不能为空
0A050361=服务组不支持绑定该类型服务
0A050362=服务删除-释放服务异常:%s
0A050363=同服务类型同ip不可重复
0A050364=平台不支持创建共享服务组
0A050365=当前用户无权创建共享服务组，请联系系统操作员
0A050366=创建共享服务组时，必须指定业务类型
0A050367=该业务类型不支持创建共享服务组
0A050368=创建该业务类型的服务组需选择一个共享的密钥管理服务
0A050369=选择的共享密钥管理服务组为空或业务类型错误，请重新选择
0A050370=服务组非共享服务组，无法进行绑定
0A050371=服务所属业务类型和共享服务组不一致，无法进行绑定
0A050372=服务组组ID不可为空
0A050373=无法找到运行状态正常的服务进行处理
0A050374=服务初始化中不可重置密码
0A050375=租户绑定共享服务组创建路由报错
0A050376=租户解绑共享服务组处理路由报错
0A050377=释放该服务后，将影响对应租户的使用
0A050378=释放该服务后，将影响对应服务组的使用
0A050379=是否共享不可为空
0A050380=专享服务组所属租户不可为空
0A050381=平台不支持创建独享服务组
0A050382=选择的共享密钥管理服务组没有服务，请重新选择
0A050383=请先释放服务，再删除该服务组
0A050384=服务不属于该服务组
0A050385=获取宿主机端口列表失败
0A050386=服务管理添加容器端口映射失败
0A050387=管理端口和业务端口不能一样
0A050388=镜像:%s_%s不存在
0A050389=服宿主机繁忙
0A050390=镜像下载和上传到远程失败
0A050391=专享服务组和专享设备组的租户不一致
0A050392=VPN创建数量已达上限
0A050393=%s镜像正在上传到%s
0A050394=分配VPN业务端口范围失败
0A050395=宿主机分配端口失败

0A050400=证方式为CRL时，凭证CRL不能为空
0A050401=认证方式为OCSP时，OCSP地址不能为空
0A050402=认证方式为OCSP时，OCSP客户端证书不能为空
0A050403=认证方式为OCSP时，OCSP认证口令不能为空
0A050404=解析文件失败
0A050405=信任域证书不能为空
0A050406=添加信任域失败
0A050407=无法构造有效的证书链
0A050408=PEM编码CRL失败
0A050409=验证CRL失败
0A050410=证书标签已存在
0A050411=信任域证书不存在
0A050412=口令解密失败
0A050413=租户信息不存在
0A050414=认证方式为OCSP时，CA证书不能为空
0A050415=认证方式为OCSP时，解析客户端证书失败
0A050416=认证方式为OCSP时，解析CA证书失败
0A050417=认证方式为OCSP时，解析节点证书失败
0A050418=认证方式为OCSP时，解析节点不能为空
0A050419=证书文件类型错误，仅允许上传cer或p7b格式证书文件
0A050420=CRL验证文件类型错误，仅允许上传crl格式证书文件
0A050421=OCSP客户端证书文件类型错误，证书必须为pfx文件
0A050422=OCSP-CA证书文件类型错误，证书必须为cer或p7b文件
0A050423=OCSP节点证书文件类型错误，证书必须为cer或p7b文件
0A050424=信任域证书文件大小不能超过20KB
0A050425=CRL验证文件大小不能超过20MB
0A050426=OCSP客户端证书文件大小不能超过20MB
0A050427=OCSP-CA证书文件大小不能超过20KB
0A050428=OCSP节点证书文件大小不能超过20KB
0A050429=请输入正确的OCSP地址
0A050430=证书序列号已存在


0A050501=无法连接到密钥管理系统
0A050502=无权调用KMS
0A050503=配置值格式错误

0A050801=路由请求规则uris不可为空
0A050802=路由请求规则ipPorts不可为空
0A050803=请求参数位置不符合要求，可选值：http,post_arg,cookie,arg
0A050804=路由ID不可为空
0A050805=路由请求服务名称不可为空
0A050806=路由添加失败
0A050807=附加参数varsName、varsVal、varsOper不可为空
0A050808=运算符不符合要求，可选值为：==   ~=    >    <   IN
0A050809=服务类型ID不可为空
0A050810=租户ID不可为空
0A050811=租户编码不可为空
0A050812=数据库主键标识ID不可为空
0A050813=应用ID不可为空
0A050814=应用编码不可为空
0A050815=应用绑定服务选择服务组，服务不属于:pki、svs、digist、kms、tsa、sms、otp、tsa
0A050816=网关信息不可重复添加
0A050817=网关信息不存在
0A050818=路由更新失败
0A050819=路由删除失败
0A050820=路由查询失败
0A050821=路由类型不能为空
0A050822=TCP端口不足
0A050823=TCP路由更新失败
0A050824=网关下绑定服务不允许删除
0A050825=网关名称重复
0A050826=网关组件标识重复
0A050827=网关IP端口重复
0A050828=系统中不存在管理类型网关，请先添加网关
0A050829=系统中不存在业务类型网关，请先添加网关
0A050830=有服务信息存在，同一网关类型下至少保留一个网关
0A050831=组ID不可为空
0A050832=组标识不可为空
0A050833=区域id不可为空
0A050834=有服务信息存在，区域范围内同一网关类型至少保留一个网关
0A050835=系统不存在可用网关
0A050836=网关信息错误，网关IP不存在或重复:%s
0A050837=区域下只允许添加业务网关
0A050838=区域模式不允许添加管理网关
0A050839=至少保留一个管理网关
0A050840=无法删除已绑定服务的网关
0A050841=区域下不存在可用网关
0A050842=系统中不存在可用网关
0A050843=区域下不存在可用的网关SDK端口
0A050844=系统中不存在可用的网关SDK端口
0A050845=不允许删除管理网关
0A050846=密码服务代理已存在
0A050847=网关不可使用，请确认网关状态后添加
0A050848=业务网关所属区域不可为空
0A050849=网关IP,API端口重复


0A050901=一次导出操作日志不能大于5000条
0A050902=没有需要导出的日志数据
0A050903=此服务类型没有对应的下载文档
0A050921=配额信息不存在
0A050922=配额字典表中信息不存在
0A050923=配额值不能为0
0A050924=配额值小于允许设置的最小配额值
0A050925=配额值大于允许设置的最大配额值
0A050991=请上传主密钥备份文件
0A050992=主密钥备份文件内容为空或文件被损坏
0A050993=请上传密钥备份文件
0A050994=密钥备份文件内容为空或文件被损坏
0A050995=上传文件必须是.bk文件

0A051001=参数%s错误！
0A051101=申请许可证记录不存在
0A051102=申请许可证不允许下载
0A051103=申请许可证数据被非法纂改
0A051104=申请许可证已使用，不允许重复导入
0A051105=许可证数据与原申请数据不匹配，不允许导入
0A051106=不支持的许可类型
0A051107=申请一次永久许平台许可证时数量不允许超过1个
0A051005=许可证无效
0A051006=许可证已使用
0A051007=许可证数据被非法纂改
0A051008=许可证使用中
0A051009=许可证不存在
0A051010=许可证绑定服务实例不存在
0A051011=许可证失效
0A051012=永久许可证不支持续约
0A051013=许可证未使用，无法释放资源
0A051014=服务实例已绑定许可证，无法重复绑定
0A051015=不支持业务服务许可证
0A051016=无可用的许可证
0A051017=业务服务存在续约许可证，不支持释放
0A051099=许可证内部异常，请联系管理员！

0A051201=监控地址必须是服务所在服务器地址
0A051202=大屏指标模板数据不允许为空，请联系管理员！
0A051203=参数%s不允许为空

0A051251=初始化参数接口异常:%s
0A051252=初始化配置接口异常:%s
0A051253=初始化设备接口异常:%s
0A051254=初始化启动接口异常:%s
0A051255=初始化修改自启参数接口异常:%s
0A051256=查询端口可用情况接口异常:%s

0A051301=系统初始化信息失败
0A051302=解析jdbc-url失败
0A051303=添加数据库服务失败
0A051304=添加数据库实例失败
0A051305=添加数据库实例与服务组关系失败
0A051306=获取初始化人员信息失败

0A051401=区域不存在
0A051402=区域标识或名称已存在
0A051403=添加区域数据库操作错误
0A051404=区域名称重复
0A051405=区域下包含租户，无法删除
0A051406=区域下包含网关，无法删除
0A051407=区域下包含网关路由，无法删除
0A051408=区域下包含密码设备，无法删除
0A051409=区域下包含密码服务，无法删除
0A051410=区域ID不可为空
0A051411=区域下包含数据库服务，无法删除
0A051412=区域下包含应用，无法删除
0A051413=区域下包含设备组，无法删除
0A051414=区域下包含服务组，无法删除
0A051415=区域ID和所属租户区域ID不一致
0A051416=数据库所属区域和区域ID不一致
0A051417=业务对象IP和端口重复
0A051418=存在正在使用的区域，区域模式无法修改
0A051419=服务云标识已存在


0A051321=SIM盾证书未申请
0A051322=申请证书异常
0A051323=更新证书异常
0A051324=撤销证书异常
0A051325=重置PIN码异常
0A051326=设置PIN码异常
0A051327=修改PIN码异常
0A051328=SIM流水日志不存在
0A051329=SIM盾用户状态不合法
0A051330=手机号已被其他用户占用
0A051331=SIM盾证书数据库操作失败
0A051332=登录失败，未开通SIM盾
0A051333=请求已过期
0A051334=请求处理异常
0A051335=证书已申请成功，请勿重复申请
0A051336=回调接口处理证书请求异常
0A051337=回调接口签名验签异常
0A051338=证书状态不合法
0A051339=Sim盾用户不存在
0A051340=签名值、方法和状态信息不可为空
0A051341=SIM签名请求失败
0A051342=手机号格式错误
0A051343=SIM盾异步请求错误：%s
0A051344=SIM登录检查失败：%s
0A051345=会话keyId不可为空
0A051346=SIM验签失败


0A051361=SIMKEY用户不存在
0A051362=SIMKEY用户状态不合法
0A051363=SIMKEY用户权限错误
0A051364=绑定或解绑目标用户不存在
0A051365=绑定或解绑目标SIMKEY用户不存在
0A051366=不可绑定已经绑定了的SIMKEY用户
0A051367=SIMKEY用户未与此用户绑定，不可解绑
0A051368=SIMKEY手机号已存在
0A051369=SIMKEY白名单项不存在
0A051370=Excel文件数据格式错误或数据过长,请检查第%s行数据
0A051371=Excel文件手机号码重复或已在白名单中，请检查第%s行手机号码
0A051372=Excel文件解析错误
0A051373=Excel文件格式错误，请上传xlsx格式的文件
0A051374=Excel大小超出限制，请上传2M以内的文件
0A051375=Excel文件与模板文件不匹配，请下载模板文件填写
0A051376=手机号已注册SIMKEY
0A051377=SIMKEY用户已开通业务，不可重复开通
0A051378=SIMKEY用户被禁用，不可开通SIMKEY
0A051379=无开通权限，请联系管理员添加白名单
0A051380=生成登录验证码失败
0A051381=登录请求已过期
0A051382=SIMKEY用户未绑定系统用户
0A051383=SIMKEY手机号不在白名单
0A051384=SIMKEY用户被禁用
0A051385=SIMKEY用户未开通
0A051386=SIMKEY用户证书未申请
0A051387=取SIMKEY证书错误，SIMKEY证书无效
0A051388=SIMKEY登录验证签名值失败
0A051389=无此业务操作类型
0A051390=SIMKEY用户当前有正常使用证书，不需要申请
0A051391=SIMKEY用户没有启用状态的证书
0A051392=SIMKEY用户未申请证书或不存在已启用的证书，无法更新证书
0A051393=用户名或密码错误,登录失败
0A051394=SIMKEY获取证书信息失败
0A051395=SIMKEY登录状态检测错误：%s
0A051396=SIMKEY用户未申请证书


0A051451=获取redis中的密钥值出错
0A051452=输入口令解密出错
0A051453=密码错误

0A051461=告警源已过期
0A051462=区域告警信息查询失败

0A051471=管控服务已存在
0A051472=管控服务不存在
0A051473=管控服务状态异常
0A051474=管控服务入库失败
0A051475=无法操作，请先在该区域下添加管控服务
0A051476=管控服务删除失败
0A051477=当前区域下主管控服务已存在;


0A051501=监控服务禁止删除
0A051502=监控服务ID不存在
0A051503=区域下已存在监控服务，请勿重复添加
0A051504=删除监控服务失败
0A051505=查询监控服务区域ID为空
0A051506=区域下不存在可用网关
0A051507=区域下不存在监控服务
0A051508=监控服务状态异常
0A051509=监控服务添加失败

0A051480=认证服务不可用
0A051481=认证服务不存在
0A051482=认证服务地址已存在
0A051483=认证服务区域不可变更
0A051484=认证服务名称已存在
0A051485=认证服务ip不可变更
0A051486=认证服务端口不可变更
0A051487=区域下存在应用

0A051521=网关统计组件标识已存在
0A051522=网关统计组件不存在
0A051523=区域下最后一个统计组件不可删除
0A051524=删除网关统计组件失败
0A051525=当前区域下不存在该网关



0A051601=服务标识不存在
0A051602=应用标识不存在
0A051603=租户标识不存在
0A051604=采集时间格式化小时失败
0A051605=采集数据目标ip端口错误
0A051606=采集时间不存在
0A051607=统计分析参数查询不存在
0A051608=查询类型不存在
0A051609=查询时间格式不正确
0A051610=应用不存在
0A051611=应用所属租户信息不存在
0A051612=请求统计模块失败,%s
0A051613=权限错误，请选择您本单位下的租户

0A051700=hdfs未知异常
0A051701=本地文件不存在
0A051702=hdfs文件已存在
0A051721=文件hash读取异常
0A051722=镜像文件已损坏
0A051723=超过上传镜像文件最大总和限制
0A051724=镜像文件异常
0A051725=镜像文件异常
0A051726=已存在相同镜像
0A051727=镜像不存在
0A051728=镜像已启用
0A051729=镜像不可修改
0A051730=镜像初始化中



0A051801=获取指定密码产品开通状态失败

#云算力推送
0A051901=批量数据不能为空
0A051902=Kafka推送数据失败
0A051903=租户未开通该产品

0A052771=查询监控数据异常：%s
0A052772=时间格式错误
0A052773=查询对象类型不存在
0A052774=数据统计类型不存在