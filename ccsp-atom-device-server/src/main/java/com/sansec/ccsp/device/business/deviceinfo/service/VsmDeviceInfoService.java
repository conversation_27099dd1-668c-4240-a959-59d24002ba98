package com.sansec.ccsp.device.business.deviceinfo.service;

import com.sansec.ccsp.device.business.devicenet.entity.DeviceNetDetailPO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdsDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoPageDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsm.VsmDeviceInfoCreateDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsm.VsmDeviceInfoEditDTO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoVO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceStatusVO;
import com.sansec.ccsp.device.deviceinfo.response.vsm.VsmDeviceInfoVO;
import com.sansec.ccsp.device.devicenet.response.DeviceNetDetailVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/2/25 15:28
 * @Description: 虚拟机对外接口
 */
public interface VsmDeviceInfoService {

    /**
     * @param deviceInfoPageDTO 筛选条件
     * @return 查询结果
     * @Description: 分页查询
     */
    SecRestResponse<SecPageVO<DeviceInfoVO>> find(DeviceInfoPageDTO deviceInfoPageDTO);

    /**
     * @param vsmDeviceInfoCreateDTO
     * @return
     * @Description: 批量创建虚拟机
     */
    SecRestResponse<List<DeviceNetDetailVO>>  createVsm(VsmDeviceInfoCreateDTO vsmDeviceInfoCreateDTO);

    /**
     * @param deviceIdDTO
     * @return
     * @Description: 重启虚拟机
     */
    SecRestResponse<Long> restart(DeviceIdDTO deviceIdDTO);

    /**
     * @param deviceIdDTO
     * @return
     * @Description: 删除虚拟机
     */
    SecRestResponse<Long> delete(DeviceIdDTO deviceIdDTO);

    /**
     * 强制删除虚拟机
     * @param deviceIdDTO
     * @return
     */
    SecRestResponse<Long> forceDelete(DeviceIdDTO deviceIdDTO);

    /**
     * @param deviceIdDTO
     * @return
     * @Description: 启动虚拟机
     */
    SecRestResponse<Long> start(DeviceIdDTO deviceIdDTO);

    /**
     * @param deviceIdDTO
     * @return
     * @Description: 停止虚拟机
     */
    SecRestResponse<Long> stop(DeviceIdDTO deviceIdDTO);

    /**
     * @param deviceIdsDTO
     * @return
     * @Description: 获取虚拟机状态
     */
    SecRestResponse<List<DeviceStatusVO>> status(DeviceIdsDTO deviceIdsDTO);

    /**
     * @param deviceIdDTO
     * @return
     * @Description: 获取虚拟机内部设备详情
     */
    SecRestResponse<VsmDeviceInfoVO> vsmInfo(DeviceIdDTO deviceIdDTO);

    /**
     * @param deviceIdDTO
     * @return
     * @Description: 获取虚拟机基本信息
     */
    SecRestResponse<DeviceInfoVO> info(DeviceIdDTO deviceIdDTO);

    /**
     * @param vsmDeviceInfoEditDTO
     * @return
     * @Description: 编辑虚拟机
     */
    SecRestResponse<Long> edit(VsmDeviceInfoEditDTO vsmDeviceInfoEditDTO);

    SecRestResponse<List<Long>> getVsmIdList();

    /**
     * 根据ip查询设备（不止虚机）
     * @param ip
     * @return
     */
    SecRestResponse<DeviceInfoVO> getDeviceByIp(String ip);

    List<DeviceInfoVO> findDeviceUnUsed(DeviceInfoPageDTO deviceInfoPageDTO);
}
