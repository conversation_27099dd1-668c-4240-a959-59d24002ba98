package com.sansec.ccsp.common.business.role.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.common.business.role.entity.UserRolePO;
import com.sansec.ccsp.common.role.request.UserRoleDTO;
import com.sansec.ccsp.common.role.response.UserRoleVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

 /**
 * @Description: 用户角色表;(USER_ROLE)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Mapper(componentModel = "spring")
public interface UserRoleConvert{
    /**
     * dtoToPo
     * @param userRoleDTO
     * @return
     */
    @Mappings({})
    UserRolePO dtoToPo(UserRoleDTO userRoleDTO);

    /**
     * poToDto
     * @param userRolePO
     * @return
     */
    UserRoleDTO poToDto(UserRolePO userRolePO);

    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<UserRoleDTO> poToDto(List<UserRolePO> list);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<UserRoleVO> pagePOToSecPageVOPage(IPage<UserRolePO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<UserRoleVO> convert(List<UserRolePO> list);

    @Mappings({})
    UserRoleVO convertVo(UserRolePO request);
}
