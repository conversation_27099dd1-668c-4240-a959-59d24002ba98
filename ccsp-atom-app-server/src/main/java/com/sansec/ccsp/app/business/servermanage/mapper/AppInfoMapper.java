package com.sansec.ccsp.app.business.servermanage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.app.business.servermanage.entity.AppInfoPO;
import com.sansec.ccsp.app.info.request.AppInfoPageListDTO;
import com.sansec.ccsp.app.info.response.AppIdNameVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * APP_INFO
 * <AUTHOR>
 * 2023-02-18 13:59:19
 */
public interface AppInfoMapper extends BaseMapper<AppInfoPO> {

    IPage<AppInfoPO> findPageList(IPage<AppInfoPO> page, @Param("dto") AppInfoPageListDTO dto);

    List<AppIdNameVO> getAppIdNameListByBusiType(@Param("busiType") Long busiType, @Param("tenantId") Long tenantId);
}