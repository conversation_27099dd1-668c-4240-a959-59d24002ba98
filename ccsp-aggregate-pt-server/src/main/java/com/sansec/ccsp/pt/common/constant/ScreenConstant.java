package com.sansec.ccsp.pt.common.constant;
/**
 * @Describe 大屏常量数据
 * <AUTHOR>
 * @Date 2023/7/10 11:43
 **/
public class ScreenConstant {

    /**
     * 空值
     */
    public static final String SCREEN_NULL_VALUE = "-";
    // 增量类
    public static final Integer INCRE_STATISTIC_TYPE = 1;

    /**
     * 二级指标总次数
     */
    public static final String INDEX_2_TOTALS="totals";
    /**
     * 二级指标成功次数
     */
    public static final String INDEX_2_SUCCESS="totalNumSuccess";
    /**
     * 二级指标失败次数
     */
    public static final String INDEX_2_ERROR="totalError";
    /**
     * 设备总数量
     */
    public static final String DEVICE_STATUS_SUM="device_sum";
    /**
     * 设备使用状态
     */
    public static final String DEVICE_STATUS_RUN="device_used";
    /**
     * 设备异常状态
     */
    public static final String DEVICE_STATUS_ERROR="device_error";
    /**
     * 设备空闲状态
     */
    public static final String DEVICE_STATUS_UNUSED="device_unused";

    /**
     * 服务总数量
     */
    public static final String SERVICE_STATUS_SUM="service_sum";
    /**
     * 服务运行状态
     */
    public static final String SERVICE_STATUS_RUN="service_run";
    /**
     * 服务异常状态
     */
    public static final String SERVICE_STATUS_ERROR="service_error";
    /**
     * 服务启动状态
     */
    public static final String SERVICE_STATUS_START="service_start";
    /**
     * 服务停止状态
     */
    public static final String SERVICE_STATUS_STOP="service_stop";
    /**
     * 租户总数
     */
    public static final String CENTER_TENANT_NUM="tenant_num";
    /**
     * 许可总数
     */
    public static final String CENTER_LICENSE_NUM="license_num";
    /**
     * 大屏中央指标模板
     */
    public static final String INDEX_TEMPLATE_CENTER="center";
    /**
     * 大屏中央指标模板租户
     */
    public static final String INDEX_TEMPLATE_CENTER_TENANT="center-tenant";

}
