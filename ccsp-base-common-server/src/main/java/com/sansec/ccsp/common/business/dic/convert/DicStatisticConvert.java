package com.sansec.ccsp.common.business.dic.convert;

import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import com.sansec.ccsp.common.dic.request.DicStatisticDTO;
import com.sansec.ccsp.common.dic.response.DicStatisticVO;
import com.sansec.ccsp.common.business.dic.entity.DicStatisticPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.common.param.response.SecPageVO;
import java.util.List;

 /**
 * @description : 统计指标字典表;(DIC_STATISTIC)实体类转换接口
 * <AUTHOR> x<PERSON><PERSON>jiaw<PERSON>
 * @date : 2023-5-13
 */
@Mapper(componentModel = "spring")
public interface DicStatisticConvert{
    /**
     * dtoToPo
     * @param dicStatisticDTO
     * @return
     */
    @Mappings({})
    DicStatisticPO dtoToPo(DicStatisticDTO dicStatisticDTO);
    
    /**
     * poToDto
     * @param dicStatisticPO
     * @return
     */
    DicStatisticDTO poToDto(DicStatisticPO dicStatisticPO);
    
    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<DicStatisticDTO> poToDto(List<DicStatisticPO> list);
     
    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<DicStatisticVO> pagePOToSecPageVOPage(IPage<DicStatisticPO> iPage);
    
    @InheritConfiguration(name = "convertVo")
    List<DicStatisticVO> convert(List<DicStatisticPO> list);
    
    @Mappings({})
    DicStatisticVO convertVo(DicStatisticPO request);
}