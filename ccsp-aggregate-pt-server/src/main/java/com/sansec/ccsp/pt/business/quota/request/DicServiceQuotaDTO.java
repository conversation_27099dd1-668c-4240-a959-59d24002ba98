package com.sansec.ccsp.pt.business.quota.request;

import lombok.Data;

 /**
  * <AUTHOR> xia<PERSON><PERSON>awei
  * @description : 业务服务配额信息;
  * @date : 2023-5-10
  */
 @Data
 public class DicServiceQuotaDTO {
     /**
      * 主键
      */
     private Long id;
     /**
      * 配额名称;key值
      */
     private String quotaName;
     /**
      * 配额显示名称;展示名称
     */
    private String showName;
    /**
     * 配额值单位
     */
    private String valueUnit;
    /**
     * 服务类型ID
     */
    private Long serviceTypeId;
    /**
     * 服务标识
     */
    private String serviceCode;
    /**
     * 时间是否生效;1：有效，0：不生效
     */
    private Integer timeValid;
    /**
     * 默认配额值
     */
    private Integer defaultValue;
    /**
     * 是否显示;1：显示；0：不显示
     */
    private Integer isEnable;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
     /**
      * 管理侧，1：服务侧管理，2：平台侧管理
      */
   private Integer manageType;
}