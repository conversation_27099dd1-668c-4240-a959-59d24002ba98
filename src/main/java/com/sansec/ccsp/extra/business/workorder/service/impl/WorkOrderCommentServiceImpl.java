package com.sansec.ccsp.extra.business.workorder.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.extra.business.workorder.convert.WorkOrderCommentConvert;
import com.sansec.ccsp.extra.business.workorder.entity.WorkOrderCommentPO;
import com.sansec.ccsp.extra.business.workorder.mapper.WorkOrderCommentMapper;
import com.sansec.ccsp.extra.business.workorder.response.WorkOrderCommentVO;
import com.sansec.ccsp.extra.business.workorder.service.WorkOrderCommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class WorkOrderCommentServiceImpl extends ServiceImpl<WorkOrderCommentMapper, WorkOrderCommentPO> implements WorkOrderCommentService {
    @Resource
    private WorkOrderCommentConvert workOrderCommentConvert;

}
