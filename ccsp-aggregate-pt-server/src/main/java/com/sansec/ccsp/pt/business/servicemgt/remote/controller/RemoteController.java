package com.sansec.ccsp.pt.business.servicemgt.remote.controller;

import com.sansec.ccsp.pt.business.servicemgt.remote.request.*;
import com.sansec.ccsp.pt.business.servicemgt.remote.service.RemoteService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description: 管控测试类
 * @Date: 2023年2月21日
 */
@RestController
@RequestMapping("/remote/v1")
@Validated
public class RemoteController {
    @Resource
    private RemoteService remoteService;

    /**
     * @description: 初始化参数
     * @param initServiceParamDTO
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/initParam/{remoteIp}/{remotePort}")
    public void initParam(@RequestBody InitServiceParamDTO initServiceParamDTO, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.initParam(initServiceParamDTO,remoteIp,remotePort);
    }

    /**
     * @description: 初始化启动
     * @param initServiceStartDTO
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/initStart/{remoteIp}/{remotePort}")
    public void initStart(@RequestBody InitServiceStartDTO initServiceStartDTO, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.initStart(initServiceStartDTO,remoteIp,remotePort);
    }

    /**
     * @description: 初始化配置
     * @param initServiceConfigDTO
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/initConfig/{remoteIp}/{remotePort}")
    public void initConfig(@RequestBody InitServiceConfigDTO initServiceConfigDTO, @PathVariable(value = "remoteIp")String remoteIp, @PathVariable(value = "remotePort")Integer remotePort) {
        remoteService.initConfig(initServiceConfigDTO,remoteIp,remotePort);
    }

    /**
     * @description: 初始化jce
     * @param initServiceParamDeviceDTO
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/initJce/{remoteIp}/{remotePort}")
    public void initJce(@RequestBody InitServiceParamDeviceDTO initServiceParamDeviceDTO, @PathVariable(value = "remoteIp") String remoteIp, @PathVariable(value = "remotePort") Integer remotePort) {
        remoteService.initJce(initServiceParamDeviceDTO, remoteIp, remotePort);
    }

    /**
     * @description: 初始化修改自启参数
     * @param initEditStartParam
     * @param remoteIp
     * @param remotePort
     * @return: void
     */
    @PostMapping("/initEditStartParam/{remoteIp}/{remotePort}")
    public void initEditStartParam(@RequestBody InitEditStartParamDTO initEditStartParam, @PathVariable(value = "remoteIp") String remoteIp, @PathVariable(value = "remotePort") Integer remotePort) {
        remoteService.initEditStartParam(initEditStartParam, remoteIp, remotePort);
    }

    /**
     * @param initServiceParamDeviceDTO
     * @description: 获取平台管控服务信息
     * @return: void
     */
//    @PostMapping("/getPtRemoteServiceInfo")
//    public void getPtRemoteServiceInfo(@RequestBody InitServiceParamDeviceDTO initServiceParamDeviceDTO) {
//        remoteService.getPtRemoteServiceInfo();
//    }

}