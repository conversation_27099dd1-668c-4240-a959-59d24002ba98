package com.sansec.ccsp.device.business.devicedic.convert;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.device.business.devicedic.entity.DicDeviceInteractionTypePO;
import com.sansec.ccsp.device.business.devicedic.request.DicDeviceInteractionTypeDTO;
import com.sansec.ccsp.device.business.devicedic.response.DicDeviceInteractionTypeVO;
import com.sansec.ccsp.device.devicedic.response.DicDeviceInteractionTypeApiVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

 /**
 * @Description: 设备交互类型字典表;(DIC_DEVICE_INTERACTION_TYPE)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Mapper(componentModel = "spring")
public interface DicDeviceInteractionTypeConvert{
    /**
     * dtoToPo
     * @param dicDeviceInteractionTypeDTO
     * @return
     */
    @Mappings({})
    DicDeviceInteractionTypePO dtoToPo(DicDeviceInteractionTypeDTO dicDeviceInteractionTypeDTO);
    
    /**
     * poToDto
     * @param dicDeviceInteractionTypePO
     * @return
     */
    DicDeviceInteractionTypeDTO poToDto(DicDeviceInteractionTypePO dicDeviceInteractionTypePO);
    
    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<DicDeviceInteractionTypeDTO> poToDto(List<DicDeviceInteractionTypePO> list);
     
    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<DicDeviceInteractionTypeVO> pagePOToSecPageVOPage(IPage<DicDeviceInteractionTypePO> iPage);
    
    @InheritConfiguration(name = "convertVo")
    List<DicDeviceInteractionTypeVO> convert(List<DicDeviceInteractionTypePO> list);
    
    @Mappings({})
    DicDeviceInteractionTypeVO convertVo(DicDeviceInteractionTypePO request);

    List<DicDeviceInteractionTypeApiVO> poToApiVoList(List<DicDeviceInteractionTypePO> poList);
}