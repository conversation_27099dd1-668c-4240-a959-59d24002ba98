package com.sansec.ccsp.servicemgt.business.servicegroup.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.servicemgt.business.servicegroup.convert.ServiceGroupToGroupConvert;
import com.sansec.ccsp.servicemgt.business.servicegroup.entity.ServiceGroupToBusiTypePO;
import com.sansec.ccsp.servicemgt.business.servicegroup.entity.ServiceGroupToGroupPO;
import com.sansec.ccsp.servicemgt.business.servicegroup.mapper.ServiceGroupToGroupMapper;
import com.sansec.ccsp.servicemgt.business.servicegroup.service.ServiceGroupToGroupService;
import com.sansec.ccsp.servicemgt.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.servicemgt.servicegroup.request.ServiceGroupToGroupAddDTO;
import com.sansec.ccsp.servicemgt.servicegroup.request.ServiceGroupToGroupDTO;
import com.sansec.ccsp.servicemgt.servicegroup.request.ServiceGroupToGroupGetDTO;
import com.sansec.ccsp.servicemgt.servicegroup.request.ServiceGroupToGroupPageDTO;
import com.sansec.ccsp.servicemgt.servicegroup.response.ServiceGroupToGroupVO;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;

import java.time.LocalDateTime;

import com.sansec.common.id.IdGenerator;

import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> xiaojiawei
 * @description : 服务组关联服务组（330）;(SERVICE_GROUP_TO_GROUP)表服务实现类
 * @date : 2024-3-12
 */
@Service
@Slf4j
public class ServiceGroupToGroupServiceImpl extends ServiceImpl<ServiceGroupToGroupMapper, ServiceGroupToGroupPO> implements ServiceGroupToGroupService {
    @Resource
    private ServiceGroupToGroupConvert serviceGroupToGroupConvert;


    @Override
    public void addKmsGroup(ServiceGroupToGroupAddDTO serviceGroupToGroupAddDTO) {
        ServiceGroupToGroupPO serviceGroupToGroupPO = new ServiceGroupToGroupPO();
        serviceGroupToGroupPO.setId(IdGenerator.ins().generator());
        serviceGroupToGroupPO.setServiceGroupId(serviceGroupToGroupAddDTO.getServiceGroupId());
        serviceGroupToGroupPO.setKmsGroupId(serviceGroupToGroupAddDTO.getKmsGroupId());
        serviceGroupToGroupPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.insert(serviceGroupToGroupPO);
    }

    @Override
    public SecRestResponse<ServiceGroupToGroupVO> getKmsGroup(ServiceGroupToGroupGetDTO serviceGroupToGroupGetDTO) {
        QueryWrapper<ServiceGroupToGroupPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ServiceGroupToGroupPO::getServiceGroupId, serviceGroupToGroupGetDTO.getServiceGroupId());
        List<ServiceGroupToGroupPO> list = this.list(queryWrapper);

        if (CollectionUtils.isNotEmpty(list)) {
            ServiceGroupToGroupVO serviceGroupToGroupVO = serviceGroupToGroupConvert.convertVo(list.get(0));
            return ResultUtil.ok(serviceGroupToGroupVO);
        } else {
            log.error("ServiceGroupToGroupServiceImpl.getKmsGroup(); code={};error={}",
                    SecErrorCodeConstant.SERVICE_GROUP_TO_GROUP_ERROR, "服务组无绑定密钥管理服务组信息");
            throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_TO_GROUP_ERROR);
        }
    }

    @Override
    public SecRestResponse<List<ServiceGroupToGroupVO>> getServiceGroupByKmsGroupId(Long kmsServiceGroupId) {
        QueryWrapper<ServiceGroupToGroupPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ServiceGroupToGroupPO::getKmsGroupId, kmsServiceGroupId);
        List<ServiceGroupToGroupPO> list = this.list(queryWrapper);

        if (CollectionUtils.isNotEmpty(list)) {
            return ResultUtil.ok(serviceGroupToGroupConvert.convert(list));
        } else {
            return ResultUtil.ok(new ArrayList<>());
        }
    }

    @Override
    public SecRestResponse<Object> deleteToKmsGroup(ServiceGroupToGroupGetDTO ServiceGroupToGroupGetDTO) {
        QueryWrapper<ServiceGroupToGroupPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ServiceGroupToGroupPO::getServiceGroupId, ServiceGroupToGroupGetDTO.getServiceGroupId());
        List<ServiceGroupToGroupPO> list = this.list(queryWrapper);

        if (CollectionUtils.isNotEmpty(list)) {
            boolean isSuccess = this.remove(queryWrapper);
            if (isSuccess) {
                return ResultUtil.ok();
            } else {
                log.error("ServiceGroupToGroupServiceImpl.getKmsGroup(); code={};error={}",
                        SecErrorCodeConstant.SERVICE_GROUP_TO_GROUP_DELETE_ERROR, "删除服务组绑定密钥管理服务组信息失败");
                throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_TO_GROUP_DELETE_ERROR);
            }
        } else {
            log.error("ServiceGroupToGroupServiceImpl.getKmsGroup(); code={};error={}",
                    SecErrorCodeConstant.SERVICE_GROUP_TO_GROUP_ERROR, "服务组无绑定密钥管理服务组信息");
            throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_TO_GROUP_ERROR);
        }
    }
}