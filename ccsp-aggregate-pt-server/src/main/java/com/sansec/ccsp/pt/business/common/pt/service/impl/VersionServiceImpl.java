package com.sansec.ccsp.pt.business.common.pt.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.pt.business.common.config.service.CommonConfigService;
import com.sansec.ccsp.pt.business.common.pt.convert.VersionConvert;
import com.sansec.ccsp.pt.business.common.pt.entity.VersionPO;
import com.sansec.ccsp.pt.business.common.pt.mapper.VersionMapper;
import com.sansec.ccsp.pt.business.common.pt.response.VersionVO;
import com.sansec.ccsp.pt.business.common.pt.service.VersionService;
import com.sansec.ccsp.pt.common.constant.CommonConstant;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class VersionServiceImpl extends ServiceImpl<VersionMapper, VersionPO> implements VersionService {
    @Resource
    private VersionConvert versionConvert;

    @Resource
    CommonConfigService commonConfigService;
    /** 
     * 分页查询
     *
     * @return 查询结果
     */
    @Override
    public SecRestResponse<List<VersionVO>> find(){
        String sysOperationMode = commonConfigService.getConfigValueByConfigCode(CommonConstant.SYS_OPERATION_MODE);
        String addServiceIsAutoAddDevice = commonConfigService.getConfigValueByConfigCode(CommonConstant.ADD_SERVICE_IS_AUTO_ADD_DEVICE);


        LambdaQueryWrapper<VersionPO> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(VersionPO::getSordNum);
        List<VersionPO> versionPoList = baseMapper.selectList(queryWrapper);
        List<VersionPO> resPoList=new ArrayList<>();
        //多租户模式只返回平台版本V1.0
        //目前使用多租户模式，addServiceIsAutoAddDevice=true两个变量判断华为模式，没有找到别的好方法
        if(sysOperationMode.equals("2") && addServiceIsAutoAddDevice.equals("true")){
            for (VersionPO versionPO : versionPoList) {
                if(versionPO.getProductCode().equals("CCSP")){
                    versionPO.setVersion("V1.0");
                    resPoList.add(versionPO);
                    break;
                }
            }
        }else{
            resPoList=versionPoList;
        }
        List<VersionVO> versionVoS = versionConvert.convertVo(resPoList);
        return ResultUtil.ok(versionVoS);
    }
}