package com.sansec.ccsp.device.deviceinfo.request.vsm;

import com.sansec.ccsp.common.pattern.CommonPattern;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.*;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/2/23 17:26
 * @Description: 批量创建虚拟机
 */
@Data
public class VsmDeviceInfoCreateDTO {
    /**
     * 宿主机设备ID
     */
    @NotNull(message = "宿主机设备ID不可为空")
    private Long deviceId;
    /**
     * 设备类型ID
     */
    @NotNull(message = "设备类型ID不可为空")
    private Long deviceTypeId;
    /**
     * 虚机数量
     */
    @NotNull(message = "虚机数量不可为空")
    @Min(value = 1, message = "虚拟机数量范围1-32")
    @Max(value = 32, message = "虚拟机数量范围1-32")
    private Integer vsmNum;
    /**
     * 管理端口
     */
    @NotNull(message = "管理端口不可为空")
    @Range(min = 1, max = 65535, message = "端口请输入1-65535的数字")
    private Integer mgtPort;
    /**
     * 服务端口
     */
    @NotNull(message = "业务端口不可为空")
    @Range(min = 1,max = 65535, message = "端口请输入1-65535的数字")
    private Integer busiPort;
    /**
     * 服务连接密码
     */
    @Pattern(regexp = "^$|^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$", message = "连接密码需为标准的Base64格式")
    private String connectAuthCode;
    /**
     * 虚拟机网络ID
     */
    @NotNull(message = "虚拟机网络ID不可为空")
    private Long netConfigId;
    /**
     * 备注
     */
    @Size(max = 100, message = "备注长度限制100")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "备注请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
    private String remark;

    /**
     * 虚拟机资源分配
     */
    @NotNull(message = "资源配置ID不可为空")
    private Long resourceId;

    private Long regionId;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 是否支持代理
     */
    private boolean proxyRouteIs;
    /**
     * 宿主机区域网关IP
     */
    private String gatewayIp;
    /**
     * 宿主机区域网关端口
     */
    int gatewayPort;


    /**
     * 虚拟机区域网关IP
     */
    private String vsmGatewayIp;
    /**
     * 虚拟机区域网关端口
     */
    int vsmGatewayPort;



}
