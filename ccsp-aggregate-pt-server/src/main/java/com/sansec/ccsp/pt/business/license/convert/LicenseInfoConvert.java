package com.sansec.ccsp.pt.business.license.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.pt.business.license.entity.LicenseInfoPO;
import com.sansec.ccsp.pt.business.license.request.LicenseInfoDTO;
import com.sansec.ccsp.pt.business.license.response.LicenseInfoVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR> wwl
 * @description : 许可证表;(license_Info)实体类转换接口
 * @date : 2023-5-8
 */
@Mapper(componentModel = "spring")
public interface LicenseInfoConvert {
    /**
     * dtoToPo
     *
     * @param licenseInfoDTO
     * @return
     */
    @Mappings({})
    LicenseInfoPO dtoToPo(LicenseInfoDTO licenseInfoDTO);

    /**
     * poToDto
     *
     * @param licenseInfoPO
     * @return
     */
    LicenseInfoDTO poToDto(LicenseInfoPO licenseInfoPO);

    /**
     * poToDto-list
     *
     * @param list
     * @return
     */
    List<LicenseInfoDTO> poToDto(List<LicenseInfoPO> list);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<LicenseInfoVO> pagePOToSecPageVOPage(IPage<LicenseInfoPO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<LicenseInfoVO> convert(List<LicenseInfoPO> list);

    @Mappings({})
    LicenseInfoVO convertVo(LicenseInfoPO request);
}