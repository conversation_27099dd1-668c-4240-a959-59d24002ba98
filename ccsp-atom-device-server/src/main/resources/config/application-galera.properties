spring.datasource.driver=mariadb
spring.datasource.url=**************************************,10.0.105.244,10.0.105.245:3306/ccsp2_3test?autoReconnect=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull
spring.datasource.userName=N03NvcDbgUdrkASmrwW3sQ==
spring.datasource.passWord=IdCF5A1OLh7ntrpVIEmd6A==
pagehelper.helper-dialect=mysql

spring.datasource.initialSize = 30
spring.datasource.maxActive = 100
spring.datasource.minIdle = 30
spring.datasource.maxWait = 20000
spring.datasource.timeBetweenConnectErrorMillis = 90000
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT 1
spring.datasource.testOnBorrow = false
spring.datasource.testOnReturn = false
spring.datasource.testWhileIdle = true
# 连接池中连接最小空闲时间（毫秒）
spring.datasource.minEvictableIdleTimeMillis = 300000
