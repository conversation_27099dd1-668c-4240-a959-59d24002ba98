spring.datasource.driver=highgo
spring.datasource.url=************************************************************
spring.datasource.userName=/A3px4MunNcP3n+tAw9Gow==
spring.datasource.passWord=LKTDSyk58IT2y+MqRsfJqA==
mybatis.databaseType=postgresql

spring.datasource.initialSize = 5
spring.datasource.maxActive = 10
spring.datasource.minIdle = 5
spring.datasource.maxWait = 20000
spring.datasource.timeBetweenConnectErrorMillis = 90000
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT 1
spring.datasource.testOnBorrow = false
spring.datasource.testOnReturn = false
spring.datasource.testWhileIdle = true
# 连接池中连接最小空闲时间（毫秒）
spring.datasource.minEvictableIdleTimeMillis = 300000