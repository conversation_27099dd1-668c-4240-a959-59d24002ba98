package com.sansec.ccsp.pt.business.statistic.alarm.convert;

import com.sansec.ccsp.staticapi.alarm.request.AlarmInfoDTO;
import com.sansec.ccsp.staticapi.alarm.response.AlarmInfoPageVO;
import com.sansec.ccsp.staticapi.alarm.response.AlarmInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;

/**
 * @Description: 告警类型转换
 * @CreateTime: 2023-11-27
 * @Author: wang<PERSON><PERSON><PERSON>
 */
@Mapper(componentModel = "spring")

public interface AlarmConvert {

    @Mappings({})
    AlarmInfoDTO pageVOToInfoDTO(AlarmInfoPageVO pageVO);

    @Mappings({})
    AlarmInfoDTO infoVOToDTO(AlarmInfoVO alarmInfoVO);
}
