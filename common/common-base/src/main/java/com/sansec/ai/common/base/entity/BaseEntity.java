package com.sansec.ai.common.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

import java.util.Date;

/**
 * BaseEntity
 *
 * <AUTHOR>
 * @since 2024/3/12 16:16
 */
@Data
public class BaseEntity extends BasePO {

    /**
     * 删除标记 0-未删除 1-已删除
     */
    @TableField(fill = FieldFill.INSERT)
    @TableLogic(value = "0", delval = "1")
    protected Integer flagDel;
    /**
     * 新建用户id
     */
    @TableField(fill = FieldFill.INSERT)
    protected Long createBy;
    /**
     * 更新用户id
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Long updateBy;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    protected Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Date updateTime;
    /**
     * 备注
     */
    protected String remark;

}
