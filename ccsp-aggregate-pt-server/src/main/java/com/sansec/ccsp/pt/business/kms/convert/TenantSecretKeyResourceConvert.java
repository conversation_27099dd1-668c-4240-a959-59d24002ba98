package com.sansec.ccsp.pt.business.kms.convert;

import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import com.sansec.ccsp.pt.business.kms.request.TenantSecretKeyResourceDTO;
import com.sansec.ccsp.pt.business.kms.response.TenantSecretKeyResourceVO;
import com.sansec.ccsp.pt.business.kms.entity.TenantSecretKeyResourcePO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.common.param.response.SecPageVO;

import java.util.List;

/**
 * tenant_secret_key_resource
 *
 * <AUTHOR>
 * 2023-03-09 11:54:05
 */
@Mapper(componentModel = "spring")
public interface TenantSecretKeyResourceConvert {

    /**
     * dtoToPo
     *
     * @param tenantSecretKeyResourceDTO
     * @return
     */
    @Mappings({})
    TenantSecretKeyResourcePO dtoToPo(TenantSecretKeyResourceDTO tenantSecretKeyResourceDTO);

    /**
     * poToDto
     *
     * @param tenantSecretKeyResourcePO
     * @return
     */
    TenantSecretKeyResourceDTO poToDto(TenantSecretKeyResourcePO tenantSecretKeyResourcePO);

    /**
     * poToDto-list
     *
     * @param list
     * @return
     */
    List<TenantSecretKeyResourceDTO> poToDto(List<TenantSecretKeyResourcePO> list);


    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<TenantSecretKeyResourceVO> pagePOToSecPageVOPage(IPage<TenantSecretKeyResourcePO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<TenantSecretKeyResourceVO> convert(List<TenantSecretKeyResourcePO> list);

    @Mappings({})
    TenantSecretKeyResourceVO convertVo(TenantSecretKeyResourcePO request);
}