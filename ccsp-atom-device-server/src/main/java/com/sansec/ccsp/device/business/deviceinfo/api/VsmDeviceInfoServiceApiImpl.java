package com.sansec.ccsp.device.business.deviceinfo.api;

import com.sansec.ccsp.device.business.deviceinfo.service.VsmDeviceInfoService;
import com.sansec.ccsp.device.deviceinfo.api.VsmDeviceInfoServiceApi;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdsDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoPageDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsm.VsmDeviceInfoCreateDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsm.VsmDeviceInfoEditDTO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoVO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceStatusVO;
import com.sansec.ccsp.device.deviceinfo.response.vsm.VsmDeviceInfoVO;
import com.sansec.ccsp.device.devicenet.response.DeviceNetDetailVO;
import com.sansec.ccsp.security.annotation.IgnoreToken;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: zhandaojian
 * @Date: 2023/2/27 14:43
 * @Description: 虚拟机对外接口实现
 */
@RestController
public class VsmDeviceInfoServiceApiImpl implements VsmDeviceInfoServiceApi {
    @Resource
    private VsmDeviceInfoService vsmDeviceInfoService;


    @Override
    public SecRestResponse<SecPageVO<DeviceInfoVO>> find(DeviceInfoPageDTO deviceInfoPageDTO) {
        return vsmDeviceInfoService.find(deviceInfoPageDTO);
    }

    @Override
    public SecRestResponse<List<DeviceNetDetailVO>> createVsm(VsmDeviceInfoCreateDTO vsmDeviceInfoCreateDTO) {
        return vsmDeviceInfoService.createVsm(vsmDeviceInfoCreateDTO);
    }

    @Override
    public SecRestResponse<Long> restart(DeviceIdDTO deviceIdDTO) {
        return vsmDeviceInfoService.restart(deviceIdDTO);
    }

    @Override
    public SecRestResponse<Long> delete(DeviceIdDTO deviceIdDTO) {
        return vsmDeviceInfoService.delete(deviceIdDTO);
    }

    @Override
    public SecRestResponse<Long> forceDelete(DeviceIdDTO deviceIdDTO) {
        return vsmDeviceInfoService.forceDelete(deviceIdDTO);
    }

    @Override
    public SecRestResponse<Long> start(DeviceIdDTO deviceIdDTO) {
        return vsmDeviceInfoService.start(deviceIdDTO);
    }

    @Override
    public SecRestResponse<Long> stop(DeviceIdDTO deviceIdDTO) {
        return vsmDeviceInfoService.stop(deviceIdDTO);
    }

    @Override
    @IgnoreToken
    public SecRestResponse<List<DeviceStatusVO>> status(DeviceIdsDTO deviceIdsDTO) {
        return vsmDeviceInfoService.status(deviceIdsDTO);
    }

    @Override
    public SecRestResponse<VsmDeviceInfoVO> vsmInfo(DeviceIdDTO deviceIdDTO) {
        return vsmDeviceInfoService.vsmInfo(deviceIdDTO);
    }

    @Override
    public SecRestResponse<DeviceInfoVO> info(DeviceIdDTO deviceIdDTO) {
        return vsmDeviceInfoService.info(deviceIdDTO);
    }

    @Override
    public SecRestResponse<Long> edit(VsmDeviceInfoEditDTO vsmDeviceInfoEditDTO) {
        return vsmDeviceInfoService.edit(vsmDeviceInfoEditDTO);
    }

    @Override
    public SecRestResponse<List<Long>> getVsmIdList() {
        return vsmDeviceInfoService.getVsmIdList();
    }

    @Override
    public SecRestResponse<DeviceInfoVO> getDeviceByIp(DeviceInfoDTO deviceInfoDTO) {
        return vsmDeviceInfoService.getDeviceByIp(deviceInfoDTO.getMgtIp());
    }

    @Override
    public SecRestResponse<List<DeviceInfoVO>> findDeviceUnUsed(DeviceInfoPageDTO deviceInfoPageDTO) {
        return ResultUtil.ok(vsmDeviceInfoService.findDeviceUnUsed(deviceInfoPageDTO));
    }
}
