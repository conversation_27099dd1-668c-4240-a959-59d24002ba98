package com.sansec.ccsp.device.devicenet.response;

import lombok.Data;

 /**
 * @Description: 虚拟机网络配置;
 * <AUTHOR> x<PERSON><PERSON><PERSON>aw<PERSON>
 * @Date: 2023-2-18
 */
@Data
public class DeviceVsmNetConfigVO{
    /**
     * 网络配置ID
     */
    private Long id;
    /**
     * 网络名称
     */
    private String netName;
    /**
     * 网卡名称
     */
    private String netCardName;
    /**
     * 关联宿主机序列号
     */
    private String hostSerialnum;
    /**
     * 关联宿主机ID
     */
    private Long hostId;
    /**
     * 网域
     */
    private String subdomain;
    /**
     * 网关
     */
    private String gateway;
    /**
     * 超融合网络ID
     */
    private Long hccsId;
    /**
     * 子网掩码
     */
    private String mask;
    /**
     * 是否作废;默认为0
     */
    private Integer invalidFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}