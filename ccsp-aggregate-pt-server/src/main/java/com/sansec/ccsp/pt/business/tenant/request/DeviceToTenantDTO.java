package com.sansec.ccsp.pt.business.tenant.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: wangjunjie
 * @Date: 2023-02-24
 * @Description: 绑定租户设备
 */
@Data
public class DeviceToTenantDTO {
    @NotEmpty(message = "设备组列表不可为空")
    private List<Long> deviceIdList;
    @NotNull(message = "租户ID不可为空")
    private Long tenantId;

}
