package com.sansec.ccsp.datastatic.business.statistic.realtime.convert;


import com.sansec.ccsp.datastatic.business.statistic.realtime.entity.StatisticMinuteDataPO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.entity.StatisticMinuteDataRealPO;
import com.sansec.ccsp.staticapi.realtime.response.StatisticFindDataVO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticMinuteDataVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.Mapping;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

@Mapper(componentModel = "spring")
public interface StatisticMinuteDataConvert {



    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<StatisticMinuteDataVO> pagePOToSecPageVOPage(IPage<StatisticMinuteDataPO> iPage);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<StatisticFindDataVO> pagePOToSecPageFindDataVOPage(IPage<StatisticMinuteDataPO> iPage);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<StatisticFindDataVO> pageRealPOToSecPageFindDataVOPage(IPage<StatisticMinuteDataRealPO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<StatisticMinuteDataVO> convert(List<StatisticMinuteDataPO> list);

    @Mappings({})
    StatisticMinuteDataVO convertVo(StatisticMinuteDataPO request);

}
