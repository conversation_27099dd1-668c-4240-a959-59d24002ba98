package com.sansec.ccsp.pt.business.appuser.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.pt.business.appuser.entity.SecComItemConfigPO;
import com.sansec.ccsp.pt.demo.request.ConfigSaveReqDTO;
import com.sansec.ccsp.pt.demo.response.ComConfigVO;
import com.sansec.ccsp.pt.demo.response.SecComItemConfigVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.*;

import java.util.List;

/**
 * @Description: ConfigServiceApi层
 * <AUTHOR>
 * @Date: 2022/2/17 18:01
 */
@Mapper(componentModel = "spring")
public interface SecConfigConvert {


    /**
     * list 转换  可以 用 name 制定  对象转换的方法名
     * @param list
     * @return
     */
    @InheritConfiguration(name = "secComItemConfigToComConfigVO")
    List<ComConfigVO> secComItemConfigListToComConfigVOList(List<SecComItemConfigPO> list);

    @Mappings({})
    ComConfigVO secComItemConfigToComConfigVO(SecComItemConfigPO request);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<SecComItemConfigVO> secComItemConfigPagePOToSecPageVOPage (IPage<SecComItemConfigPO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<SecComItemConfigVO> convert(List<SecComItemConfigPO> list);

    @Mappings({})
    SecComItemConfigVO convertVo(SecComItemConfigPO request);

    @Mappings({})
    SecComItemConfigPO dtoToPo(ConfigSaveReqDTO request);
}
