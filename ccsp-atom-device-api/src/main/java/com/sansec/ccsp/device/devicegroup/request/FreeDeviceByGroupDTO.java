package com.sansec.ccsp.device.devicegroup.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 根据设备组获取空闲设备
 * @CreateTime: 2023-09-25
 * @Author: wangjunjie
 */
@Data
public class FreeDeviceByGroupDTO {
    /**
     * 设备组ID
     */
    @NotNull(message = "设备组id不可为空")
    private Long deviceGroupId;
    /**
     * 设备类型
     */
    private Long deviceTypeId;
    /**
     * 是否支持对外服务，不支持需排除对外服务设备类型的设备
     */
    private Integer isRest;

}
