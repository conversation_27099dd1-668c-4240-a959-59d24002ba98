package com.sansec.ccsp.common.business.dic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

/**
 * @description : 用户类型（330）;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-3-7
 */
@TableName("DIC_USER_TYPE")
@Data
public class DicUserTypePO extends BasePO{
    /**
     * ID
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 用户类型id;1平台用户 2租户用户 3应用用户 4单位用户
     */
    private Integer userTypeId;
    /**
     * 用户类型名称
     */
    private String userTypeName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}