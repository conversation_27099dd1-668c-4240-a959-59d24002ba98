<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ccsp.datastatic.business.statistic.realtime.mapper.StatisticAllCollectDataMapper">
    <sql id="Base_Column_List" >
    ID,CALL_NUM,COLLECT_NUM,REGION_CODE,TENANT_CODE,APP_CODE,SERVICE_IP,SERVICE_PORT,SERVICE_TYPE_CODE,SERVICE_URL,AVG_COST_TIME,MAX_COST_TIME,SERVICE_FLOW_NUM,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>

    <update id="updateByCondition" parameterType="com.sansec.ccsp.datastatic.business.statistic.realtime.request.StatisticAllCollectDataDTO">
        UPDATE statistic_all_collect_data
        <set>
            <if test="dto.callNum != null">
                CALL_NUM = CALL_NUM + #{dto.callNum},
            </if>
            <if test="dto.collectNum != null">
                COLLECT_NUM = COLLECT_NUM + 1,
            </if>
            <if test="dto.maxCostTime != null">
                MAX_COST_TIME = GREATEST(#{dto.maxCostTime}, MAX_COST_TIME),
            </if>
            <if test="dto.serviceFlowNum != null">
                SERVICE_FLOW_NUM = SERVICE_FLOW_NUM + #{dto.serviceFlowNum},
            </if>
        </set>
        <where>
            <if test="dto.serviceIp != null">
                AND SERVICE_IP = #{dto.serviceIp}
            </if>
            <if test="dto.servicePort != null">
                AND SERVICE_PORT = #{dto.servicePort}
            </if>
            <if test="dto.serviceTypeCode != null">
                AND SERVICE_TYPE_CODE = #{dto.serviceTypeCode}
            </if>
            <if test="dto.regionCode != null">
                AND REGION_CODE = #{dto.regionCode}
            </if>
            <if test="dto.tenantCode != null">
                AND TENANT_CODE = #{dto.tenantCode}
            </if>
            <if test="dto.appCode != null">
                AND APP_CODE = #{dto.appCode}
            </if>
            <if test="dto.id != null">
                AND ID = #{dto.id}
            </if>
        </where>
    </update>
    
</mapper>