package com.sansec.ccsp.pt.business.common.dic.service;

import com.sansec.ccsp.common.dic.response.DicShareGroupTypeVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description : 支持创建共享服务类型（330）;(DIC_SHARE_GROUP_TYPE)表服务接口
 * @date : 2024-3-11
 */
public interface DicShareGroupTypeService {
    /**
     * 获取可共享的服务类型ID列表
     *
     * @return
     */
    List<Long> getShareBuseTypeList();

    /**
     * 获取列表
     *
     * @return
     */
    List<DicShareGroupTypeVO> getList();

    /**
     * 判断服务类型是否支持共享
     *
     * @param busiTypeId
     * @return
     */
    Boolean isCanShare(Long busiTypeId);

    DicShareGroupTypeVO getShareGroupType(Long busiTypeId);

    /**
     * 获取可共享的业务类型
     *
     * @return
     */
    List<DicShareGroupTypeVO> getShareBusiTypeList();
}