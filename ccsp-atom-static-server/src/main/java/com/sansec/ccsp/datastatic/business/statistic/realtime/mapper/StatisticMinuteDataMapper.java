package com.sansec.ccsp.datastatic.business.statistic.realtime.mapper;

import com.sansec.ccsp.datastatic.business.statistic.realtime.entity.StatisticMinuteDataPO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.request.StatisticGroupDataDTO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticDataVO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticGroupDataVO;
import com.sansec.ccsp.staticapi.realtime.request.StatisticAnalyzeDataQueryDTO;
import com.sansec.ccsp.staticapi.realtime.response.StatisticGateWayDataVO;
import com.sansec.db.mapper.SansecBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface StatisticMinuteDataMapper extends SansecBaseMapper<StatisticMinuteDataPO> {

    /**
     * @Description: 根据条件查询集合
     * @Param: [statisticRealTimeDataAnalyzeDTO]
     * @return: java.util.List<com.sansec.ccsp.datastatic.business.statistic.realtime.entity.StatisticRealTimeDataPO>
     * @Author: zhaozhuang
     * @Date: 2024/6/26
     */

    List<StatisticGroupDataVO> analyzeDataByHour(StatisticGroupDataDTO dto);

    List<StatisticGateWayDataVO> analyzeDataKafka(StatisticGroupDataDTO dto);

    /**
     * @Description: 删除指定时间之前的数据
     * @Param: [deleteBeforeTime]  格式yyyy-MM-dd HH
     * @return: void
     * @Author: zhaozhuang
     * @Date: 2024/6/28
     */
    void clearMinuteData(String deleteBeforeTime);

    /**
     * @Description: 分钟数据查询
     * @Param: [statisticAnalyzeDataQueryDTO]
     * @return: java.util.List<com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticDataVO>
     * @Author: zhaozhuang
     * @Date: 2024/7/17
     */

    List<StatisticDataVO> queryAnalyzeMinuteData(StatisticAnalyzeDataQueryDTO statisticAnalyzeDataQueryDTO);
}
