package com.sansec.ccsp.pt.business.servicemgt.proxyroute.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sansec.ccsp.pt.business.servicemgt.proxyroute.entity.ProxyRouteRecordPO;
import com.sansec.ccsp.pt.business.servicemgt.proxyroute.request.ProxyRouteRecordDTO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * <AUTHOR> xia<PERSON><PERSON>aw<PERSON>
 * @description : 代理路由记录表;(PROXY_ROUTE_RECORD)表服务接口
 * @date : 2024-3-6
 */
public interface ProxyRouteRecordService extends IService<ProxyRouteRecordPO> {

    /**
     * 新增数据
     *
     * @param proxyRouteRecordDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(ProxyRouteRecordDTO proxyRouteRecordDTO);

    /**
     * 更新数据
     *
     * @param proxyRouteRecordDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(ProxyRouteRecordDTO proxyRouteRecordDTO);

    /**
     * 通过主键删除数据
     *
     * @param proxyRouteRecordDTO
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(ProxyRouteRecordDTO proxyRouteRecordDTO);

    /**
     * 查询路由记录表
     *
     * @return 查询结果
     */
    List<ProxyRouteRecordPO> searchRouteRecord(ProxyRouteRecordDTO searchDTO);

}