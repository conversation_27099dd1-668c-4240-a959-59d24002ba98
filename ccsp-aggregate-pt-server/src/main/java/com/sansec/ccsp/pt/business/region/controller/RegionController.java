package com.sansec.ccsp.pt.business.region.controller;

import com.sansec.ccsp.pt.business.statistic.log.aspect.OperateManageLog;
import com.sansec.ccsp.pt.business.region.request.RegionIdDTO;
import com.sansec.ccsp.security.annotation.HasAnyPermissions;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.utils.ResultUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sansec.ccsp.pt.business.region.request.RegionDTO;
import com.sansec.ccsp.pt.business.region.request.RegionEditDTO;
import com.sansec.ccsp.pt.business.region.request.RegionPageDTO;
import com.sansec.ccsp.pt.business.region.response.RegionVO;
import com.sansec.ccsp.pt.business.region.service.RegionService;
import javax.annotation.Resource;

/**
 * @description : 区域表(1030);(REGION)表控制层
 * <AUTHOR> xia<PERSON>jiaw<PERSON>
 * @date : 2023-9-18
 */
@RestController
@RequestMapping("/region/v1")
@Validated
public class RegionController{
    @Resource
    private RegionService regionService;

    /**
     * 分页查询
     *
     * @param regionPageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    @HasAnyPermissions("region:region:list")
    public SecRestResponse<SecPageVO<RegionVO>> find(@Validated @RequestBody RegionPageDTO regionPageDTO){
        return regionService.find(regionPageDTO);
    }

    /**
     * 新增数据
     *
     * @param regionDTO 实例对象
     * @return 实例对象
     */
    @OperateManageLog(module = "区域管理", desc = "新增区域信息")
    @PostMapping("/add")
    @HasAnyPermissions("region:region:add")
    public SecRestResponse<Object> add(@Validated @RequestBody RegionDTO regionDTO){
        return regionService.add(regionDTO);
    }

    /**
     * 更新数据
     *
     * @param regionEditDTO 实例对象
     * @return 实例对象
     */
    @OperateManageLog(module = "区域管理", desc = "编辑区域信息")
    @PostMapping("/edit")
    @HasAnyPermissions("region:region:edit")
    public SecRestResponse<Object> edit(@Validated @RequestBody RegionEditDTO regionEditDTO){
        return regionService.edit(regionEditDTO);
    }

    /**
     * 通过主键删除数据
     *
     * @param regionIdDTO 实例对象
     * @return 实例对象
     */
    @OperateManageLog(module = "区域管理", desc = "删除区域信息")
    @PostMapping("/deleteById")
    @HasAnyPermissions("region:region:delete")
    public SecRestResponse<Object> deleteById(@Validated @RequestBody RegionIdDTO regionIdDTO){
        return regionService.deleteById(regionIdDTO);
    }

    /**
     * 区域详情
     *
     * @param regionIdDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/info")
    @HasAnyPermissions("region:region:detail")
    public SecRestResponse<Object> info(@Validated @RequestBody RegionIdDTO regionIdDTO){
        return regionService.info(regionIdDTO);
    }

    /**
     * 区域下拉列表
     *
     *
     * @return 实例对象
     */
    @PostMapping("/getRegionIdNameList")
    @HasAnyPermissions("region:region:idnamelist")
    public SecRestResponse<Object> getRegionIdNameList(){
        return regionService.getRegionIdNameList();
    }


    /**
     * 获取区域Id->Name映射Map
     *
     * @return 实例对象
     */
    @PostMapping("/getRegionIdNameMap")
    public SecRestResponse<Object> getRegionIdNameMap() {
        return ResultUtil.ok(regionService.getRegionIdNameMap());
    }
}