package com.sansec.ai.extra.dify.service;

import com.sansec.ai.extra.dify.request.DifyInitRequest;
import com.sansec.ai.extra.dify.request.DifyInsInfo;
import com.sansec.ai.extra.dify.request.DifyInstanceProperties;
import com.sansec.ai.extra.dify.result.DifyInstanceStatusVo;
import com.sansec.common.exception.BusinessException;

/**
 * Dify部署相关接口
 *
 * <AUTHOR>
 * @since 2025/5/12 18:50
 */
public interface DifyDeployService {

	/**
	 * 根据门户标识，初始化专用数据库
	 *
	 * @param portalCode 门户标识
	 * @throws BusinessException
	 */
	void initDatabase(String portalCode) throws BusinessException;

	/**
	 * 根据门户标识+实例配置，初始化专用配置
	 *
	 * @param portalCode 门户标识
	 * @param properties 实例配置
	 * @throws BusinessException
	 */
	void initConfig(String portalCode, DifyInstanceProperties properties) throws BusinessException;

	/**
	 * 根据门户标识，创建Dify实例对应的容器
	 *
	 * @param portalCode 门户标识
	 * @throws BusinessException
	 */
	void createContainer(String portalCode) throws BusinessException;

	/**
	 * 根据门户标识+初始化参数，初始化Dify实例
	 *
	 * @param difyInsInfo
	 * @param request     初始化参数
	 * @throws BusinessException
	 */
	void initInstance(DifyInsInfo difyInsInfo, DifyInitRequest request);

	/**
	 * 根据门户标识，删除Dify实例所有数据
	 *
	 * @param portalCode
	 * @throws BusinessException
	 */
	void deleteAllInfo(String portalCode) throws BusinessException;

	/**
	 * 根据门户标识，检查实例状态
	 *
	 * @param portalCode 门户标识
	 * @return
	 */
	DifyInstanceStatusVo checkInstanceStatus(String portalCode);

	/**
	 * 停止某个门户的Dify实例
	 *
	 * @param portalCode 门户标识
	 * @throws BusinessException
	 */
	void stopInstance(String portalCode) throws BusinessException;

	/**
	 * 启动某个门户的Dify实例
	 *
	 * @param portalCode 门户标识
	 * @throws BusinessException
	 */
	void startInstance(String portalCode) throws BusinessException;

	/**
	 * 删除某个门户的Dify实例。
	 *
	 * @param portalCode 门户标识
	 * @throws BusinessException
	 */
	void removeInstance(String portalCode) throws BusinessException;
}
