package com.sansec.ccsp.device.business.devicedic.service;


import com.sansec.ccsp.device.business.devicedic.request.DicDeviceMachineTypeDTO;
import com.sansec.ccsp.device.business.devicedic.request.DicDeviceMachineTypePageDTO;
import com.sansec.ccsp.device.business.devicedic.response.DicDeviceMachineTypeVO;
import com.sansec.ccsp.device.devicedic.response.DicDeviceMachineTypeApiVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * @Description: 密码机服务类型字典表;(DIC_DEVICE_MACHINE_TYPE)表服务接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
public interface DicDeviceMachineTypeService{
    /** 
     * 分页查询
     *
     * @param dicDeviceMachineTypePageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<DicDeviceMachineTypeVO>> find(DicDeviceMachineTypePageDTO dicDeviceMachineTypePageDTO);

    /**
     * 获取密码机服务类型字典表中INVALID_FLAG=0的所有数据
     * @return
     */
    SecRestResponse<List<DicDeviceMachineTypeApiVO>> findList();

    /** 
     * 新增数据
     *
     * @param dicDeviceMachineTypeDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(DicDeviceMachineTypeDTO dicDeviceMachineTypeDTO);
    /** 
     * 更新数据
     *
     * @param dicDeviceMachineTypeDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(DicDeviceMachineTypeDTO dicDeviceMachineTypeDTO);
    /** 
     * 通过主键删除数据
     *
     * @param dicDeviceMachineTypeDTO
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(DicDeviceMachineTypeDTO dicDeviceMachineTypeDTO);
    /**
     * 判断密码机服务类型是否存在
     *
     * @param machineTypeId
     * @return boolean
     */
    boolean hasMachineType(Long machineTypeId);
}