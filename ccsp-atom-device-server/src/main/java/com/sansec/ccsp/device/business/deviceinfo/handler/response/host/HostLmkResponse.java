package com.sansec.ccsp.device.business.deviceinfo.handler.response.host;

import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/2/22 14:27
 * @Description: 物理机基础响应
 */
@Data
public class HostLmkResponse<T> {

    /**
     * 状态码
     */
    private int status;

    /**
     * 状态描述
     */
    private String message;

    /**
     * 服务器响应时间
     */
    private String timestamp;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 服务器处理时间(毫秒数)
     */
    private long costMillis;

    /**
     * 返回数据对象
     */
    private T result;

}
