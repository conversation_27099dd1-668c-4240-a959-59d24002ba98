package com.sansec.ccsp.extra.business.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.extra.business.constant.CommonConstant;
import com.sansec.ccsp.extra.business.constant.SecErrorCodeConstant;
import com.sansec.ccsp.extra.business.product.convert.*;
import com.sansec.ccsp.extra.business.product.entity.*;
import com.sansec.ccsp.extra.business.product.mapper.*;
import com.sansec.ccsp.extra.business.product.request.*;
import com.sansec.ccsp.extra.business.product.response.*;
import com.sansec.ccsp.extra.business.product.service.ProductService;
import com.sansec.ccsp.extra.business.product.service.TenantToProductService;
import com.sansec.ccsp.extra.business.utils.HttpsServiceUtil;
import com.sansec.ccsp.extra.business.workorder.request.AttachDeleteDTO;
import com.sansec.ccsp.extra.business.workorder.request.AttachDownloadDTO;
import com.sansec.ccsp.extra.hdfs.HadoopTemplate;
import com.sansec.ccsp.extra.securityconf.util.LoginUserUtil;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class ProductServiceImpl extends ServiceImpl<ServiceProductMapper, ServiceProductPO> implements ProductService {
    @Autowired
    private ServiceProductMapper serviceProductMapper;
    @Resource
    private ServiceProductConvert serviceProductConvert;
    @Autowired
    private ProductSceneMapper productSceneMapper;
    @Autowired
    private ProductSuperiorityMapper productSuperiorityMapper;
    @Autowired
    private ProductSpecsMapper productSpecsMapper;
    @Autowired
    private ProductUseMapper productUseMapper;
    @Autowired
    private ProductAttachmentMapper productAttachmentMapper;
    @Resource
    private ProductAttachmentConvert productAttachmentConvert;
    @Autowired
    private TenantToProductMapper tenantToProductMapper;
    @Resource
    ProductSceneConvert productSceneConvert;
    @Resource
    ProductUseConvert productUseConvert;
    @Resource
    ProductSuperiorityConvert productSuperiorityConvert;
    @Resource
    ProductSpecsConvert productSpecsConvert;
    @Resource
    HttpsServiceUtil httpsServiceUtil;
    @Resource
    TenantToProductConvert tenantToProductConvert;
    @Autowired
    private TenantToProductService tenantToProductService;

    //服务器本地存储文件路径 如果storetype配置为hdfs，此文件夹只是做临时存储，会随时删除，如果配置为local，此文件夹为最终存储路径
    @Value("${workorder.upload.temppath}")
    private String tempPath;

    //上传文件存储方式
    @Value("${workorder.upload.storetype}")
    private String storeType;
    @Resource
    HadoopTemplate hadoopTemplate;

    public static final String FILE_NOT_EXIST_ERROR = "The file system does not exist for this file,errorcode:{},attachmentPath:{}";
    public static final String PRODUCT_ATTACH_NOT_MATCH = "ProductServiceImpl#submit product_attach_not_match";

    /**
     * 分页查询密码产品列表
     */
    @Override
    public SecRestResponse<SecPageVO<ServiceProductVO>> productList(ServiceProductSecDTO serviceProductDTO) {
        //构建分页查询Page对象
        IPage<ServiceProductPO> page = new Page<>(serviceProductDTO.getPageNum(), serviceProductDTO.getPageSize());
        //构建wrapper
        LambdaQueryWrapper<ServiceProductPO> queryWrapper = new LambdaQueryWrapper<>();
        //密码产品名称不为空时，模糊查询
        queryWrapper.like(StringUtils.isNotBlank(serviceProductDTO.getProductName()),
                ServiceProductPO::getProductName,
                serviceProductDTO.getProductName());
        //服务分类不为空时，模糊查询
        queryWrapper.like(StringUtils.isNotBlank(serviceProductDTO.getServiceTypeName()),
                ServiceProductPO::getServiceTypeName,
                serviceProductDTO.getServiceTypeName());
        //服务子类不为空时，模糊查询
        queryWrapper.like(StringUtils.isNotBlank(serviceProductDTO.getServiceChildTypeName()),
                ServiceProductPO::getServiceChildTypeName,
                serviceProductDTO.getServiceChildTypeName());
        //更新时间倒序
        queryWrapper.orderByDesc(ServiceProductPO::getUpdateTime);
        //过滤草稿，查询数据类型为正式的密码产品
        queryWrapper.eq(ServiceProductPO::getDataType, CommonConstant.SERVICE_PRODUCT_DATA_TYPE_FORMAL);
        //查询
        IPage<ServiceProductPO> iPage = serviceProductMapper.selectPage(page, queryWrapper);
        //PO转换为VO
        SecPageVO<ServiceProductVO> serviceProductVOSecPageVO = serviceProductConvert.pagePOToSecPageVOPage(iPage);
        List<ServiceProductVO> list = serviceProductVOSecPageVO.getList();

        // 封装销量和数量
        LambdaQueryWrapper<TenantToProductPO> queryWrapper_ttp = new LambdaQueryWrapper<>();
        queryWrapper_ttp.select(TenantToProductPO::getProductId,TenantToProductPO::getHasNum,TenantToProductPO::getState);
        List<TenantToProductPO> list_ttp = tenantToProductMapper.selectList(queryWrapper_ttp);
        for (ServiceProductVO serviceProductVO : list) {
            int salesVolume = 0, volumeInUse = 0;
            for (TenantToProductPO ttp_po : list_ttp) {
                if (ttp_po.getProductId().equals(serviceProductVO.getId())) {
                    Integer hasNum = ttp_po.getHasNum();
                    salesVolume += hasNum;
                    if (1 == ttp_po.getState()) { volumeInUse += hasNum; }
                }
            }
            serviceProductVO.setSalesVolume(salesVolume);
            serviceProductVO.setVolumeInUse(volumeInUse);
        }

        //返回
        return ResultUtil.ok(serviceProductVOSecPageVO);
    }

    /**
     * 开通密码服务
     */
    @Override
    public SecRestResponse<Object> openProduct(OpenProductDTO openProductDTO) {
        Long productId = openProductDTO.getProductId();
        ServiceProductPO serviceProductPO = getServiceProductPO(productId);
        //校验密码产品是否存在
        if (ObjectUtils.isEmpty(serviceProductPO)) {
            log.error("ProductServiceImpl#openProduct serviceProduct is not exist");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_NOT_EXIST);
        }
        //密码产品存在，但该密码产品状态为上架状态
        if (Objects.equals(serviceProductPO.getProductState(), CommonConstant.SERVICE_PRODUCT_OPEN_STATUS)) {
            log.error("ProductServiceImpl#openProduct serviceProductStatus is already open");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_ALREADY_OPEN);
        }
        //产品存在，并且未开通，设置密码上架状态为1
        LambdaUpdateWrapper<ServiceProductPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ServiceProductPO::getProductState, CommonConstant.SERVICE_PRODUCT_OPEN_STATUS);
        updateWrapper.eq(ServiceProductPO::getId, productId);
        //根据Id修改状态为上架状态
        serviceProductMapper.update(null, updateWrapper);
        return ResultUtil.ok();
    }

    /**
     * 根据Id查询密码产品
     */
    @Override
    public SecRestResponse<ServiceProductVO> findProductById(Long id) {
        //校验Id
        if (ObjectUtils.isEmpty(id)) {
            log.error("ProductServiceImpl#findProductById productId is null");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_ID_IS_NULL);
        }
        //查询密码产品
        ServiceProductPO serviceProductPO = serviceProductMapper.selectOne(new LambdaQueryWrapper<ServiceProductPO>().eq(ServiceProductPO::getId, id));
        if (ObjectUtils.isEmpty(serviceProductPO)) {
            //密码产品不存在
            log.error("ProductServiceImpl#findProductById serviceProduct is not exist");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_NOT_EXIST);
        }
        return ResultUtil.ok(serviceProductConvert.convertVo(serviceProductPO));
    }

    /**
     * 关闭密码服务
     */
    @Override
    public SecRestResponse<Object> closeProduct(CloseProductDTO closeProductDTO) {
        Long productId = closeProductDTO.getProductId();
        //构造wrapper
        ServiceProductPO serviceProductPO = getServiceProductPO(productId);
        //检验密码产品是否存在
        if (ObjectUtils.isEmpty(serviceProductPO)) {
            log.error("ProductServiceImpl#closeProduct serviceProduct is not exist");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_NOT_EXIST);
        }
        //密码产品存在并且状态为下架状态
        if (Objects.equals(serviceProductPO.getProductState(), CommonConstant.SERVICE_PRODUCT_CLOSE_STATUS)) {
            log.error("ProductServiceImpl#closeProduct serviceProductStatus is already close");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_ALREADY_CLOSE);
        }
        //设置密码服务状态为0
        LambdaUpdateWrapper<ServiceProductPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ServiceProductPO::getProductState, CommonConstant.SERVICE_PRODUCT_CLOSE_STATUS);
        updateWrapper.eq(ServiceProductPO::getId, productId);
        //根据Id修改状态为下架状态
        serviceProductMapper.update(null, updateWrapper);
        return ResultUtil.ok();
    }

    /**
     * 删除密码产品
     */
    @Override
    public SecRestResponse<Object> delProduct(DeleteProductDTO deleteProductDTO) {
        Long productId = deleteProductDTO.getProductId();
        //获取密码产品
        ServiceProductPO serviceProductPO = getServiceProductPO(productId);
        //检验密码产品是否存在
        if (ObjectUtils.isEmpty(serviceProductPO)) {
            log.error("ProductServiceImpl#delProduct serviceProduct is not exist");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_NOT_EXIST);
        }
        //密码产品存在，但为上架状态，不允许删除
        if (Objects.equals(serviceProductPO.getProductState(), CommonConstant.SERVICE_PRODUCT_OPEN_STATUS)) {
            log.error("ProductServiceImpl#delProduct serviceProductStatus is open not delete");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_STATUS_IS_OPEN_NOT_DELETE);
        }
        //根据产品ID去租户配额表查询
        List<TenantToProductVO> tenantToProductVOList = tenantToProductService.findOpenListByProductId(productId);
        //产品存在,不可以删除
        if (!CollectionUtils.isEmpty(tenantToProductVOList)) {
            log.error("ProductServiceImpl#delProduct has quota  not delete ");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_HAS_QUOTA_NOT_DELETE);
        }
        //删除密码产品
        serviceProductMapper.delete(new LambdaQueryWrapper<ServiceProductPO>().eq(ServiceProductPO::getId, productId));
        //删除与该密码产品相关信息（场景、规格、优势、功能）
        delRelateInfoOfProduct(productId);
        //删除附件
        productAttachmentMapper.delete(new LambdaQueryWrapper<ProductAttachmentPO>()
                .eq(ProductAttachmentPO::getProductId, productId));
        return ResultUtil.ok();
    }

    /**
     * 密码服务预览
     */
    @Override
    public SecRestResponse<ProductPreviewVO> productPreview(ProductPreviewDTO productPreviewDTO) {
        //检验Id
        Long productId = productPreviewDTO.getProductId();
        if (ObjectUtils.isEmpty(productId)) {
            log.error("ProductServiceImpl#productPreview productId is null");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_ID_IS_NULL);
        }
        //构建返回VO对象
        ProductPreviewVO productPreviewVO = new ProductPreviewVO();
        //构造wrapper
        LambdaQueryWrapper<ServiceProductPO> queryWrapper = new LambdaQueryWrapper<>();
        //查询条件：Id
        queryWrapper.eq(ServiceProductPO::getId, productId);
        //查询
        ServiceProductPO serviceProductPO = serviceProductMapper.selectOne(queryWrapper);
        //密码产品不存在
        if (ObjectUtils.isEmpty(serviceProductPO)) {
            log.error("ProductServiceImpl#productPreview serviceProduct is not exist");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_NOT_EXIST);
        }
        //获取产品优势
        List<ProductSuperiorityPO> productSuperiorityPOS = productSuperiorityMapper.selectList(new LambdaQueryWrapper<ProductSuperiorityPO>()
                .eq(ProductSuperiorityPO::getProductId, productId));
        //获取产品功能
        List<ProductUsePO> productUsePOS = productUseMapper.selectList(new LambdaQueryWrapper<ProductUsePO>()
                .eq(ProductUsePO::getProductId, productId));
        //获取产品规格
        List<ProductSpecsPO> productSpecsPOS = productSpecsMapper.selectList(new LambdaQueryWrapper<ProductSpecsPO>()
                .eq(ProductSpecsPO::getProductId, productId));
        //获取应用场景
        List<ProductScenePO> productScenePOS = productSceneMapper.selectList(new LambdaQueryWrapper<ProductScenePO>()
                .eq(ProductScenePO::getProductId, productId));
        //封装密码产品VO
        setProductBaseVO(productPreviewVO, serviceProductPO);
        //封装密码产品信息其他相关信息VO
        setProductInfoVO(productPreviewVO, productSuperiorityPOS, productUsePOS, productSpecsPOS, productScenePOS);
        return ResultUtil.ok(productPreviewVO);
    }

    /**
     * 密码产品详情
     */
    @Override
    public SecRestResponse<ProductPreviewVO> productDetail(ProductPreviewDTO productPreviewDTO) {
        //检验Id
        Long productId = productPreviewDTO.getProductId();
        if (ObjectUtils.isEmpty(productId)) {
            log.error("ProductServiceImpl#productPreview productId is null");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_ID_IS_NULL);
        }
        //构建返回VO对象
        ProductPreviewVO previewVO = new ProductPreviewVO();
        //构造wrapper
        LambdaQueryWrapper<ServiceProductPO> queryWrapper = new LambdaQueryWrapper<>();
        //查询条件：Id
        queryWrapper.eq(ServiceProductPO::getId, productId);
        //查询
        ServiceProductPO serviceProductPO = serviceProductMapper.selectOne(queryWrapper);
        //密码产品不存在 或是草稿数据
        if (ObjectUtils.isEmpty(serviceProductPO) || CommonConstant.SERVICE_PRODUCT_DATA_TYPE_DRAFT.equals(serviceProductPO.getDataType())) {
            log.error("ProductServiceImpl#productPreview serviceProduct is not exist");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_NOT_EXIST);
        }
        //获取产品优势
        List<ProductSuperiorityPO> productSuperiorityPOS = productSuperiorityMapper.selectList(new LambdaQueryWrapper<ProductSuperiorityPO>()
                .eq(ProductSuperiorityPO::getProductId, productId));
        //获取产品功能
        List<ProductUsePO> productUsePOS = productUseMapper.selectList(new LambdaQueryWrapper<ProductUsePO>()
                .eq(ProductUsePO::getProductId, productId));
        //获取产品规格
        List<ProductSpecsPO> productSpecsPOS = productSpecsMapper.selectList(new LambdaQueryWrapper<ProductSpecsPO>()
                .eq(ProductSpecsPO::getProductId, productId));
        //获取应用场景
        List<ProductScenePO> productScenePOS = productSceneMapper.selectList(new LambdaQueryWrapper<ProductScenePO>()
                .eq(ProductScenePO::getProductId, productId));
        //封装密码产品VO
        setProductBaseVO(previewVO, serviceProductPO);
        //封装密码产品信息其他相关信息VO
        setProductInfoVO(previewVO, productSuperiorityPOS, productUsePOS, productSpecsPOS, productScenePOS);

        //查询有无配额信息
        TenantToProductPO tenantToProductPO = tenantToProductService.getOne(new LambdaQueryWrapper<TenantToProductPO>()
                .eq(TenantToProductPO::getTenantId, LoginUserUtil.getTenantId())
                .eq(TenantToProductPO::getProductId, productId));
        if (!ObjectUtils.isEmpty(tenantToProductPO)) {
            TenantToProductVO tenantToProductVO = tenantToProductConvert.convertVo(tenantToProductPO);
            //计算剩余天数
            if (StringUtils.isNotBlank(tenantToProductVO.getExpirationTime())) {
                LocalDateTime expireDateTime = DateUtils.string2LocalDateTime(tenantToProductVO.getExpirationTime());
                Long expireDateSeconds = DateUtils.localDateTime2Seconds(expireDateTime);
                LocalDateTime curDateTime = DateUtils.date2LocalDateTime(new Date());
                Long curDateSeconds = DateUtils.localDateTime2Seconds(curDateTime);
                if (expireDateSeconds <= curDateSeconds) {
                    tenantToProductVO.setRemainingDays(0);
                } else {
                    int remainingDays = (int) (((expireDateSeconds - curDateSeconds) / 3600 / 24) + 1);
                    tenantToProductVO.setRemainingDays(remainingDays);
                }
            } else {
                tenantToProductVO.setRemainingDays(null);
            }
            previewVO.setTenantToProduct(tenantToProductVO);
        }

        return ResultUtil.ok(previewVO);
    }

    /**
     * 密码产品附件上传
     */
    @Override
    public SecRestResponse<ProductAttachmentVO> attachUpload(MultipartFile file) {
        //图标5M 场景20M
        // 其余100M
        String workOrderAttachmentSize = httpsServiceUtil.getConfigValueByConfigCode("workOrderAttachmentSize");
        long uploadSize = Long.parseLong(workOrderAttachmentSize);
        //文件不可为空，文件不能超过配置文件中配制的允许大小
        if (file == null || file.isEmpty()) {
            log.error("The attachment upload file is empty,errorcode:{}", SecErrorCodeConstant.WORKORDER_UPLOAD_FILE_NULL);
            throw new BusinessException(SecErrorCodeConstant.WORKORDER_UPLOAD_FILE_NULL);
        }
        long megabytes = DataSize.ofBytes(file.getSize()).toMegabytes();
        if (megabytes > uploadSize) {
            log.error("Attachment upload file size exceeds the limit,errorcode:{}", SecErrorCodeConstant.WORKORDER_UPLOAD_FILE_SIZE_EXCEED);
            throw new BusinessException(SecErrorCodeConstant.WORKORDER_UPLOAD_FILE_SIZE_EXCEED, true, uploadSize);
        }
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            log.error("The file name uploaded to the attachment is empty,errorcode:{}", SecErrorCodeConstant.WORKORDER_UPLOAD_FILE_NAME_NULL);
            throw new BusinessException(SecErrorCodeConstant.WORKORDER_UPLOAD_FILE_NAME_NULL);
        }
        //随机生成唯一路径，防止冲突
        String randomId = IdGenerator.ins().generatorString();
        String randomPath = "/b" + randomId;

        String fileDirPath = tempPath + randomPath;
        File fileDir = new File(fileDirPath);

        //中文文件名有可能出错
        String fileType = originalFilename.substring(originalFilename.lastIndexOf('.'));
        String filePath = fileDirPath + File.separator + randomId + fileType;
        //限制文件类型
        String workOrderAttachmentType = httpsServiceUtil.getConfigValueByConfigCode("productAttachmentType");
        List<String> workOrderAttachmentTypeList = Arrays.asList(workOrderAttachmentType.split(","));
        if(!workOrderAttachmentTypeList.contains(fileType)){
            log.error("The file type uploaded to the attachment is not allowed,errorcode:{}",SecErrorCodeConstant.WORKORDER_UPLOAD_FILE_TYPE_NOT_ALLOW);
            throw new BusinessException(SecErrorCodeConstant.WORKORDER_UPLOAD_FILE_TYPE_NOT_ALLOW);
        }

        //创建文件目录
        if (!fileDir.exists()) {
            fileDir.mkdirs();
        }
        File f = new File(filePath);
        try {
            //将文件写入到临时目录
            file.transferTo(f);
        } catch (Exception e) {
            log.error("Attachment writing to temporary directory failed,errorcode:{}", SecErrorCodeConstant.WORKORDER_UPLOAD_TEMP_FILE_FAIL);
            throw new BusinessException(SecErrorCodeConstant.WORKORDER_UPLOAD_TEMP_FILE_FAIL);
        }
        //文件存储在hdfs
        if (storeType.equals("hdfs")) {
            //文件上传hadoop
            String hadoopPath = "/Product" + randomPath + File.separator + randomId + fileType;
            try {
                hadoopTemplate.upload(true, true, filePath, hadoopPath);
            } catch (Exception e) {
                log.error("Attachment upload Hadoop failed,errorcode:{},error:{}", SecErrorCodeConstant.WORKORDER_UPLOAD_HDFS_FAIL, e);
                throw new BusinessException(SecErrorCodeConstant.WORKORDER_UPLOAD_HDFS_FAIL);
            } finally {
                deleteFileAndDir(fileDirPath);
            }
            //保存附件信息到数据库
            ProductAttachmentVO productAttachmentVO = saveProductFileToDB(originalFilename, hadoopPath);
            return ResultUtil.ok(productAttachmentVO);
        }
        //文件存储在本地
        else {
            ProductAttachmentVO productAttachmentVO = saveProductFileToDB(originalFilename, filePath);
            return ResultUtil.ok(productAttachmentVO);
        }
    }

    /**
     * 密码产品附件下载
     */
    @Override
    public void attachDownload(AttachDownloadDTO attachDownloadDTO, HttpServletResponse response) {
        Long attachmentId = attachDownloadDTO.getAttachmentId();
        ProductAttachmentPO productAttachmentPO = productAttachmentMapper.selectById(attachmentId);
        if (ObjectUtils.isEmpty(productAttachmentPO)) {
            log.error("Attachment does not exist,errorcode:{}", SecErrorCodeConstant.WORKORDER_ATTACHMENT_RECORD_NOT_EXIST);
            throw new BusinessException(SecErrorCodeConstant.WORKORDER_ATTACHMENT_RECORD_NOT_EXIST);
        }
        ProductAttachmentVO attachmentVO = productAttachmentConvert.convertVo(productAttachmentPO);
        String attachmentPath = attachmentVO.getStoragePath();
        String fileName = attachmentVO.getFileName();
        String filePath = null;
        String fileDirPath = null;
        if (storeType.equals("hdfs")) {
            boolean exist = hadoopTemplate.isExist(attachmentPath);
            if (!exist) {
                log.error(FILE_NOT_EXIST_ERROR, SecErrorCodeConstant.WORKORDER_ATTACHMENT_NOT_EXIST, attachmentPath);
                throw new BusinessException(SecErrorCodeConstant.WORKORDER_ATTACHMENT_NOT_EXIST);
            }
            fileDirPath = tempPath + attachmentPath.substring(0, attachmentPath.lastIndexOf('/'));
            File fileDir = new File(fileDirPath);
            filePath = tempPath + attachmentPath;
            //创建文件目录
            if (!fileDir.exists()) {
                fileDir.mkdirs();
            }
            try {
                hadoopTemplate.download(attachmentPath, filePath);
            } catch (Exception e) {
                log.error("File system download failed,errorcode:{},attachmentPath:{},error:{}", SecErrorCodeConstant.WORKORDER_ATTACHMENT_DOWNLOAD_FAIL, attachmentPath, e);
                throw new BusinessException(SecErrorCodeConstant.WORKORDER_ATTACHMENT_DOWNLOAD_FAIL);
            }
            //检验本地服务器文件是否存在
            File file = new File(filePath);
            if (!file.exists()) {
                log.error("File system download failed,errorcode:{},attachmentPath:{}", SecErrorCodeConstant.WORKORDER_ATTACHMENT_DOWNLOAD_FAIL, attachmentPath);
                throw new BusinessException(SecErrorCodeConstant.WORKORDER_ATTACHMENT_DOWNLOAD_FAIL);
            }
        }
        //本地文件存储
        else {
            filePath = attachmentPath;
            //检验本地服务器文件是否存在
            File file = new File(filePath);
            if (!file.exists()) {
                log.error(FILE_NOT_EXIST_ERROR, SecErrorCodeConstant.WORKORDER_ATTACHMENT_NOT_EXIST, attachmentPath);
                throw new BusinessException(SecErrorCodeConstant.WORKORDER_ATTACHMENT_NOT_EXIST);
            }
        }

        try (
                FileInputStream fileInputStream = new FileInputStream(filePath);
                BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream);
                ServletOutputStream outputStream = response.getOutputStream()
        ) {
            String fileNameSend = URLEncoder.encode(fileName, CharEncoding.UTF_8).replace("\\+", "%20");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileNameSend);
            byte[] buffer = new byte[102400];
            int bytesRead;
            while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        } catch (Exception e) {
            log.error("File stream send failed {}", e);
        } finally {
            //hdfs存储删除本地临时存储
            if (storeType.equals("hdfs")) {
                deleteFileAndDir(fileDirPath);
            }
        }
    }

    /**
     * 密码产品附件删除
     */
    @Override
    public SecRestResponse<Object> attachDelete(AttachDeleteDTO attachDeleteDTO) {
        Long attachmentId = attachDeleteDTO.getAttachmentId();
        ProductAttachmentPO productAttachmentPO = productAttachmentMapper.selectById(attachmentId);
        if (ObjectUtils.isEmpty(productAttachmentPO)) {
            log.error("Attachment does not exist,errorcode:{}", SecErrorCodeConstant.WORKORDER_ATTACHMENT_RECORD_NOT_EXIST);
            throw new BusinessException(SecErrorCodeConstant.WORKORDER_ATTACHMENT_RECORD_NOT_EXIST);
        }
        ProductAttachmentVO attachmentVO = productAttachmentConvert.convertVo(productAttachmentPO);
        String attachmentPath = attachmentVO.getStoragePath();
        //文件存储在hdfs
        if (storeType.equals("hdfs")) {
            boolean exist = hadoopTemplate.isExist(attachmentPath);
            if (!exist) {
                log.error(FILE_NOT_EXIST_ERROR, SecErrorCodeConstant.WORKORDER_ATTACHMENT_NOT_EXIST, attachmentPath);
                throw new BusinessException(SecErrorCodeConstant.WORKORDER_ATTACHMENT_NOT_EXIST);
            }
            try {
                hadoopTemplate.delete(attachmentPath, true);
                String dirPath = attachmentPath.substring(0, attachmentPath.lastIndexOf('/'));
                hadoopTemplate.delete(dirPath, true);
            } catch (Exception e) {
                log.error("File deletion failed,errorcode:{},attachmentPath:{}", SecErrorCodeConstant.WORKORDER_ATTACHMENT_DELETE_FAIL, attachmentPath);
                throw new BusinessException(SecErrorCodeConstant.WORKORDER_ATTACHMENT_DELETE_FAIL);
            }
        }
        //文件存储在本地
        else {
            File file = new File(attachmentPath);
            if (!file.exists()) {
                log.error(FILE_NOT_EXIST_ERROR, SecErrorCodeConstant.WORKORDER_ATTACHMENT_NOT_EXIST, attachmentPath);
                throw new BusinessException(SecErrorCodeConstant.WORKORDER_ATTACHMENT_NOT_EXIST);
            }
            String dirPath = attachmentPath.substring(0, attachmentPath.lastIndexOf('/'));
            try {
                deleteFileAndDir(dirPath);
            } catch (Exception e) {
                log.error("File deletion failed,errorcode:{},attachmentPath:{}", SecErrorCodeConstant.WORKORDER_ATTACHMENT_DELETE_FAIL, attachmentPath);
                throw new BusinessException(SecErrorCodeConstant.WORKORDER_ATTACHMENT_DELETE_FAIL);
            }
        }
        productAttachmentMapper.deleteById(attachmentId);
        return ResultUtil.ok();
    }

    /**
     * 草稿提交
     */
    @Override
    public SecRestResponse<Object> submitDraft(ProductSubmitDTO productSubmitDTO) {
        //密码产品
        ServiceProductPO serviceProduct = serviceProductConvert.dtoToPo(productSubmitDTO.getServiceProduct());
        if (ObjectUtils.isEmpty(serviceProduct)) {
            log.error("ProductServiceImpl#submitDraft product base info is null");
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_BASE_INFO_IS_NULL);
        }
        //检查密码产品的图标附件是否匹配
        Long productIconId = serviceProduct.getProductIconId();
        ProductAttachmentPO productAttachmentICON = productAttachmentMapper.selectById(productIconId);
        if (ObjectUtils.isEmpty(productAttachmentICON)) {
            log.error("productIconId:{},productAttachmentICON:{},code:{}",productIconId,productAttachmentICON,SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
        }
        //检查密码产品文件附件是否匹配
        Long annexId = serviceProduct.getAnnexId();
        if (ObjectUtils.isNotEmpty(annexId)) {
            ProductAttachmentPO productAttachmentAnnex = productAttachmentMapper.selectById(annexId);
            if (ObjectUtils.isEmpty(productAttachmentAnnex)) {
                log.error("annexId:{},productAttachmentAnnex:{},code:{}",annexId,productAttachmentAnnex,SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
                throw new BusinessException(SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
            }
        }
        //检查密码产品PDF附件是否匹配
        Long pdfAnnexId = serviceProduct.getPdfAnnexId();
        if (ObjectUtils.isNotEmpty(pdfAnnexId)) {
            ProductAttachmentPO productAttachmentPDF = productAttachmentMapper.selectById(pdfAnnexId);
            if (ObjectUtils.isEmpty(productAttachmentPDF)) {
                log.error("pdfAnnexId:{},productAttachmentPDF:{},code:{}",pdfAnnexId,productAttachmentPDF,SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
                throw new BusinessException(SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
            }
        }
        //规格信息
        List<ProductSpecsPO> specsList = productSpecsConvert.dtoToPo(productSubmitDTO.getSpecsList());
        //应用场景
        List<ProductScenePO> sceneList = productSceneConvert.dtoToPo(productSubmitDTO.getSceneList());
        //检查应用场景图片附件是否匹配
        if (!CollectionUtils.isEmpty(sceneList)) {
            sceneList.forEach(productScenePO -> {
                Long sceneIconId = productScenePO.getSceneIconId();
                ProductAttachmentPO productAttachmentScene = productAttachmentMapper.selectById(sceneIconId);
                if (ObjectUtils.isEmpty(productAttachmentScene)) {
                    log.error("sceneIconId:{},productAttachmentScene:{},code:{}",sceneIconId,productAttachmentScene,SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
                    throw new BusinessException(SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
                }
            });
        }
        //产品优势
        List<ProductSuperiorityPO> superiorityList = productSuperiorityConvert.dtoToPo(productSubmitDTO.getSuperiorityList());
        //检查产品优势图片附件是否匹配
        if (!CollectionUtils.isEmpty(superiorityList)) {
            superiorityList.forEach(productSuperiorityPO -> {
                Long superiorityIconId = productSuperiorityPO.getSuperiorityIconId();
                ProductAttachmentPO productAttachmentSuper = productAttachmentMapper.selectById(superiorityIconId);
                if (ObjectUtils.isEmpty(productAttachmentSuper)) {
                    log.error("superiorityIconId:{},productAttachmentSuper:{},code:{}",superiorityIconId,productAttachmentSuper,SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
                    throw new BusinessException(SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
                }
            });
        }
        //产品功能
        List<ProductUsePO> useList = productUseConvert.dtoToPo(productSubmitDTO.getUseList());
        //获取草稿Id
        Long draftProductId = productSubmitDTO.getDraftProductId();
        //草稿Id不为空
        if (draftProductId != null) {
            //删除密码产品数据类型为草稿
            serviceProductMapper.delete(new LambdaQueryWrapper<ServiceProductPO>()
                    .eq(ServiceProductPO::getId, draftProductId)
                    .eq(ServiceProductPO::getDataType, CommonConstant.SERVICE_PRODUCT_DATA_TYPE_DRAFT));
            //删除密码产品的场景、规格、功能、优势相关信息
            delRelateInfoOfProduct(productSubmitDTO.getDraftProductId());
        }
        //设置密码产品Id
        if ((draftProductId != null)) {
            serviceProduct.setId(draftProductId);
        } else {
            serviceProduct.setId(IdGenerator.ins().generator());
        }
        //数据类型为草稿
        serviceProduct.setDataType(CommonConstant.SERVICE_PRODUCT_DATA_TYPE_DRAFT);
        //创建人
        serviceProduct.setCreateBy(LoginUserUtil.getUserId());
        //创建时间
        serviceProduct.setCreateTime(DateUtils.getCurrentLocalDateTime2String());
        //新增密码产品基础信息(草稿)
        serviceProductMapper.insert(serviceProduct);
        //新增密码产品相关信息(数据类型为草稿)
        //获取新增密码产品的Id
        Long productId = serviceProduct.getId();
        //设置草稿提交时密码产品相关信息，草稿不处理附件表
        setSubmitProductInfo(specsList, sceneList, superiorityList, useList, productId);
        //返回产品Id
        return ResultUtil.ok(productId);
    }

    /**
     * 正式提交
     */
    @Override
    public SecRestResponse<Object> submit(ProductSubmitDTO productSubmitDTO) {
        //密码产品
        ServiceProductPO serviceProduct = serviceProductConvert.dtoToPo(productSubmitDTO.getServiceProduct());
        if (ObjectUtils.isEmpty(serviceProduct)) {
            log.error("product_base_info_is_null,excode:{}",SecErrorCodeConstant.PRODUCT_BASE_INFO_IS_NULL);
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_BASE_INFO_IS_NULL);
        }
        //检查密码产品的图标附件是否匹配
        Long productIconId = serviceProduct.getProductIconId();
        ProductAttachmentPO productAttachmentICON = productAttachmentMapper.selectById(productIconId);
        if (ObjectUtils.isEmpty(productAttachmentICON)) {
            log.error("productIconId:{},productAttachmentICON:{},eccode:{}",productIconId,productAttachmentICON,SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
        }
        //检验文件附件是否匹配
        Long annexId = serviceProduct.getAnnexId();
        if (ObjectUtils.isNotEmpty(annexId)) {
            ProductAttachmentPO productAttachmentAnnex = productAttachmentMapper.selectById(annexId);
            if (ObjectUtils.isEmpty(productAttachmentAnnex)) {
                log.error("productIconId:{},productAttachmentAnnex:{},excode:{}",annexId,productAttachmentAnnex,SecErrorCodeConstant.PRODUCT_BASE_INFO_IS_NULL);
                throw new BusinessException(SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
            }
        }
        //检查密码产品PDF附件是否匹配
        Long pdfAnnexId = serviceProduct.getPdfAnnexId();
        if (ObjectUtils.isNotEmpty(pdfAnnexId)) {
            ProductAttachmentPO productAttachmentPDF = productAttachmentMapper.selectById(pdfAnnexId);
            if (ObjectUtils.isEmpty(productAttachmentPDF)) {
                log.error("pdfAnnexId:{},productAttachmentPDF:{},excode:{}",pdfAnnexId,productAttachmentPDF,SecErrorCodeConstant.PRODUCT_BASE_INFO_IS_NULL);
                throw new BusinessException(SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
            }
        }
        //规格信息
        List<ProductSpecsPO> specsList = productSpecsConvert.dtoToPo(productSubmitDTO.getSpecsList());
        //应用场景
        List<ProductScenePO> sceneList = productSceneConvert.dtoToPo(productSubmitDTO.getSceneList());
        //检查应用场景图片附件是否匹配
        if (!CollectionUtils.isEmpty(sceneList)) {
            sceneList.forEach(productScenePO -> {
                Long sceneIconId = productScenePO.getSceneIconId();
                ProductAttachmentPO productAttachmentScene = productAttachmentMapper.selectById(sceneIconId);
                if (ObjectUtils.isEmpty(productAttachmentScene)) {
                    log.error("sceneIconId:{},productAttachmentScene:{},excode:{}",sceneIconId,productAttachmentScene,SecErrorCodeConstant.PRODUCT_BASE_INFO_IS_NULL);
                    throw new BusinessException(SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
                }
            });
        }
        //产品优势
        List<ProductSuperiorityPO> superiorityList = productSuperiorityConvert.dtoToPo(productSubmitDTO.getSuperiorityList());
        //检查产品优势图片附件是否匹配
        if (!CollectionUtils.isEmpty(superiorityList)) {
            superiorityList.forEach(productSuperiorityPO -> {
                Long superiorityIconId = productSuperiorityPO.getSuperiorityIconId();
                ProductAttachmentPO productAttachmentSuper = productAttachmentMapper.selectById(superiorityIconId);
                if (ObjectUtils.isEmpty(productAttachmentSuper)) {
                    log.error("superiorityIconId:{},productAttachmentSuper:{},excode:{}",superiorityIconId,productAttachmentSuper,SecErrorCodeConstant.PRODUCT_BASE_INFO_IS_NULL);
                    throw new BusinessException(SecErrorCodeConstant.PRODUCT_ATTACH_NOT_MATCH);
                }
            });
        }
        //产品功能
        List<ProductUsePO> useList = productUseConvert.dtoToPo(productSubmitDTO.getUseList());
        //获取草稿Id
        Long draftProductId = productSubmitDTO.getDraftProductId();
        //草稿Id不为空
        if (draftProductId != null) {
            //删除密码产品数据类型为草稿
            serviceProductMapper.delete(new LambdaQueryWrapper<ServiceProductPO>()
                    .eq(ServiceProductPO::getId, draftProductId)
                    .eq(ServiceProductPO::getDataType, CommonConstant.SERVICE_PRODUCT_DATA_TYPE_DRAFT));
            //删除密码产品的场景、规格、功能、优势相关信息
            delRelateInfoOfProduct(draftProductId);
        }
        //产品Id不为空
        String createTime = null;
        if (ObjectUtils.isNotEmpty(serviceProduct.getId())) {
            ServiceProductPO serviceProductPO = serviceProductMapper.selectById(serviceProduct.getId());
            createTime = serviceProductPO.getCreateTime();
            //密码产品Id存在,删除该密码产品的全部信息且数据类型为正式
            serviceProductMapper.delete(new LambdaQueryWrapper<ServiceProductPO>()
                    .eq(ServiceProductPO::getId, serviceProduct.getId())
                    .eq(ServiceProductPO::getDataType, CommonConstant.SERVICE_PRODUCT_DATA_TYPE_FORMAL));
            //删除该密码产品的场景、规格、功能、优势相关信息
            delRelateInfoOfProduct(serviceProduct.getId());
        }
        //新建密码产品
        //设置密码产品Id
        Long productId = serviceProduct.getId();
        if (ObjectUtils.isEmpty(productId)) {
            serviceProduct.setId(IdGenerator.ins().generator());
        } else {
            serviceProduct.setId(productId);
            //密码产品数据类型为正式类型
            serviceProduct.setDataType(CommonConstant.SERVICE_PRODUCT_DATA_TYPE_FORMAL);
            //密码产品创建人
            serviceProduct.setCreateBy(LoginUserUtil.getUserId());
            //密码产品创建时间
            if (StringUtils.isNotBlank(createTime)) {
                serviceProduct.setCreateTime(createTime);
            }
            //密码产品修改人
            serviceProduct.setUpdateBy(LoginUserUtil.getUserId());
            //密码产品修改时间
            serviceProduct.setUpdateTime(DateUtils.getCurrentLocalDateTime2String());
            //新增密码产品基础信息
            serviceProductMapper.insert(serviceProduct);
            //设置密码产品相关信息
            setSubmitProductInfo(specsList, sceneList, superiorityList, useList, serviceProduct.getId());
            //数据类型为正式时，处理附件表
            setSubmitProductionAttach(serviceProduct, sceneList, superiorityList, serviceProduct.getId());
            //返回产品Id
            return ResultUtil.ok(serviceProduct.getId());
        }
        //密码产品数据类型为正式类型
        serviceProduct.setDataType(CommonConstant.SERVICE_PRODUCT_DATA_TYPE_FORMAL);
        //密码产品创建人
        serviceProduct.setCreateBy(LoginUserUtil.getUserId());
        //密码产品创建时间
        serviceProduct.setCreateTime(DateUtils.getCurrentLocalDateTime2String());
        //密码产品修改人
        serviceProduct.setUpdateBy(LoginUserUtil.getUserId());
        //密码产品修改时间
        serviceProduct.setUpdateTime(DateUtils.getCurrentLocalDateTime2String());
        //新增密码产品基础信息
        serviceProductMapper.insert(serviceProduct);
        //设置密码产品相关信息
        setSubmitProductInfo(specsList, sceneList, superiorityList, useList, serviceProduct.getId());
        //数据类型为正式时，处理附件表
        setSubmitProductionAttach(serviceProduct, sceneList, superiorityList, serviceProduct.getId());
        //返回产品Id
        return ResultUtil.ok(serviceProduct.getId());
    }

    /**
     * 密码产品附件文件下载Get请求
     */
    @Override
    public void attachDownloadGet(AttachDownloadDTO attachDownloadDTO, HttpServletResponse response) {
        attachDownload(attachDownloadDTO, response);
    }

    private void setSubmitProductionAttach(ServiceProductPO serviceProduct, List<ProductScenePO> sceneList, List<ProductSuperiorityPO> superiorityList, Long productId) {
        Long annexId = serviceProduct.getAnnexId();
        if (ObjectUtils.isNotEmpty(annexId) && ObjectUtils.isNotEmpty(productAttachmentMapper.selectById(annexId))) {
            productAttachmentMapper.update(null, new LambdaUpdateWrapper<ProductAttachmentPO>()
                    .set(ProductAttachmentPO::getProductId, productId)
                    .set(ProductAttachmentPO::getUpdateBy, LoginUserUtil.getUserId())
                    .set(ProductAttachmentPO::getUpdateTime, DateUtils.getCurrentLocalDateTime2String())
                    .eq(ProductAttachmentPO::getId, annexId));
        }
        //密码产品图标附件
        Long productIconId = serviceProduct.getProductIconId();
        if (ObjectUtils.isNotEmpty(productIconId) && ObjectUtils.isNotEmpty(productAttachmentMapper.selectById(productIconId))) {
            productAttachmentMapper.update(null, new LambdaUpdateWrapper<ProductAttachmentPO>()
                    .set(ProductAttachmentPO::getProductId, productId)
                    .set(ProductAttachmentPO::getUpdateBy, LoginUserUtil.getUserId())
                    .set(ProductAttachmentPO::getUpdateTime, DateUtils.getCurrentLocalDateTime2String())
                    .eq(ProductAttachmentPO::getId, productIconId));
        }
        //密码产品PDF附件
        Long pdfAnnexId = serviceProduct.getPdfAnnexId();
        if (ObjectUtils.isNotEmpty(pdfAnnexId) && ObjectUtils.isNotEmpty(productAttachmentMapper.selectById(pdfAnnexId))) {
            productAttachmentMapper.update(null, new LambdaUpdateWrapper<ProductAttachmentPO>()
                    .set(ProductAttachmentPO::getProductId, productId)
                    .set(ProductAttachmentPO::getUpdateBy, LoginUserUtil.getUserId())
                    .set(ProductAttachmentPO::getUpdateTime, DateUtils.getCurrentLocalDateTime2String())
                    .eq(ProductAttachmentPO::getId, pdfAnnexId));
        }
        //优势图标附件
        if (!CollectionUtils.isEmpty(superiorityList)) {
            superiorityList.forEach(productSuperiorityPO -> {
                Long superiorityIconId = productSuperiorityPO.getSuperiorityIconId();
                if (ObjectUtils.isNotEmpty(superiorityIconId) && ObjectUtils.isNotEmpty(productAttachmentMapper.selectById(superiorityIconId))) {
                    productAttachmentMapper.update(null, new LambdaUpdateWrapper<ProductAttachmentPO>()
                            .set(ProductAttachmentPO::getProductId, productId)
                            .set(ProductAttachmentPO::getUpdateBy, LoginUserUtil.getUserId())
                            .set(ProductAttachmentPO::getUpdateTime, DateUtils.getCurrentLocalDateTime2String())
                            .eq(ProductAttachmentPO::getId, superiorityIconId));
                }
            });
        }
        //场景图标附件
        if (!CollectionUtils.isEmpty(sceneList)) {
            sceneList.forEach(productScenePO -> {
                Long sceneIconId = productScenePO.getSceneIconId();
                if (ObjectUtils.isNotEmpty(sceneIconId) && ObjectUtils.isNotEmpty(productAttachmentMapper.selectById(sceneIconId))) {
                    productAttachmentMapper.update(null, new LambdaUpdateWrapper<ProductAttachmentPO>()
                            .set(ProductAttachmentPO::getProductId, productId)
                            .set(ProductAttachmentPO::getUpdateBy, LoginUserUtil.getUserId())
                            .set(ProductAttachmentPO::getUpdateTime, DateUtils.getCurrentLocalDateTime2String())
                            .eq(ProductAttachmentPO::getId, sceneIconId));
                }
            });
        }

    }

    /**
     * 设置提交时密码产品相关信息
     */
    private void setSubmitProductInfo(List<ProductSpecsPO> specsList, List<ProductScenePO> sceneList, List<ProductSuperiorityPO> superiorityList, List<ProductUsePO> useList, Long productId) {
        // 规格信息
        if (!CollectionUtils.isEmpty(specsList)) {
            specsList.forEach(productSpecsPO -> {
                productSpecsPO.setId(IdGenerator.ins().generator());
                productSpecsPO.setProductId(productId);
                productSpecsPO.setCreateBy(LoginUserUtil.getUserId());
                productSpecsPO.setCreateTime(DateUtils.getCurrentLocalDateTime2String());
                productSpecsMapper.insert(productSpecsPO);
            });
        }
        //应用场景
        if (!CollectionUtils.isEmpty(sceneList)) {
            sceneList.forEach(productScenePO -> {
                productScenePO.setId(IdGenerator.ins().generator());
                productScenePO.setProductId(productId);
                productScenePO.setCreateBy(LoginUserUtil.getUserId());
                productScenePO.setCreateTime(DateUtils.getCurrentLocalDateTime2String());
                productSceneMapper.insert(productScenePO);
            });
        }
        //产品优势
        if (!CollectionUtils.isEmpty(superiorityList)) {
            superiorityList.forEach(productSuperiorityPO -> {
                productSuperiorityPO.setId(IdGenerator.ins().generator());
                productSuperiorityPO.setProductId(productId);
                productSuperiorityPO.setCreateBy(LoginUserUtil.getUserId());
                productSuperiorityPO.setCreateTime(DateUtils.getCurrentLocalDateTime2String());
                productSuperiorityMapper.insert(productSuperiorityPO);
            });
        }
        //产品功能
        if (!CollectionUtils.isEmpty(useList)) {
            useList.forEach(productUsePO -> {
                productUsePO.setId(IdGenerator.ins().generator());
                productUsePO.setProductId(productId);
                productUsePO.setCreateBy(LoginUserUtil.getUserId());
                productUsePO.setCreateTime(DateUtils.getCurrentLocalDateTime2String());
                productUseMapper.insert(productUsePO);
            });
        }
    }

    /**
     * [新建/编辑]取消
     */
    @Override
    public SecRestResponse<Object> cancelById(CancelProductDTO cancelProductDTO) {
        //检验Id
        Long productId = cancelProductDTO.getProductId();
        if (ObjectUtils.isEmpty(productId)) {
            return ResultUtil.ok();
        }
        //根据Id查询密码产品
        ServiceProductPO serviceProductPO = serviceProductMapper.selectOne(new LambdaQueryWrapper<ServiceProductPO>()
                .eq(ServiceProductPO::getId, productId));
        // 密码产品不存在
        if (ObjectUtils.isEmpty(serviceProductPO)) {
            return ResultUtil.ok();
        }
        //密码产品存在，状态为正式数据
        if (Objects.equals(serviceProductPO.getDataType(), CommonConstant.SERVICE_PRODUCT_DATA_TYPE_FORMAL)) {
            //正式数据不可删除
            log.error("service_product is formal not delete,code:{}",SecErrorCodeConstant.PRODUCT_DATA_TYPE_IS_FORMAL_NOT_DELETE);
            throw new BusinessException(SecErrorCodeConstant.PRODUCT_DATA_TYPE_IS_FORMAL_NOT_DELETE);
        }
        //密码产品存在，单数据状态为草稿，删除草稿数据
        serviceProductMapper.deleteById(serviceProductPO);
        //删除产品优势、规格、功能、场景相关信息及其优势和场景的附件信息
        delRelateInfoOfProductAndAttach(productId);
        //删除密码产品附件
        Long annexId = serviceProductPO.getAnnexId();
        if (ObjectUtils.isNotEmpty(annexId)) {
            //删除附件表中数据
            productAttachmentMapper.delete(new LambdaQueryWrapper<ProductAttachmentPO>()
                    .eq(ProductAttachmentPO::getId, annexId)
                    .isNull(ProductAttachmentPO::getProductId));
        }
        //删除密码产品PDF附件
        Long pdfAnnexId = serviceProductPO.getPdfAnnexId();
        if (ObjectUtils.isNotEmpty(pdfAnnexId)) {
            //删除附件表中数据
            productAttachmentMapper.delete(new LambdaQueryWrapper<ProductAttachmentPO>()
                    .eq(ProductAttachmentPO::getId, pdfAnnexId)
                    .isNull(ProductAttachmentPO::getProductId));
        }
        //删除密码产品图标附件
        productAttachmentMapper.delete(new LambdaQueryWrapper<ProductAttachmentPO>()
                .eq(ProductAttachmentPO::getId, serviceProductPO.getProductIconId())
                .isNull(ProductAttachmentPO::getProductId));
        return ResultUtil.ok();
    }

    /**
     * 返回Map<k,v>,k:密码产品id,v:密码产品name
     */
    @Override
    public SecRestResponse<Map<Long, String>> productOfMap() {
        //构建wrapper
        LambdaQueryWrapper<ServiceProductPO> queryWrapper = new LambdaQueryWrapper<>();
        //更新时间倒序
        queryWrapper.orderByDesc(ServiceProductPO::getUpdateTime);
        //过滤草稿，查询数据类型为正式的密码产品
        queryWrapper.eq(ServiceProductPO::getDataType, CommonConstant.SERVICE_PRODUCT_DATA_TYPE_FORMAL);
        //查询
        List<ServiceProductPO> serviceProductPOS = serviceProductMapper.selectList(queryWrapper);
        //返回为map
        Map<Long, String> res = serviceProductPOS.stream().collect(Collectors.toMap(ServiceProductPO::getId, ServiceProductPO::getProductName, (k1, k2) -> k1));
        return ResultUtil.ok(res);
    }

    /**
     * 查询所有密码产品list
     */
    @Override
    public SecRestResponse<List<ServiceProductVO>> findProductList() {
        //构建wrapper
        LambdaQueryWrapper<ServiceProductPO> queryWrapper = new LambdaQueryWrapper<>();
        //更新时间倒序
        queryWrapper.orderByDesc(ServiceProductPO::getUpdateTime);
        //过滤草稿，查询数据类型为正式的密码产品
        queryWrapper.eq(ServiceProductPO::getDataType, CommonConstant.SERVICE_PRODUCT_DATA_TYPE_FORMAL);
        queryWrapper.eq(ServiceProductPO::getProductState, CommonConstant.SERVICE_PRODUCT_OPEN_STATUS);
        //查询
        List<ServiceProductPO> serviceProductPOS = serviceProductMapper.selectList(queryWrapper);
        return ResultUtil.ok(serviceProductConvert.convert(serviceProductPOS));
    }

    /**
     * 删除目录下的文件和文件夹
     */
    private void deleteFileAndDir(String fileDirPath) {
        //删除目录下的文件
        File fileDir = new File(fileDirPath);
        if (fileDir.exists()) {
            File[] files = fileDir.listFiles();
            for (File file : files) {
                file.delete();
            }
            fileDir.delete();
        }

    }

    /**
     * 密码产品附件信息入库
     */
    private ProductAttachmentVO saveProductFileToDB(String originalFilename, String path) {
        ProductAttachmentPO attachmentPO = new ProductAttachmentPO();
        attachmentPO.setId(IdGenerator.ins().generator());
        attachmentPO.setStoragePath(path);
        attachmentPO.setFileName(originalFilename);
        attachmentPO.setCreateBy(LoginUserUtil.getUserId());
        attachmentPO.setCreateTime(DateUtils.getCurrentLocalDateTime2String());
        productAttachmentMapper.insert(attachmentPO);
        return productAttachmentConvert.convertVo(attachmentPO);
    }

    /**
     * 删除与密码产品相关的信息
     */
    private void delRelateInfoOfProduct(Long productId) {
        //删除产品优势
        productSuperiorityMapper.delete(new LambdaQueryWrapper<ProductSuperiorityPO>()
                .eq(ProductSuperiorityPO::getProductId, productId));
        //删除产品功能
        productUseMapper.delete(new LambdaQueryWrapper<ProductUsePO>()
                .eq(ProductUsePO::getProductId, productId));
        //删除产品规格
        productSpecsMapper.delete(new LambdaQueryWrapper<ProductSpecsPO>()
                .eq(ProductSpecsPO::getProductId, productId));
        //删除应用场景
        productSceneMapper.delete(new LambdaQueryWrapper<ProductScenePO>()
                .eq(ProductScenePO::getProductId, productId));
    }

    private void delRelateInfoOfProductAndAttach(Long productId) {
        List<ProductSuperiorityPO> productSuperiorityPOS = productSuperiorityMapper
                .selectList(new LambdaQueryWrapper<ProductSuperiorityPO>().eq(ProductSuperiorityPO::getProductId, productId));
        if (!CollectionUtils.isEmpty(productSuperiorityPOS)) {
            productSuperiorityPOS.forEach(productSuperiorityPO -> {
                Long productSuperiorityPOProductId = productSuperiorityPO.getProductId();
                Long superiorityIconId = productSuperiorityPO.getSuperiorityIconId();
                //删除产品优势
                productSuperiorityMapper.delete(new LambdaQueryWrapper<ProductSuperiorityPO>()
                        .eq(ProductSuperiorityPO::getProductId, productSuperiorityPOProductId));
                if (ObjectUtils.isNotEmpty(superiorityIconId)) {
                    productAttachmentMapper.delete(new LambdaQueryWrapper<ProductAttachmentPO>()
                            .eq(ProductAttachmentPO::getId, superiorityIconId)
                            .isNull(ProductAttachmentPO::getProductId));
                }
            });
        }
        //删除产品功能
        productUseMapper.delete(new LambdaQueryWrapper<ProductUsePO>()
                .eq(ProductUsePO::getProductId, productId));
        //删除产品规格
        productSpecsMapper.delete(new LambdaQueryWrapper<ProductSpecsPO>()
                .eq(ProductSpecsPO::getProductId, productId));
        //删除应用场景
        List<ProductScenePO> productScenePOS = productSceneMapper
                .selectList(new LambdaQueryWrapper<ProductScenePO>().eq(ProductScenePO::getProductId, productId));
        if (!CollectionUtils.isEmpty(productScenePOS)) {
            productScenePOS.forEach(productScenePO -> {
                Long productScenePOProductId = productScenePO.getProductId();
                Long sceneIconId = productScenePO.getSceneIconId();
                //删除产品优势
                productSuperiorityMapper.delete(new LambdaQueryWrapper<ProductSuperiorityPO>()
                        .eq(ProductSuperiorityPO::getProductId, productScenePOProductId));
                if (ObjectUtils.isNotEmpty(sceneIconId)) {
                    productAttachmentMapper.delete(new LambdaQueryWrapper<ProductAttachmentPO>()
                            .eq(ProductAttachmentPO::getId, sceneIconId)
                            .isNull(ProductAttachmentPO::getProductId));
                }
            });
        }
    }

    /**
     * 获取密码产品
     */
    private ServiceProductPO getServiceProductPO(Long productId) {
        //构造wrapper
        LambdaQueryWrapper<ServiceProductPO> queryWrapper = new LambdaQueryWrapper<>();
        //查询条件：Id
        queryWrapper.eq(ServiceProductPO::getId, productId);
        //查询
        return serviceProductMapper.selectOne(queryWrapper);
    }

    /**
     * 设置密码产品相关信息VO
     */
    private void setProductInfoVO(ProductPreviewVO productPreviewVO, List<ProductSuperiorityPO> productSuperiorityPOS, List<ProductUsePO> productUsePOS, List<ProductSpecsPO> productSpecsPOS, List<ProductScenePO> productScenePOS) {
        //封装产品功能
        if (!CollectionUtils.isEmpty(productUsePOS)) {
            List<ProductUseVO> useList = new ArrayList<>();
            for (ProductUsePO productUsePO : productUsePOS) {
                ProductUseVO productUseVO = new ProductUseVO();
                productUseVO.setTitle(productUsePO.getTitle());
                productUseVO.setRemark(productUsePO.getRemark());
                productUseVO.setSordNum(productUsePO.getSordNum());
                useList.add(productUseVO);
            }
            productPreviewVO.setUseList(useList);
        }
        //封装规格
        if (!CollectionUtils.isEmpty(productSpecsPOS)) {
            List<ProductSpecsVO> specsList = new ArrayList<>();
            for (ProductSpecsPO productSpecsPO : productSpecsPOS) {
                ProductSpecsVO productSpecsVO = new ProductSpecsVO();
                productSpecsVO.setTitle(productSpecsPO.getTitle());
                productSpecsVO.setProductUnit(productSpecsPO.getProductUnit());
                productSpecsVO.setProductPrice(productSpecsPO.getProductPrice());
                productSpecsVO.setProductDiscount(productSpecsPO.getProductDiscount());
                productSpecsVO.setSordNum(productSpecsPO.getSordNum());
                productSpecsVO.setRemark(productSpecsPO.getRemark());
                specsList.add(productSpecsVO);
            }
            productPreviewVO.setSpecsList(specsList);
        }
        //封装优势
        if (!CollectionUtils.isEmpty(productSuperiorityPOS)) {
            List<ProductSuperiorityVO> superiorityList = new ArrayList<>();
            for (ProductSuperiorityPO productSuperiorityPO : productSuperiorityPOS) {
                ProductSuperiorityVO productSuperiorityVO = new ProductSuperiorityVO();
                productSuperiorityVO.setTitle(productSuperiorityPO.getTitle());
                productSuperiorityVO.setSuperiorityIconId(productSuperiorityPO.getSuperiorityIconId());
                productSuperiorityVO.setSordNum(productSuperiorityPO.getSordNum());
                productSuperiorityVO.setRemark(productSuperiorityPO.getRemark());
                //封装产品优势图片附件
                ProductAttachmentPO productSuperiorityIconAttach = productAttachmentMapper.selectOne(new LambdaQueryWrapper<ProductAttachmentPO>()
                        .eq(ProductAttachmentPO::getId, productSuperiorityPO.getSuperiorityIconId()));
                if (ObjectUtils.isNotEmpty(productSuperiorityIconAttach)) {
                    SuperiorityIconAttach superiorityIconAttach = new SuperiorityIconAttach();
                    superiorityIconAttach.setId(productSuperiorityIconAttach.getId());
                    superiorityIconAttach.setProductId(productSuperiorityIconAttach.getProductId());
                    superiorityIconAttach.setStoragePath(productSuperiorityIconAttach.getStoragePath());
                    superiorityIconAttach.setFileName(productSuperiorityIconAttach.getFileName());
                    superiorityIconAttach.setRemark(productSuperiorityIconAttach.getRemark());
                    productSuperiorityVO.setSuperiorityIconAttach(superiorityIconAttach);
                }
                superiorityList.add(productSuperiorityVO);
            }
            productPreviewVO.setSuperiorityList(superiorityList);
        }

        //封装应用场景
        if (!CollectionUtils.isEmpty(productScenePOS)) {
            List<ProductSceneVO> sceneList = new ArrayList<>();
            for (ProductScenePO productScenePO : productScenePOS) {
                ProductSceneVO productSceneVO = new ProductSceneVO();
                productSceneVO.setTitle(productScenePO.getTitle());
                productSceneVO.setSceneIconId(productScenePO.getSceneIconId());
                productSceneVO.setSordNum(productScenePO.getSordNum());
                productSceneVO.setRemark(productScenePO.getRemark());
                //封装场景图片附件
                ProductAttachmentPO productSceneAttachment = productAttachmentMapper.selectOne(new LambdaQueryWrapper<ProductAttachmentPO>()
                        .eq(ProductAttachmentPO::getId, productScenePO.getSceneIconId()));
                if (ObjectUtils.isNotEmpty(productSceneAttachment)) {
                    SceneIconAttach sceneIconAttach = new SceneIconAttach();
                    sceneIconAttach.setId(productSceneAttachment.getId());
                    sceneIconAttach.setFileName(productSceneAttachment.getFileName());
                    sceneIconAttach.setRemark(productSceneAttachment.getRemark());
                    sceneIconAttach.setProductId(productSceneAttachment.getProductId());
                    sceneIconAttach.setStoragePath(productSceneAttachment.getStoragePath());
                    productSceneVO.setSceneIconAttach(sceneIconAttach);
                }
                sceneList.add(productSceneVO);
            }
            productPreviewVO.setSceneList(sceneList);
        }
    }

    /**
     * 设置密码产品基础信息VO
     */
    private void setProductBaseVO(ProductPreviewVO productPreviewVO, ServiceProductPO serviceProductPO) {
        ServiceProductVO serviceProductVO = new ServiceProductVO();
        serviceProductVO.setId(serviceProductPO.getId());
        serviceProductVO.setProductType(serviceProductPO.getProductType());
        serviceProductVO.setProductName(serviceProductPO.getProductName());
        serviceProductVO.setProductIconId(serviceProductPO.getProductIconId());
        // 设置服务分类、服务子类
        serviceProductVO.setServiceTypeName(serviceProductPO.getServiceTypeName());
        serviceProductVO.setServiceChildTypeName(serviceProductPO.getServiceChildTypeName());
        //设置密码销量
        Integer salesVolume = tenantToProductService.getProductHasNum(serviceProductPO.getId(), -1).getResult();
        if (ObjectUtils.isNotEmpty(salesVolume)) {
            serviceProductVO.setSalesVolume(salesVolume);
        } else {
            serviceProductVO.setSalesVolume(0);
        }
        serviceProductVO.setSalesVolume(serviceProductPO.getSalesVolume());
        serviceProductVO.setProductUnit(serviceProductPO.getProductUnit());
        Long annexId = serviceProductPO.getAnnexId();
        if (ObjectUtils.isNotEmpty(annexId)) {
            serviceProductVO.setAnnexId(annexId);
        }
        Long pdfAnnexId = serviceProductPO.getPdfAnnexId();
        if (ObjectUtils.isNotEmpty(pdfAnnexId)) {
            serviceProductVO.setPdfAnnexId(pdfAnnexId);
        }
        serviceProductVO.setProductState(serviceProductPO.getProductState());
        serviceProductVO.setRemark(serviceProductPO.getRemark());
        //获取附件
        ProductAttachmentPO productAttachmentPO = productAttachmentMapper.selectOne(new LambdaQueryWrapper<ProductAttachmentPO>()
                .eq(ProductAttachmentPO::getId, serviceProductPO.getAnnexId()));
        //封装附件
        if (ObjectUtils.isNotEmpty(productAttachmentPO)) {
            AnnexAttach annexAttach = new AnnexAttach();
            annexAttach.setId(productAttachmentPO.getId());
            annexAttach.setProductId(productAttachmentPO.getProductId());
            annexAttach.setStoragePath(productAttachmentPO.getStoragePath());
            annexAttach.setFileName(productAttachmentPO.getFileName());
            annexAttach.setRemark(productAttachmentPO.getRemark());
            serviceProductVO.setAnnexAttach(annexAttach);
        }
        //获取图标附件
        ProductAttachmentPO productAttachmentPOOfIcon = productAttachmentMapper.selectOne(new LambdaQueryWrapper<ProductAttachmentPO>()
                .eq(ProductAttachmentPO::getId, serviceProductPO.getProductIconId()));
        //封装图标产品附件
        if (ObjectUtils.isNotEmpty(productAttachmentPOOfIcon)) {
            ProductionIconAttach productionIconAttach = new ProductionIconAttach();
            productionIconAttach.setId(productAttachmentPOOfIcon.getId());
            productionIconAttach.setProductId(productAttachmentPOOfIcon.getProductId());
            productionIconAttach.setStoragePath(productAttachmentPOOfIcon.getStoragePath());
            productionIconAttach.setFileName(productAttachmentPOOfIcon.getFileName());
            productionIconAttach.setRemark(productAttachmentPOOfIcon.getRemark());
            serviceProductVO.setProductionIconAttach(productionIconAttach);
        }
        //获取PDF附件
        ProductAttachmentPO productAttachmentPDF = productAttachmentMapper.selectOne(new LambdaQueryWrapper<ProductAttachmentPO>()
                .eq(ProductAttachmentPO::getId, serviceProductPO.getPdfAnnexId()));
        //封装PDF附件
        if (ObjectUtils.isNotEmpty(productAttachmentPDF)) {
            PdfAnnexAttach pdfAnnexAttach = new PdfAnnexAttach();
            pdfAnnexAttach.setId(productAttachmentPDF.getId());
            pdfAnnexAttach.setProductId(productAttachmentPDF.getProductId());
            pdfAnnexAttach.setStoragePath(productAttachmentPDF.getStoragePath());
            pdfAnnexAttach.setFileName(productAttachmentPDF.getFileName());
            pdfAnnexAttach.setRemark(productAttachmentPDF.getRemark());
            serviceProductVO.setPdfAnnexAttach(pdfAnnexAttach);
        }
        productPreviewVO.setServiceProduct(serviceProductVO);
    }

    /**
     * 查询密码产品的列顺序
     */
    @Override
    public SecRestResponse<String[]> findProductColumnSequence() {
        String[] sequence = new String[]{"serviceTypeName","serviceChildTypeName","volumeInUse","productUnit","productState","remark","createTime","updateTime"};
        return ResultUtil.ok(sequence);
    }
}
