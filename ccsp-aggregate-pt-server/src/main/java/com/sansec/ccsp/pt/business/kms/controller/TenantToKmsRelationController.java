package com.sansec.ccsp.pt.business.kms.controller;

import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.param.response.SecPageVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sansec.ccsp.pt.business.kms.request.TenantToKmsRelationDTO;
import com.sansec.ccsp.pt.business.kms.request.TenantToKmsRelationPageDTO;
import com.sansec.ccsp.pt.business.kms.response.TenantToKmsRelationVO;
import com.sansec.ccsp.pt.business.kms.service.TenantToKmsRelationService;
import javax.annotation.Resource;

 /**
 * @Description: 租户与kms租户关联关系表;(TENANT_TO_KMS_RELATION)表控制层
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@RestController
@RequestMapping("/tenantToKmsRelation/v1")
@Validated
public class TenantToKmsRelationController{
    @Resource
    private TenantToKmsRelationService tenantToKmsRelationService;
    
    /** 
     * 分页查询
     *
     * @param tenantToKmsRelationPageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    public SecRestResponse<SecPageVO<TenantToKmsRelationVO>> find(@RequestBody TenantToKmsRelationPageDTO tenantToKmsRelationPageDTO){
        return tenantToKmsRelationService.find(tenantToKmsRelationPageDTO);
    }
    
    /** 
     * 新增数据
     *
     * @param tenantToKmsRelationDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    public SecRestResponse<Object> add(@RequestBody TenantToKmsRelationDTO tenantToKmsRelationDTO){
        return tenantToKmsRelationService.add(tenantToKmsRelationDTO);
    }
    
    /** 
     * 更新数据
     *
     * @param tenantToKmsRelationDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/edit")
    public SecRestResponse<Object> edit(@RequestBody TenantToKmsRelationDTO tenantToKmsRelationDTO){
        return tenantToKmsRelationService.edit(tenantToKmsRelationDTO);
    }
    
    /** 
     * 通过主键删除数据
     *
     * @param tenantToKmsRelationDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/deleteById")
    public SecRestResponse<Object> deleteById(@RequestBody TenantToKmsRelationDTO tenantToKmsRelationDTO){
        return tenantToKmsRelationService.deleteById(tenantToKmsRelationDTO);
    }
}