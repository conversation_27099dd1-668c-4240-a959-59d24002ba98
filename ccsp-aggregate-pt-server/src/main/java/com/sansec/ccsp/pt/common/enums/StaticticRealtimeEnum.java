package com.sansec.ccsp.pt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 实时统计枚举定义
 * @Author: zhangweicheng
 * @Date: 2024/3/12
 */

@Getter
@AllArgsConstructor
public enum StaticticRealtimeEnum {


    DAY("DAY", "DAY", "天统计"),
    HOUR("HOUR", "HOUR", "小时统计"),
    MONTH("MONTH", "MONTH", "月统计"),
    MINUTE("MINUTE", "MINUTE", "实时分钟统计");

    private String code;

    private String name;

    private String remark;


    public static StaticticRealtimeEnum byCode(String code) {
        return Arrays.stream(values()).filter(item -> item.getCode().equals(code)).findAny().orElse(null);
    }

}