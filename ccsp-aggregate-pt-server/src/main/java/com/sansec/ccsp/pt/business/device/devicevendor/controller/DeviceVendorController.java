package com.sansec.ccsp.pt.business.device.devicevendor.controller;

import com.sansec.ccsp.device.devicevendor.request.DeviceVendorDTO;
import com.sansec.ccsp.device.devicevendor.request.DeviceVendorEditDTO;
import com.sansec.ccsp.device.devicevendor.request.DeviceVendorIdDTO;
import com.sansec.ccsp.device.devicevendor.request.DeviceVendorPageDTO;
import com.sansec.ccsp.device.devicevendor.response.DeviceVendorInfoVO;
import com.sansec.ccsp.device.devicevendor.response.DeviceVendorVO;
import com.sansec.ccsp.pt.business.statistic.log.aspect.OperateManageLog;
import com.sansec.ccsp.pt.business.device.devicevendor.service.DeviceVendorService;
import com.sansec.ccsp.security.annotation.HasAnyPermissions;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * 设备管理-厂商管理
 */

@RestController
@RequestMapping("/deviceVendor/v1")
public class DeviceVendorController {
    @Resource
    DeviceVendorService deviceVendorService;

    /**
     * 分页查询
     *
     * @param deviceVendorPageDTO 筛选条件
     * @return 查询结果
     */
    @HasAnyPermissions("device:vendor:list")
    @PostMapping("/find")
    public SecRestResponse<SecPageVO<DeviceVendorVO>> find(@Validated @RequestBody DeviceVendorPageDTO deviceVendorPageDTO){
        return deviceVendorService.find(deviceVendorPageDTO);
    }

    /**
     * 新增数据
     *
     * @param deviceVendorDTO 实例对象
     * @return 实例对象
     */
    @OperateManageLog(module = "厂商管理",desc = "新增厂商信息")
    @HasAnyPermissions("device:vendor:add")
    @PostMapping("/add")
    public SecRestResponse<Object> add(@Validated @RequestBody DeviceVendorDTO deviceVendorDTO){
        return deviceVendorService.add(deviceVendorDTO);
    }

    /**
     * 通过主键删除数据
     *
     * @param deviceVendorDTO 实例对象
     * @return 实例对象
     */
    @OperateManageLog(module = "厂商管理",desc = "删除厂商信息")
    @HasAnyPermissions("device:vendor:delete")
    @PostMapping("/delete")
    public SecRestResponse<Object> deleteById(@Validated @RequestBody DeviceVendorIdDTO deviceVendorDTO){
        return deviceVendorService.deleteById(deviceVendorDTO);
    }

    /**
     * 通过主键删除数据
     *
     * @param deviceVendorDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/getOne")
    public SecRestResponse<DeviceVendorVO> getOne(@Validated @RequestBody DeviceVendorIdDTO deviceVendorDTO){
        return deviceVendorService.getOne(deviceVendorDTO);
    }

    /**
     * 更新数据
     *
     * @param deviceVendorDTO
     * @return 实例对象
     */
    @OperateManageLog(module = "厂商管理",desc = "编辑厂商信息")
    @HasAnyPermissions("device:vendor:edit")
    @PostMapping("/edit")
    public SecRestResponse<Object> edit(@Validated @RequestBody DeviceVendorEditDTO deviceVendorDTO){
        return deviceVendorService.edit(deviceVendorDTO);
    }

    /**
     * 获取厂商下拉列表
     * @return
     */
    @PostMapping("/findDeviceVendorList")
    public SecRestResponse<List<DeviceVendorInfoVO>> findDeviceVendorList(){
        return deviceVendorService.findDeviceVendorList();
    }

}
