package com.sansec.ccsp.pt.business.servicemgt.gateway.controller;

import com.sansec.ccsp.pt.business.statistic.log.aspect.OperateManageLog;
import com.sansec.ccsp.pt.business.servicemgt.gateway.entity.ServiceGatewayByRegionIdDTO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.request.*;
import com.sansec.ccsp.pt.business.servicemgt.gateway.response.ServiceGatewayVO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.service.ServiceGatewayService;
import com.sansec.ccsp.pt.common.bean.EnumVO;
import com.sansec.ccsp.security.annotation.HasAnyPermissions;
import com.sansec.ccsp.security.annotation.IgnoreToken;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> xiaojiawei
 * @Description: API网关表;(SERVICE_GATEWAY)表控制层
 * @Date: 2023-2-18
 */
@RestController
@RequestMapping("/gateway/v1")
@Validated
public class ServiceGatewayController {
    @Resource
    private ServiceGatewayService serviceGatewayService;

    /**
     * 分页查询
     *
     * @param serviceGatewayPageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    @HasAnyPermissions("servicemgt:gateway:list")
    public SecRestResponse<SecPageVO<ServiceGatewayVO>> find(@Validated @RequestBody ServiceGatewayPageDTO serviceGatewayPageDTO) {
        return serviceGatewayService.find(serviceGatewayPageDTO);
    }

    /**
     * 新增数据
     *
     * @param serviceGatewayDTO 实例对象
     * @return 实例对象
     */
    @OperateManageLog(module = "网关管理",desc = "新增网关")
    @PostMapping("/add")
    @HasAnyPermissions("servicemgt:gateway:add")
    public SecRestResponse<Object> add(@Validated @RequestBody ServiceGatewayDTO serviceGatewayDTO) {
        return serviceGatewayService.add(serviceGatewayDTO);
    }

    /**
     * 更新数据
     *
     * @param serviceGatewayDTO 实例对象
     * @return 实例对象
     */
    @OperateManageLog(module = "网关管理",desc = "编辑网关")
    @PostMapping("/edit")
    @HasAnyPermissions("servicemgt:gateway:edit")
    public SecRestResponse<Object> edit(@Validated @RequestBody ServiceGatewayEditDTO serviceGatewayDTO) {
        return serviceGatewayService.edit(serviceGatewayDTO);
    }

    /**
     * 通过主键删除数据
     *
     * @param serviceGatewayDeleteDTO 实例对象
     * @return 实例对象
     */
    @OperateManageLog(module = "网关管理",desc = "删除网关")
    @PostMapping("/deleteById")
    @HasAnyPermissions("servicemgt:gateway:delete")
    public SecRestResponse<Object> deleteById(@Validated @RequestBody ServiceGatewayDeleteDTO serviceGatewayDeleteDTO) {
        return serviceGatewayService.deleteById(serviceGatewayDeleteDTO);
    }

    /**
     * 获取网关的枚举接口
     *
     * @return
     */
    @PostMapping("/getGatewayEnum")
    @HasAnyPermissions("servicemgt:gateway:gatewayEnum")
    public SecRestResponse<List<EnumVO>> getGatewayEnum() {
        return serviceGatewayService.getGatewayEnum();
    }

    @PostMapping("/getByBusiUrlAndTenant")
    @HasAnyPermissions("servicemgt:gateway:queryBusiUrl")
    public SecRestResponse<List<ServiceGatewayVO>> getByBusiUrlAndTenant(@Validated @RequestBody ServiceGatewayBusiTypeDTO busiTypeDTO) {
        return serviceGatewayService.getByBusiUrlAndTenant(busiTypeDTO);
    }

    /**
     * 初始化时自动添加网关
     *
     * @return 实例对象
     */
    @PostMapping("/initAutoAdd")
    @IgnoreToken
    public SecRestResponse<Object> initAutoAdd() {
        return serviceGatewayService.initAutoAdd();
    }


    /**
     * 通过区域ID查询网关列表
     *
     * @return 实例对象
     */
    @PostMapping("/findServiceGatewayListByRegionId")
    @HasAnyPermissions("servicemgt:gateway:list")
    public SecRestResponse<List<ServiceGatewayVO>> findServiceGatewayListByRegionId(@Validated @RequestBody ServiceGatewayByRegionIdDTO serviceGatewayByRegionIdDTO) {
        return serviceGatewayService.findServiceGatewayListByRegionId(serviceGatewayByRegionIdDTO);
    }

}