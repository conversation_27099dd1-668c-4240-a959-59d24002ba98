package com.sansec.ccsp.pt.business.sim.request;

import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Description: 流水列表入参
 * @CreateTime: 2023-09-13
 * @Author: wang<PERSON><PERSON>e
 */
@Data
public class SimFlowDataPageDTO extends SecPageDTO {
    /**
     * 主键uuid
     */
    private String id;
    /**
     * 日志类型；1sim盾  2simkey
     */
    @NotNull(message = "日志类型不可为空")
    @Min(value = 1, message = "日志类型不存在")
    @Max(value = 2, message = "日志类型不存在")
    private Integer flowType;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 会话公钥id
     */
    @NotNull(message = "sessionKeyId不可为空")
    private String sessionKeyId;

}
