package com.sansec.ccsp.extra.business.license.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.extra.business.license.entity.LicenseRecordPO;
import com.sansec.ccsp.extra.business.license.reponse.LicenseRecordVO;
import com.sansec.ccsp.extra.business.license.request.LicenseRecordAddDTO;
import com.sansec.ccsp.extra.business.license.request.LicenseRecordDTO;
import com.sansec.ccsp.extra.business.license.request.LicenseRecordEditDTO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR> xiaojiawei
 * @description : 许可授权记录表;(LICENSE_RECORD)实体类转换接口
 * @date : 2024-4-15
 */
@Mapper(componentModel = "spring")
public interface LicenseRecordConvert {
    /**
     * dtoToPo
     *
     * @param licenseRecordDTO
     * @return
     */
    @Mappings({})
    LicenseRecordPO dtoToPo(LicenseRecordDTO licenseRecordDTO);

    @Mappings({})
    LicenseRecordPO dtoToPo(LicenseRecordAddDTO licenseRecordAddDTO);
    @Mappings({})
    LicenseRecordPO dtoToPo(LicenseRecordEditDTO licenseRecordEditDTO);
    /**
     * poToDto
     *
     * @param licenseRecordPO
     * @return
     */
    LicenseRecordDTO poToDto(LicenseRecordPO licenseRecordPO);

    /**
     * poToDto-list
     *
     * @param list
     * @return
     */
    List<LicenseRecordDTO> poToDto(List<LicenseRecordPO> list);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<LicenseRecordVO> pagePOToSecPageVOPage(IPage<LicenseRecordPO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<LicenseRecordVO> convert(List<LicenseRecordPO> list);

    @Mappings({})
    LicenseRecordVO convertVo(LicenseRecordPO request);
}