package com.sansec.ccsp.pt.business.common.pt.controller;


import com.sansec.ccsp.common.user.enums.UserRoleEnum;
import com.sansec.ccsp.common.user.response.UserInfoVO;
import com.sansec.ccsp.pt.business.common.organization.service.OrganizationInfoService;
import com.sansec.ccsp.pt.business.common.pt.response.ChartVO;
import com.sansec.ccsp.pt.business.common.pt.response.ParamOfSystemOperationsVO;
import com.sansec.ccsp.pt.business.common.pt.response.ParamOfTenantOperationsVO;
import com.sansec.ccsp.pt.business.common.pt.service.CommonService;
import com.sansec.ccsp.pt.business.common.user.service.UserInfoUtilService;
import com.sansec.ccsp.pt.business.license.response.ServerExpiryTimeVO;
import com.sansec.ccsp.pt.business.quota.request.TopNumDTO;
import com.sansec.ccsp.pt.business.statistic.statistic.request.CommonTenantCodeDTO;
import com.sansec.ccsp.pt.business.tenant.request.TenantIdDTO;
import com.sansec.ccsp.pt.business.tenant.response.TenantVO;
import com.sansec.ccsp.pt.business.tenant.service.TenantService;
import com.sansec.ccsp.pt.common.constant.CommonConstant;
import com.sansec.ccsp.security.util.LoginUserUtil;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangjunjie
 * @Description:
 * @Date: 2023-2-19
 */
@RestController
@RequestMapping("/common/v1")
@Validated
public class CommonController {

    @Resource
    private CommonService commonService;

    @Resource
    private TenantService tenantService;

    @Resource
    private UserInfoUtilService userInfoUtilService;

    @Resource
    private OrganizationInfoService organizationInfoService;

    /**
     * @Description: 获取系统操作员首页参数
     * @return
     */
    @PostMapping("/getNumParamOfSystemOperations")
    public SecRestResponse<ParamOfSystemOperationsVO> getNumParamOfSystemOperations() {
        return commonService.getNumParamOfSystemOperations();
    }

    /**
     * @Description: 获取租户操作员首页参数
     * @return
     */
    @PostMapping("/getNumParamOfTenantOperations")
    public SecRestResponse<ParamOfTenantOperationsVO> getNumParamOfTenantOperations() {
        TenantIdDTO tenantIdDTO = new TenantIdDTO();
        tenantIdDTO.setTenantId(LoginUserUtil.getTenantId());
        return commonService.getNumParamOfTenantOperations(tenantIdDTO);
    }

    /**
     * @Description: 首页设备饼图
     */
    @PostMapping("/getDevChart")
    public SecRestResponse<List<ChartVO>> getDevChart( @RequestBody CommonTenantCodeDTO commonTenantCodeDTO) {
        Long tenantId = LoginUserUtil.getTenantId();
        if (CommonConstant.TOP_TENANT_ID.equals(tenantId)) {
            return commonService.getDevChartOfSysOpers(commonTenantCodeDTO);
        } else {
            return commonService.getDevChartOfTenOpers(new TenantIdDTO(tenantId));
        }
    }

    /**
     * @Description: 首页服务饼图
     */
    @PostMapping("/getSerChart")
    public SecRestResponse<List<ChartVO>> getSerChart( @RequestBody CommonTenantCodeDTO commonTenantCodeDTO) {
        Long tenantId = LoginUserUtil.getTenantId();
        String tenantCode = commonTenantCodeDTO.getTenantCode();
        // 平台三元
        if (LoginUserUtil.isPtUser()) {
            return commonService.getSerChartOfSysOpers(commonTenantCodeDTO);
        } else if (LoginUserUtil.getRoleId().equals(UserRoleEnum.ORG_AUDIT.getRoleId())) {
            // 单位审计员
            //获取当前单位下所有租户的数据
            UserInfoVO userInfoVO = userInfoUtilService.selectByUserId(LoginUserUtil.getUserId());
            List<TenantVO> tenantVOS = organizationInfoService.searchTenantListById(userInfoVO.getOrganizationId());
            List<Long> tenantIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(tenantVOS)) {
                if(StringUtils.isNotBlank(tenantCode)){
                    tenantIds = tenantVOS.stream().filter(item -> item.getTenantCode().equals(tenantCode)).map(TenantVO::getTenantId).collect(Collectors.toList());
                } else {
                    tenantIds = tenantVOS.stream().map(TenantVO::getTenantId).collect(Collectors.toList());
                }
            }
            return commonService.getSerChartOfOrganOpers(tenantIds);
        } else {
            //普通租户
            return commonService.getSerChartOfTenOpers(new TenantIdDTO(tenantId));
        }
    }

//
//
//    /**
//     * @Description: KMS饼图
//     * @return
//     */
//    @PostMapping("/getCertChartOfTenOpers")
//    public SecRestResponse<List<ChartVO>> getCertChartOfTenOpers() {
//        return commonService.getCertChartOfTenOpers(null);
//    }
//    /**
//     * @Description: KMS统计信息
//     * @return
//     */
//    @PostMapping("/getKMSStatistics")
//    public SecRestResponse<KMSStatisticsVO> getKMSStatistics() {
//        return commonService.getKMSStatistics(null);
//    }
//
//    /**
//     * @Description: 租户服务调用次数统计
//     * @return
//     */
//    @PostMapping("/getTenantServiceCall")
//    public SecRestResponse<List<TenantServiceCallVO>> getTenantServiceCall() {
//        return commonService.getTenantServiceCall();
//    }
//
//    /**
//     * @Description: 近30天密码服务调用次数
//     * @return
//     */
//    @PostMapping("/getKeyServiceCall")
//    public SecRestResponse<KeyServiceCallVO> getKeyServiceCall() {
//        return commonService.getKeyServiceCall();
//    }

    /**
     * @Description: 获取平台有效期开始时间、结束时间
     * @return
     */
    @PostMapping("/getPlatformExpiryTime")
    public SecRestResponse<Object> getPlatformExpiryTime() {
        return commonService.getPlatformExpiryTime();
    }

    /**
     * @return
     * @Description: 获取服务有效期结束天数
     */
    @PostMapping("/getServerExpiryTime")
    public SecRestResponse<List<ServerExpiryTimeVO>> getServerExpiryTime(@RequestBody @Validated TopNumDTO topNumDTO) {
        return ResultUtil.ok(commonService.getServerExpiryTime(topNumDTO));
    }

}
