package com.sansec.ccsp.pt.business.device.devicemonitor.service;

import com.sansec.ccsp.device.devicemonitor.request.*;
import com.sansec.ccsp.device.devicemonitor.response.DicSnmpAuthenticationVO;
import com.sansec.ccsp.device.devicemonitor.response.DicSnmpPrivacyVO;
import com.sansec.ccsp.device.devicemonitor.response.GetDeviceMonitorConfigVO;
import com.sansec.ccsp.device.devicemonitor.response.OidEntityVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

public interface DeviceMonitorConfigService {

    SecRestResponse<Object> setDeviceMonitorConfig(SetDeviceMonitorConfigDTO setDeviceMonitorConfigDTO);

    SecRestResponse<GetDeviceMonitorConfigVO> getDeviceMonitorConfig(GetDeviceMonitorConfigDTO getDeviceMonitorConfigDTO);

    GetDeviceMonitorConfigVO getByDeviceType(Long deviceTypeId);

    SecRestResponse<List<DicSnmpAuthenticationVO>> getSnmpAuthenticationList();

    SecRestResponse<List<DicSnmpPrivacyVO>> getSnmpPrivacyList();

    /**
     * oid列表
     * @return
     */
    SecRestResponse<List<OidEntityVO>> oidList(OidListDTO oidListDTO);

    /**
     * 添加oid
     * @param addOidDTO
     * @return
     */
    SecRestResponse<Object> addOid(AddOidDTO addOidDTO);

    /**
     * 编辑OID
     * @param editOidDTO
     * @return
     */
    SecRestResponse<Object> editOid(EditOidDTO editOidDTO);

    /**
     * 删除oid
     * @param deleteOidDTO
     * @return
     */
    SecRestResponse<Object> deleteOid(DeleteOidDTO deleteOidDTO);
}
