package com.sansec.ccsp.staticapi.screen.api.fallback;

import com.sansec.ccsp.staticapi.screen.api.ScreenTemplateRelApi;
import com.sansec.ccsp.staticapi.screen.request.ScreenTemplateIndexDTO;
import com.sansec.ccsp.staticapi.screen.request.ScreenTemplateRelDTO;
import com.sansec.ccsp.staticapi.screen.request.ScreenTemplateRelPageDTO;
import com.sansec.ccsp.staticapi.screen.response.ScreenTemplateRelVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ScreenTemplateRelFallBack implements FallbackFactory<ScreenTemplateRelApi> {
    @Override
    public ScreenTemplateRelApi create(Throwable cause) {
        return new ScreenTemplateRelApi() {

            /**
             * @param screenTemplateRelDTO
             * @return SecRestResponse<Object> 返回类型
             * @Description: 服务组添加
             */
            @Override
            public SecRestResponse<Long> add(ScreenTemplateRelDTO screenTemplateRelDTO) {
                log.error("大屏指标添加:{}", cause);
                return SecRestResponse.fallback(cause);
            }

            /**
             * @param screenTemplateRelDTO
             * @return SecRestResponse<Object> 返回类型
             * @Description: 服务组编辑
             */
            @Override
            public SecRestResponse<Long> edit(ScreenTemplateRelDTO screenTemplateRelDTO) {
                log.error("大屏指标修改:{}", cause);
                return SecRestResponse.fallback(cause);
            }

            /**
             * @param screenTemplateRelDTO
             * @return SecRestResponse<Object> 返回类型
             * @Description: 服务组根据ID删除
             */
            @Override
            public SecRestResponse<Long> deleteById(ScreenTemplateRelDTO screenTemplateRelDTO) {
                log.error("大屏指标删除:{}", cause);
                return SecRestResponse.fallback(cause);
            }

            /**
             * @param screenTemplateRelPageDTO
             * @return SecPageVO<ScreenIndexVO> 返回类型
             * @Description: 服务组分页查询
             */
            @Override
            public SecRestResponse<SecPageVO<ScreenTemplateRelVO>> find(ScreenTemplateRelPageDTO screenTemplateRelPageDTO) {
                log.error("大屏指标分页查询:{}", cause);
                return SecRestResponse.fallback(cause);
            }

            /**
             * 根据主键查询实例对象
             *
             * @param id
             * @return 实例对象
             */
            @Override
            public SecRestResponse<ScreenTemplateRelVO> findById(Long id) {
                log.error("大屏指标查询对象:{}", cause);
                return SecRestResponse.fallback(cause);
            }

            /**
             * 根据模板编号和指标类型查询实例对象
             *
             * @param screenTemplateIndexDTO
             * @return 实例对象
             */
            @Override
            public SecRestResponse<ScreenTemplateRelVO> findOne(ScreenTemplateIndexDTO screenTemplateIndexDTO) {
                log.error("大屏指标查询对象:{}", cause);
                return SecRestResponse.fallback(cause);
            }
        };
    }
}
