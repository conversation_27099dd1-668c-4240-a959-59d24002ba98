0=operate success!
10001=param error！
10002=id not found！
10003=add info not found！
10004=info already exists！
10005=query info not found！
10006=update info not found！
10007=update info already exists！
10009=encryption failed!
10010=decryption failed!
20001=auth mode not found!
88888=please relogin!
80001=Permission denied!
90001=unknown exception
#初始化
01003=not fine init node info
01005=init node failed!
01006=please first init flow settings
01007=get subjectDN error
01008=Initialized, no need to repeat operation
01009=%smust be initialized
01026=Get device session info error!
#登录
02008=password error format
02101=generate image code error
02201=vertify UKey error
02202=invalid cert
02301=user locked Please try again later
02302=invalid vertify code
02303=vertify code error
02304=the user name or password is incorrect
02305=token error format
02306=encode error
02307=You cannot use recently used passwords！
02308=old password is incorrect！
02310=user not login!
02401=user not found!
02402=user name or password is incorrect. Your account will be locked after%s attempts！
02403=The account is locked. Please wait %s second and try to login again!
02410=user data inconsistency, not allow to login
02411=incorrect password
02412=Error initializing %s user number, correct number is %s
02413=The number of users in this role has reached the maximum
02414=UKey is not a registered user, please register as a user first
#首页
03000=index page not found！
03005=menu query field parse error！
03006=menu type not found :0/1!
#角色列表
04000=name already exists！
04001=menu ids not found！
04006=name not found！
04007=role and menu id not found！
04010=super manage not found！
04013=super、audit 、operate role not allowed to be deleted！
04014=preset roles in advance cannot be modified！
04015=the role of the bound user cannot be deleted！
04016=built in role permissions cannot be modified！
#用户管理
05002=role ids not found！
05003=psd verify failed！
05004=user type not found(1:username 2:UKey)！
05005=cert not found！
05006=user status pass failed！
05007=user already exists！
05008=name and  psd not found！
05009=dn and  cert not found！
05011=super user not deleted！
05012=UKey already bound error！
05014=user passwd not found ！
05015=init user type not found，choose item：super、audit、operate!
05016=deleting the currently logged in user is not allowed!
05017=the user cannot be deleted!
05100=hmac generate error
#证书和CA管理
07001=Certificate name cannot be empty
07002=Certificate subject name cannot be empty
07003=Certificate name already exists
07004=Certificate country cannot be empty
07005=Certificate algorithm cannot be empty
07006=Certificate algorithm only supports RSA and SM2
07007=Certificate algorithm length cannot be empty
07008=Certificate validity period cannot be empty
07009=Certificate validity period error(1-7300)
07010=Certificate name can only contain letters and numbers and _-, length can not exceed 100bit
07011=Certificate Common name can only contain letters and numbers and _-, length can not exceed 100bit
07012=Certificate Organization name can only contain letters and numbers and _-, length can not exceed 100bit
07013=Certificate unit name can only contain letters and numbers and _-, length can not exceed 100bit
07014=Certificate locality  can only contain letters and numbers and _-, length can not exceed 100bit
07015=Certificate province name can only contain letters and numbers and _-, length can not exceed 100bit
07016=Certificate country name can only contain letters and numbers and _-, length can not exceed 64bit
07017=Certificate email format is incorrect,please change the email
07018=Generate key pair error
07019=Encrypt private key error
07020=Get certificate subjectDN error
07021=Not support key algorithm
07022=Create certificate error
07023=Encode certificate error
07024=Certificate cannot be blank
07025=Invalid certificate info,please check the certificate file
07026=Vertify certificate chain error
07027=CA certificate not specified
07028=Incorrect use of certificate
07029=Certificate duraiton cannot be blank
07030=Certificate request cannot be blank
07031=CA is not exist
07032=CA status not actived
07033=Certificate validity period is too long
07034=Invalid certificate request
07035=Certificate request signature invalid
07036=CA certificate invalid
07037=Get public key from certificate request error
07038=Not support certificate request key alogrithm
07039=The certificate request algorithm does not match the CA certificate algorithm
07040=Decrypt CA private key error
07041=Generate private key from spec error
07042=Generate CA public key from spec error
07043=Generate certificate from spec error
07044=Generate key pair error
07045=construct X509 error
07046=Create certificate request error
07047=Query CA expiration error
07048=Create KeyStore error
07049=Decrypt certificate private key error
07050=Failed to write to file
07051=CA certificate does not exist
07052=The certificate required to install does not exist
07053=Certificate information cannot be empty
07054=Invalid certificate files
07055=Certificate is already expired
07056=Certificate and CA do not match
07057=Certificate is not match with the record
07058=Certificate protection password cannot be empty
07059=Certificate file cannot be null
07060=Issue cannot be empty
07061=Certificate file parsing error
07062=Get CA certificate error
07063=Get key entry from pfx fail
07064=Certificate is not match with the record
07065=Certificate is not match with the CA
07066=Certificate Protection Key cannot be empty
07067=CRL information cannot be empty
07068=Failed to parse CRL
07069=certificates have been revoked
07070=CRL imported successfully, no certificate revoked
07071=File name can not null
07072=The certificate has already been used
#证书和CA有关的异常
07100=Check parentcert.subject != childcert.issuer
07101=Check parent cert validity error
07102=Check children cert validity error
07103=Check children certficate signature error
07104=Check selfsign cert issuer != subject
07105=Check self-signed cert validity error
07106=Check for self-signed certificate signature error
07107=Invalid certificate request
07108=keypair isn't existing
07109=Generate publicKey error
07110=Generate privateKey error
07111=Generate key error
07112=UnKnow key algorithm
07113=The algorithm can only be RSA or SM2
07114=Key length error
07115=Key index error [1-100]
07116=Encrypt exception
07117=Decrypt exception
07118=Verify exception
07119=Sign exception
07120=Generate keystore exception
07121=Generate PKCS10CertificationRequest error
07122=Generate serial number error
07123=Generate certificate extension error
07124=Generate V3 X509 certificate error
07125=Generate PKCS12 certificate error
07126=Generate user subjectKeyIdentifier error
07127=Generate user authorityKeyIdentifier error
07128=Initliaze CertificateFactory error
07129=Exception caught when attempting to read CertPath by PKCS7/PEM/PkiPath encoding
07130=Certificate file format error
07131=The certificate file is too large（30Kb）
07132=Add certificate base extended exception
07133=Add certificate purpose extended exception
07134=Certificates that only support SM2 and RSA algorithms
#License
09001=license uid not found！
09002=read license file error！
09003=license upload repeat error！
09004=license info change error！
09005=license has expired, please replace it in time！
09006=license file suffix must be .license！
09007=abnormal reading license authorization code！
09008=license has been used, please replace it in time！
#网络配置
11000=ip address format error
11001=dns file create fail error
11002=dns edit fail error
11003=dns read fail error
11004=dns address already exist error
11005=dns address not exist error
11006=port range error
11007=network card not exist error
11008=network card configuration file read error
11009=The ipv4 address of the existing network card %s is in the same network segment
11010=The ipv6 address of the existing network card %s is in the same network segment
11011=subnet cards too few
11012=network card configuration file write error
11013=network card enable fail error
11014=network card disable fail error
11015=default gateway read fail error
11016=default gateway write fail error
11017=gateway set fail error
11018=route file create fail error
11019=route add fail error
11020=route delete fail error
11021=net card already bond error
11022=number of dns exceeds limit 3 error
11023=slave card should not edit error
11024=route for the net (host) already exists
11025=There are associated routes, please delete them and try again
11026=Missing subnet prefix
11027=Configure the gateway before binding
11028=The NIC is incompletely configured
#系统配置
12501=do not find config!
12502=do not meet the format requirements!
12503=init card config not find!
12504=Configuration cannot be modified!
12505=image not exist!
12506=image download error!
#日志管理
13001=Business service log configuration interface request failed
13002=No related services found
13003=Log file does not exist
13004=Failed to read log file
13005=Database query parameter assembly failed
13006=Audit log not found
13007=File Hash calculation failed
13008=Service already exists
13009=Service not found
13010=Failed to execute command line
13011=Syslog-ng service operation failed
13012=Syslog-ng service configuration file write failed
13013=Syslog-ng service configuration content assembly failed
13015=CA not exist
13014=Spring bean get failed
13017=Integrity check fails and auditing is not supported
#备份与恢复
14000=query backup task failed
14001=submit backup request by key failed
14002=submit backup request by pwd failed
14003=check file path mustn't be empty
14004=backup file path mustn't be empty
14005=backup status mustn't be empty
14006=backup id mustn't be empty
14007=update backup history error
14008=query backup history failed
14009=file not found or has been deleted
14010=encode file name failed
14012=read or write file failed
14013=UKey quantity not matched
14015=recover file path is empty
14016=submit recover request by key(path) error
14017=submit recover request by key(file) error
14018=recover file path is empty
14019=update recover history failed
14020=query recover history failed
14021=upload recover file failed
14022=submit recover request by pwd(path) error
14023=submit recover request by pwd(file) error
14025=delete task error
14026=update task error
14027=duplicate keys exist
14028=invoke rest api error
14101=current backup type already exist
14102=backup record not exist
14103=status not known
14104=recover id not exist
14105=recover status not exist
14106=Do not delete during backup
14107=Do not delete backup files while they are in use
#监控模块
#系统管理
16000=Failed to execute shell command!
16001=Update system time type cannot be empty: 0 manual update, 1 ntp update!
16002=NTP source update time parameter cannot be empty！
16003=Manual update time cannot be empty!
16004=The format of manually specifying time is incorrect:yyyy-MM-dd HH:mm:ss!
16005=NTP data source is not available!
16006=NTP server time out!
16007=NTP server amount cannot exceed 3
16008=NTP server duplicate
16010=Failed to download the picture!
16011=Failed to raid check!
#升级回滚
17001=Business service connection failed
17002=Business service data configuration error
17003=Stop service exception
17004=Start service exception
17101=Upgrade package integrity verification failed
17102=Upgrade package description file parsing exception
17103=Upgrade package service version is too low
17104=Upgrade record does not exist
17105=Backup of installation files failed
17106=Abnormal installation of upgrade package
17107=Clear upload file exception
17108=There is no backup record and cannot be rolled back
17109=Rollback version faile
17110=execShell:%s 
17111=loading file exception
17112=upload file failed
17113=version number cannot be empty
17114=version number rules are not unified
17115=unsupported algorithm
17116=secret key length mismatch
17117=failed to generate secret key
17118=HMAC error encryption error
17119=signature failed
17120=signature verification failed
17121=upgrade package name error
17122=The upgrade record cannot be rolled back
17123=upgrade failed
#HSM
21002=not support
21003=communication error
21004=hsm error
21005=open device error
21006=open session error
21007=Permissions not met
21008=Key not exist
21009=alg not support
21010=step error
21011=alg mode not support
21012=public key error
21013=private key error
21014=sign error
21015=verify error
21016=symm error
21017=connect error
21018=set socket error
21019=send LOGINRequest error
21020=recv LOGINRequest error
21021=socket recv 0
21022=timeout
21023=no available hsm
21024=no available csm
21025=config error
21026=Get device session info error!
21027=buffer too small
21028=pad error
21029=length error
21030=step error
21031=Permissions not met
21032=Permissions are not met
21033=Permissions are not met
21034=login error
21035=user id error
21036=parameter error
21037=key type error
21038=IC card Locked，Please contact manufacturer
21039=IC type error
21040=IC card or Reader error
21041=IC card or Reader error
21042=IC card PIN error，Also try again 0 times
21043=IC card PIN error，Also try again 1 times
21044=IC card PIN error，Also try again 2 times
21045=IC card PIN error，Also try again 3 times
21046=IC card PIN error，Also try again 4 times
21047=IC card PIN error，Also try again 5 times
21048=IC card PIN error，Also try again 6 times
21049=IC card PIN error，Also try again 7 times
21050=IC card PIN error，Also try again 8 times
21051=IC card PIN error，Also try again 9 times
21052=IC card PIN error，Also try again 10 times
21053=IC card PIN error，Also try again 11 times
21054=IC card PIN error，Also try again 12 times
21055=IC card PIN error，Also try again 13 times
21056=IC card PIN error，Also try again 14 times
0A050372=service group id can not be empty
0A050846=add region %s 失败