package com.sansec.ccsp.pt.demo.api;

import com.sansec.ccsp.pt.demo.api.fallback.ConfigServiceFallBack;
import com.sansec.ccsp.pt.demo.request.ConfigSaveReqDTO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @Description: ConfigServiceApi层
 * <AUTHOR>
 * @date: 2022/2/17 18:01
 */
@FeignClient(name = "${spring.protocol.prefix}${spring.application.pt.name}" , fallbackFactory = ConfigServiceFallBack.class)
public interface PtComConfigServiceApi {
    @PostMapping(value = "/ccsp/ptconfigapi/v1/saveapi")
    SecRestResponse<Object> save(ConfigSaveReqDTO reqDTO);
}
