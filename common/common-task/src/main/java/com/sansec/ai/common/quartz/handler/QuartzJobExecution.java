package com.sansec.ai.common.quartz.handler;

import com.sansec.ai.common.quartz.entity.po.SysJobPO;
import com.sansec.ai.common.utils.MethodInvokeUtil;
import org.quartz.JobExecutionContext;

import java.lang.reflect.InvocationTargetException;

/**
 * 定时任务处理（允许并发执行）
 *
 * <AUTHOR>
 * @since 2025-2-24
 */
public class QuartzJobExecution extends AbstractQuartzJob {
	@Override
	protected void doExecute(JobExecutionContext context, SysJobPO sysJob) throws ClassNotFoundException, InvocationTargetException, NoSuchMethodException, IllegalAccessException, InstantiationException {
		MethodInvokeUtil.invokeMethod(sysJob.getJsonParam());
	}
}
