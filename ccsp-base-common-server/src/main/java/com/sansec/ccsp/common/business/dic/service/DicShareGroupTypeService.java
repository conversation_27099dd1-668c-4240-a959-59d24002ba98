package com.sansec.ccsp.common.business.dic.service;

import com.sansec.ccsp.common.dic.request.DicShareGroupTypeDTO;
import com.sansec.ccsp.common.dic.request.DicShareGroupTypePageDTO;
import com.sansec.ccsp.common.dic.response.DicShareGroupTypeVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * <AUTHOR> xia<PERSON>jiaw<PERSON>
 * @description : 支持创建共享服务类型（330）;(DIC_SHARE_GROUP_TYPE)表服务接口
 * @date : 2024-3-11
 */
public interface DicShareGroupTypeService {
    /**
     * 分页查询
     *
     * @param dicShareGroupTypePageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<DicShareGroupTypeVO>> find(DicShareGroupTypePageDTO dicShareGroupTypePageDTO);

    SecRestResponse<List<DicShareGroupTypeVO>> getList();

    /**
     * 新增数据
     *
     * @param dicShareGroupTypeDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(DicShareGroupTypeDTO dicShareGroupTypeDTO);

    /**
     * 更新数据
     *
     * @param dicShareGroupTypeDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(DicShareGroupTypeDTO dicShareGroupTypeDTO);

    /**
     * 通过主键删除数据
     *
     * @param dicShareGroupTypeDTO
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(DicShareGroupTypeDTO dicShareGroupTypeDTO);
}