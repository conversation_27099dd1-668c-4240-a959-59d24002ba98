package com.sansec.ccsp.common.user.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>jiaw<PERSON>
 * @Description: 用户信息表;
 * @Date: 2023-2-18
 */
@Data
public class EditSelfUserInfoDTO {

    /**
     * 名称
     */
    @NotBlank(message = "名称不可为空")
    @Size(max = 50, message = "名称最大支持50字符数")
    private String userName;

    /**
     * 手机号码;加密存储
     */
    private String phoneNum;

    /**
     * 邮箱;加密存储
     */
    private String emailAddress;

    /**
     * 组织机构ID
     */
    private Long organizationId;

}
