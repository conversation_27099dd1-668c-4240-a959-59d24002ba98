package com.sansec.ccsp.device.business.metrics.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 度量集类型
 *
 * <AUTHOR>
 * @date 2024/06/20
 */
@Getter
@AllArgsConstructor
@JSONType(serializeEnumAsJavaBean = true)
public enum EnumMetricsSetGroup {
    CPU("cpu","CPU", "Get","_collect_cpu","_agg_cpu", "device"),
    MEM("mem", "内存", "Get","_collect_mem","_agg_mem", "device"),
    DISK("disk", "磁盘", "Walk","_collect_disk","_agg_disk", "device");


    private String code;

    private String name;

    private String collectMethod;

    private String collectSuffix;

    private String aggSuffix;

    private String fixPrefix;

    private static Map<String, EnumMetricsSetGroup> map = new HashMap<>();

    static {
        for (EnumMetricsSetGroup value : values()) {
            map.put(value.getCode(), value);
        }
    }

    public String getFixCollectMetricsCode(){
        return fixPrefix + collectSuffix;
    }

    public String getFixAggMetricsCode(){
        return fixPrefix + aggSuffix;
    }

    public static List<EnumMetricsSetGroup> getAllModelType(){
        return new ArrayList<>(map.values());
    }

    public static String getValidatedRegex(){
        return StringUtils.join(Arrays.stream(values()).map(EnumMetricsSetGroup::getCode).collect(Collectors.toList()), "|");
    }

    public static EnumMetricsSetGroup getByCode(String code){
        return map.get(code);
    }
}
