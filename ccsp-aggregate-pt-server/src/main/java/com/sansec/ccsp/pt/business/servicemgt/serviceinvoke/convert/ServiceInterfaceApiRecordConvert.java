package com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.entity.ServiceInterfaceApiRecordPO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request.ServiceInterfaceApiRecordDTO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.response.ServiceInterfaceApiRecordVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @Description:
 * @CreateTime: 2023-04-23
 * @Author: wangjunjie
 */
@Mapper(componentModel = "spring")
public interface ServiceInterfaceApiRecordConvert {

    ServiceInterfaceApiRecordPO dtoToPo(ServiceInterfaceApiRecordDTO dto);

    ServiceInterfaceApiRecordVO poToVo(ServiceInterfaceApiRecordPO po);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<ServiceInterfaceApiRecordVO> pagePOToSecPageVOPage(IPage<ServiceInterfaceApiRecordPO> iPage);

    List<ServiceInterfaceApiRecordVO> listPoToListVo(List<ServiceInterfaceApiRecordPO> tenantListPO);
}