CREATE TABLE "ccsp_statistic"."alarm_history" (
                                                  "id" int8 NOT NULL,
                                                  "tenant_id" int8,
                                                  "tenant_name" varchar(255) COLLATE "pg_catalog"."default",
                                                  "region_id" int8,
                                                  "region_name" varchar(255) COLLATE "pg_catalog"."default",
                                                  "alarm_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "source_ip" varchar(20) COLLATE "pg_catalog"."default",
                                                  "source_name" varchar(255) COLLATE "pg_catalog"."default",
                                                  "content" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "times" int4,
                                                  "first_time" varchar(30) COLLATE "pg_catalog"."default",
                                                  "last_time" varchar(30) COLLATE "pg_catalog"."default",
                                                  "status" int4,
                                                  "remark" varchar(1000) COLLATE "pg_catalog"."default",
                                                  "update_by" int8,
                                                  "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."id" IS '主键';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."tenant_name" IS '租户名称';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."region_id" IS '区域ID';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."region_name" IS '区域名称';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."alarm_code" IS '告警标识';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."source_ip" IS '告警源IP';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."source_name" IS '告警源名称';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."content" IS '告警内容';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."times" IS '告警次数';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."first_time" IS '首次告警时间';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."last_time" IS '末次告警时间';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."status" IS '告警状态;1告警中2已处置3已恢复';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."update_by" IS '处置人';
COMMENT ON COLUMN "ccsp_statistic"."alarm_history"."update_time" IS '处置时间';
COMMENT ON TABLE "ccsp_statistic"."alarm_history" IS '告警历史表';


CREATE TABLE "ccsp_statistic"."alarm_info" (
                                               "id" int8 NOT NULL,
                                               "tenant_id" int8,
                                               "region_id" int8,
                                               "alarm_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                               "source_id" int8,
                                               "source_ip" varchar(20) COLLATE "pg_catalog"."default",
                                               "content" varchar(255) COLLATE "pg_catalog"."default",
                                               "times" int4,
                                               "first_time" varchar(30) COLLATE "pg_catalog"."default",
                                               "last_time" varchar(30) COLLATE "pg_catalog"."default",
                                               "status" int4
)
;
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."id" IS '主键';
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."region_id" IS '区域ID';
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."alarm_code" IS '告警标识';
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."source_id" IS '告警源ID';
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."source_ip" IS '告警源IP';
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."content" IS '告警内容';
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."times" IS '告警次数';
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."first_time" IS '首次告警时间';
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."last_time" IS '末次告警时间';
COMMENT ON COLUMN "ccsp_statistic"."alarm_info"."status" IS '告警状态;1告警中2已处置3已恢复';
COMMENT ON TABLE "ccsp_statistic"."alarm_info" IS '告警信息表';

CREATE TABLE "ccsp_statistic"."alarm_oid" (
                                              "id" int8 NOT NULL,
                                              "alarm_code" varchar(50) COLLATE "pg_catalog"."default",
                                              "oid" varchar(50) COLLATE "pg_catalog"."default",
                                              "name" varchar(50) COLLATE "pg_catalog"."default",
                                              "desc" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON TABLE "ccsp_statistic"."alarm_oid" IS '告警指标';

CREATE TABLE "ccsp_statistic"."dic_alarm_type" (
                                                   "id" int8 NOT NULL,
                                                   "code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "level" int4 NOT NULL,
                                                   "tenant_visible" int4 NOT NULL
)
;
COMMENT ON COLUMN "ccsp_statistic"."dic_alarm_type"."id" IS '主键';
COMMENT ON COLUMN "ccsp_statistic"."dic_alarm_type"."code" IS '标识';
COMMENT ON COLUMN "ccsp_statistic"."dic_alarm_type"."name" IS '名称';
COMMENT ON COLUMN "ccsp_statistic"."dic_alarm_type"."level" IS '等级;1为最高等级，随递增级别减弱';
COMMENT ON COLUMN "ccsp_statistic"."dic_alarm_type"."tenant_visible" IS '租户可见;0租户不可见，1租户可见';
COMMENT ON TABLE "ccsp_statistic"."dic_alarm_type" IS '告警类型字典';

CREATE TABLE "ccsp_statistic"."statistic_all_collect_data" (
                                                               "id" int8 NOT NULL,
                                                               "call_num" int8 NOT NULL,
                                                               "collect_num" int8 NOT NULL,
                                                               "region_code" varchar(200) COLLATE "pg_catalog"."default",
                                                               "tenant_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                               "app_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                               "service_ip" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                               "service_port" int4,
                                                               "service_type_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                               "service_url" varchar(200) COLLATE "pg_catalog"."default",
                                                               "avg_cost_time" int8,
                                                               "max_cost_time" int8,
                                                               "service_flow_num" int8,
                                                               "invalid_flag" int4 NOT NULL DEFAULT 0,
                                                               "remark" varchar(1000) COLLATE "pg_catalog"."default",
                                                               "create_by" int8,
                                                               "create_time" varchar(30) COLLATE "pg_catalog"."default",
                                                               "update_by" int8,
                                                               "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."call_num" IS '调用次数';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."collect_num" IS '采集数量;此条汇总结果来自于多少数量的数据';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."region_code" IS '区域标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."app_code" IS '应用标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."service_ip" IS '服务IP';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."service_port" IS '服务端口';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."service_type_code" IS '服务标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."service_url" IS '服务URL';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."avg_cost_time" IS '平均耗时';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."max_cost_time" IS '最大耗时';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."service_flow_num" IS '流量';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_statistic"."statistic_all_collect_data"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_statistic"."statistic_all_collect_data" IS '实时数据汇总拆分（330）';

CREATE TABLE "ccsp_statistic"."statistic_config_param" (
                                                           "id" int8 NOT NULL,
                                                           "region_code" varchar(200) COLLATE "pg_catalog"."default",
                                                           "tenant_code" varchar(100) COLLATE "pg_catalog"."default",
                                                           "app_code" varchar(100) COLLATE "pg_catalog"."default",
                                                           "service_type_code" varchar(100) COLLATE "pg_catalog"."default",
                                                           "target_code" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "target_child_code" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "multiple_val" int8 NOT NULL,
                                                           "max_val" int8,
                                                           "float_val" int8 DEFAULT 0
)
;
COMMENT ON COLUMN "ccsp_statistic"."statistic_config_param"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_statistic"."statistic_config_param"."region_code" IS '区域标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_config_param"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_config_param"."app_code" IS '应用标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_config_param"."service_type_code" IS '服务标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_config_param"."target_code" IS '指标名称';
COMMENT ON COLUMN "ccsp_statistic"."statistic_config_param"."target_child_code" IS '子指标名称';
COMMENT ON COLUMN "ccsp_statistic"."statistic_config_param"."multiple_val" IS '倍数';
COMMENT ON COLUMN "ccsp_statistic"."statistic_config_param"."max_val" IS '最大值';
COMMENT ON COLUMN "ccsp_statistic"."statistic_config_param"."float_val" IS '浮动值;默认为0';
COMMENT ON TABLE "ccsp_statistic"."statistic_config_param" IS '统计配置参数（330）';


CREATE TABLE "ccsp_statistic"."statistic_day_data" (
                                                       "id" int8 NOT NULL,
                                                       "collect_date_time" varchar(90) COLLATE "pg_catalog"."default",
                                                       "collect_time" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
                                                       "call_num" int8 NOT NULL,
                                                       "region_code" varchar(200) COLLATE "pg_catalog"."default",
                                                       "tenant_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                       "app_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                       "service_ip" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                       "service_port" int4,
                                                       "service_type_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                       "service_url" varchar(200) COLLATE "pg_catalog"."default",
                                                       "avg_cost_time" int8,
                                                       "max_cost_time" int8,
                                                       "service_flow_num" int8,
                                                       "invalid_flag" int4 NOT NULL DEFAULT 0,
                                                       "remark" varchar(1000) COLLATE "pg_catalog"."default",
                                                       "create_by" int8,
                                                       "create_time" varchar(30) COLLATE "pg_catalog"."default",
                                                       "update_by" int8,
                                                       "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."collect_date_time" IS '拆分数据天所在的月时间格式';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."collect_time" IS '拆分数据天时间';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."call_num" IS '调用次数';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."region_code" IS '区域标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."app_code" IS '应用标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."service_ip" IS '服务IP';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."service_port" IS '服务端口';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."service_type_code" IS '服务标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."service_url" IS '服务URL';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."avg_cost_time" IS '平均耗时';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."max_cost_time" IS '最大耗时';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."service_flow_num" IS '流量';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_statistic"."statistic_day_data"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_statistic"."statistic_day_data" IS '按照天拆分实时数据（330）';


CREATE TABLE "ccsp_statistic"."statistic_hour_data" (
                                                        "id" int8 NOT NULL,
                                                        "collect_date_time" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
                                                        "collect_time" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
                                                        "call_num" int8 NOT NULL,
                                                        "region_code" varchar(200) COLLATE "pg_catalog"."default",
                                                        "tenant_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                        "app_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                        "service_ip" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                        "service_port" int4,
                                                        "service_type_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                        "service_url" varchar(200) COLLATE "pg_catalog"."default",
                                                        "avg_cost_time" int8,
                                                        "max_cost_time" int8,
                                                        "service_flow_num" int8,
                                                        "invalid_flag" int4 NOT NULL DEFAULT 0,
                                                        "remark" varchar(1000) COLLATE "pg_catalog"."default",
                                                        "create_by" int8,
                                                        "create_time" varchar(30) COLLATE "pg_catalog"."default",
                                                        "update_by" int8,
                                                        "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."collect_date_time" IS '拆分数据小时时间所在的天时间';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."collect_time" IS '拆分数据小时时间;采集时间：字符串格式';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."call_num" IS '调用次数';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."region_code" IS '区域标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."app_code" IS '应用标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."service_ip" IS '服务IP';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."service_port" IS '服务端口';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."service_type_code" IS '服务标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."service_url" IS '服务URL';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."avg_cost_time" IS '平均耗时';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."max_cost_time" IS '最大耗时';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."service_flow_num" IS '流量';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_statistic"."statistic_hour_data"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_statistic"."statistic_hour_data" IS '按照小时拆分实时数据（330）';

CREATE TABLE "ccsp_statistic"."statistic_month_data" (
                                                         "id" int8 NOT NULL,
                                                         "collect_date_time" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
                                                         "collect_time" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
                                                         "call_num" int8 NOT NULL,
                                                         "region_code" varchar(200) COLLATE "pg_catalog"."default",
                                                         "tenant_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                         "app_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                         "service_ip" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                         "service_port" int4,
                                                         "service_type_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                         "service_url" varchar(200) COLLATE "pg_catalog"."default",
                                                         "avg_cost_time" int8,
                                                         "max_cost_time" int8,
                                                         "service_flow_num" int8,
                                                         "invalid_flag" int4 NOT NULL DEFAULT 0,
                                                         "remark" varchar(1000) COLLATE "pg_catalog"."default",
                                                         "create_by" int8,
                                                         "create_time" varchar(30) COLLATE "pg_catalog"."default",
                                                         "update_by" int8,
                                                         "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."collect_date_time" IS '拆分数据月时间所表示的年时间';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."collect_time" IS '拆分数据月时间单位';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."call_num" IS '调用次数';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."region_code" IS '区域标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."app_code" IS '应用标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."service_ip" IS '服务IP';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."service_port" IS '服务端口';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."service_type_code" IS '服务标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."service_url" IS '服务URL';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."avg_cost_time" IS '平均耗时';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."max_cost_time" IS '最大耗时';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."service_flow_num" IS '流量';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_statistic"."statistic_month_data"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_statistic"."statistic_month_data" IS '按照月拆分实时数据（330）';


CREATE TABLE "ccsp_statistic"."statistic_real_time_data" (
                                                             "id" int8 NOT NULL,
                                                             "collect_date_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
                                                             "collect_time" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
                                                             "call_num" int8 NOT NULL,
                                                             "region_code" varchar(200) COLLATE "pg_catalog"."default",
                                                             "tenant_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                             "app_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                             "service_ip" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                             "service_port" int4,
                                                             "service_type_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                             "service_url" varchar(200) COLLATE "pg_catalog"."default",
                                                             "avg_cost_time" int8,
                                                             "max_cost_time" int8,
                                                             "service_flow_num" int8,
                                                             "invalid_flag" int4 NOT NULL DEFAULT 0,
                                                             "remark" varchar(1000) COLLATE "pg_catalog"."default",
                                                             "create_by" int8,
                                                             "create_time" varchar(30) COLLATE "pg_catalog"."default",
                                                             "update_by" int8,
                                                             "update_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."id" IS 'ID';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."collect_date_time" IS '数据采集时间所在的小时时间格式';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."collect_time" IS '数据采集时间(字符串格式);数据采集时间(字符串格式)';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."call_num" IS '调用次数';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."region_code" IS '区域标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."tenant_code" IS '租户标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."app_code" IS '应用标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."service_ip" IS '服务IP';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."service_port" IS '服务端口';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."service_type_code" IS '服务标识';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."service_url" IS '服务URL';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."avg_cost_time" IS '平均耗时';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."max_cost_time" IS '最大耗时';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."service_flow_num" IS '流量';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."invalid_flag" IS '是否作废;默认为0';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."remark" IS '备注';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."create_by" IS '创建人';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."update_by" IS '更新人';
COMMENT ON COLUMN "ccsp_statistic"."statistic_real_time_data"."update_time" IS '更新时间';
COMMENT ON TABLE "ccsp_statistic"."statistic_real_time_data" IS '调用次数实时数据（330）';

ALTER TABLE monitor_connection ADD COLUMN REGION_ID int8 NULL DEFAULT NULL;

INSERT INTO alarm_oid VALUES (1, 'service_status', '.1.3.6.1.4.1.746379.1.2.1.1', 'sansec.ccsp.service.status', '服务状态');

INSERT INTO alarm_oid VALUES (2, 'service_status', '.1.3.6.1.4.1.746379.1.2.1.2', 'sansec.ccsp.service.id', '服务ID');

INSERT INTO alarm_oid VALUES (3, 'device_status', '.1.3.6.1.4.1.746379.2.2.1.1', 'sansec.ccsp.device.status', '设备状态');

INSERT INTO alarm_oid VALUES (4, 'device_status', '.1.3.6.1.4.1.746379.2.2.1.2', 'sansec.ccsp.device.id', '设备ID');

INSERT INTO alarm_oid VALUES (5, 'permission', '.1.3.6.1.4.1.746379.3.2.1.1', 'sansec.ccsp.permission.time', '许可剩余时间');

INSERT INTO alarm_oid VALUES (6, 'permission', '.1.3.6.1.4.1.746379.3.2.1.2', 'sansec.ccsp.permission.id', '服务ID');

INSERT INTO alarm_oid VALUES (7, 'product_quota', '.1.3.6.1.4.1.746379.4.2.1.1', 'sansec.ccsp.product.time', '配额剩余时间');

INSERT INTO alarm_oid VALUES (8, 'product_quota', '.1.3.6.1.4.1.746379.4.2.1.2', 'sansec.ccsp.product.id', '配额ID');

INSERT INTO dic_alarm_type (ID, CODE, NAME, LEVEL, TENANT_VISIBLE) VALUES (1, 'cpu', 'CPU使用率', 5, 0);

INSERT INTO dic_alarm_type (ID, CODE, NAME, LEVEL, TENANT_VISIBLE) VALUES (2, 'memory', '内存使用率', 5, 0);

INSERT INTO dic_alarm_type (ID, CODE, NAME, LEVEL, TENANT_VISIBLE) VALUES (3, 'disk', '磁盘使用率', 5, 0);

INSERT INTO dic_alarm_type (ID, CODE, NAME, LEVEL, TENANT_VISIBLE) VALUES (4, 'service_status', '服务状态', 3, 1);

INSERT INTO dic_alarm_type (ID, CODE, NAME, LEVEL, TENANT_VISIBLE) VALUES (5, 'device_status', '设备状态', 4, 0);

INSERT INTO dic_alarm_type (ID, CODE, NAME, LEVEL, TENANT_VISIBLE) VALUES (6, 'permission', '许可有效期', 1, 1);

INSERT INTO dic_alarm_type (ID, CODE, NAME, LEVEL, TENANT_VISIBLE) VALUES (7, 'product_quota', '配额有效期', 2, 1);

INSERT INTO "ccsp_statistic"."screen_index" VALUES (51, 'kms-密钥管理模板', '{"title":"密钥管理服务","data":[{"unit":"个","name":"密钥数量","value":"${kmsKeyNumStatistic_keyNums}","data":[{"unit":"个","name":"预激活","value":"${kmsKeyStateStatistic_1}"},{"unit":"个","name":"激活","value":"${kmsKeyStateStatistic_2}"},{"unit":"个","name":"注销","value":"${kmsKeyStateStatistic_3}"},{"unit":"个","name":"销毁","value":"${kmsKeyStateStatistic_5}"}]}]}', 0, NULL, NULL, NULL, NULL, 'kmsBusiStatistic_totalError 密钥调用错误次数');
INSERT INTO "ccsp_statistic"."screen_index" VALUES (52, 'secauth-动态令牌模板', '{"title":"动态令牌服务","data":[{"unit":"次","name":"动态令牌总次数","value":"${secauthBusiStatistic_totals}"}]}', 0, NULL, NULL, NULL, NULL, 'secauth');
INSERT INTO "ccsp_statistic"."screen_index" VALUES (53, 'pki-数据加解密模板', '{"title":"加解密服务","data":[{"name":"密钥数量","value":"${encKeyNumStatistic_keyNums}","unit":"个"},{"name":"总调用次数","value":"${encBusiStatistic_totals}","unit":"次"}]}', 0, NULL, NULL, NULL, NULL, 'pki');
INSERT INTO "ccsp_statistic"."screen_index" VALUES (54, 'secdb-数据库加密模板', '{"title":"数据库加密服务","data":[{"name":"密钥数量","value":"${secDbKeyNumStatistic_keyNums}","unit":"个","data":[{"name":"加密数据库","value":"${dbStatistic_dbNum}","unit":"个"},{"name":"加密表","value":"${dbStatistic_dbTableNum}","unit":"个"}]}]}', 0, NULL, NULL, NULL, NULL, 'secdb');
INSERT INTO "ccsp_statistic"."screen_index" VALUES (55, 'secstorage-文件加密模板', '{"title":"文件加密服务","data":[{"name":"NAS服务器","value":"${serverStatistic_nasServerNum}","unit":"个"},{"name":"文件服务器","value":"${serverStatistic_fileServerNum}","unit":"个"}]}', 0, NULL, NULL, NULL, NULL, 'secstorage');
INSERT INTO "ccsp_statistic"."screen_index" VALUES (56, 'vpn-VPN服务模板', '{"title":"SSLVPN加密通道服务","data":[{"name":"并发连接数","value":"${vpnConcurrentConnectNumStatistic_vpnConcurrentConnectNumStatistic}","unit":"个"},{"name":"新建连接数","value":"${vpnNewConnectNumStatistic_vpnNewConnectNumStatistic}","unit":"个"}]}', 0, NULL, NULL, NULL, NULL, 'vpn');
INSERT INTO "ccsp_statistic"."screen_index" VALUES (57, 'svs-签名验签模板', '{"title":"签名验签服务","data":[{"name":"应用证书数量","value":"${signAppCertStatistic_certNumbers}","unit":"个"},{"name":"用户证书数量","value":"${signUserCertStatistic_certNumbers}","unit":"个"}]}', 0, NULL, NULL, NULL, NULL, 'svs');
INSERT INTO "ccsp_statistic"."screen_index" VALUES (58, 'tsc-电子签章模板', '{"title":"电子签章服务","data":[{"name":"签章数量","value":"${sealNumStatistic_sealNum}","unit":"个","data":[{"name":"总签署次数","value":"${sealSignBusiStatistic_totals}","unit":"次"},{"name":"总验签次数","value":"${sealVerifyBusiStatistic_totals}","unit":"次"}]}]}', 0, NULL, NULL, NULL, NULL, 'tsc');
INSERT INTO "ccsp_statistic"."screen_index" VALUES (59, 'sms-协同签名模板', '{"title":"协同签名服务","data":[{"name":"用户数量","value":"${userNumStatistic_userNum}","unit":"个"},{"name":"总调用次数","value":"${splitBusiStatistic_totals}","unit":"次"}]}', 0, NULL, NULL, NULL, NULL, 'sms');
INSERT INTO "ccsp_statistic"."screen_index" VALUES (60, 'tsa-时间戳模板', '{"title":"时间戳服务","data":[{"name":"总调用次数","value":"${timeBusiStatistic_totals}","unit":"次"}]}', 0, NULL, NULL, NULL, NULL, 'tsa');
INSERT INTO "ccsp_statistic"."screen_index" VALUES (61, 'ca-数字证书认证模板', '{"title":"数字证书认证服务","data":[{"name":"用户证书数量","value":"${caUserCertStatistic_certNums}","unit":"个"}]}', 0, NULL, NULL, NULL, NULL, 'ca');


INSERT INTO screen_statistic (ID, INDEX_TYPE, STATISTIC_TYPE, STATISTIC_NAME, STATISTIC_FIRST, STATISTIC_SECOND, INVALID_FLAG, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES (130, 'ca', 2, '用户证书数量', 'caUserCertStatistic', 'certNums', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (51, '首页服务统计卡片形式', 'card', 51, 'kms', 0, NULL, NULL, NULL, NULL, '');

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (52, '首页服务统计卡片形式', 'card', 52, 'secauth', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (53, '首页服务统计卡片形式', 'card', 53, 'pki', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (54, '首页服务统计卡片形式', 'card', 54, 'secdb', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (55, '首页服务统计卡片形式', 'card', 55, 'secstorage', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (56, '首页服务统计卡片形式', 'card', 56, 'vpn', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (57, '首页服务统计卡片形式', 'card', 57, 'svs', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (58, '首页服务统计卡片形式', 'card', 58, 'tsc', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (59, '首页服务统计卡片形式', 'card', 59, 'sms', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (60, '首页服务统计卡片形式', 'card', 60, 'tsa', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO screen_template_rel (ID, TEMPLATE_NAME, TEMPLATE_CODE, INDEX_ID, INDEX_TYPE, INVALID_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, REMARK) VALUES (61, '首页服务统计卡片形式', 'card', 61, 'ca', 0, NULL, NULL, NULL, NULL, NULL);

INSERT INTO sys_job (JOB_ID, JOB_NAME, JOB_GROUP, SERVER_ID, METHOD_URL, JSON_PARAM, CRON_EXPRESSION, MISFIRE_POLICY, CONCURRENT, JOB_STATUS, CREATED_BY, CREATE_TIME, UPDATED_BY, UPDATE_TIME, REMARK) VALUES (6, 'analyze_data_by_hour', 'statistic', 'ccsp-atom-static', NULL, 'statisticRealTimeAnalyzeDataTaskApiImpl.analyzeDataByHour()', '0 0 */1 * * ?', '3', '0', '0', NULL, NULL, NULL, NULL, '每小时执行一次,按小时拆分实时数据');

INSERT INTO sys_job (JOB_ID, JOB_NAME, JOB_GROUP, SERVER_ID, METHOD_URL, JSON_PARAM, CRON_EXPRESSION, MISFIRE_POLICY, CONCURRENT, JOB_STATUS, CREATED_BY, CREATE_TIME, UPDATED_BY, UPDATE_TIME, REMARK) VALUES (7, 'analyzeDataByMonth', 'statistic', 'ccsp-atom-static', NULL, 'statisticRealTimeAnalyzeDataTaskApiImpl.analyzeDataByMonth()', '0 0 1 1 * ?', '3', '1', '0', NULL, NULL, NULL, NULL, '每月1号的凌晨一点执行,按月拆分天数据');

INSERT INTO sys_job (JOB_ID, JOB_NAME, JOB_GROUP, SERVER_ID, METHOD_URL, JSON_PARAM, CRON_EXPRESSION, MISFIRE_POLICY, CONCURRENT, JOB_STATUS, CREATED_BY, CREATE_TIME, UPDATED_BY, UPDATE_TIME, REMARK) VALUES (8, 'analyzeDataByDay', 'statistic', 'ccsp-atom-static', NULL, 'statisticRealTimeAnalyzeDataTaskApiImpl.analyzeDataByDay()', '0 30 0 * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, '每天00:30执行一次,按天拆分小时数据');

INSERT INTO sys_job (JOB_ID, JOB_NAME, JOB_GROUP, SERVER_ID, METHOD_URL, JSON_PARAM, CRON_EXPRESSION, MISFIRE_POLICY, CONCURRENT, JOB_STATUS, CREATED_BY, CREATE_TIME, UPDATED_BY, UPDATE_TIME, REMARK) VALUES (9, 'clear_real_time_data', 'statistic', 'ccsp-atom-static', NULL, 'statisticRealTimeAnalyzeDataTaskApiImpl.clearRealTimeData()', '0 30 0 * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, '每天00:30清理实时数据，保留两天的数据');

INSERT INTO sys_job (JOB_ID, JOB_NAME, JOB_GROUP, SERVER_ID, METHOD_URL, JSON_PARAM, CRON_EXPRESSION, MISFIRE_POLICY, CONCURRENT, JOB_STATUS, CREATED_BY, CREATE_TIME, UPDATED_BY, UPDATE_TIME, REMARK) VALUES (10, 'clear_data_by_hour', 'statistic', 'ccsp-atom-static', NULL, 'statisticRealTimeAnalyzeDataTaskApiImpl.clearDataByHour()', '0 0 0 * * ?', '3', '1', '0', NULL, NULL, NULL, NULL, '每天00:00清理小时数据,保留7天的数据');

INSERT INTO sys_job (JOB_ID, JOB_NAME, JOB_GROUP, SERVER_ID, METHOD_URL, JSON_PARAM, CRON_EXPRESSION, MISFIRE_POLICY, CONCURRENT, JOB_STATUS, CREATED_BY, CREATE_TIME, UPDATED_BY, UPDATE_TIME, REMARK) VALUES (11, 'clear_data_by_day', 'statistic', 'ccsp-atom-static', NULL, 'statisticRealTimeAnalyzeDataTaskApiImpl.clearDataByDay()', '0 0 2 1 * ?', '3', '1', '0', NULL, NULL, NULL, NULL, '每月1号的凌晨2点执行,保留一个月的数据');
