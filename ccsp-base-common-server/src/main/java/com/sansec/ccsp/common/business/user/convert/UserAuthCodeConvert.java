package com.sansec.ccsp.common.business.user.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.common.business.user.entity.UserAuthCodePO;
import com.sansec.ccsp.common.user.request.UserAuthCodeDTO;
import com.sansec.ccsp.common.user.response.UserAuthCodeVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

 /**
 * @Description: 用户历史口令;(USER_AUTH_CODE)实体类转换接口
 * <AUTHOR> xia<PERSON>jiaw<PERSON>
 * @Date: 2023-2-18
 */
@Mapper(componentModel = "spring")
public interface UserAuthCodeConvert{
    /**
     * dtoToPo
     * @param userAuthCodeDTO
     * @return
     */
    @Mappings({})
    UserAuthCodePO dtoToPo(UserAuthCodeDTO userAuthCodeDTO);

    /**
     * poToDto
     * @param userAuthCodePO
     * @return
     */
    UserAuthCodeDTO poToDto(UserAuthCodePO userAuthCodePO);

    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<UserAuthCodeDTO> poToDto(List<UserAuthCodePO> list);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<UserAuthCodeVO> pagePOToSecPageVOPage(IPage<UserAuthCodePO> iPage);

    @InheritConfiguration(name = "convertVo")
    List<UserAuthCodeVO> convert(List<UserAuthCodePO> list);

    @Mappings({})
    UserAuthCodeVO convertVo(UserAuthCodePO request);
}
