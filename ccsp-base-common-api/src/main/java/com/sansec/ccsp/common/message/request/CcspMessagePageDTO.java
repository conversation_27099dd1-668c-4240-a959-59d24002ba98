package com.sansec.ccsp.common.message.request;

import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @description : 消息表;
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2023-11-21
 */
@Data
public class CcspMessagePageDTO extends SecPageDTO{
    /**
     * 消息来源;1工单 2告警
     */
    private Integer source;
    /**
     * 消息状态;1未读 2已读
     */
    private Integer status;
}