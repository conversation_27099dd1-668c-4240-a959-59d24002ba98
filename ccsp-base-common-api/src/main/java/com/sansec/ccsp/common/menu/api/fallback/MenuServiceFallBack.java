package com.sansec.ccsp.common.menu.api.fallback;

import com.sansec.ccsp.common.menu.api.MenuServiceApi;
import com.sansec.ccsp.common.menu.request.AddSysMenuDTO;
import com.sansec.ccsp.common.menu.request.SetMenuDTO;
import com.sansec.ccsp.common.menu.request.SysMenuDTO;
import com.sansec.ccsp.common.menu.request.SysMenuPageDTO;
import com.sansec.ccsp.common.menu.response.RouterVO;
import com.sansec.ccsp.common.menu.response.SysMenuVO;
import com.sansec.common.param.response.SecRestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class MenuServiceFallBack implements FallbackFactory<MenuServiceApi> {

    @Override
    public MenuServiceApi create(Throwable throwable) {
        return new MenuServiceApi() {
            @Override
            public SecRestResponse<List<SysMenuVO>> find(SysMenuPageDTO sysMenuPageDTO) {
                log.error("分页查询异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> add(AddSysMenuDTO sysMenuDTO) {
                log.error("新增数据异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> setMenu(SetMenuDTO setMenuDTO) {
                log.error("临时设置角色权限异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<SysMenuVO> getById(Long menuId) {
                log.error("通过id获取菜单信息异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> edit(SysMenuDTO sysMenuDTO) {
                log.error("更新数据异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> setStatus(SysMenuDTO sysMenuDTO) {
                log.error("修改菜单状态异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> deleteById(Long id) {
                log.error("通过主键删除数据异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> treeSelect() {
                log.error("获取下拉树列表异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<List<RouterVO>> getRouters() {
                log.error("获取当前登录人菜单权限异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }
        };
    }
}
