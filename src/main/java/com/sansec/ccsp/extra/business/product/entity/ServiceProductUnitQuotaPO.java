package com.sansec.ccsp.extra.business.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description : 密码产品单位限额(330);
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @date : 2024-3-9
 */
@EqualsAndHashCode(callSuper = true)
@TableName("SERVICE_PRODUCT_UNIT_QUOTA")
@Data
public class ServiceProductUnitQuotaPO extends BasePO{
    /**
     * ID
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 密码产品ID
     */
    private Long productId;
    /**
     * 业务类型ID=服务类型ID
     */
    private Long busiTypeId;
    /**
     * 配额值
     */
    private Integer quotaValue;
    /**
     * 配额单位
     */
    private String quotaUnit;
    /**
     * 配额类型;1：网关控制； 2：密码服务控制；3：不控制
     */
    private Integer quotaType;
    /**
     * 描述
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}