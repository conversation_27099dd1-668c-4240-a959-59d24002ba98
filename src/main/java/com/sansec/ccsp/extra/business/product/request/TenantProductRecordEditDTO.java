package com.sansec.ccsp.extra.business.product.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description : 租户开通密码产品记录表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2024-4-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TenantProductRecordEditDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 租户标识
     */
    private Long tenantId;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 产品类型
     */
    private Integer productType;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 开通状态;1已开通，2未开通
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
}