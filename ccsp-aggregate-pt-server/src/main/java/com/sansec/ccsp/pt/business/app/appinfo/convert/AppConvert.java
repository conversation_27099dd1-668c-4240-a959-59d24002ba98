package com.sansec.ccsp.pt.business.app.appinfo.convert;

import com.sansec.ccsp.app.info.request.*;
import com.sansec.ccsp.app.info.response.AppRegisterCodeVO;
import com.sansec.ccsp.app.info.response.AppRegisterPageListVO;
import com.sansec.ccsp.app.info.response.BusiTypeVo;
import com.sansec.ccsp.device.devicegroup.response.DeviceGroupIdNameVO;
import com.sansec.ccsp.pt.business.app.appinfo.request.*;
import com.sansec.ccsp.pt.business.app.appinfo.response.*;
import com.sansec.ccsp.pt.business.app.group.entity.AppBusiToGroupPO;
import com.sansec.ccsp.pt.business.app.group.request.AppBusiToGroupDTO;
import com.sansec.ccsp.pt.business.kms.request.AppToKmsRelationDTO;
import com.sansec.ccsp.pt.business.kms.response.AppToKmsRelationVO;
import com.sansec.ccsp.servicemgt.servicegroup.response.ServiceGroupIdNameVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * @Describe
 * <AUTHOR>
 * @create 2023/3/13 12:10
 */
@Mapper(componentModel = "spring")
public interface AppConvert {

    AppRegisterAuditDTO ptAuditToAudit(AppRegisterBusiAuditDTO appRegisterBusiAuditDTO);

    AppRegisterInfoVO pageInfoToInfo(AppRegisterPageListVO appRegisterPageListVO);

    List<AppBusiTypeVO> deviceBusiToAppBusi(List<com.sansec.ccsp.device.devicegroup.response.BusiTypeVO> busiTypeVo);

    List<AppBusiTypeVO> serviceBusiToAppBusi(List<com.sansec.ccsp.servicemgt.servicegroup.response.BusiTypeVO> busiTypeVo);

    AppBusiToGroupPO addBusiTypeToAppBusiGroup(AppBusiTypeAddDTO appBusiTypeAddDTO);

    AppBusiTypeDTO addBusiDTOToBusiDTO(AppBusiTypeAddDTO appBusiTypeAddDTO);

    BusiTypeByAppVO busiVoToAPPBusiVo(BusiTypeVo busiTypeVo);

    List<AppDeviceGroupInfo> deviceGroupToAppDeviceGroup(List<DeviceGroupIdNameVO> list);

    List<AppServiceGroupInfo> serviceGroupToAppServiceGroup(List<ServiceGroupIdNameVO> list);

    AppBusiToGroupDTO appBusiAddToDTO(AppBusiTypeAddDTO appBusiTypeAddDTO);

    @Mapping(target = "busiTypeList", ignore = true)
    AppInfoAddDTO ptInfoBusiAddToInfoAdd(AppInfoBusiAddDTO appInfoBusiAddDTO);

    AppInfoDetailDTO dtoToDetailDto(GetByAppIdBusiTypeDTO getByAppIdBusiTypeDTO);

    AppInfoUpdateDTO ptInfoBusiEditToInfoUpdateDTO(AppInfoBusiEditDTO appInfoBusiEditDTO);
    AppToKmsRelationDTO appToKmsVoToDto(AppToKmsRelationVO appToKmsRelationVO);

    AppInfoBusiAddDTO appRegistVoToInfoBusiAddDTO(AppRegisterCodeVO appRegisterCodeVO);
}
