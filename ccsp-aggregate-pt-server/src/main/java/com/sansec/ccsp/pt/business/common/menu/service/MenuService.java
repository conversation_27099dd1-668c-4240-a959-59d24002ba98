package com.sansec.ccsp.pt.business.common.menu.service;

import com.sansec.ccsp.common.menu.request.*;
import com.sansec.ccsp.common.menu.response.RouterVO;
import com.sansec.ccsp.common.menu.response.SysMenuVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * @Description: 菜单管理
 * @Author: wangjunjie
 * @CreateTime: 2023/05/05  18:32
 */
public interface MenuService {

    SecRestResponse<List<SysMenuVO>> find(SysMenuPageDTO sysMenuPageDTO);

    SecRestResponse<Object> add(AddSysMenuDTO sysMenuDTO);

    SecRestResponse<Object> setMenu(SetMenuDTO setMenuDTO);

    SysMenuVO getById(Long menuId);

    Object edit(SysMenuDTO sysMenuDTO);

    Object setStatus(SysMenuDTO sysMenuDTO);

    SecRestResponse<Object> deleteById(Long id);

    SecRestResponse<Object> treeSelect();

    SecRestResponse<List<RouterVO>> getRouters();

    /**
     * 检测菜单是否正常
     *
     * @param checkMenuNormalDTO
     * @return
     */
    Boolean checkMenuNormal(CheckMenuNormalDTO checkMenuNormalDTO);
}
