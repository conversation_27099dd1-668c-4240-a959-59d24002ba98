package com.sansec.ccsp.pt.business.statistic.statistic.service.impl;

import com.sansec.ccsp.pt.business.statistic.statistic.service.StatisticCollectService;
import com.sansec.ccsp.pt.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.staticapi.monitor.api.StatisticCollectApi;
import com.sansec.ccsp.staticapi.monitor.request.StatisticReportMonitorDataDTO;
import com.sansec.ccsp.staticapi.monitor.response.StatisticInfoResVO;
import com.sansec.ccsp.staticapi.monitor.response.StatisticMonitorDataCommonVO;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: 首页统计服务
 * @Author: wangjunjie
 * @CreateTime: 2023/05/14  20:10
 */
@Service
@Slf4j
public class StatisticCollectServiceImpl implements StatisticCollectService {

    @Resource
    private StatisticCollectApi statisticCollectApi;

    @Override
    public void statisticCollect(StatisticInfoResVO statisticInfoResVO) {
        statisticCollectApi.statisticCollect(statisticInfoResVO);
    }

    @Override
    public void addMonitorStatisticReportData(List<StatisticReportMonitorDataDTO> statisticReportMonitorDataDTOS) {
        statisticCollectApi.addMonitorStatisticReportData(statisticReportMonitorDataDTOS);
    }
}
