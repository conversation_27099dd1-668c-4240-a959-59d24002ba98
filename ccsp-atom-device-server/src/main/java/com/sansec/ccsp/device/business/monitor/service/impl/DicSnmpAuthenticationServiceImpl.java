package com.sansec.ccsp.device.business.monitor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.device.business.monitor.convert.DicSnmpAuthenticationConvert;
import com.sansec.ccsp.device.business.monitor.entity.DicSnmpAuthenticationPO;
import com.sansec.ccsp.device.business.monitor.mapper.DicSnmpAuthenticationMapper;
import com.sansec.ccsp.device.business.monitor.service.DicSnmpAuthenticationService;
import com.sansec.ccsp.device.devicemonitor.request.DicSnmpAuthenticationDTO;
import com.sansec.ccsp.device.devicemonitor.request.DicSnmpAuthenticationPageDTO;
import com.sansec.ccsp.device.devicemonitor.response.DicSnmpAuthenticationVO;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description : snmp认证方式字典表(1030);(DIC_SNMP_AUTHENTICATION)表服务实现类
 * <AUTHOR> xiaojiawei
 * @date : 2023-9-20
 */
@Service
@Slf4j
public class DicSnmpAuthenticationServiceImpl extends ServiceImpl<DicSnmpAuthenticationMapper, DicSnmpAuthenticationPO> implements DicSnmpAuthenticationService {
    @Resource
    private DicSnmpAuthenticationConvert dicSnmpAuthenticationConvert;
    
    /** 
     * 分页查询
     *
     * @param dicSnmpAuthenticationPageDTO 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<DicSnmpAuthenticationVO>> find(DicSnmpAuthenticationPageDTO dicSnmpAuthenticationPageDTO){
        QueryWrapper<DicSnmpAuthenticationPO> queryWrapper = Wrappers.query();
        IPage<DicSnmpAuthenticationPO> page = new Page<>(dicSnmpAuthenticationPageDTO.getPageNum(),dicSnmpAuthenticationPageDTO.getPageSize());
        IPage<DicSnmpAuthenticationPO> dicSnmpAuthenticationPOPage = baseMapper.selectPage(page, queryWrapper);
        // TODO 分页对象转换
        SecPageVO<DicSnmpAuthenticationVO> dicSnmpAuthenticationPageVO = dicSnmpAuthenticationConvert.pagePOToSecPageVOPage(dicSnmpAuthenticationPOPage);
        return ResultUtil.ok(dicSnmpAuthenticationPageVO);
    }
    
    /** 
     * 新增数据
     *
     * @param dicSnmpAuthenticationDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> add(DicSnmpAuthenticationDTO dicSnmpAuthenticationDTO){
        DicSnmpAuthenticationPO dicSnmpAuthenticationPO = dicSnmpAuthenticationConvert.dtoToPo(dicSnmpAuthenticationDTO);
        dicSnmpAuthenticationPO.setId(IdGenerator.ins().generator());
        dicSnmpAuthenticationPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.insert(dicSnmpAuthenticationPO);
        return ResultUtil.ok();
    }
    
    /** 
     * 更新数据
     *
     * @param dicSnmpAuthenticationDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(DicSnmpAuthenticationDTO dicSnmpAuthenticationDTO){
        DicSnmpAuthenticationPO dicSnmpAuthenticationPO = dicSnmpAuthenticationConvert.dtoToPo(dicSnmpAuthenticationDTO);
        dicSnmpAuthenticationPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.updateById(dicSnmpAuthenticationPO);
        return ResultUtil.ok();
    }
    
    /** 
     * 通过主键删除数据
     *
     * @param dicSnmpAuthenticationDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> deleteById(DicSnmpAuthenticationDTO dicSnmpAuthenticationDTO){
        baseMapper.deleteById(dicSnmpAuthenticationDTO.getId());
        return ResultUtil.ok();
    }

    /**
     * 获取snmp认证方式字典
     *
     * @return 实例对象
     */
    @Override
    public SecRestResponse<List<DicSnmpAuthenticationVO>> getSnmpAuthenticationList() {
        List<DicSnmpAuthenticationPO> dicSnmpAuthenticationPOList = baseMapper.selectList(Wrappers.emptyWrapper());
        List<DicSnmpAuthenticationVO> dicSnmpAuthenticationVOList = dicSnmpAuthenticationConvert.convert(dicSnmpAuthenticationPOList);
        return ResultUtil.ok(dicSnmpAuthenticationVOList);
    }
}