package com.sansec.ccsp.servicemgt.business.serviceinfo.service;

import com.sansec.ccsp.servicemgt.serviceinfo.request.*;
import com.sansec.ccsp.servicemgt.serviceinfo.response.*;
import com.sansec.ccsp.servicemgt.servicetype.request.ServiceTypeDTO;
import com.sansec.ccsp.servicemgt.servicetype.response.ServiceTypeVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> xia<PERSON>jiaw<PERSON>
 * @Description: 服务信息表;(SERVICE_INFO)表服务接口
 * @Date: 2023-2-19
 */
public interface ServiceInfoService {
    /**
     * 分页查询
     *
     * @param serviceInfoPageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<ServiceInfoPageVO>> find(ServiceInfoPageDTO serviceInfoPageDTO);

    /**
     * 根据服务组分页查询
     *
     * @param serviceInfoPageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<ServiceInfoPageVO>> findByServiceGroup(ServiceInfoByServiceGroupPageDTO serviceInfoPageDTO);

    /**
     * 新增数据
     *
     * @param ServiceAddWrapper
     * @return 实例对象
     */
    SecRestResponse<Object> add(ServiceAddWrapper serviceAddWrapper);

    /**
     * 新增数据
     *
     * @param serviceInfoDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(ServiceInfoDTO serviceInfoDTO);

    /**
     * 更新数据
     *
     * @param serviceInfoEditDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(ServiceInfoEditDTO serviceInfoEditDTO);

    /**
     * 通过主键删除数据
     *
     * @param serviceInfoDeleteDTO
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(ServiceInfoDeleteDTO serviceInfoDeleteDTO);

    /**
     * @param serviceInfoParamDTO
     * @return SecRestResponse<ServiceInfoVO> 返回类型
     * @Description: 根据服务参数获取服务信息
     */
    SecRestResponse<List<ServiceInfoVO>> getServiceInfoByServiceParam(ServiceInfoParamDTO serviceInfoParamDTO);

    /**
     * @param editOperStatusParamDTO
     * @return SecRestResponse<Object> 返回类型
     * @Description: 修改服务操作状态
     */
    SecRestResponse<Object> editServiceOperStatus(EditOperStatusParamDTO editOperStatusParamDTO);

    /**
     * @param editRunStatusParamDTO
     * @return SecRestResponse<Object> 返回类型
     * @Description: 修改服务运行状态
     */
    SecRestResponse<Object> editServiceRunStatus(EditRunStatusParamDTO editRunStatusParamDTO);

    /**
     * @param findServiceRunStatusParamDTO
     * @return SecRestResponse<Map < Long, Integer>> 返回类型
     * @Description: 查询服务状态
     */
    SecRestResponse<Map<Long, Integer>> findServiceRunStatus(FindServiceRunStatusParamDTO findServiceRunStatusParamDTO);

    /**
     * @param findServiceRunStatusParamDTO
     * @return SecRestResponse<Map < Long, Integer>> 返回类型
     * @Description: 查询服务状态
     */
    SecRestResponse<Map<Long, ServiceStatusVO>> findServiceRunAndOperStatus(FindServiceRunStatusParamDTO findServiceRunStatusParamDTO);

    /**
     * @param @param  ServiceInfoVO
     * @param @return 参数
     * @return List<ServiceInfoVO> 返回类型
     * @throws
     * @Title: findList
     * @Description: 根据服务类型、服务名称、租户id查询列表信息-->可继续添加查询条件
     */
    SecRestResponse<List<ServiceInfoVO>> findList(ServiceInfoDTO serviceInfoDTO);

    /**
     * 同ip端口校验
     *
     * @param serviceInfoDTO
     * @return
     */
    SecRestResponse<Boolean> findIpPortExist(ServiceInfoDTO serviceInfoDTO);

    /**
     * 同服务类型同ip校验
     *
     * @param serviceInfoDTO
     * @return
     */
    SecRestResponse<Boolean> findTypeIpExist(ServiceInfoDTO serviceInfoDTO);

    /**
     * 获取空闲服务列表
     *
     * @return
     */
    SecRestResponse<List<ServiceInfoVO>> getServiceByFree();

    /**
     * @param @param  ServiceInfoVO
     * @param @return 参数
     * @return ServiceInfoVO 返回类型
     * @throws
     * @Title: findList
     * @Description: 根据id查询服务
     */
    SecRestResponse<ServiceInfoVO> findById(ServiceInfoDTO serviceInfoDTO);

    /**
     * 根据租户ID获取包含的所有服务
     *
     * @param serviceListByTenantIdDTO
     * @return
     */
    SecRestResponse<List<ServiceInfoVO>> getServiceByTenantId(ServiceListByTenantIdDTO serviceListByTenantIdDTO);

    /**
     * 根据服务组ID获取包含的所有服务
     *
     * @param serviceListByGroupIdDTO
     * @return
     */
    SecRestResponse<List<ServiceInfoVO>> getServiceByGroupId(ServiceListByGroupIdDTO serviceListByGroupIdDTO);


    SecRestResponse<List<ServiceInfoVO>> getServiceByGroupIds(List<Long> serviceGroupIds);

    /**
     * 平台服务总数
     *
     * @return
     */
    SecRestResponse<ServiceStatusChartVO> getServiceNumOfSystemOperations();

    /**
     * 租户服务总数
     * @param tenantId
     * @return
     */
    SecRestResponse<ServiceStatusChartVO> getServiceNumOfTenantOperations(Long tenantId);

    /**
     * 过去多个租户的服务总数
     * @param tenantIdList
     * @return
     */
    SecRestResponse<Map<Long,Integer>> getServiceNumByTenantList(List<Long> tenantIdList);

    /**
     * 平台服务饼图
     * @return
     */
    SecRestResponse<ServiceStatusChartVO> getServiceStatusOfSystemOperations();

//    SecRestResponse<ServiceStatusChartVO> getServiceStatusOfOrganOperations(List<Long> tenantIds);

    SecRestResponse<ServiceStatusChartVO> getServiceNumOfServiceGroupOperations(Long serviceGroupId);

    SecRestResponse<Map<Long,Integer>> getServiceNumByServiceGroupList(List<Long> serviceGroupIdList);


    /**
     * 租户服务饼图
     * @param tenantId
     * @return
     */
    SecRestResponse<ServiceStatusChartVO> getServiceStatusOfTenantOperations(Long tenantId);

    SecRestResponse<ServiceStatusChartVO> getServiceStatusOfServiceGroupIds(List<Long> serviceGroupIdList);

    /**
     * @param editByTenantParamDTO
     * @return SecRestResponse<Object> 返回类型
     * @Description: 修改服务信息-修改设备组
     */
    SecRestResponse<Object> editByTenant(EditByTenantParamDTO editByTenantParamDTO);

    /**
     * 查询当前服务个数
     * @return
     */
    SecRestResponse<Long> getCountService(RegionIdDTO regionId);
    /**
     * 更新完整性
     */
    void updateHmac(Long id);

    /**
     * 获取服务组和服务的对应关系，每个服务组下分别有多少服务
     * @param serviceInfoDTO
     * @return
     */
    SecRestResponse<List<ServiceGroupWithInfoVO>> getServiceGroupWithInfoVO(ServiceInfoDTO serviceInfoDTO);




    /**
     * 获取每种服务类型下有几种服务组
     *
     * @param serviceInfoDTO
     * @return
     */
    SecRestResponse<List<ServiceTypeGroupNumVO>> getServiceTypeWithGroupNum(@RequestBody ServiceInfoDTO serviceInfoDTO);

    /**
     * 根据IP查询监控中的服务
     *
     * @param serviceInfoIpDTO
     * @return
     */
    SecRestResponse<List<ServiceInfoVO>> getMonitorServiceListByIp(ServiceInfoIpDTO serviceInfoIpDTO);

    /**
     * 根据IP查询运行中的服务
     *
     * @param serviceInfoIpDTO
     * @return
     */
    SecRestResponse<List<ServiceInfoVO>> getServiceListByIp(ServiceInfoIpDTO serviceInfoIpDTO);


    /**
     * 根据租户ids和服务类型ids查询服务信息
     *
     * @param serviceInfoDTO
     * @return
     */
    SecRestResponse<List<ServiceInfoVO>> getListByTenantAndTypeId(@RequestBody ServiceInfoDTO serviceInfoDTO);

    SecRestResponse<List<ServiceInfoVO>> getListByGroupAndTypeId(ServiceGroupAndBusiTypeIdDTO dto);


    /**
     * 根据区域ID查询服务信息
     *
     * @param serviceInfoByRegionIdDTO
     * @return
     */
    SecRestResponse<List<ServiceInfoVO>> getServiceInfoListByRegionId(ServiceInfoByRegionIdDTO serviceInfoByRegionIdDTO);

    /**
     * @Description: 释放出错服务
     * @param serviceId
     */
     SecRestResponse<Object> releaseErrorService(Long serviceId);

    SecRestResponse<List<Long>> getServiceIdList();

    /**
     * 获取某个租户或全部租户已创建的服务类型列表
     * @param tenantIds
     * @return
     */
    SecRestResponse<List<String>> getServiceTypeIdList(List<String> tenantIds);

    /**
     * 获取业务服务器监控ip列表
     */
    SecRestResponse<List<String>> getBusiMonitorIpList();

    /**
     * 根据服务类型查询服务，同一个服务组只保留一个
     */
    SecRestResponse<List<ServiceInfoVO>>selectByServiceTypeIdGroupByServiceGroupId(Long serviceTypeId);


}