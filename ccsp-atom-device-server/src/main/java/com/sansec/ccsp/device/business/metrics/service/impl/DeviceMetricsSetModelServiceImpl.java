package com.sansec.ccsp.device.business.metrics.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.device.business.metrics.convert.DeviceMetricsSetModelConvert;
import com.sansec.ccsp.device.business.metrics.entity.DeviceMetricsSetModelPO;
import com.sansec.ccsp.device.business.metrics.enums.EnumMetricsSetGroup;
import com.sansec.ccsp.device.business.metrics.enums.EnumMetricsSetType;
import com.sansec.ccsp.device.business.metrics.mapper.DeviceMetricsSetModelMapper;
import com.sansec.ccsp.device.business.metrics.service.DeviceMetricsSetModelService;
import com.sansec.ccsp.device.business.metrics.util.MonitorMetricsConst;
import com.sansec.ccsp.device.metrics.request.DeviceMetricsSetModelQueryDTO;
import com.sansec.ccsp.device.metrics.response.DeviceMetricsSetModelVO;
import com.sansec.ccsp.device.metrics.response.ModelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> wangke
 * @description : 设备指标集模板;(DEVICE_METRICS_SET_MODEL)表服务实现类
 * @date : 2024-6-20
 */
@Service
@Slf4j
public class DeviceMetricsSetModelServiceImpl extends ServiceImpl<DeviceMetricsSetModelMapper, DeviceMetricsSetModelPO> implements DeviceMetricsSetModelService {
    @Resource
    private DeviceMetricsSetModelConvert deviceMetricsSetModelConvert;

    /**
     * 查询
     *
     * @param deviceMetricsSetModelQueryDTO 设备度量集模型查询dto
     * @return {@link List }<{@link DeviceMetricsSetModelVO }>
     * <AUTHOR>
     * @date 2024/06/20
     */
    @Override
    public List<DeviceMetricsSetModelVO> query(DeviceMetricsSetModelQueryDTO deviceMetricsSetModelQueryDTO) {
        if (deviceMetricsSetModelQueryDTO == null || StringUtils.isBlank(deviceMetricsSetModelQueryDTO.getGroup())) {
            return new ArrayList<>();
        }
        QueryWrapper<DeviceMetricsSetModelPO> queryWrapper = Wrappers.query();
        LambdaQueryWrapper<DeviceMetricsSetModelPO> lambdaQueryWrapper = queryWrapper.lambda();
        lambdaQueryWrapper.eq(DeviceMetricsSetModelPO::getMetricsSetType, deviceMetricsSetModelQueryDTO.getType());
        lambdaQueryWrapper.eq(DeviceMetricsSetModelPO::getMetricsSetGroup, deviceMetricsSetModelQueryDTO.getGroup());
        List<DeviceMetricsSetModelPO> deviceMetricsSetModelPOS = baseMapper.selectList(lambdaQueryWrapper);
        return deviceMetricsSetModelConvert.convert(deviceMetricsSetModelPOS);
    }

    /**
     * 获取所有模板分组
     *
     * @return {@link List }<{@link EnumMetricsSetGroup }>
     * <AUTHOR>
     * @date 2024/06/20
     */
    @Override
    public List<ModelVO> queryModelGroup() {
        List<EnumMetricsSetGroup> modelTypes = EnumMetricsSetGroup.getAllModelType();
        List<ModelVO> result = new ArrayList<>();
        for (EnumMetricsSetGroup modelType : modelTypes) {
            ModelVO modelVO = new ModelVO();
            modelVO.setCode(modelType.getCode());
            modelVO.setName(modelType.getName());
            result.add(modelVO);
        }
        return result;
    }

    @Override
    public List<ModelVO> queryMetricsType() {
        List<EnumMetricsSetType> allTypes = EnumMetricsSetType.getAllTypes();
        List<ModelVO> result = new ArrayList<>();
        for (EnumMetricsSetType metricsType : allTypes) {
            ModelVO model = new ModelVO();
            model.setCode(metricsType.getCode());
            model.setName(metricsType.getDesc());
            result.add(model);
        }
        return result;
    }

    @Override
    public DeviceMetricsSetModelVO selectById(Long modelId) {
        DeviceMetricsSetModelPO deviceMetricsSetModelPO = baseMapper.selectById(modelId);
        return deviceMetricsSetModelConvert.convertVo(deviceMetricsSetModelPO);
    }

    /**
     * 查询可编辑指标  根据分组查询采集指标（actor为1的是属性，不能编辑）
     *
     * @param group 组
     * @return {@link List }<{@link DeviceMetricsSetModelVO }>
     * <AUTHOR>
     * @date 2024/06/27
     */
    @Override
    public List<DeviceMetricsSetModelVO> queryEditableMetrics(String group) {

        QueryWrapper<DeviceMetricsSetModelPO> queryWrapper = Wrappers.query();
        LambdaQueryWrapper<DeviceMetricsSetModelPO> lambdaQueryWrapper = queryWrapper.lambda();
        lambdaQueryWrapper.eq(DeviceMetricsSetModelPO::getMetricsSetGroup, group)
                .eq(DeviceMetricsSetModelPO::getMetricsSetType, EnumMetricsSetType.COLLECT)
                .eq(DeviceMetricsSetModelPO::getActor, MonitorMetricsConst.ACTOR_METRICS);
        List<DeviceMetricsSetModelPO> deviceMetricsSetModelPOS = baseMapper.selectList(lambdaQueryWrapper);
        return deviceMetricsSetModelConvert.convert(deviceMetricsSetModelPOS);

    }

    /**
     * 按组和类型选择
     *
     * @param group 组
     * @param type  类型
     * @return {@link List }<{@link DeviceMetricsSetModelVO }>
     * <AUTHOR>
     * @date 2024/07/01
     */
    @Override
    public List<DeviceMetricsSetModelVO> selectByGroupAndType(String group, String type) {
        LambdaQueryWrapper<DeviceMetricsSetModelPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceMetricsSetModelPO::getMetricsSetGroup, group)
                .eq(DeviceMetricsSetModelPO::getMetricsSetType, type);
        List<DeviceMetricsSetModelPO> deviceMetricsSetModelPOS = baseMapper.selectList(lambdaQueryWrapper);
        return deviceMetricsSetModelConvert.convert(deviceMetricsSetModelPOS);
    }


    /**
     * 按分组 类型 代码 查找 最多找到一个
     *
     * @param group 组
     * @param type  类型
     * @param code  密码
     * @return {@link DeviceMetricsSetModelVO }
     * <AUTHOR>
     * @date 2024/07/01
     */
    @Override
    public DeviceMetricsSetModelVO selectByGroupTypeCode(String group, String type, String code){
        LambdaQueryWrapper<DeviceMetricsSetModelPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceMetricsSetModelPO::getMetricsSetGroup, group)
                .eq(DeviceMetricsSetModelPO::getMetricsSetType, type)
                .eq(DeviceMetricsSetModelPO::getCode, code);
        DeviceMetricsSetModelPO deviceMetricsSetModelPO = baseMapper.selectOne(lambdaQueryWrapper,false);
        return deviceMetricsSetModelConvert.convertVo(deviceMetricsSetModelPO);
    }
}