package com.sansec.ccsp.device.devicegroup.response;

import lombok.Data;

import java.util.List;

/**
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-03
 * @Description: 设备组下拉列表
 */
@Data
public class DeviceGroupIdNameVO {
    private Long deviceGroupId;

    private String deviceGroupCode;

    private String deviceGroupName;

    /**
     * 是否支持业务调用;0：不支持；1：支持
     */
    private Integer isRest;
    /**
     * 网络协议;1：TCP；2：HTTPS
     */
    private Integer netPro;

    /**
     * 业务类型列表
     */
    private List<BusiTypeVO> busiTypeList;
}
