package com.sansec.ccsp.extra.securityconf.interceptor;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.sansec.ccsp.extra.business.constant.SecErrorCodeConstant;
import com.sansec.ccsp.extra.business.utils.HttpsServiceUtil;
import com.sansec.ccsp.extra.securityconf.annotation.HasAnyPermissions;
import com.sansec.ccsp.extra.securityconf.annotation.IgnoreToken;
import com.sansec.ccsp.extra.securityconf.context.TokenContext;
import com.sansec.ccsp.extra.securityconf.entity.LoginUser;
import com.sansec.ccsp.extra.securityconf.entity.jwt.Payload;
import com.sansec.ccsp.extra.securityconf.service.SecurityFrameworkService;
import com.sansec.ccsp.extra.securityconf.util.LoginUserUtil;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.utils.SM3Util;
import com.sansec.engine.sm.SM2Utils;
import com.sansec.redis.utils.RedisUtill;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * jwt信息拦截器
 *
 * <AUTHOR>
 */
@Slf4j
public class LoginInterceptor implements HandlerInterceptor {

    private static final String AUTH_HEADER = "X-SW-Authorization-Token";

    private static final String UNKNOWN_PARAM = "unknown";

    @Value("${task.ignoreToken:false}")
    private Boolean taskIgnoreToken;

    @Value("${key.jwt.publicKey}")
    private String publicKey;

    @Resource
    private RedisUtill redisUtill;

    @Resource
    HttpsServiceUtil httpsServiceUtil;

    @Resource
    private SecurityFrameworkService securityFrameworkService;




    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        //避免静态资源拦截
        boolean instance = handler instanceof HandlerMethod;
        if (!instance) {
            return true;
        }
        response.setContentType("application/json;charset=utf-8");
        //如果接口或者类上有@IgnoreToken注解，意思该接口不需要token就能访问，需要放行
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        //先从类上获取该注解，判断类上是否加了IgnoreToken ，代表不需要token，直接放行
        IgnoreToken annotation = handlerMethod.getBeanType().getAnnotation(IgnoreToken.class);
        if (annotation == null && method.isAnnotationPresent(IgnoreToken.class)) {
            annotation = method.getAnnotation(IgnoreToken.class);
            log.debug("请求方法 {} 上有注解 {} ", method.getName(), annotation);
        }
        //不需要登录的接口直接返回 ture
        if (annotation != null) {
            return true;
        }

        LoginUser loginUser = null;
        //获取请求头信息
        Map<String, String> headerMap = getHeaders(request);
        //如果header里面没有token，就从参数里面获取，仅考虑get请求
        if(headerMap.get(LoginInterceptor.AUTH_HEADER.toLowerCase(Locale.ROOT)) == null){
            String requestMethod = request.getMethod();
            if(requestMethod.equalsIgnoreCase("GET")){
                String token = request.getParameter("token");
                //get请求参数中如果存在token，就把token放入headerMap中
                if(StringUtils.isNotBlank(token)){
                    headerMap.put(LoginInterceptor.AUTH_HEADER.toLowerCase(Locale.ROOT),token);
                }
            }
        }

        //获取token
        String token = headerMap.get(LoginInterceptor.AUTH_HEADER.toLowerCase(Locale.ROOT));
        if (StringUtils.isNotEmpty(token)) {
            TokenContext.addCurrentToken(token);
        }

        //避免feign调用重复鉴权
        String loginUserInfoStr = headerMap.get(LoginUserUtil.LOGIN_USER_KEY.toLowerCase(Locale.ROOT));
        if (StringUtils.isNotEmpty(loginUserInfoStr)) {
            loginUserInfoStr = new String(java.util.Base64.getDecoder().decode(loginUserInfoStr), StandardCharsets.UTF_8);
            loginUser = JSON.parseObject(loginUserInfoStr, LoginUser.class);
        } else if (StringUtils.isNotEmpty(token)) {
            String hmac = SM3Util.digestWithHex(token);
            String key = String.format("ccsp:login:token:hmac:%s", hmac);
            Payload payload = redisUtill.strGet(key, Payload.class);
            if (payload == null) {
                throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
            } else {
                //token续期
                redisUtill.strSet(key, payload, getWebTokenExpireTime(), TimeUnit.MINUTES);
            }
            loginUser = payload;
        } else if (taskIgnoreToken && Boolean.parseBoolean(headerMap.get("ignore"))) {
            return true;
        } else {
            log.info("token信息不存在 {},request={} ", method.getName(), request);
            throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
        }
        LoginUser verifyLoginUser = verifyGetLoginUser(token);
        if (verifyLoginUser != null && loginUser == null) {
            loginUser = verifyLoginUser;
        }
        //设置ip
        if (StringUtils.isEmpty(loginUser.getIp())) {
            loginUser.setIp(getIP(request));
        }
        LoginUserUtil.addCurrentLoginUser(loginUser);
        //权限校验
        if (method.isAnnotationPresent(HasAnyPermissions.class)) {
            HasAnyPermissions hasAnyPermissions = method.getAnnotation(HasAnyPermissions.class);
            if (hasAnyPermissions != null && CollUtil.isNotEmpty(Arrays.asList(hasAnyPermissions.value()))) {
                boolean hasPermissions = securityFrameworkService.hasAnyPermissions(hasAnyPermissions.value());
                if (!hasPermissions) {
                    throw new BusinessException(SecErrorCodeConstant.NO_PERMISSION);
                }
                return true;
            }
        }
        return true;
    }

    public Integer getWebTokenExpireTime() {
        int webTokenExpireTime;
        String webTokenExpire=httpsServiceUtil.getConfigValueByConfigCode("web_token_expire");
        webTokenExpireTime = Integer.parseInt(webTokenExpire);
        return webTokenExpireTime;
    }

    /*
     * 模拟用户
     */
    public LoginUser mockUser() {
        LoginUser mockUser = new LoginUser();
        mockUser.setUserId(1L);
        mockUser.setUserName("mockUser");
        mockUser.setRoleId(1L);
        mockUser.setTenantId(1L);
        mockUser.setTenantCode("CCSP_TENANT");
        return mockUser;
    }

    /**
     * 避免内存泄露
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        LoginUserUtil.removeCurrentLoginUser();
        TokenContext.removeCurrentToken();
    }

    private Map<String, String> getHeaders(HttpServletRequest request) {
        Map<String, String> headerMap = new HashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        while (enumeration.hasMoreElements()) {
            String name = enumeration.nextElement().toLowerCase(Locale.ROOT);
            String value = request.getHeader(name);
            headerMap.put(name, value);
        }
        return headerMap;
    }

    /**
     * 校验token并返回loginuser
     * @param token
     * @return
     */
    public LoginUser verifyGetLoginUser(String token) {
        String[] jwtArray = token.split("\\.");
        if (jwtArray.length != 3) {
            throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
        }
        Payload payload;
        try {
            String payloadStr = Base64Decoder.decodeStr(jwtArray[1]);
            payload = JSON.parseObject(payloadStr, Payload.class);
            if (payload.getUserId() == null) {
                throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
            }

            String sourceDataStr2 = jwtArray[0] + "." + jwtArray[1];
            boolean verify = SM2Utils.verifySign(Base64.decodeBase64(publicKey), sourceDataStr2.getBytes(StandardCharsets.UTF_8), Base64.decodeBase64(jwtArray[2]));
            if (!verify) {
                throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
            }
        } catch (Exception e) {
            log.debug("parse jwt error: {}", e.getMessage());
            throw new BusinessException(SecErrorCodeConstant.TOKEN_VERIFY_ERROR);
        }
        return payload;
    }


    /***
     * 获取客户端ip地址
     * @param request
     */
    public static String getIP(final HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        String ipStr = request.getHeader("x-forwarded-for");
        if (StringUtils.isBlank(ipStr) || UNKNOWN_PARAM.equalsIgnoreCase(ipStr)) {
            ipStr = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ipStr) || UNKNOWN_PARAM.equalsIgnoreCase(ipStr)) {
            ipStr = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ipStr) || UNKNOWN_PARAM.equalsIgnoreCase(ipStr)) {
            ipStr = request.getRemoteAddr();
        }

        // 多个路由时，取第一个非unknown的ip
        final String[] arr = ipStr.split(",");
        for (final String str : arr) {
            if (!UNKNOWN_PARAM.equalsIgnoreCase(str)) {
                ipStr = str;
                break;
            }
        }
        //目的是将localhost访问对应的ip 0:0:0:0:0:0:0:1 转成 127.0.0.1。
        return ipStr.equals("0:0:0:0:0:0:0:1") ? "127.0.0.1" : ipStr;
    }
}

