package com.sansec.ccsp.app.business.servermanage.api;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sansec.ccsp.app.business.servermanage.entity.AppInfoPO;
import com.sansec.ccsp.app.business.servermanage.response.AppInfoVO;
import com.sansec.ccsp.app.common.constant.CommonConstant;
import com.sansec.ccsp.app.info.response.*;
import com.sansec.ccsp.app.business.servermanage.service.AppInfoService;
import com.sansec.ccsp.app.business.servermanage.service.AppToBusiTypeService;
import com.sansec.ccsp.app.common.chain.ChainHandler;
import com.sansec.ccsp.app.common.constant.ErrorCodeConst;
import com.sansec.ccsp.app.common.enums.EnumAuthType;
import com.sansec.ccsp.app.info.api.AppInfoApi;
import com.sansec.ccsp.app.info.request.*;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/2/18 14:07
 */
@Slf4j
@Validated
@RestController
public class AppInfoApiImpl implements AppInfoApi {

    @Autowired
    private ChainHandler appManagePasswordAdd;
    @Autowired
    private ChainHandler appManageDelete;
    @Autowired
    private ChainHandler chainHandlerUpdate;
    @Autowired
    private AppInfoService appInfoService;
    @Autowired
    private AppToBusiTypeService appToBusiTypeService;

    /**
     * 更新配置
     * @param appInfoAddDTO
     * @return
     */
    @Override
    public SecRestResponse<AppInfoBaseVO> add(@RequestBody AppInfoAddDTO appInfoAddDTO) {
        if (EnumAuthType.CA.getKey().equals(appInfoAddDTO.getAuthType())) {
            return ResultUtil.error(ErrorCodeConst.OPERATE_NOT_SUPPORT_ERROR);
        }
        return (SecRestResponse<AppInfoBaseVO>) appManagePasswordAdd.handle(appInfoAddDTO);
    }

    /**
     * 应用添加（非链式）
     * @param appInfoAddDTO
     * @return
     */
    @Override
    public SecRestResponse<AppInfoBaseVO> addApp(AppInfoAddDTO appInfoAddDTO) {
        if (EnumAuthType.CA.getKey().equals(appInfoAddDTO.getAuthType())) {
            return ResultUtil.error(ErrorCodeConst.OPERATE_NOT_SUPPORT_ERROR);
        }
        return appInfoService.addApp(appInfoAddDTO);
    }


    @Override
    public SecRestResponse<Object> delete(@RequestBody AppInfoDeleteDTO appInfoDeleteDTO){
        return (SecRestResponse<Object>) appManageDelete.handle(appInfoDeleteDTO);
//        return appManageServiceImpl.delete(appInfo);
    }

    /**
     * 删除应用（非链式）
     * @param appInfoDeleteDTO 删除的应用信息
     * @return 删除结果
     */
    @Override
    public SecRestResponse<Object> deleteApp(@RequestBody AppInfoDeleteDTO appInfoDeleteDTO){
        return appInfoService.deleteApp(appInfoDeleteDTO);
    }

    @Override
    public SecRestResponse<AppInfoBaseVO> update(@RequestBody AppInfoUpdateDTO appInfoUpdateDTO){
        return (SecRestResponse<AppInfoBaseVO>) chainHandlerUpdate.handle(appInfoUpdateDTO);
    }

    /**
     * 编辑应用（非链式）
     * @param appInfoUpdateDTO 编辑的应用信息
     * @return 编辑应用的结果
     */
    @Override
    public SecRestResponse<AppInfoBaseVO> updateApp(AppInfoUpdateDTO appInfoUpdateDTO) {
        return appInfoService.updateApp(appInfoUpdateDTO);
    }

    @Override
    public SecRestResponse<SecPageVO<AppInfoPageListVO>> pageList(@RequestBody AppInfoPageListDTO appInfoPageListDTO){
        return appInfoService.pageList(appInfoPageListDTO);
    }

    @Override
    public SecRestResponse<List<AppInfoPageListVO>> list(@RequestBody AppInfoPageListDTO appInfoListDTO) {
        return appInfoService.list(appInfoListDTO);
    }

    @Override
    public SecRestResponse<List<AppInfoDataVO>> query(AppInfoQueryDTO appInfoQueryDTO) {
        return appInfoService.queryInfo(appInfoQueryDTO);

    }

    @Override
    public SecRestResponse<AppInfoPageListVO> info(@RequestBody AppInfoDetailDTO appInfoDetailDTO) {
        return appInfoService.detail(appInfoDetailDTO);
    }

    /**
     * 查询带密钥的应用信息
     *
     * @param appInfoDetailDTO
     * @return
     */
    @Override
    public SecRestResponse<AppDetailVO> getAppDetail(@RequestBody AppInfoDetailDTO appInfoDetailDTO) {
        return appInfoService.getAppDetail(appInfoDetailDTO);
    }

    @Override
    public SecRestResponse<List<AppDetailVO>> getAppsByTenantId(@RequestBody List<Long> tenantIds){
        return appInfoService.getAppsByTenantId(tenantIds);
    }


    @Override
    public SecRestResponse<List<BusiTypeVo>> getBusiTypeByAppId(@RequestBody AppInfoDetailDTO appInfoDetailDTO) {
        SecRestResponse<AppInfoPageListVO> response = appInfoService.detail(appInfoDetailDTO);
        if (!response.isSuccess()) {
            return ResultUtil.error(response.getCode());
        }
        return ResultUtil.ok(response.getResult().getBusiTypeList());
    }

    @Override
    public SecRestResponse<Object> appBusiTypeManage(@RequestBody AppToBusiTypeDTO appToBusiTypeDTO){
        return appToBusiTypeService.appBusiTypeUpdate(appToBusiTypeDTO);
    }

    @Override
    public SecRestResponse<AppNumberVO> getAppNumByTenantId(@RequestBody AppNumberDTO appNumberDTO){
        return appInfoService.getAppNumByTenantId(appNumberDTO);
    }

    @Override
    public SecRestResponse<Map<Long,Integer>> getAppNumByTenantIds(List<Long> tenantIdList){
        return appInfoService.getAppNumByTenantIds(tenantIdList);
    }

    @Override
    public SecRestResponse<AppInfoBaseVO> addAppBusiType(@RequestBody  AppBusiTypeDTO appBusiTypeDTO){
        return appInfoService.addAppBusiType(appBusiTypeDTO);
    }

    @Override
    public SecRestResponse<AppInfoBaseVO> deleteAppBusiType(@RequestBody @Validated AppBusiTypeDTO appBusiTypeDTO){
        return appInfoService.deleteAppBusiType(appBusiTypeDTO);
    }

    @Override
    public SecRestResponse<Object> findAppExistByTenantId(AppInfoFindDTO appInfoFindDTO) {
        return appInfoService.findAppExistByTenantId(appInfoFindDTO);
    }

    /**
     * 根据区域ID查询应用列表
     * @param appInfoByRegionIdDTO
     * @return
     */
    @Override
    public SecRestResponse<List<AppInfoBaseVO>> findAppListByRegionId(AppInfoByRegionIdDTO appInfoByRegionIdDTO) {
        return appInfoService.findAppListByRegionId(appInfoByRegionIdDTO);
    }

    /**
     * 查询全部业务账号的id->code映射map
     *
     * @return
     */
    @Override
    public SecRestResponse<Map<Long, String>> getAppIdCodeMapAll() {
        LambdaQueryWrapper<AppInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);

        Map<Long, String> appId2NameMap = new HashMap<>();
        List<AppInfoPO> appInfoPOList = appInfoService.list(queryWrapper);
        if (appInfoPOList != null && !appInfoPOList.isEmpty()) {
            appId2NameMap = appInfoPOList.stream().collect(Collectors.toMap(AppInfoPO::getAppId, AppInfoPO::getAppCode));
        }

        return ResultUtil.ok(appId2NameMap);
    }

    /**
     * 查询全部业务账号的id->name映射map
     *
     * @return
     */
    @Override
    public SecRestResponse<Map<Long, String>> getAppIdNameMapAll() {
        LambdaQueryWrapper<AppInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppInfoPO::getInvalidFlag, CommonConstant.NOT_INVALID);

        Map<Long, String> appId2NameMap = new HashMap<>();
        List<AppInfoPO> appInfoPOList = appInfoService.list(queryWrapper);
        if (appInfoPOList != null && !appInfoPOList.isEmpty()) {
            appId2NameMap = appInfoPOList.stream().collect(Collectors.toMap(AppInfoPO::getAppId, AppInfoPO::getAppName));
        }

        return ResultUtil.ok(appId2NameMap);
    }

    /**
     * 根据业务类型返回当前租户下的应用基础信息列表
     *
     * @return
     */
    @Override
    public SecRestResponse<List<AppIdNameVO>> getAppIdNameListByBusiType(String busiTypeDTOJson) {
        return appInfoService.getAppIdNameListByBusiType(JSONObject.parseObject(busiTypeDTOJson, BusiTypeDTO.class));
    }
}
