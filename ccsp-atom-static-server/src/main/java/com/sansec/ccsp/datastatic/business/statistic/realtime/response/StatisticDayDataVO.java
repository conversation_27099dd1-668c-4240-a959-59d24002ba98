package com.sansec.ccsp.datastatic.business.statistic.realtime.response;

import lombok.Data;

import java.util.Date;

/**
 * @description : 按照天拆分实时数据（330）;
 * <AUTHOR> <PERSON><PERSON><PERSON>jiaw<PERSON>
 * @date : 2024-3-7
 */
@Data
public class StatisticDayDataVO{
    /**
     * ID
     */
    private Long id;
    /**
     * 拆分数据天时间(时间戳格式)
     */
    private String collectDateTime;

    /**
     * 拆分数据天时间
     */
    private String collectTime;
    /**
     * 调用次数
     */
    private Long callNum;
    /**
     * 区域标识
     */
    private String regionCode;
    /**
     * 租户标识
     */
    private String tenantCode;
    /**
     * 应用标识
     */
    private String appCode;
    /**
     * 服务IP
     */
    private String serviceIp;
    /**
     * 服务端口
     */
    private Integer servicePort;
    /**
     * 服务标识
     */
    private String serviceTypeCode;
    /**
     * 服务URL
     */
    private String serviceUrl;
    /**
     * 平均耗时
     */
    private Long avgCostTime;
    /**
     * 最大耗时
     */
    private Long maxCostTime;
    /**
     * 流量
     */
    private Long serviceFlowNum;
    /**
     * 流量(可读、带单位)
     */
    private String serviceFlowNumReadable;
    /**
     * 峰值（TPS）
     */
    private Long peakTps;
    /**
     * 峰值（Mbps）
     */
    private Double peakMbps;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}