package com.sansec.ccsp.extra.sysconf.handle;

import com.sansec.common.constants.SecBaseErrorCodeConstant;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 统一异常处理
 */
@Slf4j
@ControllerAdvice
public class ExceptionHandle {


    /**
     *
     * 统一异常处理方法,执行顺序
     * 抛出异常的方法 -> ExceptionHandle.handle()捕获{SecPlatException.class}抛出的异常,通过ResultUtil.error()将消息数据返回
     * @param e 异常对象
     * @return 返回错误对象
     */
    @ExceptionHandler(value = BusinessException.class)
    @ResponseBody
    public SecRestResponse<Object> handle(BusinessException e){
        //判断是否是继承自Exception.class的BusinessException.class,如果是,则将异常消息码返回给前台
        log.error("response:(code:{},message:{})",e.getCode(),e.getMessage());
        return ResultUtil.error(e.getCode(),null,e.getMessage());
    }

    /**
     *
     * 统一异常处理方法,执行顺序
     * 抛出异常的方法 -> ExceptionHandle.handle()捕获{Exception.class抛出的异常,通过ResultUtil.error()将消息数据返回
     * @param e 异常对象
     * @return 返回错误对象
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public SecRestResponse<Object> handle(Exception e){
        //判断是否是继承自Exception.class的SecPlatException.class,如果是,则将异常消息码返回给前台
        log.error("handle error",e);
        return ResultUtil.error(SecBaseErrorCodeConstant.UNKNOWN_ERROR);
    }

    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    @ResponseBody
    public SecRestResponse<Object> httpMessageNotReadableHandle(HttpMessageNotReadableException e) {
        log.error("请求参数错误：", e);
        return ResultUtil.error("0A130010", "参数数据类型转换异常");
    }

    /**
     * 仅仅捕捉校验所产生的异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public SecRestResponse<Object> methodArgumentNotValidHandler(MethodArgumentNotValidException exception) {
        log.error("请求参数错误：", exception);
        FieldError fieldError = exception.getBindingResult().getFieldError();
        return ResultUtil.error("0A130011", "参数错误：" + fieldError.getDefaultMessage());
    }

}
