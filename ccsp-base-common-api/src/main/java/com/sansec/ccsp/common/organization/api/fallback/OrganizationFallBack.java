package com.sansec.ccsp.common.organization.api.fallback;

import com.sansec.ccsp.common.organization.api.OrganizationApi;
import com.sansec.ccsp.common.organization.request.OrganizationInfoDTO;
import com.sansec.ccsp.common.organization.request.OrganizationInfoPageDTO;
import com.sansec.ccsp.common.organization.response.OrganizationIdNameVO;
import com.sansec.ccsp.common.organization.response.OrganizationInfoPageResult;
import com.sansec.ccsp.common.organization.response.OrganizationInfoVO;
import com.sansec.common.param.response.SecRestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class OrganizationFallBack implements FallbackFactory<OrganizationApi> {

    @Override
    public OrganizationApi create(Throwable throwable) {
        return new OrganizationApi() {

            /**
             * 分页查询
             *
             * @param organizationInfoPageDTO 筛选条件
             * @return 查询结果
             */
            @Override
            public SecRestResponse<List<OrganizationInfoVO>> find(OrganizationInfoPageDTO organizationInfoPageDTO) {
                log.error("分页查询异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            /**
             * 分页查询
             *
             * @param organizationInfoPageDTO 筛选条件
             * @return 查询结果
             */
            @Override
            public SecRestResponse<OrganizationInfoPageResult> findTree(OrganizationInfoPageDTO organizationInfoPageDTO) {
                log.error("树形分页查询异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            /**
             * 新增数据
             *
             * @param organizationInfoDTO 实例对象
             * @return 实例对象
             */
            @Override
            public SecRestResponse<Object> add(OrganizationInfoDTO organizationInfoDTO) {
                log.error("新增数据异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            /**
             * 更新数据
             *
             * @param organizationInfoDTO 实例对象
             * @return 实例对象
             */
            @Override
            public SecRestResponse<Object> edit(OrganizationInfoDTO organizationInfoDTO) {
                log.error("更新数据异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            /**
             * 通过主键删除数据
             *
             * @param organizationInfoDTO 实例对象
             * @return 实例对象
             */
            @Override
            public SecRestResponse<Object> deleteById(OrganizationInfoDTO organizationInfoDTO) {
                log.error("通过主键删除数据异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            /**
             * 通过主键查询
             *
             * @param organizationId id
             * @return 实例对象
             */
            @Override
            public SecRestResponse<OrganizationInfoVO> selectById(Long organizationId) {
                log.error("通过主键查询数据异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            /**
             * 通过主键查询
             *
             * @param organizationIds id
             * @return 实例对象
             */
            @Override
            public SecRestResponse<Map<Long, OrganizationInfoVO>> selectByIds(List<Long> organizationIds) {
                log.error("通过主键查询数据异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<List<OrganizationIdNameVO>> getOrganizationIdNameList() {
                log.error("获取单位基本数据异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

	        /**
	         * 加密所有组织数据
	         *
	         * @return
	         */
	        @Override
	        public SecRestResponse<Integer> encryptAllData() {
		        log.error("加密所有组织数据异常：", throwable);
		        return SecRestResponse.fallback(throwable);
	        }
        };
    }
}
