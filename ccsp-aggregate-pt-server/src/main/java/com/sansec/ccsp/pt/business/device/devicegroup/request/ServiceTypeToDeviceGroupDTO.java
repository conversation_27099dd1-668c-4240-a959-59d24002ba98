package com.sansec.ccsp.pt.business.device.devicegroup.request;

import lombok.Data;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description : 服务类型绑定设备组关联表;
 * @date : 2023-5-11
 */
@Data
public class ServiceTypeToDeviceGroupDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 服务类型ID
     */
    private Long serviceTypeId;
    /**
     * 设备组ID
     */
    private Long deviceGroupId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
}