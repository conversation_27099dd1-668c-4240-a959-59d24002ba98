-- 3.2.0 升级至 3.2.5 的升级脚本 例如新增字段、数据变动、表变动
INSERT INTO `config` VALUES (5467049690000640603, 0, 'screen_template_code', '大屏模板编号', 'default', 0, 1, 1, 1, '大屏模板编号', NULL,'2023-07-07 18:32:57', NULL, NULL);
INSERT INTO `config` VALUES (5467049690000640111, 0, 'quota_alarm_percentage', '大屏配额告警百分比', '50', 0, 1, 1, 1, '大屏配额告警百分比值为：0-100', NULL,'2023-07-07 18:32:57', NULL, NULL);
INSERT INTO `config` VALUES (5467049690000640222, 0, 'screen_quota_scroll_flag', '大屏滚动告警编是否开启', 'true', 0, 1, 1, 1, '大屏滚动告警编是否开启：true false', NULL,'2023-07-07 18:32:57', NULL, NULL);
INSERT INTO `config` VALUES (5467049690000640333, 0, 'db_init_check_time', '数据库初始化结果查询最大时间(秒)', '300', 0, 0, 0, 1, '数据库初始化结果查询最大时间(秒)', NULL,'2023-07-07 18:32:57', NULL, NULL);
-- 添加监控指标
INSERT INTO `dic_statistic`
VALUES (23, 'secdb', 2, '数据库加密密钥数量', 'secDbKeyNumStatistic ', 1, 0, '.1.3.6.1.4.1.746376.2.1.0', '个');
-- 添加增量指标计算单位
INSERT INTO `config` VALUES (5492748794601407007, 9, 'incre_cal_compute_time_util', '增量计算时间单位', 'minute', 0, 0, 0, 53, 'hour,minute,second', NULL, NULL, NULL, NULL);
-- 删除增量定时配置(改用时间戳)
DELETE FROM config WHERE CONFIG_ID = 5492748794601406993;
DELETE FROM config WHERE CONFIG_ID = 5492748794601407007;
DELETE FROM config WHERE CONFIG_ID = 5492748794601407008;


-- 配置正则表

-- ----------------------------
-- Table structure for config_regular
-- ----------------------------
DROP TABLE IF EXISTS `config_regular`;
CREATE TABLE `config_regular`  (
   `ID` bigint(20) NOT NULL COMMENT 'ID',
   `CONFIG_CODE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '配置编码',
   `REGULAR` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '正则表达式',
   `INVALID_FLAG` int(11) NULL DEFAULT 0 COMMENT '是否作废;默认为0',
   `REMARK` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
   `CREATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
   `CREATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
   `UPDATE_BY` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
   `UPDATE_TIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新时间',
   PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '配置表正则校验' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of config_regular
-- ----------------------------
INSERT INTO `config_regular` VALUES (1, 'openAuthCodeLogin', '^(true)|(false)$', 0, '是否开启口令登录，true\false', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (2, 'defaultAuthCode', NULL, 1, '默认口令', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (3, 'openUKeyLogin', '^(true)|(false)$', 0, '是否开启UKey登录,，true\false', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (4, 'authCodeHistoryLimit', '^([1-9]|[1-9][0-9]|100)$', 0, '历史口令限制，[1,100]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (5, 'unloggedTime', '^([1-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '长时间未登录禁用租户，[1,365]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (6, 'authCodeExpireDate', '^([1-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '口令有效期，[1,365]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (7, 'authCodeExpirationReminder', '^([1-9]|[1-2][0-9]|30)$', 0, '口令有效期告警，[1,30]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (8, 'loginFailuresAllowedTimes', '^([3-9]|[1-2][0-9]|30)$', 0, '登陆失败次数限制，[3,30]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (9, 'loginErrorLockTime', '^([5-9]|[1-2][0-9]|30)$', 0, '登陆失败锁定时长(分钟)，[5,30]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (10, 'forceUpdatePassword', '^(true)|(false)$', 0, '是否强制修改默认口令，true\false', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (11, 'screen_template_code', '^(default)$', 1, '大屏模板编号', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (12, 'quota_alarm_percentage', '^([1-9]|[1-9][0-9]|100)$', 0, '大屏配额告警百分比，[1,100]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (13, 'screen_quota_scroll_flag', '^(true)|(false)$', 0, '大屏滚动告警是否开启，true\false', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (14, 'platfrom_alarm_day', '^([7-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '平台许可告警天数，[7,365]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (15, 'server_alarm_day', '^([7-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360|361|363|362|364|365))$', 0, '服务许可告警天数，[7,365]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (16, 'api_token_expire', '^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|1[0-9][0-9][0-7][0-9])|10080+$', 0, '接口token有效期，[1,10080]', NULL, NULL, NULL, NULL);
INSERT INTO `config_regular` VALUES (17, 'web_token_expire', '^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|1[0-9][0-9][0-7][0-9])|10080+$', 0, '页面token有效期，[1,10080]', NULL, NULL, NULL, NULL);
