package com.sansec.ccsp.pt.business.common.config.controller;


import com.sansec.ccsp.common.config.request.ConfigPageDTO;
import com.sansec.ccsp.common.config.request.EditConfigDTO;
import com.sansec.ccsp.common.config.response.ConfigVO;
import com.sansec.ccsp.pt.business.statistic.log.aspect.OperateManageLog;
import com.sansec.ccsp.pt.business.common.config.service.CommonConfigService;
import com.sansec.ccsp.security.annotation.HasAnyPermissions;
import com.sansec.ccsp.security.annotation.IgnoreToken;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 配置Controller
 * @CreateTime: 2023-05-05
 * @Author: wangjunjie
 */
@RequestMapping("/common/api/config/v1")
@RestController
public class CommonConfigController {

    @Resource
    private CommonConfigService commonConfigService;

    /**
     * @param configCode 配置编码
     * @return 配置值
     * @Description: 查询配置
     */
    @PostMapping(value = "/getConfigValueByConfigCode/{configCode}")
    @IgnoreToken
    public SecRestResponse<String> getConfigValueByConfigCode(@PathVariable(value = "configCode") String configCode) {
        return ResultUtil.ok(commonConfigService.getConfigValueByConfigCode(configCode));
    }

    /**
     * 批量获取配置值
     */
    @PostMapping(value = "/getConfigByCodeList")
    @IgnoreToken
    public SecRestResponse<List<ConfigVO>> getConfigByCodeList(@RequestBody List<String> configCodeList) {
        return ResultUtil.ok(commonConfigService.getConfigByCodeList(configCodeList));
    }

    /**
     * @return 配置值
     * @Description: 获取配置信息
     */
    @PostMapping(value = "/getSysOperation")
    @IgnoreToken
    public SecRestResponse<List<ConfigVO>> getSysOperation() {
        return commonConfigService.getSysOperation();
    }



    /**
     *
     * @return
     * @Description: 获取人员管理配置
     */
    @PostMapping("/getUserConfig")
    @HasAnyPermissions("common:userManagerConfig:list")
    public SecRestResponse<List<ConfigVO>> getUserConfig() {
        return commonConfigService.getUserConfig();
    }

    /**
     *
     * @return
     * @Description: 获取系统配置
     */
    @PostMapping("/getSystemConfig")
    @HasAnyPermissions("common:sysConfig:list")
    public SecRestResponse<SecPageVO<ConfigVO>> getSystemConfig() {
        return commonConfigService.getSystemConfig();
    }

    /**
     * @param configPageDTO
     * @return
     * @Description: 列表查询系统配置
     */
    @PostMapping("/find")
    public SecRestResponse<List<ConfigVO>> find(@RequestBody @Validated ConfigPageDTO configPageDTO) {
        return commonConfigService.find(configPageDTO);
    }

    /**
     * @Description: 分页查询配置
     */
    @PostMapping("/findConfig")
    public SecRestResponse<SecPageVO<ConfigVO>> findConfig(@RequestBody @Validated ConfigPageDTO configPageDTO) {
        return commonConfigService.findConfig(configPageDTO);
    }

    /**
     * @param editConfigDTO
     * @return
     * @Description: 编辑系统配置
     */
    @OperateManageLog(module = "配置管理",desc = "编辑系统配置")
    @PostMapping("/editConfig")
    @HasAnyPermissions("common:sysConfig:edit")
    public SecRestResponse<Object> editConfig(@RequestBody @Validated EditConfigDTO editConfigDTO) {
        return commonConfigService.editConfig(editConfigDTO);
    }

    @OperateManageLog(module = "配置管理",desc = "清除区域配置")
    @PostMapping("/setRegionConfigFalse")
    @HasAnyPermissions("common:sysConfig:edit")
    public SecRestResponse<Object> setRegionConfigFalse() {
        return commonConfigService.setRegionConfigFalse();
    }

    @PostMapping("/isRegionMode")
    public SecRestResponse<Boolean> isRegionMode() {
        return ResultUtil.ok(commonConfigService.isRegionMode());
    }

}
