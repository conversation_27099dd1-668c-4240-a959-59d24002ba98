package com.sansec.ccsp.pt.business.servicemgt.serviceinfo.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.sansec.ccsp.common.config.api.CommonConfigServiceApi;
import com.sansec.ccsp.common.config.response.ConfigVO;
import com.sansec.ccsp.common.dic.api.DicScriptPathApi;
import com.sansec.ccsp.common.dic.request.DicScriptPathSeekValueDTO;
import com.sansec.ccsp.common.enums.ProxyRouteTypeEnum;
import com.sansec.ccsp.device.devicegroup.api.DeviceGroupServiceApi;
import com.sansec.ccsp.device.devicegroup.request.DeviceGroupFindListDTO;
import com.sansec.ccsp.device.devicegroup.response.DeviceGroupVO;
import com.sansec.ccsp.pt.business.common.config.service.CommonConfigService;
import com.sansec.ccsp.pt.business.common.menu.service.impl.ServiceMenuLoginUrlTask;
import com.sansec.ccsp.pt.business.device.devicegroup.response.DeviceAndGroupVo;
import com.sansec.ccsp.pt.business.device.devicegroup.service.DeviceGroupService;
import com.sansec.ccsp.pt.business.device.devicegroup.service.impl.ServiceAutoBindDeviceServiceImpl;
import com.sansec.ccsp.pt.business.docker.entity.DockerImagePO;
import com.sansec.ccsp.pt.business.docker.response.DockerImageVO;
import com.sansec.ccsp.pt.business.docker.service.DockerImageService;
import com.sansec.ccsp.pt.business.fileinfo.entity.FileInfoPO;
import com.sansec.ccsp.pt.business.fileinfo.service.FileInfoService;
import com.sansec.ccsp.pt.business.license.service.LicenseUseService;
import com.sansec.ccsp.pt.business.license.service.LicenseUseTimeService;
import com.sansec.ccsp.pt.business.pub.request.CheckUpgradeAuthCodeDTO;
import com.sansec.ccsp.pt.business.pub.service.PubKeyService;
import com.sansec.ccsp.pt.business.quota.service.TenantQuotaInfoService;
import com.sansec.ccsp.pt.business.region.service.RegionService;
import com.sansec.ccsp.pt.business.servicemgt.enums.DeployMod;
import com.sansec.ccsp.pt.business.servicemgt.gateway.request.ServiceGatewayDTO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.request.ServiceGatewayRouteDTO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.request.UpdateRouteDTO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.response.ServiceGatewayRouteVO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.response.ServiceGatewayVO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.service.ServiceGatewayRouteService;
import com.sansec.ccsp.pt.business.servicemgt.gateway.service.ServiceGatewayService;
import com.sansec.ccsp.pt.business.servicemgt.proxyroute.request.ProxyRouteDTO;
import com.sansec.ccsp.pt.business.servicemgt.proxyroute.service.ProxyRouteService;
import com.sansec.ccsp.pt.business.servicemgt.remote.constant.RemoteConstant;
import com.sansec.ccsp.pt.business.servicemgt.remote.request.*;
import com.sansec.ccsp.pt.business.servicemgt.remote.response.RemoteSysStatusCheckVo;
import com.sansec.ccsp.pt.business.servicemgt.remote.service.RegionRemoteAdapterService;
import com.sansec.ccsp.pt.business.servicemgt.servicegroup.service.ServiceGroupService;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.convert.InterInvokerConvert;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.entity.DatabaseUpgradeRecordPO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.request.ServiceAddContext;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.request.ServiceBindServiceGroupDTO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.request.ServiceEditDTO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.request.ServiceUpgradeDTO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.response.ServiceTenantVO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.service.ServiceInfoPortsService;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.service.ServiceInfoService;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.entity.ServiceInterfaceApiRecordPO;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request.*;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.service.InterInvokeService;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.service.ServiceFactory;
import com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.service.ServiceInterfaceApiRecordService;
import com.sansec.ccsp.pt.business.servicemgt.servicetype.service.ServiceTypeService;
import com.sansec.ccsp.pt.business.statistic.monitor.service.MonitorConnectionService;
import com.sansec.ccsp.pt.business.tenant.entity.TenantPO;
import com.sansec.ccsp.pt.business.tenant.mapper.TenantMapper;
import com.sansec.ccsp.pt.business.tenant.response.TenantToServiceGroupVO;
import com.sansec.ccsp.pt.business.tenant.response.TenantVO;
import com.sansec.ccsp.pt.business.tenant.service.TenantToServiceGroupService;
import com.sansec.ccsp.pt.common.constant.CommonConstant;
import com.sansec.ccsp.pt.common.constant.ScriptConstant;
import com.sansec.ccsp.pt.common.constant.ServiceInfoConstant;
import com.sansec.ccsp.pt.common.docker.DockerUtils;
import com.sansec.ccsp.pt.common.enums.*;
import com.sansec.ccsp.pt.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.pt.common.util.HttpUtils;
import com.sansec.ccsp.pt.common.util.Utils;
import com.sansec.ccsp.security.entity.LoginUser;
import com.sansec.ccsp.security.util.LoginUserUtil;
import com.sansec.ccsp.servicemgt.dbmgt.api.DbUnitServiceApi;
import com.sansec.ccsp.servicemgt.dbmgt.api.DbUnitToGroupServiceApi;
import com.sansec.ccsp.servicemgt.dbmgt.request.FindByGroupAndTypeDTO;
import com.sansec.ccsp.servicemgt.dbmgt.response.DbInfoByGroupAndTypeVO;
import com.sansec.ccsp.servicemgt.servicegroup.api.ServiceGroupServiceApi;
import com.sansec.ccsp.servicemgt.servicegroup.request.ServiceGroupInfoDTO;
import com.sansec.ccsp.servicemgt.servicegroup.request.ServiceInGroupDTO;
import com.sansec.ccsp.servicemgt.servicegroup.response.ServiceGroupVO;
import com.sansec.ccsp.servicemgt.serviceinfo.api.ServiceInfoServiceApi;
import com.sansec.ccsp.servicemgt.serviceinfo.request.*;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceInfoPageVO;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceInfoVO;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceStatusVO;
import com.sansec.ccsp.servicemgt.serviceport.api.ServiceDockerPortMappingApi;
import com.sansec.ccsp.servicemgt.serviceport.request.ServiceDockerPortMappingDTO;
import com.sansec.ccsp.servicemgt.serviceport.request.ServiceInfoAddPortsDTO;
import com.sansec.ccsp.servicemgt.serviceport.response.ServiceGroupDockerPortMappingVO;
import com.sansec.ccsp.servicemgt.servicetype.request.ServiceTypeDTO;
import com.sansec.ccsp.servicemgt.servicetype.response.ServiceTypeVO;
import com.sansec.ccsp.staticapi.monitor.request.MonitorConnectionDTO;
import com.sansec.ccsp.task.task.request.SysTaskDTO;
import com.sansec.ccsp.task.task.service.SysTaskService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import com.sansec.component.algorithm.utill.ComponentSynthesisEncryptionUtil;
import com.sansec.redis.lock.DistributedLock;
import com.sansec.sdk.docker.Docker;
import com.sansec.sdk.docker.api.container.request.ContainerCreateParam;
import com.sansec.sdk.docker.api.container.request.PortMapping;
import com.sansec.sdk.docker.api.container.response.ContainerCreateResult;
import com.sansec.sdk.docker.api.image.response.Image;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sansec.ccsp.pt.common.constant.RedisConsts.REDIS_KEY_ADD_VPN_PORT_RANGE;
import static com.sansec.ccsp.pt.common.constant.RedisConsts.REDIS_KEY_UPLOAD_IMAGES;
import static com.sansec.ccsp.pt.common.error.SecErrorCodeConstant.*;
import static com.sansec.sdk.docker.api.NetWorkProtocol.tcp;

/**
 * <AUTHOR>
 * @Description: 服务信息表;(SERVICE_INFO)表  服务实现类
 * @Date: 2023年2月21日
 */
@Service
@Slf4j
public class ServiceInfoServiceImpl implements ServiceInfoService {

    public static final String TASK_ID = "taskId";
    private static final String SERVICE_ID_KEY = "serviceId";

    private static final String IS_SHARE = "isShare";

    private static final String SERVICE_GROUP_ID = "serviceGroupId";

    private static final String DEVICE_IDS = "deviceIds";

    @Resource
    private ServiceInfoServiceApi serviceInfoServiceApi;
    @Resource
    private ServiceFactory serviceFactory;
    @Resource
    private TenantMapper tenantMapper;
    @Resource
    private DeviceGroupServiceApi deviceGroupServiceApi;
    @Resource
    private InterInvokerConvert interInvokerConvert;
    @Resource
    private ServiceGatewayRouteService serviceGatewayRouteService;
    @Resource
    private ServiceGatewayService serviceGatewayService;
    @Resource
    private SysTaskService sysTaskService;
    @Resource
    CommonConfigServiceApi configServiceApi;
    @Resource
    private ServiceTypeService serviceTypeService;
    @Resource
    private LicenseUseService licenseUseService;
    @Resource
    private LicenseUseTimeService licenseUseTimeService;
    @Resource
    private DeviceGroupService deviceGroupService;
    @Resource
    private RegionRemoteAdapterService remoteService;
    @Resource
    private MonitorConnectionService monitorConnectionService;
    @Resource
    private ServiceInterfaceApiRecordService serviceInterfaceApiRecordService;
    @Resource
    private ServiceGroupServiceApi serviceGroupServiceApi;
    @Resource
    private DbUnitToGroupServiceApi dbUnitToGroupServiceApi;
    @Resource
    private DicScriptPathApi dicScriptPathApi;
    @Resource
    private DbUnitServiceApi dbUnitServiceApi;
    @Resource
    private CommonConfigService commonConfigService;
    @Resource
    private ServiceGroupService serviceGroupService;
    @Resource
    private ServiceMenuLoginUrlTask serviceMenuLoginUrlTask;
    @Resource
    private RegionService regionService;
    @Resource
    private RegionRemoteAdapterService regionRemoteAdapterService;
    @Resource
    private ServiceAutoBindDeviceServiceImpl serviceAutoBindDeviceService;

    @Resource
    private PubKeyService pubKeyService;

    @Resource
    private TenantToServiceGroupService tenantToServiceGroupService;

    @Resource
    private DockerImageService dockerImageService;

    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(10, 10, 1, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10), new ThreadPoolExecutor.CallerRunsPolicy());

    @Value("${svs.pt.gateway}")
    private String svsPtGateWayUrl;

    @Resource
    private ServiceInfoPortsService serviceInfoPortsService;

    @Resource
    private ServiceDockerPortMappingApi serviceDockerPortMappingApi;

    @Resource
    private FileInfoService fileInfoService;

    @Resource
    private DistributedLock distributedLock;

    @Resource
    private TenantQuotaInfoService tenantQuotaInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecRestResponse<Object> initAdd(ServiceInfoDTO serviceInfoDTO) {

        ServiceAddContext serviceAddContext = new ServiceAddContext();

        serviceInfoDTO.setId(IdGenerator.ins().generator());

        //数据准备
        initAddPrepare(serviceInfoDTO, serviceAddContext);

        //3.3.1 先添加管控代理
        //添加区域管控代理
        this.initAddRemoteProxy(serviceInfoDTO);

        //调用【根据密码服务类型查询最新镜像】接口，判断是容器模式or进程模式
        //如果是容器模式,调用【获取可用端口】接口并修改serviceInfoDTO、serviceAddContext.serviceInfoVO中的端口信息，并填充其他容器信息
        this.containerPrepare(serviceAddContext,serviceInfoDTO);

        //数据校验
        initAddCheck(serviceInfoDTO, serviceAddContext);

        //添加区域密码服务代理
        this.initAddServiceProxy(serviceInfoDTO);

//        Long serviceGroupId = null;
//        if(serviceInfoDTO.boolShare()){
//            //如果是共享模式，需要保存一下服务组id
//        serviceGroupId = serviceInfoDTO.getServiceGroupId();
//        }

        //3.3.1 共享、独享都需要前端传服务组id
        Long serviceGroupId = serviceInfoDTO.getServiceGroupId();


        //保存服务信息表,操作状态初始化中
        serviceInfoDTO.setOperStatus(ServiceOperStatusEnum.RUN_INIT_ING.getId());
        //服务组值为空 当异步初始化失败时可以直接删除 删除的校验会校验服务组 当异步成功后再更新服务组
//        serviceInfoDTO.setServiceGroupId(null);

        //3.3.1  if(容器模式) 新增数据到service_docker_port_mapping表
        //3.3.1              保存容器信息和镜像信息到service_info表
        SecRestResponse<Object> response = serviceInfoServiceApi.add(new ServiceAddWrapper(interInvokerConvert.serviceTypeVoToDto(serviceAddContext.getServiceTypeVO()),serviceInfoDTO));

        //无法直接从租户信息中取得服务组id，需要从这里传进去
        serviceInfoDTO.setServiceGroupId(serviceGroupId);
        //开启服务添加异步任务
        initAddAsynTaskStart(serviceInfoDTO);

        return response;
    }

    /**
     * @param serviceInfoDTO
     * @param serviceAddContext
     * @description: 数据准备
     * @return: void
     */
    private void initAddPrepare(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
        //获取所有配置信息
        List<ConfigVO> configVOList = configServiceApi.getConfigVOAll();
        Map<String, String> configMap = configVOList.stream().collect(Collectors.toMap(ConfigVO::getConfigCode, ConfigVO::getConfigValue, (key1, key2) -> key2));
        serviceAddContext.setConfigMap(configMap);

        //初始化租户及组信息
        initTenantGroupInfo(serviceInfoDTO);

        //获取服务类型信息
        ServiceTypeDTO serviceTypeDTO = new ServiceTypeDTO();
        serviceTypeDTO.setId(serviceInfoDTO.getServiceTypeId());
        ServiceTypeVO serviceTypeVO = serviceTypeService.findById(serviceTypeDTO);
        serviceAddContext.setServiceTypeVO(serviceTypeVO);

        //初始化网关信息 网关校验
        initGateWayInfo(serviceInfoDTO, serviceTypeVO);

        ServiceInfoVO infoVO = interInvokerConvert.dtoToVo(serviceInfoDTO);
        serviceAddContext.setServiceInfoVO(infoVO);

        // 331 @weic 租户模式下 + 专享模式   需要判断 服务组的租户是否等于设备组的租户
        if (CommonConstant.SYS_OPERATION_MODE_MULTI.equals(serviceAddContext.getConfigMap().get(CommonConstant.SYS_OPERATION_MODE))
            && !serviceInfoDTO.boolShare()) {
            // 服务组id不能为空
            if (ObjectUtils.isEmpty(serviceInfoDTO.getServiceGroupId())) {
                throw new BusinessException(SecErrorCodeConstant.SERVICE_SERVICE_GROUP_ID_NULL);
            }
            // 设备组id不能为空
            if (ObjectUtils.isEmpty(serviceInfoDTO.getDeviceGroupId())) {
                throw new BusinessException(SecErrorCodeConstant.SERVICE_DEVICE_GROUP_ID_NULL);
            }
            ServiceGroupVO serviceGroupVO = serviceGroupService.getServiceGroupById(serviceInfoDTO.getServiceGroupId());
            DeviceGroupVO deviceGroupVO = deviceGroupService.getDeviceGroupInfoById(serviceInfoDTO.getDeviceGroupId());
            //
            if (serviceGroupVO==null || deviceGroupVO==null){
                throw new BusinessException(SINGLE_SERVICE_DEVICE_GROUP_TENANT_ERROR);
            }
            //
            serviceAddContext.setServiceGroupVO(serviceGroupVO);
            serviceAddContext.setDeviceGroupVO(deviceGroupVO);
            //
            if(!serviceGroupVO.getTenantId().equals(deviceGroupVO.getTenantId())){
                throw new BusinessException(SINGLE_SERVICE_DEVICE_GROUP_TENANT_ERROR);
            }
        }
    }

    /**
     * @param serviceInfoDTO
     * @param serviceAddContext
     * @description: 数据校验
     * @return: void
     */
    private void initAddCheck(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
        //多租户模式下
        if ("2".equals(serviceAddContext.getConfigMap().get(CommonConstant.SYS_OPERATION_MODE))) {
            //共享模式
            if (serviceInfoDTO.boolShare()) {
                //设备组id不能为空
                if (ObjectUtils.isEmpty(serviceInfoDTO.getServiceGroupId())) {
                    throw new BusinessException(SecErrorCodeConstant.SERVICE_SERVICE_GROUP_ID_NULL);
                }
                //服务组id不能为空
                if (ObjectUtils.isEmpty(serviceInfoDTO.getDeviceGroupId())) {
                    throw new BusinessException(SecErrorCodeConstant.SERVICE_DEVICE_GROUP_ID_NULL);
                }
                //非共享模式
            } else if (!serviceInfoDTO.boolShare() && ObjectUtils.isEmpty(serviceInfoDTO.getTenantId())) {
                //租户id不能为空
                throw new BusinessException(SecErrorCodeConstant.SERVICE_TENANT_ID_NULL);
            }
            //单租户模式下设备组id不可为空
        } else if ("1".equals(serviceAddContext.getConfigMap().get(CommonConstant.SYS_OPERATION_MODE)) && ObjectUtils.isEmpty(serviceInfoDTO.getDeviceGroupId())) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_DEVICE_GROUP_ID_NULL);
        }

        if (regionService.checkIsRegionMode() && null == serviceInfoDTO.getRegionId()){
            throw new BusinessException(SecErrorCodeConstant.GATEWAY_REGION_ID_NOT_NULL);
        }

        //服务名称不可重复
        ServiceInfoParamDTO serviceInfoParamDTO = new ServiceInfoParamDTO();
        serviceInfoParamDTO.setServiceName(serviceInfoDTO.getServiceName());
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.getServiceInfoByServiceParam(serviceInfoParamDTO);
        if (!CollectionUtils.isEmpty(res.getResult())) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_NAME_EXEIT);
        }

        //同ip端口校验  同服务类型同ip校验
        initAddPortCheck(serviceInfoDTO);

        //设备校验
        initAddDeviceCheck(serviceAddContext);

        //许可证校验
        if (CommonConstant.TRUE.equals(serviceAddContext.getConfigMap().get(CommonConstant.SERVICE_IS_BIND_LICENSE))) {
            SecRestResponse<Boolean> response = licenseUseService.isExistAvailableLicense();
            if (!response.isSuccess()) {
                throw new BusinessException(SecErrorCodeConstant.LICENSE_NOT_AVAILABLE);
            }
        }

        //根据服务组id和服务类型查询数据库信息方法校验
        initAddDbCheck(serviceInfoDTO);
    }

    /**
     * @param serviceAddContext
     * @description: 数据校验-设备校验并获取设备信息
     * @return: void
     */
    private List<DeviceInvokeDTO> initAddDeviceCheck(ServiceAddContext serviceAddContext) {
        List<DeviceInvokeDTO> list = new ArrayList<>();
        ServiceTypeVO serviceTypeVO = serviceAddContext.getServiceTypeVO();
        ServiceInfoVO infoVO = serviceAddContext.getServiceInfoVO();

        //1分配采用页面选择的设备组或资源组设备  2自动分配需要判断租户下是否存在可用设备,存在可用设备需要下发设备信息 3页面选择设备
        if (1 == serviceTypeVO.getConfigDeviceType()) {
            //采用页面选择的设备组或资源组设备 传入设备组id
            list = deviceGroupService.getDeviceInvokeList(infoVO.getDeviceGroupId(), null, null);
        } else if (2 == serviceTypeVO.getConfigDeviceType()) {
            //适配530华为情况 自动选择空闲设备 不传入设备组id
            list = deviceGroupService.getDeviceInvokeList(null, null, null);
        } else if (3 == serviceTypeVO.getConfigDeviceType()) {
            //服务类型的configDeviceType为3时页面选择设备信息 校验设备id是否存在
            if (CollectionUtils.isEmpty(infoVO.getDeviceIdList())) {
                throw new BusinessException(SecErrorCodeConstant.TENANT_DEVICE_GROUP_NOT_EXIST_DEVICE);
            }

            list = deviceGroupService.getDeviceInvokeList(null, null, infoVO.getDeviceIdList());
        }
        // 无空闲设备可用
        if (3 != serviceTypeVO.getConfigDeviceType() && CollectionUtils.isEmpty(list)) {
            throw new BusinessException(SecErrorCodeConstant.NO_FREE_DEVICE_AVAILABLE);
        }
        return list;
    }

    /**
     * @param serviceInfoDTO
     * @description: 根据服务组id和服务类型查询数据库信息方法校验
     * @return: com.sansec.ccsp.servicemgt.dbmgt.response.DbInfoByGroupAndTypeVO
     */
    private DbInfoByGroupAndTypeVO initAddDbCheck(ServiceInfoDTO serviceInfoDTO) {
        FindByGroupAndTypeDTO findByGroupAndTypeDTO = new FindByGroupAndTypeDTO();
        findByGroupAndTypeDTO.setServiceTypeId(serviceInfoDTO.getServiceTypeId());
        findByGroupAndTypeDTO.setServiceGroupId(serviceInfoDTO.getServiceGroupId());
        SecRestResponse<DbInfoByGroupAndTypeVO> response = dbUnitToGroupServiceApi.findByGroupAndType(findByGroupAndTypeDTO);
        if (!Utils.isExistVal(response)) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_NOT_BIND_DB);
        }
        return response.getResult();
    }

    /**
     * @param serviceInfo
     * @description: 启动服务添加异步任务
     * @return: void
     */
    private void initAddAsynTaskStart(ServiceInfoDTO serviceInfo) {
        SysTaskDTO sysTaskDTO = new SysTaskDTO();
        sysTaskDTO.setTaskId(IdGenerator.ins().generator());
        String taskName = "initAddAsynTask" + serviceInfo.getId();
        sysTaskDTO.setTaskName(taskName);
        sysTaskDTO.setTaskGroup("initAddAsynTask");
        JSONObject jo = new JSONObject();
        jo.put(TASK_ID, sysTaskDTO.getTaskId());
        jo.put(SERVICE_ID_KEY, serviceInfo.getId());
        jo.put(IS_SHARE,serviceInfo.getIsShare());
        jo.put(SERVICE_GROUP_ID,serviceInfo.getServiceGroupId());
        if (!CollectionUtils.isEmpty(serviceInfo.getDeviceIdList())) {
            jo.put(DEVICE_IDS, serviceInfo.getDeviceIdList());
        }
        String jsonParam = "serviceInfoServiceImpl.initAddAsynTask('" + jo.toJSONString() + "')";
        sysTaskDTO.setJsonParam(jsonParam);
        sysTaskService.add(sysTaskDTO);
    }

    @Override
    public void initAddAsynTask(String jsonParm) {
        log.info("服务添加异步任务-开启,jsonParm = {}", jsonParm);
        SecRestResponse<ServiceInfoVO> response = serviceInfoServiceApi.findById(ServiceInfoDTO.builder().id(Long.valueOf(JSON.parseObject(jsonParm).getString(SERVICE_ID_KEY))).build());
        ServiceInfoDTO serviceInfoDTO = interInvokerConvert.voToDto(response.getResult());
        String deviceIds = JSON.parseObject(jsonParm).getString(DEVICE_IDS);
        if (StringUtils.isNotBlank(deviceIds)) {
            List<Long> deviceIdList = JSON.parseObject(deviceIds, new TypeReference<List<Long>>() {
            });
            serviceInfoDTO.setDeviceIdList(deviceIdList);
        }

        serviceInfoDTO.setIsShare(Integer.parseInt(JSON.parseObject(jsonParm).getString(IS_SHARE)));
        String serviceGroupIdStr = JSON.parseObject(jsonParm).getString(SERVICE_GROUP_ID);
        if(StringUtils.isNotBlank(serviceGroupIdStr)){
            serviceInfoDTO.setServiceGroupId(Long.parseLong(serviceGroupIdStr));
        }
        InterInvokeService invokeService = serviceFactory.getService(serviceInfoDTO.getServiceTypeId());
        ServiceAddContext serviceAddContext = new ServiceAddContext();

        try {
            log.info("服务添加异步任务-数据准备");
            initAddPrepare(serviceInfoDTO, serviceAddContext);

            log.info("服务添加异步任务-数据展示,serviceInfoDTO = {}", serviceInfoDTO);

            //3.3.1 if(容器模式)
            //3.3.1   获取一下宿主机的负载情况
            //3.3.1   调用镜像管理获取密码子服务最新的【镜像信息】
            //3.3.1   if(宿主机没有最新的密码子服务镜像)
            //3.3.1      1.调用管控接口上传最新镜像
            //3.3.1      2.创建容器并返回【容器信息】
            this.handleContainerMod(serviceInfoDTO, serviceAddContext);


            log.info("服务添加异步任务-初始化参数");
            //3.3.1 docker exec /opt/sansec/xxx/setup/setup.sh info...  改remoteService中对应的方法
            initAddInitServiceParam(serviceInfoDTO, serviceAddContext, true);

            log.info("服务添加异步任务-初始化配置");
            //3.3.1 docker exec /opt/sansec/ccsp/initConfig.sh key value key... value...
            initAddInitServiceConfig(serviceInfoDTO, serviceAddContext);

            log.info("服务添加异步任务-初始化修改自启参数");
            //3.3.1 docker exec  sed -i '$a sh /opt/sansec/xxx/setup/startService.sh' /opt/sansec/ccsp/startService.sh
            initEditStartParam(serviceInfoDTO);

            log.info("服务添加异步任务-初始化启动");
            //3.3.1 docker exec /opt/sansec/ccsp/startService.sh
            initAddInitServiceStart(serviceAddContext.getServiceInfoVO());

            log.info("服务添加异步任务-调用服务接口");
            serviceAddInterInvoke(serviceInfoDTO, invokeService, serviceAddContext);

            log.info("服务添加异步任务-设备操作");
            serviceAddDeviceOper(serviceInfoDTO, serviceAddContext, invokeService);

            log.info("服务添加异步任务-许可证绑定");
            try {
                //许可证绑定
                bindLicense(serviceInfoDTO, serviceAddContext);
            } catch (Exception e) {
                log.error("服务添加异步任务-绑定许可证异常,serviceInfoDTO = {}", serviceInfoDTO, e);
                //异常回滚-设备删除方法
                serviceDeleteDeviceOper(serviceInfoDTO, serviceAddContext);
                throw new BusinessException(SecErrorCodeConstant.SERVICE_ADD_ERROR, true, e.getMessage());
            }

            try {
                log.info("服务添加异步任务-绑定路由");
                bindRoute(serviceInfoDTO, serviceAddContext);

                log.info("服务添加异步任务-添加服务所在服务器监控");
                serviceAddMonitorDevice(serviceAddContext);

                log.info("服务添加异步任务-更新服务信息");
                serviceInfoDTO.setOperStatus(ServiceOperStatusEnum.RUN.getId());
                serviceInfoServiceApi.edit(interInvokerConvert.dtoToEdit(serviceInfoDTO));

//                ServiceGroupVO serviceGroupVO = serviceAddContext.getServiceGroupVO();
//                //0：不包含，租户绑定共享组；1：包含，租户开通密码产品
//                String hasProduct = commonConfigService.getConfigValueByConfigCode(CommonConstant.HAS_PRODUCT);
//                String sysOperationMode = commonConfigService.getConfigValueByConfigCode(CommonConstant.SYS_OPERATION_MODE);
//                if("0".equals(hasProduct) && CommonConstant.SYS_OPERATION_MODE_MULTI.equals(sysOperationMode) && !serviceGroupVO.checkIsShare()){
//                    //系统不包含密码产品，多租户，专享服务组，满足这些条件，添加配额
//                    log.info("服务添加异步任务--配额");
//                    TenantQuotaInfoServiceDTO tenantQuotaInfoServiceDTO = new TenantQuotaInfoServiceDTO();
//                    tenantQuotaInfoServiceDTO.setTenantId(serviceInfoDTO.getTenantId());
//                    tenantQuotaInfoServiceDTO.setServiceCode(ServiceTypeEnum.byId(serviceInfoDTO.getServiceTypeId()).getCode());
//                    tenantQuotaInfoServiceDTO.setServiceGroupId(serviceInfoDTO.getServiceGroupId());
//                    tenantQuotaInfoService.addByTenantAndService(tenantQuotaInfoServiceDTO);
//                }

            } catch (Exception e) {
                log.error("服务添加异步任务后续配置异常,serviceInfoDTO = {}", serviceInfoDTO, e);
                //异常回滚-设备删除方法
                serviceDeleteDeviceOper(serviceInfoDTO, serviceAddContext);

                //异常回滚-解绑许可证
                unbindLicense(serviceInfoDTO, serviceAddContext);
                throw new BusinessException(SecErrorCodeConstant.SERVICE_ADD_ERROR, true, e.getMessage());
            }

            //刷新菜单
            try {
                serviceMenuLoginUrlTask.getServiceLoginMenu();
            } catch (Exception e) {
                log.error("刷新菜单异常initAddAsynTask,jsonParm = {}", jsonParm);
            }
        } catch (Exception e) {
            log.error("initAddAsynTask-服务添加异步任务异常,serviceInfoDTO={}", serviceInfoDTO, e);
            updateOperStatus(serviceInfoDTO.getId(), ServiceOperStatusEnum.RUN_ERROR.getId());
            // 释放出错服务到顶级租户
            serviceInfoServiceApi.releaseErrorService(new ServiceIdDTO(serviceInfoDTO.getId()));
            //报错异常信息入库 供页面展示
            recordPtOperationLog("服务初始化添加", serviceInfoDTO, e.getMessage());
            //3.3.1   暂时不删除失败容器(保留失败日志)和service_docker_port_mapping表数据(校验时用到),删除服务时再删除
        }
    }

    @Override
    public void serviceBindServiceGroup(ServiceBindServiceGroupDTO bindServiceGroupDTO) {
        log.info("服务切换服务组-校验已绑定服务组不可切换");
        ServiceInfoVO serviceInfoVO = getServiceVO(bindServiceGroupDTO.getServiceId());
        ServiceInfoDTO serviceInfoDTO = interInvokerConvert.voToDto(serviceInfoVO);
        serviceInfoDTO.setServiceGroupId(bindServiceGroupDTO.getServiceGroupId());
        InterInvokeService invokeService = serviceFactory.getService(serviceInfoDTO.getServiceTypeId());
        ServiceAddContext serviceAddContext = new ServiceAddContext();

        try {
            log.info("服务切换服务组-数据准备");
            initAddPrepare(serviceInfoDTO, serviceAddContext);

            log.info("服务切换服务组-数据展示,serviceInfoDTO = {},serviceAddContext = {}", serviceInfoDTO, serviceAddContext);

            log.info("服务切换服务组-初始化参数");
            initAddInitServiceParam(serviceInfoDTO, serviceAddContext, false);

            log.info("服务切换服务组-初始化启动");
            initAddInitServiceStart(serviceAddContext.getServiceInfoVO());

            log.info("服务切换服务组-调用服务接口");
            serviceAddInterInvoke(serviceInfoDTO, invokeService, serviceAddContext);
        } catch (Exception e) {
            log.error("serviceBindServiceGroup-服务切换服务组异常,serviceInfoDTO={}", serviceInfoDTO, e);
            //报错异常信息入库 供页面展示
            recordPtOperationLog("服务切换服务组", serviceInfoDTO, e.getMessage());
            throw new BusinessException(SecErrorCodeConstant.SERVICE_BIND_SERVICE_GROUP_ERROR, true, e.getMessage());
        }
    }

    /**
     * @param id
     * @return SecRestResponse<Object> 返回类型
     * @Description: 重置数据库密码 开启异步任务重新setup 重新启动
     */
    @Override
    public SecRestResponse<Object> resetDbAuthCode(Long id) {
        ServiceInfoParamDTO serviceInfoParamDTO = new ServiceInfoParamDTO();
        serviceInfoParamDTO.setId(id);
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.getServiceInfoByServiceParam(serviceInfoParamDTO);
        if (CollectionUtils.isEmpty(res.getResult())) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_NOT_EXEIST);
        }

        if(Objects.equals(ServiceOperStatusEnum.RUN_INIT_ING.getId(), res.getResult().get(0).getOperStatus())){
            throw new BusinessException(SecErrorCodeConstant.SERVICE_INIT_ING_DONT_RESET_DB_AUTH_ERROR);
        }

        //更新运行状态为初始化中
        updateOperStatus(id, ServiceOperStatusEnum.RUN_INIT_ING.getId());

        //开启异步任务
        resetDbAuthCodeAsynTaskStart(id);
        return ResultUtil.ok();
    }

    @Override
    public void resetDbAuthCodeTask(String jsonParm) {
        log.info("重置数据库密码异步任务-开启,jsonParm = {}", jsonParm);
        SecRestResponse<ServiceInfoVO> response = serviceInfoServiceApi.findById(ServiceInfoDTO.builder().id(Long.valueOf(JSON.parseObject(jsonParm).getString(SERVICE_ID_KEY))).build());
        ServiceInfoDTO serviceInfoDTO = interInvokerConvert.voToDto(response.getResult());
        ServiceAddContext serviceAddContext = new ServiceAddContext();

        try {
            log.info("重置数据库密码-数据准备");
            initAddPrepare(serviceInfoDTO, serviceAddContext);

            log.info("重置数据库密码-数据展示,serviceInfoDTO = {},serviceAddContext = {}", serviceInfoDTO, serviceAddContext);

            log.info("重置数据库密码-初始化参数");
            //TODO 3.3.1 docker exec xxx finish
            initAddInitServiceParam(serviceInfoDTO, serviceAddContext, false);

            log.info("重置数据库密码-初始化启动");
            //TODO 3.3.1 docker exec xxx finish
            initAddInitServiceStart(serviceAddContext.getServiceInfoVO());

            log.info("重置数据库密码-更新服务状态为运行中");
            updateOperStatus(serviceInfoDTO.getId(), ServiceOperStatusEnum.RUN.getId());
        } catch (Exception e) {
            log.error("resetDbAuthCodeTask-重置数据库密码异步任务异常,serviceInfoDTO={}", serviceInfoDTO, e);
            updateOperStatus(serviceInfoDTO.getId(), ServiceOperStatusEnum.RUN_ERROR.getId());
            //报错异常信息入库 供页面展示
            recordPtOperationLog("重置数据库密码", serviceInfoDTO, e.getMessage());
        }

    }

    /**
     * @param id
     * @description: 重置数据库密码添加异步任务
     * @return: void
     */
    private void resetDbAuthCodeAsynTaskStart(Long id) {
        SysTaskDTO sysTaskDTO = new SysTaskDTO();
        sysTaskDTO.setTaskId(IdGenerator.ins().generator());
        String taskName = "resetDbAuthCodeAsynTask" + id;
        sysTaskDTO.setTaskName(taskName);
        sysTaskDTO.setTaskGroup("resetDbAuthCodeAsynTask");
        JSONObject jo = new JSONObject();
        jo.put(TASK_ID, sysTaskDTO.getTaskId());
        jo.put(SERVICE_ID_KEY, id);
        String jsonParam = "serviceInfoServiceImpl.resetDbAuthCodeTask('" + jo.toJSONString() + "')";
        sysTaskDTO.setJsonParam(jsonParam);
        sysTaskService.add(sysTaskDTO);
    }


    /**
     * @param serviceInfoDTO
     * @param serviceAddContext
     * @param isInitJce         是否需要调用管控程序初始化设备 服务添加异步任务需要  服务切换服务组不需要因为设备没有换
     * @description: 服务添加异步任务-初始化参数
     * @return: void
     */
    private void initAddInitServiceParam(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext, Boolean isInitJce) {
        InitServiceParamDTO initParamDTO = new InitServiceParamDTO();
        try {
            log.info("服务初始化参数-获取数据库信息");
            initAddInitServiceParamDb(initParamDTO, serviceInfoDTO, serviceAddContext);

            log.info("服务初始化参数-获取网关信息");

            //1.区域模式给子服务下发的网关信息必须是区域下未经映射的网关信息(realip、realport)
            //2.非区域模式给子服务下发的网关信息直接取可用的网关信息
            ServiceGatewayVO serviceGatewayVO = serviceGatewayService.getAliveGateWay(serviceInfoDTO.getRegionId(), 2);
            String gatewayRealIp = StringUtils.isNotEmpty(serviceGatewayVO.getRealIp()) ? serviceGatewayVO.getRealIp() : serviceGatewayVO.getIp() ;
            Integer gatewayRealPort = serviceGatewayVO.getRealPort() != null ? serviceGatewayVO.getRealPort() : serviceGatewayVO.getPort();

            initParamDTO.setBusiGatewayIp(gatewayRealIp);
            initParamDTO.setBusiGatewayPort(gatewayRealPort);

            log.info("服务初始化参数-获取及操作密码机信息");
            initAddInitServiceParamDeviceOper(initParamDTO, serviceInfoDTO, serviceAddContext, isInitJce);

            log.info("服务初始化参数-调用管控服务初始化配置接口");
            String initParamScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                    builder().scriptCode(ScriptConstant.INIT_PARAM_SCRIPT).serviceTypeId(serviceInfoDTO.getServiceTypeId()).build()).getResult();
            initParamDTO.setRunScript(getScriptPath(serviceInfoDTO.getServiceTypeId(),initParamScript));
            if (ServiceTypeEnum.cryptoService.containsKey(serviceInfoDTO.getServiceTypeId())){
                //pki svs特殊处理 以后都需要下发多个网关 来实现高可用
                InitSvsServiceParamDTO svsParamDTO = getSvsParamDTO(initParamDTO);
                remoteService.initSvsParam(svsParamDTO,serviceInfoDTO);
            }else{
                remoteService.initParam(initParamDTO,serviceInfoDTO);
            }

        } catch (Exception e) {
            log.error("initAddInitServiceParam-服务初始化参数异常,serviceInfoDTO={}", serviceInfoDTO, e);
            throw new BusinessException(SecErrorCodeConstant.INIT_ADD_INIT_SERVICE_PARAM_ERROR, true, e.getMessage());
        }
    }

    public InitSvsServiceParamDTO getSvsParamDTO(InitServiceParamDTO commonParamDTO){
        InitSvsServiceParamDTO svsParamDTO = new InitSvsServiceParamDTO();
        svsParamDTO.setDbType(commonParamDTO.getDbType());
        svsParamDTO.setDbCase(commonParamDTO.getDbCase());
        svsParamDTO.setDbSchema(commonParamDTO.getDbSchema());
        svsParamDTO.setDbIp(commonParamDTO.getDbIp());
        svsParamDTO.setDbPort(commonParamDTO.getDbPort());
        svsParamDTO.setDbUser(commonParamDTO.getDbUser());
        svsParamDTO.setDbAuthCode(commonParamDTO.getDbAuthCode());
        svsParamDTO.setDeviceType(commonParamDTO.getDeviceType());
        svsParamDTO.setRunScript(commonParamDTO.getRunScript());
        svsParamDTO.setDbIpPort(commonParamDTO.getDbIpPort());

        StringBuilder ipSb=new StringBuilder();
        StringBuilder portSb=new StringBuilder();


        if (regionService.checkIsRegionMode()) {
            //查询区域下的业务网关
            ServiceGatewayDTO gatewayDTO = new ServiceGatewayDTO();
            gatewayDTO.setRegionId(commonParamDTO.getRegionId());
            gatewayDTO.setGatewayType(2);
            List<ServiceGatewayVO> gatewayVOList = serviceGatewayService.queryByParam(gatewayDTO);
            for (ServiceGatewayVO gateway : gatewayVOList) {
                ipSb.append(gateway.getRealIp()).append(",");
                portSb.append(gateway.getRealPort()).append(",");
            }
        } else {
            //非区域下取管理网关
            String[] svsPtGateway = svsPtGateWayUrl.split(",");
            for (String gateway : svsPtGateway) {
                String[] gatewayinfos = gateway.split(":");
                String ip = gatewayinfos[0];
                String port = gatewayinfos[1];
                ipSb.append(ip).append(",");
                portSb.append(port).append(",");
            }

        }
        ipSb.deleteCharAt(ipSb.length() - 1);
        portSb.deleteCharAt(portSb.length() - 1);
        svsParamDTO.setBusiGatewayIp(ipSb.toString());
        svsParamDTO.setBusiGatewayPort(portSb.toString());
        return svsParamDTO;
    }

    /**
     * @param serviceInfoDTO
     * @param serviceAddContext
     * @description: 服务添加异步任务-初始化配置-加解密配置下发
     * 密码机服务调用kms的形式 调用本机kms  2只调用远程kms   3先调本机kms报错再调远程kms
     * @return: void
     */
    private void initAddInitServiceConfig(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
        if (ServiceTypeEnum.cryptoService.containsKey(serviceInfoDTO.getServiceTypeId())) {
            InitServiceConfigDTO initServiceConfigDTO = new InitServiceConfigDTO();
            //只有共享模式下取配置,独享模式固定取"1"
            initServiceConfigDTO.setConfigList(Collections.singletonList(ServiceConfigDTO.builder().configCode(CommonConstant.CRYPTO_SERVICE_USE_KMS_TYPE)
                    .configValue(serviceInfoDTO.boolShare()?serviceAddContext.getConfigMap().get(CommonConstant.CRYPTO_SERVICE_USE_KMS_TYPE):"1").build()));
            String initConfigScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                    builder().scriptCode(ScriptConstant.INIT_CONFIG_SCRIPT).serviceTypeId(null).build()).getResult();
            initServiceConfigDTO.setRunScript(initConfigScript);
            String configScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                    builder().scriptCode(ScriptConstant.CONFIG_SCRIPT).serviceTypeId(null).build()).getResult();
            initServiceConfigDTO.setConfigScript(configScript);

            remoteService.initConfig(initServiceConfigDTO, serviceInfoDTO);
        }
    }

    private void initEditStartParam(ServiceInfoDTO serviceInfoDTO) {
        InitEditStartParamDTO initEditStartParamDTO = new InitEditStartParamDTO();
        String initEditStartParamScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                builder().scriptCode(ScriptConstant.INIT_EDIT_START_PARAM_SCRIPT).serviceTypeId(null).build()).getResult();
        initEditStartParamDTO.setEditScript(initEditStartParamScript);
        String editStartServiceParam = "sh " + dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                builder().scriptCode(ScriptConstant.INIT_START_SCRIPT).serviceTypeId(serviceInfoDTO.getServiceTypeId()).build()).getResult();
        initEditStartParamDTO.setStartServiceParam(editStartServiceParam);

        remoteService.initEditStartParam(initEditStartParamDTO, serviceInfoDTO);
    }

    /**
     * @param serviceInfoDTO
     * @param serviceAddContext
     * @param isInitJce         是否需要调用管控程序初始化设备
     * @description: 服务添加异步任务-初始化配置-设备相关操作
     * @return: void
     */
    private void initAddInitServiceParamDeviceOper(InitServiceParamDTO initParamDTO, ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext, Boolean isInitJce) {
        List<DeviceInvokeDTO> deviceList = initAddDeviceCheck(serviceAddContext);
        //电子签章需要调用管控服务初始化设备 不然它的业务服务起不来 下面轮询获取状态就获取不到正常的状态
        if (isInitJce && ServiceTypeEnum.TSC.getId().equals(serviceInfoDTO.getServiceTypeId())) {
            InitServiceParamDeviceDTO initServiceParamDeviceDTO = new InitServiceParamDeviceDTO();
            //随机给一台设备使其可以启动即可 后续还会重新下发设备信息
            initServiceParamDeviceDTO.setHsmList(Collections.singletonList(deviceList.get(0)));
            String initJceScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                    builder().scriptCode(ScriptConstant.INIT_JCE_SCRIPT).serviceTypeId(null).build()).getResult();
            initServiceParamDeviceDTO.setRunScript(initJceScript);
            String jceIniScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                    builder().scriptCode(ScriptConstant.JCE_INI_SCRIPT).serviceTypeId(serviceInfoDTO.getServiceTypeId()).build()).getResult();
            initServiceParamDeviceDTO.setIniScript(getScriptPath(serviceInfoDTO.getServiceTypeId(), jceIniScript));
            remoteService.initJce(initServiceParamDeviceDTO, serviceInfoDTO);
        }

        //1时填protocol=33554432  2时填protocol=50331648
        String protocol = deviceList.get(0).getProtocol();
        initParamDTO.setDeviceType("33554432".equals(protocol) ? 1 : 2);
    }

    private void initAddInitServiceParamDb(InitServiceParamDTO initParamDTO, ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
        DbInfoByGroupAndTypeVO dbVO = initAddDbCheck(serviceInfoDTO);
        initParamDTO.setDbType(dbVO.getDatabaseTypeCode());
        initParamDTO.setDbUser(ComponentSynthesisEncryptionUtil.decPwd(dbVO.getUnitUser()));
        initParamDTO.setDbAuthCode(ComponentSynthesisEncryptionUtil.decPwd(dbVO.getAuthCode()));
        initParamDTO.setRegionId(serviceInfoDTO.getRegionId());

        //mysql 只需要实例库名称 高斯 openguass kingbase需要实例库名称和模式名称 dm需要模式名称
        if ("mysql".equals(dbVO.getDatabaseTypeCode())) {
            initParamDTO.setDbCase(dbVO.getDatabaseUnitName());
            initParamDTO.setDbSchema("1");
        } else if ("gauss".equals(dbVO.getDatabaseTypeCode()) || "opengauss".equals(dbVO.getDatabaseTypeCode()) ||
                "kingbase".equals(dbVO.getDatabaseTypeCode())) {
            initParamDTO.setDbCase(dbVO.getCaseName());
            initParamDTO.setDbSchema(dbVO.getDatabaseUnitName());
        } else if ("dm".equals(dbVO.getDatabaseTypeCode())) {
            initParamDTO.setDbCase("1");
            initParamDTO.setDbSchema(dbVO.getDatabaseUnitName());
        }

        initParamDTO.setDbIp(dbVO.getDatabaseIp().trim().split(",")[0]);
        initParamDTO.setDbPort(Integer.parseInt(dbVO.getDatabasePort().trim().split(",")[0]));
        initParamDTO.setDbIpPort(getDbIpPort(dbVO.getDatabaseIp(), dbVO.getDatabasePort()));
        //连接数据库是否需要映射地址 若为false 则前端页面不展示映射地址并且后端不使用 若为true 前端填写必填 后端使用映射地址
        //哪些服务类型需要使用映射地址连接数据库
        String needDbMapIpServiceTypeId = serviceAddContext.getConfigMap().get(CommonConstant.NEED_DB_MAP_IP_SERVICE_TYPE_ID);
        if ("true".equals(serviceAddContext.getConfigMap().get(CommonConstant.IS_NEED_DB_MAP_IP)) && StringUtils.isNotBlank(needDbMapIpServiceTypeId)) {
            List<String> list = Arrays.asList(needDbMapIpServiceTypeId.trim().split(","));
            if (list.contains(serviceInfoDTO.getServiceTypeId().toString())) {
                if (StringUtils.isBlank(dbVO.getDatabaseMapIp())) {
                    throw new BusinessException(SecErrorCodeConstant.DB_MAP_IP_NOT_NULL);
                }
                initParamDTO.setDbIp(dbVO.getDatabaseMapIp().trim().split(",")[0]);
                initParamDTO.setDbPort(Integer.parseInt(dbVO.getDatabaseMapPort().trim().split(",")[0]));
                initParamDTO.setDbIpPort(getDbIpPort(dbVO.getDatabaseMapIp(), dbVO.getDatabaseMapPort()));
            }
        }
    }

    /**
     * @param dbIp
     * @param dbPort
     * @description: 获取ip和端口组合 ************:1000,************:1001
     * @return: java.lang.String
     */
    private String getDbIpPort(String dbIp, String dbPort) {
        StringBuilder dbIpPort = new StringBuilder();
        String[] ipArr = dbIp.trim().split(",");
        String[] portArr = dbPort.trim().split(",");
        for (int i = 0; i < ipArr.length; i++) {
            dbIpPort.append(ipArr[i]).append(":").append(portArr[i]).append(",");
        }
        dbIpPort.deleteCharAt(dbIpPort.length() - 1);
        return dbIpPort.toString();
    }

    private void initAddInitServiceStart(ServiceInfoVO infoVO) {
        log.info("服务初始化启动-调用管控服务初始化启动接口");
        InitServiceStartDTO initServiceStartDTO = new InitServiceStartDTO();
        String initStartScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                builder().scriptCode(ScriptConstant.INIT_START_SCRIPT).serviceTypeId(infoVO.getServiceTypeId()).build()).getResult();
        initServiceStartDTO.setRunScript(getScriptPath(infoVO.getServiceTypeId(), initStartScript));
        remoteService.initStart(initServiceStartDTO, infoVO);

        //轮询获取状态接口五分钟，每30秒获取一次服务状态接口，若接口返回正常状态则进行下一步，若三分钟还没有反正正常状态则失败退出
        boolean resultStatus = false;
        String errMsg = "";
        for (int i = 16; i > 0; i--) {
            try {
                Thread.sleep(30000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }

            //查询服务状态
            InterInvokeService service = serviceFactory.getService(infoVO.getServiceTypeId());
            SecRestResponse<Map<Long, Integer>> result = service.getServiceState(InvokeServiceStateDTO.builder().data(Lists.newArrayList(infoVO)).build());
            //获取状态不成功或者成功但状态异常 尝试再次启动服务
            if ((!result.isSuccess() || result.getResult() == null) || (result.isSuccess() && result.getResult() != null && 1 != result.getResult().get(infoVO.getId()))) {
                log.info(i + "-服务初始化启动-调用管控服务初始化启动接口-状态异常,infoVO={},result={}", infoVO,result);
                if(result.isSuccess() && result.getResult() != null && 2 == result.getResult().get(infoVO.getId())){
                    errMsg = "密码服务返回运行状态为2（失败）";
                }else{
                    errMsg = result.toString();
                }

            } else {
                resultStatus = true;
                break;
            }
        }
        log.info("服务初始化启动-调用管控服务初始化启动接口-循环调用查询状态接口判断结果,resultStatus={},infoVO={},errMsg={}", resultStatus, infoVO,errMsg);
        if (!resultStatus) {
            log.error("initAddInitServiceStart-服务初始化启动异常,infoVO={},errMsg={}", infoVO,errMsg);
            throw new BusinessException(SecErrorCodeConstant.INIT_ADD_INIT_SERVICE_START_ERROR,true,errMsg);
        }
    }

    private void updateOperStatus(Long serviceId, Integer operStatus) {
        EditOperStatusParamDTO editStatus = new EditOperStatusParamDTO();
        editStatus.setId(serviceId);
        editStatus.setOperStatus(operStatus);
        SecRestResponse<Object> serviceInfoVOSecRestResponse = serviceInfoServiceApi.editServiceOperStatus(editStatus);
        if (!serviceInfoVOSecRestResponse.isSuccess()) {
            log.error("更新服务操作状态失败updateOperStatus,serviceId = {}, serviceRunState = {}", serviceId, operStatus);
        }
    }

    /**
     * @param serviceTypeId
     * @param scriptPath
     * @description: 根据服务类型获取脚本路径
     * @return: java.lang.String
     */
    private String getScriptPath(Long serviceTypeId, String scriptPath) {
        return String.format(scriptPath, ServiceTypeScriptPathEnum.byId(serviceTypeId).getScriptPath());
    }

    private void serviceAddMonitorDevice(ServiceAddContext serviceAddContext) {

        ServiceInfoVO infoVO = serviceAddContext.getServiceInfoVO();

        ServiceTypeVO serviceTypeVO = serviceAddContext.getServiceTypeVO();
        ServiceInfoVO serviceInfoVO = serviceAddContext.getServiceInfoVO();
        //是否监控服务设备 1是 2否
        if (2 == serviceTypeVO.getIsMonitorDevice()) {
            return;
        }

        //调用监控服务接口新增监控设备
        MonitorConnectionDTO monitorConnectionDTO = new MonitorConnectionDTO();
        monitorConnectionDTO.setSourceType(1);
        monitorConnectionDTO.setDeviceType(Integer.valueOf(MonitorDeviceTypeEnum.NORMAL.getKey()));
        monitorConnectionDTO.setConnectionName(infoVO.getMgtIp() + "服务器");
        monitorConnectionDTO.setTenantCode(CommonConstant.TOP_TENANT_CODE);
        monitorConnectionDTO.setIp(infoVO.getMgtIp());
        monitorConnectionDTO.setProxyRoute(regionService.checkIsRegionMode());
        String gatewayUrl = "https://"+infoVO.getBusiGatewayIp()+":"+infoVO.getBusiGatewayPort();
        monitorConnectionDTO.setGatewayApiUrl(gatewayUrl);
        //除签章外其他服务监控端口填写web平台端口

        if (ServiceTypeEnum.TSC.getId().equals(infoVO.getServiceTypeId())) {
            //TODO 3.3.1 电子签章的nginx需要代理监控组件端口
            // 3.3.1 监控端口用serviceInfoVO的管理端口信息
            // 3.3.1 不能直接用serviceTypeVO.getMonitorPort 同步修改项目中的其他地方
            monitorConnectionDTO.setPort(serviceInfoVO.getMonitorPort());
        } else {
            // 3.3.1 监控端口用serviceInfoVO的管理端口信息
            monitorConnectionDTO.setPort(serviceInfoVO.getMgtPort());
        }
        monitorConnectionService.addDevice(monitorConnectionDTO);
    }

    /**
     * @param apiName
     * @param serviceInfoDTO
     * @param resultMsg
     * @description: 记录服务日志
     * @return: void
     */
    protected void recordPtOperationLog(String apiName, ServiceInfoDTO serviceInfoDTO, String resultMsg) {
        try {
            ServiceInterfaceApiRecordPO serviceInterfaceApiRecordPO = new ServiceInterfaceApiRecordPO();
            serviceInterfaceApiRecordPO.setId(IdGenerator.ins().generator());
            serviceInterfaceApiRecordPO.setServiceId(serviceInfoDTO.getId());
            serviceInterfaceApiRecordPO.setApiName(apiName);
            serviceInterfaceApiRecordPO.setIp(serviceInfoDTO.getMgtIp());
            serviceInterfaceApiRecordPO.setPort(serviceInfoDTO.getMgtPort());
            serviceInterfaceApiRecordPO.setResponseInfo(resultMsg);
            serviceInterfaceApiRecordPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
            serviceInterfaceApiRecordService.insert(serviceInterfaceApiRecordPO);
        } catch (Exception e) {
            log.error("记录服务日志异常recordOperationLog error: {}", e.getMessage(), e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecRestResponse<Object> add(ServiceInfoDTO serviceInfoDTO) {
        ServiceAddContext serviceAddContext = new ServiceAddContext();

        //校验准备
        serviceAddCheckPrepare(serviceInfoDTO, serviceAddContext);

        //服务接口调用工厂实现
        InterInvokeService invokeService = serviceFactory.getService(serviceInfoDTO.getServiceTypeId());

        //调用各个服务接口 下发公钥  初始化租户
        serviceAddInterInvoke(serviceInfoDTO, invokeService, serviceAddContext);

        //设备操作
        serviceAddDeviceOper(serviceInfoDTO, serviceAddContext, invokeService);

        try {
            //许可证绑定
            bindLicense(serviceInfoDTO, serviceAddContext);
        } catch (Exception e) {
            log.error("服务添加-绑定许可证异常,serviceInfoDTO = {}", serviceInfoDTO, e);
            //异常回滚-设备删除方法
            serviceDeleteDeviceOper(serviceInfoDTO, serviceAddContext);
            throw new BusinessException(SecErrorCodeConstant.SERVICE_ADD_ERROR, true, e.getMessage());
        }

        SecRestResponse<Object> response = null;

        try {
            //绑定路由
            bindRoute(serviceInfoDTO, serviceAddContext);

            //调用服务管理微服务保存
            response = serviceInfoServiceApi.add(serviceInfoDTO);

//            //更新租户状态
//            tenantService.updateTenantInit(serviceInfoDTO.getTenantId());
        } catch (Exception e) {
            log.error("服务添加异常,serviceInfoDTO = {}", serviceInfoDTO, e);
            //异常回滚-设备删除方法
            serviceDeleteDeviceOper(serviceInfoDTO, serviceAddContext);

            //异常回滚-解绑许可证
            unbindLicense(serviceInfoDTO, serviceAddContext);
            throw new BusinessException(SecErrorCodeConstant.SERVICE_ADD_ERROR, true, e.getMessage());
        }

        //异步任务 服务添加后续操作 1检查服务是否启动,没有启动尝试启动 2添加服务所在服务器监控 3添加配额
        serviceFollowOper(serviceInfoDTO);

        return response;
    }

    /**
     * @param serviceInfoDTO
     * @param serviceAddContext
     * @description: 校验准备
     * @return: void
     */
    private void serviceAddCheckPrepare(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
        //ip端口校验
        initAddPortCheck(serviceInfoDTO);

        //获取所有配置信息
        List<ConfigVO> configVOList = configServiceApi.getConfigVOAll();
        Map<String, String> configMap = configVOList.stream().collect(Collectors.toMap(ConfigVO::getConfigCode, ConfigVO::getConfigValue, (key1, key2) -> key2));
        serviceAddContext.setConfigMap(configMap);

        //许可证校验
        if (CommonConstant.TRUE.equals(serviceAddContext.getConfigMap().get(CommonConstant.SERVICE_IS_BIND_LICENSE))) {
            SecRestResponse<Boolean> response = licenseUseService.isExistAvailableLicense();
            if (!response.isSuccess()) {
                throw new BusinessException(SecErrorCodeConstant.LICENSE_NOT_AVAILABLE);
            }
        }

        //服务名称不可重复
        ServiceInfoParamDTO serviceInfoParamDTO = new ServiceInfoParamDTO();
        serviceInfoParamDTO.setServiceName(serviceInfoDTO.getServiceName());
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.getServiceInfoByServiceParam(serviceInfoParamDTO);
        if (!CollectionUtils.isEmpty(res.getResult())) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_NAME_EXEIT);
        }

        //初始化组信息 服务组id 设备组id 如果为空 按照租户id 查询出来组装进去
        initTenantGroupInfo(serviceInfoDTO);

        ServiceTypeDTO serviceTypeDTO = new ServiceTypeDTO();
        serviceTypeDTO.setId(serviceInfoDTO.getServiceTypeId());
        ServiceTypeVO serviceTypeVO = serviceTypeService.findById(serviceTypeDTO);
        serviceAddContext.setServiceTypeVO(serviceTypeVO);

        //初始化网关信息
        initGateWayInfo(serviceInfoDTO, serviceTypeVO);

        ServiceInfoVO infoVO = interInvokerConvert.dtoToVo(serviceInfoDTO);
        serviceAddContext.setServiceInfoVO(infoVO);

        serviceInfoDTO.setId(IdGenerator.ins().generator());
    }

    /**
     * @param serviceInfoDTO
     * @param serviceAddContext
     * @param invokeService
     * @description: 设备相关操作
     * @return: void
     */
    private void serviceAddDeviceOper(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext, InterInvokeService invokeService) {
        log.info("绑定设备信息serviceAddDeviceOper,serviceInfoDTO = {}", serviceInfoDTO);
        ServiceInfoVO infoVO = serviceAddContext.getServiceInfoVO();
        ServiceTypeVO serviceTypeVO = serviceAddContext.getServiceTypeVO();

        //1分配采用资源组设备  2自动分配需要判断租户下是否存在可用设备,存在可用设备需要下发设备信息 3页面选择设备
        if (1 == serviceTypeVO.getConfigDeviceType()) {
            //判断是否存在设备,服务启动依赖设备
            List<DeviceInvokeDTO> list = deviceExistCheck(infoVO);

            //绑定设备组的话需要调用接收设备接口
            bindDeviceByList(infoVO, invokeService, list);
        } else {
            DeviceAndGroupVo deviceAndGroup = null;
            if (2 == serviceTypeVO.getConfigDeviceType()) {
                //自动添加设备,内部包含设备不存在校验
                deviceAndGroup = deviceGroupService.addFreeDeviceToGroup(infoVO.getTenantId(), infoVO.getServiceTypeId(), null);
            } else if (3 == serviceTypeVO.getConfigDeviceType()) {
                //服务类型的configDeviceType为3时页面选择设备信息 校验设备id是否存在
                if (CollectionUtils.isEmpty(infoVO.getDeviceIdList())) {
                    throw new BusinessException(SecErrorCodeConstant.TENANT_DEVICE_GROUP_NOT_EXIST_DEVICE);
                }

                deviceAndGroup = deviceGroupService.addFreeDeviceToGroup(infoVO.getTenantId(), infoVO.getServiceTypeId(), infoVO.getDeviceIdList());
            } else {
                throw new BusinessException(SecErrorCodeConstant.SERVICE_TYPE_INFO_ERROR);
            }

            //缓存设备信息
            serviceInfoDTO.setDeviceGroupId(deviceAndGroup.getDeviceGroupId());
            serviceAddContext.setDeviceIds(deviceAndGroup.getDeviceIds());
            serviceAddContext.setServiceTypeToDeviceGroupId(deviceAndGroup.getServiceTypeToDeviceGroupId());

            //根据设备组id获取设备信息
            List<DeviceInvokeDTO> list = deviceGroupService.getDeviceInvokeListByGroupId(deviceAndGroup.getDeviceGroupId());

            //下发设备信息
            try {
                //当前服务设备下发
                bindDeviceByList(infoVO, invokeService, list);
                //同设备组的服务设备下发
                bindDeviceByDeviceGroupId(deviceAndGroup.getDeviceGroupId(), list);
                //添加服务自动绑定设备关联关系
                serviceAutoBindDeviceService.bathInsert(serviceInfoDTO.getId(), deviceAndGroup.getDeviceIds());
            } catch (Exception e) {
                log.error("自动添加设备-下发设备信息异常-serviceAddDeviceOper,serviceInfoDTO = {}", serviceInfoDTO);
                //异常回滚-设备删除方法
                serviceDeleteDeviceOper(serviceInfoDTO, serviceAddContext);
                throw new BusinessException(SecErrorCodeConstant.RECEIVE_DEVICE_ERROR);
            }
        }
    }

    /**
     * @param serviceInfoDTO
     * @param serviceAddContext
     * @description: 设备删除相关操作
     * @return: void
     */
    private void serviceDeleteDeviceOper(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
        log.info("回滚设备信息serviceDeleteDeviceOper,serviceInfoDTO = {}, serviceAddContext-deviceId = {}", serviceInfoDTO, serviceAddContext.getDeviceIds());
        if (CommonConstant.FALSE.equals(serviceAddContext.getConfigMap().get(CommonConstant.ADD_SERVICE_IS_AUTO_ADD_DEVICE))) {
            return;
        }
        //异常回滚-设备删除方法
        try {
            deviceGroupService.delDeviceToPt(serviceInfoDTO.getTenantId(), serviceInfoDTO.getDeviceGroupId(),
                    serviceAddContext.getDeviceIds(), serviceAddContext.getServiceTypeToDeviceGroupId());
        } catch (Exception e) {
            log.error("异常回滚-设备删除方法异常serviceDeleteDeviceOper, serviceAddContext-deviceId = {}", serviceAddContext.getDeviceIds());
        }
    }

    private void serviceAddInterInvoke(ServiceInfoDTO serviceInfoDTO, InterInvokeService invokeService, ServiceAddContext serviceAddContext) {

        log.info("服务添加异步任务-调用服务接口-下发公钥");
        configAuthPk(serviceInfoDTO, invokeService, serviceAddContext);

        log.info("服务添加异步任务-调用服务接口-初始化租户");
        initTenantUserWithShare(serviceInfoDTO, invokeService);

        //下发配置 给kms、密码机服务主要是下发网关的ip与写死的端口
//        pushDeviceConfig(infoVO, invokeService);

    }


    /**
     * 同ip端口校验  同服务类型同ip校验
     *
     * @param serviceInfoDTO
     */
    private void initAddPortCheck(ServiceInfoDTO serviceInfoDTO) {
        //同ip端口校验
        SecRestResponse<Boolean> ipPortExist = serviceInfoServiceApi.findIpPortExist(serviceInfoDTO);
        Boolean result = ipPortExist.getResult();
        if (result) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_ADD_PORT_EXIST);
        }

        // 进程模式
        if(serviceInfoDTO.getDeployMod() == DeployMod.PID.getValue()){
            if (serviceInfoDTO.getMgtPort() == null ){
                throw new BusinessException(REQ_PARAM_ERROR,true,"管理端口不可为空");
            }
            if (serviceInfoDTO.getBusiPort() == null){
                throw new BusinessException(REQ_PARAM_ERROR,true,"业务端口不可为空");
            }
        }
        //同服务类型同ip校验
        if (serviceInfoDTO.getDeployMod() == DeployMod.DOCKER.getValue()) {
            //容器模式  同服务组下不能有相同ip的vpn
            if (serviceInfoDTO.getServiceTypeId().equals(ServiceTypeEnum.VPN.getId())) {
                ServiceInfoDTO findParam = new ServiceInfoDTO();
                findParam.setServiceTypeId(ServiceTypeEnum.VPN.getId());
//                findParam.setServiceGroupId(serviceInfoDTO.getServiceGroupId());
                SecRestResponse<List<ServiceInfoVO>> response = serviceInfoServiceApi.findList(findParam);
                List<ServiceInfoVO> sameGroupService = response.getResult();
                sameGroupService.forEach(sgs -> {
                    if (sgs.getBusiIp().equals(serviceInfoDTO.getBusiIp()) || sgs.getMgtIp().equals(serviceInfoDTO.getMgtIp())) {
                        if (null == sgs.getServiceGroupId()) {
                            log.error("not bind group service's ip is same with param,param:{}", serviceInfoDTO);
                            throw new BusinessException(REQ_PARAM_ERROR, true, "存在未绑定服务组且相同ip的VPN服务");
                        }
                        if (serviceInfoDTO.getServiceGroupId().equals(sgs.getServiceGroupId())) {
                            log.error("same group service's ip can not be same,param:{}", serviceInfoDTO);
                            throw new BusinessException(REQ_PARAM_ERROR, true, "同一服务组下不能有相同ip的VPN服务");
                        }

                    }
                });
            }
            return;
        }
        SecRestResponse<Boolean> typeIpExist = serviceInfoServiceApi.findTypeIpExist(serviceInfoDTO);
        Boolean typeIpExistResult = typeIpExist.getResult();
        if (typeIpExistResult) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_ADD_TYPE_IP_EXIST);
        }
    }

    /**
     * 判断是否存在设备,部分服务启动依赖设备
     *
     * @param infoVO
     */
    private List<DeviceInvokeDTO> deviceExistCheck(ServiceInfoVO infoVO) {
        List<DeviceInvokeDTO> list = new ArrayList<>();
        if (infoVO.getDeviceGroupId() != null) {
            list = deviceGroupService.getDeviceInvokeListByGroupId(infoVO.getDeviceGroupId());
            if (!CollectionUtils.isEmpty(list)) {
                return list;
            } else {
                log.error("deviceExistCheck-租户默认设备组未绑定设备,infoVO = {}", infoVO);
                throw new BusinessException(SecErrorCodeConstant.TENANT_DEVICE_GROUP_NOT_EXIST_DEVICE);
            }
        }
        return list;
    }

    @Override
    public Map<Long, Integer> getServiceNumByTenantList(List<Long> tenantIdList) {
        List<TenantToServiceGroupVO> allTenantToServiceGroupVOList = tenantToServiceGroupService.getServiceGroupByTenantIds(tenantIdList);
        List<Long> allServiceGroupIDs = allTenantToServiceGroupVOList.stream().map(TenantToServiceGroupVO::getServiceGroupId).distinct().collect(Collectors.toList());
        SecRestResponse<Map<Long, Integer>> restResponse = serviceInfoServiceApi.getServiceNumByServiceGroupList(allServiceGroupIDs);
        if (!restResponse.isSuccess()) {
            throw new BusinessException(restResponse.getCode());
        }
        Map<Long, Integer> serviceGroupNumMap = restResponse.getResult();
        Map<Long, Integer> tenantNumMap = new HashMap<>();
        tenantIdList.forEach(tenantId->{
            List<TenantToServiceGroupVO> tenantToServiceGroupVOList = tenantToServiceGroupService.getServiceGroupByTenantId(tenantId);
            if(tenantToServiceGroupVOList.isEmpty()){
                tenantNumMap.put(tenantId,0);
            }
            List<Long> tenantBindServiceGroupIds = tenantToServiceGroupVOList.stream().map(TenantToServiceGroupVO::getServiceGroupId).distinct().collect(Collectors.toList());
            tenantBindServiceGroupIds.forEach(serviceGroupId->{
                int serviceGroupNum = serviceGroupNumMap.get(serviceGroupId);
                if(tenantNumMap.containsKey(tenantId)){
                    tenantNumMap.put(tenantId, tenantNumMap.get(tenantId)+serviceGroupNum );
                } else{
                    tenantNumMap.put(tenantId, serviceGroupNum );
                }
            });
        });
        return tenantNumMap;
    }


    private void initGateWayInfo(ServiceInfoDTO serviceInfoDTO, ServiceTypeVO serviceTypeVO) {
        //查询网关信息传入服务信息实体
        if (regionService.checkIsRegionMode()) {
            ServiceGatewayVO serviceGatewayVO = serviceGatewayService.getAliveGateWay(serviceInfoDTO.getRegionId(), 2);

            if (null == serviceGatewayVO) {
                throw new BusinessException(GATEWAY_NOT_EXIST);
            }

            serviceInfoDTO.setMgtGatewayIp(serviceGatewayVO.getIp());
            serviceInfoDTO.setMgtGatewayPort(serviceGatewayVO.getPort());

            serviceInfoDTO.setBusiGatewayIp(serviceGatewayVO.getIp());
            serviceInfoDTO.setBusiGatewayPort(serviceGatewayVO.getPort());

        } else {
            //非区域模式的逻辑
            if (serviceTypeVO.getIsCreateMgtRoute() == 1) {
                ServiceGatewayVO serviceGatewayVO = serviceGatewayService.getAliveGateWay(serviceInfoDTO.getRegionId(), 1);
                if (null == serviceGatewayVO) {
                    throw new BusinessException(GATEWAY_NOT_EXIST);
                }
                serviceInfoDTO.setMgtGatewayIp(serviceGatewayVO.getIp());
                serviceInfoDTO.setMgtGatewayPort(serviceGatewayVO.getPort());
            }
            if (serviceTypeVO.getIsCreateBusiRoute() == 1) {
                ServiceGatewayVO serviceGatewayVO = serviceGatewayService.getAliveGateWay(serviceInfoDTO.getRegionId(), 2);
                if (null == serviceGatewayVO) {
                    throw new BusinessException(GATEWAY_NOT_EXIST);
                }
                serviceInfoDTO.setBusiGatewayIp(serviceGatewayVO.getIp());
                serviceInfoDTO.setBusiGatewayPort(serviceGatewayVO.getPort());
            }
        }
    }

    /**
     * 初始化租户及组信息
     *
     * @param serviceInfoDTO
     */
    private void initTenantGroupInfo(ServiceInfoDTO serviceInfoDTO) {
//        if ("1".equals(serviceAddContext.getConfigMap().get(CommonConstant.SYS_OPERATION_MODE)) || serviceInfoDTO.boolShare()) {
//            serviceInfoDTO.setTenantId(1L);
//        }
        //3.3.1 通过服务组获取租户id
        ServiceGroupVO serviceGroupVO = serviceGroupService.getServiceGroupById(serviceInfoDTO.getServiceGroupId());
        log.info("通过服务组获取租户id,serviceGroupVO:{}", serviceGroupVO);
        serviceInfoDTO.setTenantId(serviceGroupVO.getTenantId());
        QueryWrapper<TenantPO> queryWrapper = Wrappers.query();
        queryWrapper.lambda().eq(TenantPO::getTenantId, serviceInfoDTO.getTenantId());
        TenantPO tenant = tenantMapper.selectOne(queryWrapper);
        if (StringUtils.isBlank(serviceInfoDTO.getTenantName())) {
            serviceInfoDTO.setTenantName(tenant.getTenantName());
        }
        if (StringUtils.isBlank(serviceInfoDTO.getTenantCode())) {
            serviceInfoDTO.setTenantCode(tenant.getTenantCode());
        }
        if (ObjectUtils.isEmpty(serviceInfoDTO.getServiceGroupId())) {
            serviceInfoDTO.setServiceGroupId(tenant.getServiceGroupId());
        }
        //单租户模式页面传入设备组id,多租户模式取默认设备组id
        if (ObjectUtils.isEmpty(serviceInfoDTO.getDeviceGroupId())) {
            serviceInfoDTO.setDeviceGroupId(tenant.getDeviceGroupId());
        }

        //获取服务组标识
        ServiceGroupInfoDTO serviceGroupInfoDTO = new ServiceGroupInfoDTO();
        serviceGroupInfoDTO.setServiceGroupId(serviceInfoDTO.getServiceGroupId());
        serviceInfoDTO.setServiceGroupCode(serviceGroupServiceApi.findById(serviceGroupInfoDTO).getServiceGroupCode());
    }


    /**
     * 初始化租户,处理共享模式
     *
     * @param serviceInfoDTO
     * @param invokeService
     */
    private void initTenantUserWithShare(ServiceInfoDTO serviceInfoDTO, InterInvokeService invokeService) {
        //单独处理 共享模式下pki、svs不需要初始化租户的情况
        String crypto_service_use_kms_type = commonConfigService.getConfigValueByConfigCode(CommonConstant.CRYPTO_SERVICE_USE_KMS_TYPE);
        if ("2".equals(crypto_service_use_kms_type) && serviceInfoDTO.boolShare() &&  ServiceTypeEnum.cryptoService.containsKey(serviceInfoDTO.getServiceTypeId())) {
            return;
        }
        this.initTenantUser(serviceInfoDTO,invokeService);
    }

    /**
     * 初始化租户
     *
     * @param serviceInfoDTO
     * @param invokeService
     */
    private void initTenantUser(ServiceInfoDTO serviceInfoDTO, InterInvokeService invokeService) {
        //判断是否需要初始化租户
        if (ServiceTypeEnum.initTenantService.containsKey(serviceInfoDTO.getServiceTypeId())) {
            this.invokeInitTenantUser(serviceInfoDTO, invokeService);
        }
    }

    /**
     * 实际调用初始化租户的方法
     *
     * @param serviceInfoDTO
     * @param invokeService
     */
    private void invokeInitTenantUser(ServiceInfoDTO serviceInfoDTO, InterInvokeService invokeService) {
        SecRestResponse<Object> res = invokeService.initTenantUser(serviceInfoDTO, TenantInvokeDTO.builder()
                .tenantCode(serviceInfoDTO.getTenantCode())
                .tenantName(serviceInfoDTO.getTenantName()).build());
        if (!res.isSuccess()) {
            throw new BusinessException(SecErrorCodeConstant.INIT_TENANT_ERROR);
        }
    }

    /**
     * 调用密码服务添加租户,此方法仅用于共享模式
     *
     * @param serviceInfoVO
     * @param tenantVO
     */
    @Override
    public void serviceInfoAddTenantCode(ServiceInfoVO serviceInfoVO, TenantVO tenantVO) {
        InterInvokeService invokeService = serviceFactory.getService(serviceInfoVO.getServiceTypeId());

        ServiceInfoDTO serviceInfoDTO = interInvokerConvert.voToDto(serviceInfoVO);
        serviceInfoDTO.setTenantCode(tenantVO.getTenantCode());
        serviceInfoDTO.setTenantName(tenantVO.getTenantName());
        serviceInfoDTO.setIsShare(1);//仅用于共享
        //初始化租户
        initTenantUserWithShare(serviceInfoDTO, invokeService);
    }

    /**
     * 调用密码服务删除租户
     *
     * @param serviceInfoVO
     * @param tenantVO
     */
    @Override
    public void serviceInfoDeleteTenantCode(ServiceInfoVO serviceInfoVO, TenantVO tenantVO) {
        String crypto_service_use_kms_type = commonConfigService.getConfigValueByConfigCode(CommonConstant.CRYPTO_SERVICE_USE_KMS_TYPE);
        if ("2".equals(crypto_service_use_kms_type) && ServiceTypeEnum.cryptoService.containsKey(serviceInfoVO.getServiceTypeId()) ){
            return;
        }
        InterInvokeService invokeService = serviceFactory.getService(serviceInfoVO.getServiceTypeId());

        ServiceInfoDTO serviceInfoDTO = interInvokerConvert.voToDto(serviceInfoVO);
        serviceInfoDTO.setTenantCode(tenantVO.getTenantCode());
        serviceInfoDTO.setTenantName(tenantVO.getTenantName());

        deleteTenantUser(serviceInfoDTO, invokeService);
    }

    /**
     * 1.查询设备组下面的设备信息
     * 2.然后调用 “接受设备信息接口”
     *
     * @param infoVO
     * @param invokeService
     * @param list
     */
    private void bindDeviceByList(ServiceInfoVO infoVO, InterInvokeService invokeService, List<DeviceInvokeDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("下发设备信息bindDeviceByList-设备组不存在设备,infoVO = {}", infoVO);
            throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_NOT_EXIST_DEVICE);
        }

        DeviceInvokeBeanDTO beanDTO = new DeviceInvokeBeanDTO();
        beanDTO.setHsmList(list);
        log.info("当前服务设备下发bindDeviceByList,infoVO = {}, list = {}", infoVO, list);
        SecRestResponse<Object> res = invokeService.receiveDeviceInfo(Lists.newArrayList(infoVO), beanDTO);
        if (!res.isSuccess()) {
            throw new BusinessException(SecErrorCodeConstant.RECEIVE_DEVICE_ERROR);
        }
    }

    /**
     * 同设备组的服务设备下发
     *
     * @param deviceGroupId
     * @param list
     */
    public void bindDeviceByDeviceGroupId(Long deviceGroupId, List<DeviceInvokeDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("下发设备信息bindDeviceByDeviceGroupId-设备组不存在设备,deviceGroupId = {}", deviceGroupId);
            throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_NOT_EXIST_DEVICE);
        }

        //调用服务接收设备信息接口
        SecRestResponse<List<ServiceInfoVO>> response = serviceInfoServiceApi.findList(ServiceInfoDTO.builder().deviceGroupId(deviceGroupId).build());
        if (Utils.isExistVal(response) && !CollectionUtils.isEmpty(list)) {
            DeviceInvokeBeanDTO beanDTO = new DeviceInvokeBeanDTO();
            beanDTO.setHsmList(list);
            for (ServiceInfoVO infoVO : response.getResult()) {
                log.info("同设备组的服务设备下发bindDeviceByDeviceGroupId,infoVO = {},list = {}", infoVO, list);
                InterInvokeService invokeService = serviceFactory.getService(infoVO.getServiceTypeId());
                SecRestResponse<Object> res = invokeService.receiveDeviceInfo(Lists.newArrayList(infoVO), beanDTO);
                if (!res.isSuccess()) {
                    throw new BusinessException(SecErrorCodeConstant.RECEIVE_DEVICE_ERROR);
                }
            }
        }
    }

    /**
     * 1.查询设备组下面的设备信息
     * 2.然后调用 “接受设备信息接口”
     *
     * @param infoVO
     * @param invokeService
     */
//    private void bindDevice(ServiceInfoVO infoVO, InterInvokeService invokeService) {
//        if (infoVO.getDeviceGroupId() != null) {
//            List<DeviceInvokeDTO> list = deviceGroupService.getDeviceInvokeListByGroupId(infoVO.getDeviceGroupId());
//            if (!CollectionUtils.isEmpty(list)) {
//                DeviceInvokeBeanDTO beanDTO = new DeviceInvokeBeanDTO();
//                beanDTO.setHsmList(list);
//
//                SecRestResponse<Object> res = invokeService.receiveDeviceInfo(Lists.newArrayList(infoVO), beanDTO);
//                if (!res.isSuccess()) {
//                    throw new BusinessException(SecErrorCodeConstant.RECEIVE_DEVICE_ERROR);
//                }
//            } else {
//                log.error("下发设备信息bindDevice-设备组不存在设备,infoVO = {}", infoVO);
//            }
//        }
//    }

    /**
     * 1.查询设备组下面的设备信息
     * 2.然后调用 “接受设备信息接口”
     *
     * @param infoVO
     * @param invokeService
     */
    private void bindDeviceAndCheck(ServiceInfoVO infoVO, InterInvokeService invokeService) {
        if (infoVO.getDeviceGroupId() != null) {
            List<DeviceInvokeDTO> list = deviceGroupService.getDeviceInvokeListByGroupId(infoVO.getDeviceGroupId());
            if (!CollectionUtils.isEmpty(list)) {
                DeviceInvokeBeanDTO beanDTO = new DeviceInvokeBeanDTO();
                beanDTO.setHsmList(list);

                SecRestResponse<Object> res = invokeService.receiveDeviceInfo(Lists.newArrayList(infoVO), beanDTO);
                if (!res.isSuccess()) {
                    throw new BusinessException(SecErrorCodeConstant.RECEIVE_DEVICE_ERROR);
                }
            } else {
                log.error("下发设备信息bindDeviceAndCheck-设备组不存在设备,infoVO = {}", infoVO);
                throw new BusinessException(SecErrorCodeConstant.DEVICE_GROUP_NOT_EXIST_DEVICE);
            }
        }
    }

    /**
     * 配置公钥
     *
     * @param serviceInfoDTO
     * @param invokeService
     * @param serviceAddContext
     */
    private void configAuthPk(ServiceInfoDTO serviceInfoDTO, InterInvokeService invokeService, ServiceAddContext serviceAddContext) {
        SecRestResponse<Object> res = invokeService.configAuthPk( serviceInfoDTO, serviceAddContext.getConfigMap());
        if (!res.isSuccess()) {
            throw new BusinessException(SecErrorCodeConstant.CONFIG_PUBLIC_KEY_FUN_ERROR);
        }
    }

//    /**
//     * @Description: 下发配置信息
//     * @param infoVO
//     * @param invokeService
//     */
//    private void pushDeviceConfig(ServiceInfoVO infoVO, InterInvokeService invokeService) {
//        if (ServiceTypeEnum.appGroupService.containsKey(infoVO.getServiceTypeId())) {
//            Map<String, String> configMap = new HashMap<>();
//            //ip : port
//            configMap.put("url", Utils.ipPort(infoVO.getBusiGatewayIp(), infoVO.getBusiGatewayPort()));
//            SecRestResponse<Object> res = invokeService.receiveConfig(Lists.newArrayList(infoVO), configMap);
//            if (!res.isSuccess()) {
//                throw new BusinessException(SecErrorCodeConstant.RECEIVE_CONFIG_ERROR);
//            }
//        }
//
//    }

    private void bindLicense(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
        log.info("绑定许可证bindLicense,serviceInfoDTO = {}", serviceInfoDTO);
        if (CommonConstant.FALSE.equals(serviceAddContext.getConfigMap().get(CommonConstant.SERVICE_IS_BIND_LICENSE))) {
            return;
        }

        SecRestResponse<Object> response = licenseUseService.bindServer(serviceInfoDTO.getId());
        if (!response.isSuccess()) {
            throw new BusinessException(response.getCode());
        }
    }

    private void unbindLicense(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
        log.info("解绑许可证unbindLicense,serviceInfoDTO = {}", serviceInfoDTO);
        if (CommonConstant.FALSE.equals(serviceAddContext.getConfigMap().get(CommonConstant.SERVICE_IS_BIND_LICENSE))) {
            return;
        }

        try {
            licenseUseService.unbindServer(serviceInfoDTO.getId());
        } catch (Exception e) {
            log.error("解绑许可证异常unbindLicense, serviceInfoDTO = {}", serviceInfoDTO);
        }
    }

    private void bindRoute(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
        ServiceInfoVO serviceInfoVO = serviceAddContext.getServiceInfoVO();
        if (null == serviceInfoDTO.getTcpPort()) {
            serviceInfoDTO.setTcpPort(serviceAddContext.getServiceTypeVO().getTcpPort());
        }
        if (null == serviceInfoDTO.getExpandPort()) {
            serviceInfoDTO.setExpandPort(serviceAddContext.getServiceTypeVO().getExpandPort());
        }
        serviceInfoVO.setTcpPort(serviceInfoDTO.getTcpPort());
        serviceInfoVO.setExpandPort(serviceInfoDTO.getExpandPort());
        serviceGroupService.serviceGroupAddService(serviceInfoDTO.getServiceGroupId(), serviceInfoVO);
    }


//    private void bindRoute_bak(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
//        if (serviceInfoDTO.boolShare()) {
//            serviceGroupService.serviceGroupAddService(serviceInfoDTO.getServiceGroupId(),serviceAddContext.getServiceInfoVO());
//            serviceInfoDTO.setTcpPort(serviceAddContext.getServiceTypeVO().getTcpPort());
//            serviceInfoDTO.setExpandPort(serviceAddContext.getServiceTypeVO().getExpandPort());
//        } else {
//            if (ServiceTypeEnum.TSC.getId().equals(serviceInfoDTO.getServiceTypeId())) {
//                //绑定管理路由信息  电子签章的管理路由绑定ip端口是他们业务服务的ip端口，他们的页面接口在业务服务里
//                ServiceInfoDTO serviceInfoTsc = new ServiceInfoDTO();
//                BeanUtils.copyProperties(serviceInfoDTO, serviceInfoTsc);
//                serviceInfoTsc.setMgtIp(serviceInfoDTO.getBusiIp());
//                serviceInfoTsc.setMgtPort(serviceInfoDTO.getBusiPort());
//                bindManagerRoute(serviceInfoTsc, 1);
//            } else {
//                //绑定管理路由信息
//                bindManagerRoute(serviceInfoDTO, 1);
//            }
//
//            //绑定服务业务路由   文件加密服务不创建业务路由，只创建kmip路由
//            if (!ServiceTypeEnum.SECSTORAGE.getId().equals(serviceInfoDTO.getServiceTypeId())) {
//                bindServiceRoute(serviceInfoDTO, 2);
//            }
//
//            //读取配置 若密码机三个服务下有kms 则需要给服务创建kmscore路由 因为页面添加加解密应用需要调用加解密的kmscore
//            if (ServiceTypeEnum.cryptoService.containsKey(serviceInfoDTO.getServiceTypeId())) {
//                //绑定KMS密管组件路由
//                ServiceInfoDTO serviceInfoKmsCore = new ServiceInfoDTO();
//                BeanUtils.copyProperties(serviceInfoDTO, serviceInfoKmsCore);
//                //前端暂时不选择，直接默认服务类型表的默认扩展端口
//                serviceInfoKmsCore.setBusiPort(serviceAddContext.getServiceTypeVO().getExpandPort());
//                bindServiceRoute(serviceInfoKmsCore, 3);
//                serviceInfoDTO.setExpandPort(serviceAddContext.getServiceTypeVO().getExpandPort());
//            }
//
//            //kms特殊处理
//            if (ServiceTypeEnum.KMS.getId().equals(serviceInfoDTO.getServiceTypeId())) {
//                //绑定KMS密管组件路由
//                ServiceInfoDTO serviceInfoKmsCore = new ServiceInfoDTO();
//                BeanUtils.copyProperties(serviceInfoDTO, serviceInfoKmsCore);
//                serviceInfoKmsCore.setBusiPort(serviceAddContext.getServiceTypeVO().getExpandPort());
//                bindServiceRoute(serviceInfoKmsCore, 3);
//                serviceInfoDTO.setExpandPort(serviceAddContext.getServiceTypeVO().getExpandPort());
//
//                //绑定KMIP路由
//                ServiceInfoDTO serviceInfoKmip = new ServiceInfoDTO();
//                BeanUtils.copyProperties(serviceInfoDTO, serviceInfoKmip);
//                serviceInfoKmip.setBusiPort(serviceAddContext.getServiceTypeVO().getTcpPort());
//                bindServiceRoute(serviceInfoKmip, 4);
//                serviceInfoDTO.setTcpPort(serviceAddContext.getServiceTypeVO().getTcpPort());
//            }
//
//            //文件加密服务特殊处理
//            if (ServiceTypeEnum.SECSTORAGE.getId().equals(serviceInfoDTO.getServiceTypeId())) {
//                //绑定KMIP路由
//                ServiceInfoDTO serviceInfoKmip = new ServiceInfoDTO();
//                BeanUtils.copyProperties(serviceInfoDTO, serviceInfoKmip);
//                serviceInfoKmip.setBusiPort(serviceAddContext.getServiceTypeVO().getTcpPort());
//                bindServiceRoute(serviceInfoKmip, 4);
//                serviceInfoDTO.setTcpPort(serviceAddContext.getServiceTypeVO().getTcpPort());
//            }
//        }
//
//    }

//    /**
//     * 绑定服务路由
//     *
//     * @param serviceInfo
//     */
//    private void bindServiceRoute(ServiceInfoDTO serviceInfo, Integer routeType) {
//        serviceGatewayRouteService.serviceBindRoute(ServiceRouteBindDTO.builder()
//                .gatewayIp(serviceInfo.getBusiGatewayIp())
//                .routeType(routeType)
//                .serviceIdList(Collections.singletonList(serviceInfo.getId()))
//                .serviceTypeId(serviceInfo.getServiceTypeId())
//                .serviceName(serviceInfo.getServiceName())
//                .groupId(serviceInfo.getServiceGroupId())
//                .groupType(GroupTypeEnum.SERVICE.getCode())
//                .groupCode(serviceInfo.getServiceGroupCode())
//                .serviceType(ServiceEnum.SERVICE.getCode())
//                .tenantId(serviceInfo.getTenantId())
//                .tenantCode(serviceInfo.getTenantCode())
//                .isActiveStandby(serviceInfo.getIsActiveStandby())
//                .ipPorts(Collections.singletonList(serviceInfo.getBusiIp() + ":" + serviceInfo.getBusiPort()))
//                .regionId(serviceInfo.getRegionId())
//                .build());
//
//    }

//    /**
//     * 绑定管理路由
//     *
//     * @param serviceInfo
//     */
//    private void bindManagerRoute(ServiceInfoDTO serviceInfo, Integer routeType) {
//        serviceGatewayRouteService.serviceBindRoute(ServiceRouteBindDTO.builder()
//                .gatewayIp(serviceInfo.getMgtGatewayIp())
//                .routeType(routeType)
//                .serviceIdList(Collections.singletonList(serviceInfo.getId()))
//                .serviceTypeId(serviceInfo.getServiceTypeId())
//                .serviceName(serviceInfo.getServiceName())
//                .groupId(serviceInfo.getServiceGroupId())
//                .groupType(GroupTypeEnum.SERVICE.getCode())
//                .groupCode(serviceInfo.getServiceGroupCode())
//                .serviceType(ServiceEnum.SERVICE.getCode())
//                .tenantId(serviceInfo.getTenantId())
//                .tenantCode(serviceInfo.getTenantCode())
//                .isActiveStandby(serviceInfo.getIsActiveStandby())
//                .ipPorts(Collections.singletonList(serviceInfo.getMgtIp() + ":" + serviceInfo.getMgtPort()))
//                .regionId(serviceInfo.getRegionId())
//                .build());
//    }

    /**
     * @param serviceInfo
     * @description: 检查服务是否启动, 没有启动尝试启动
     * @return: void
     */
    private void serviceFollowOper(ServiceInfoDTO serviceInfo) {
        SysTaskDTO sysTaskDTO = new SysTaskDTO();
        sysTaskDTO.setTaskId(IdGenerator.ins().generator());
        String taskName = "serviceFollowOper" + serviceInfo.getId();
        sysTaskDTO.setTaskName(taskName);
        sysTaskDTO.setTaskGroup("serviceFollowOper");
        JSONObject jo = new JSONObject();
        jo.put(TASK_ID, sysTaskDTO.getTaskId());
        jo.put(SERVICE_ID_KEY, serviceInfo.getId());
        String jsonParam = "serviceInfoTaskServiceImpl.serviceFollowOper(\"" + jo.toJSONString() + "\")";
        sysTaskDTO.setJsonParam(jsonParam);
        sysTaskService.add(sysTaskDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecRestResponse<Object> edit(ServiceInfoEditDTO serviceInfoEditDTO) {
        SecRestResponse<ServiceInfoVO> response = serviceInfoServiceApi.findById(ServiceInfoDTO.builder().id(serviceInfoEditDTO.getId()).build());
        ServiceInfoVO serviceInfoVO = response.getResult();
        if (ObjectUtils.isEmpty(serviceInfoVO)) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_NOT_EXEIST);
        }

        //判断是否重名
        if (StringUtils.isNotBlank(serviceInfoEditDTO.getServiceName())
                && !serviceInfoVO.getServiceName().equals(serviceInfoEditDTO.getServiceName())) {
            //服务名称不可重复
            ServiceInfoParamDTO serviceInfoParamDTO = new ServiceInfoParamDTO();
            serviceInfoParamDTO.setServiceName(serviceInfoEditDTO.getServiceName());
            SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.getServiceInfoByServiceParam(serviceInfoParamDTO);
            if (!CollectionUtils.isEmpty(res.getResult())) {
                throw new BusinessException(SecErrorCodeConstant.SERVICE_NAME_EXEIT);
            }
        }

        List<ConfigVO> configVOList = configServiceApi.getConfigVOAll();
        Map<String, String> configMap = configVOList.stream().collect(Collectors.toMap(ConfigVO::getConfigCode, ConfigVO::getConfigValue, (key1, key2) -> key2));

        //单租户模式绑定设备组调用接收设备接口
        if (!"2".equals(configMap.get(CommonConstant.SYS_OPERATION_MODE))) {
            if (ObjectUtils.isEmpty(serviceInfoEditDTO.getDeviceGroupId())) {
                throw new BusinessException(SecErrorCodeConstant.SERVICE_DEVICE_GROUP_ID_NULL);
            }
            InterInvokeService invokeService = serviceFactory.getService(serviceInfoEditDTO.getServiceTypeId());
            ServiceInfoVO vo = interInvokerConvert.dtoEditToVo(serviceInfoEditDTO);
            bindDeviceAndCheck(vo, invokeService);
        }

        // 服务操作是否选择主备 true备机改为主机需要编辑路由权重
        // 且要修改的主备机与修改之前主备机不一致
        if (CommonConstant.TRUE.equals(configMap.get(CommonConstant.IS_CHOSE_ACTIVE_STANDBY))
                && !Objects.equals(serviceInfoEditDTO.getIsActiveStandby(), serviceInfoVO.getIsActiveStandby())) {
            //只能从备机改为主机
            if (2 == serviceInfoEditDTO.getIsActiveStandby()) {
                throw new BusinessException(SecErrorCodeConstant.NOT_ACTIVE_TO_STANDBY);
            }
            //备机改为主机需要编辑路由ip权重
            UpdateRouteDTO updateRouteDTO = new UpdateRouteDTO();
            if (ServiceTypeEnum.TSC.getId().equals(serviceInfoVO.getServiceTypeId())) {
                updateRouteDTO.setMgtIpPorts(Collections.singletonList(serviceInfoVO.getBusiIp() + ":" + serviceInfoVO.getBusiPort()));
            } else {
                updateRouteDTO.setMgtIpPorts(Collections.singletonList(serviceInfoVO.getMgtIp() + ":" + serviceInfoVO.getMgtPort()));
            }

            updateRouteDTO.setBusiIpPorts(Collections.singletonList(serviceInfoVO.getBusiIp() + ":" + serviceInfoVO.getBusiPort()));
            updateRouteDTO.setExpandIpPorts(Collections.singletonList(serviceInfoVO.getBusiIp() + ":" + serviceInfoVO.getExpandPort()));
            updateRouteDTO.setTcpIpPorts(Collections.singletonList(serviceInfoVO.getBusiIp() + ":" + serviceInfoVO.getTcpPort()));
            updateRouteDTO.setServiceIdList(Collections.singletonList(serviceInfoVO.getId()));
            updateRouteDTO.setIsActiveStandby(serviceInfoEditDTO.getIsActiveStandby());
            updateRouteDTO.setRegionId(serviceInfoVO.getRegionId());

            serviceGatewayRouteService.updateBindRoute(updateRouteDTO);
        }

        ServiceInfoEditDTO needEditDTO = new ServiceInfoEditDTO();
        needEditDTO.setId(serviceInfoEditDTO.getId());
        needEditDTO.setIsActiveStandby(serviceInfoEditDTO.getIsActiveStandby());
        needEditDTO.setDeviceGroupId(serviceInfoEditDTO.getDeviceGroupId());
        needEditDTO.setServiceGroupId(serviceInfoEditDTO.getServiceGroupId());
        needEditDTO.setServiceName(serviceInfoEditDTO.getServiceName());
        needEditDTO.setRemark(serviceInfoEditDTO.getRemark());
        return serviceInfoServiceApi.edit(needEditDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecRestResponse<Object> deleteById(ServiceInfoDeleteDTO serviceInfoDeleteDTO) {
        ServiceInfoVO serviceInfoVO = getServiceVO(serviceInfoDeleteDTO.getId());
        QueryWrapper<TenantPO> queryWrapper = Wrappers.query();
        queryWrapper.lambda().eq(TenantPO::getTenantId, 1L);
        TenantPO tenant = tenantMapper.selectOne(queryWrapper);
        //单租户模式并且为顶级租户默认服务组 释放服务后删除 不校验服务组是否存在
        String sysOperationMode = commonConfigService.getSysOperationMode();
        if (CommonConstant.SINGLE_TENANT_MODE.equals(sysOperationMode)
                && tenant.getServiceGroupId().equals(serviceInfoVO.getServiceGroupId())) {
            try {
                //释放服务
                ServiceInGroupDTO serviceInGroupDTO = new ServiceInGroupDTO();
                serviceInGroupDTO.setServiceId(serviceInfoVO.getId());
                serviceInGroupDTO.setServiceGroupId(serviceInfoVO.getServiceGroupId());
                serviceGroupService.deleteService(serviceInGroupDTO);
            } catch (Exception e) {
                log.error("服务删除-释放服务异常,serviceInfoVO = {}", serviceInfoVO, e);
                throw new BusinessException(SecErrorCodeConstant.SERVICE_DELETE_GROUP_UNBIND_SERVICE_ERROR, true, e.getMessage());
            }
        } else {
            //绑定服务组不可删除
            if (serviceInfoVO.getServiceGroupId() != null
                    && !serviceInfoVO.getOperStatus().equals(ServiceOperStatusEnum.RUN_ERROR.getId())
                    && !serviceInfoVO.getRunStatus().equals(ServiceRunStatusEnum.RUN_ERROR.getId())
                    && !serviceInfoVO.getRunStatus().equals(ServiceRunStatusEnum.UPGRADE_ERROR.getId())) {
                throw new BusinessException(SecErrorCodeConstant.SERVICE_CANT_DELETE_SERVICE_GROUP_EXIST);
            }
        }

        //删除服务代理和管控代理
        ServiceInfoDTO serviceInfoDTO = interInvokerConvert.voToDto(serviceInfoVO);
        this.deleteServiceProxy(serviceInfoDTO);
        // 331 @weic 容器模式下 先不要删管控代理 等删了Docker容器之后 要不然容器删除不了了
        if (serviceInfoVO.getDeployMod() != 2) {
            this.deleteRemoteProxy(serviceInfoDTO);
        }

        SecRestResponse<String> res = configServiceApi.getConfigValueByConfigCode(CommonConstant.SERVICE_IS_BIND_LICENSE);
        if (CommonConstant.TRUE.equals(res.getResult())) {
            //删除许可证
            licenseUseTimeService.deleteById(serviceInfoVO.getId());
        }

        //删除服务监控,若同ip还存在操作状态正常的服务则不删
        if (CollectionUtils.isEmpty(getMonitorServiceListByIp(serviceInfoVO.getMgtIp()))) {
            MonitorConnectionDTO dto = new MonitorConnectionDTO();
            dto.setIp(serviceInfoVO.getMgtIp());
            dto.setProxyRoute(regionService.checkIsRegionMode());
            String gatewayUrl = "https://"+serviceInfoVO.getBusiGatewayIp()+":"+serviceInfoVO.getBusiGatewayPort();
            dto.setGatewayApiUrl(gatewayUrl);
            monitorConnectionService.deleteDevice(dto);
        }

        //3.3.1 事务执行 if(容器模式)
        //3.3.1           删除service_docker_port_mapping表的数据
        //3.3.1           删除对应容器
        if (serviceInfoVO.getDeployMod() == 2) {
            Map<String, String> remoteHost = regionRemoteAdapterService.getRemoteHost(serviceInfoDTO);
            Docker docker = DockerUtils.https(remoteHost.get(RemoteConstant.REMOTE_IP),String.valueOf(remoteHost.get(RemoteConstant.REMOTE_PORT)),remoteService.generateHeader(serviceInfoDTO),remoteService.getSwak());
            docker.container().stop(serviceInfoVO.getContainerName());
            docker.container().remove(serviceInfoVO.getContainerName());
            // 331 @weic 容器模式下 先不要删管控代理 等删了Docker容器之后
            this.deleteRemoteProxy(serviceInfoDTO);
        }
        return serviceInfoServiceApi.deleteById(serviceInfoDeleteDTO);
    }

    public ServiceInfoVO getServiceVO(Long id) {
        SecRestResponse<ServiceInfoVO> response = serviceInfoServiceApi.findById(ServiceInfoDTO.builder().id(id).build());
        ServiceInfoVO serviceInfoVO = response.getResult();
        if (ObjectUtils.isEmpty(serviceInfoVO)) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_NOT_EXEIST);
        }

        return serviceInfoVO;
    }


    @Override
    public SecRestResponse<Object> deleteByTenantId(Long tenantId) {
        TenantPO tenantPO = tenantMapper.selectById(tenantId);
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.findList(ServiceInfoDTO.builder().tenantId(tenantId).build());
        if (Utils.isExistVal(res)) {
            res.getResult().forEach(infoVO -> {
                InterInvokeService service = serviceFactory.getService(infoVO.getServiceTypeId());
                deleteTenantUser(ServiceInfoDTO.builder().tenantId(infoVO.getTenantId()).tenantCode(tenantPO.getTenantCode())
                        .tenantName(tenantPO.getTenantName()).serviceTypeId(infoVO.getServiceTypeId())
                        .mgtIp(infoVO.getMgtIp()).mgtPort(infoVO.getMgtPort()).build(), service);
            });
        }
        return ResultUtil.ok();
    }



    /**
     * 初始化租户
     *
     * @param serviceInfoDTO
     * @param invokeService
     */
    private void deleteTenantUser(ServiceInfoDTO serviceInfoDTO, InterInvokeService invokeService) {
        //判断是否需要初始化租户
        if (ServiceTypeEnum.initTenantService.containsKey(serviceInfoDTO.getServiceTypeId())
                && 1 != serviceInfoDTO.getTenantId()) {
            SecRestResponse<Object> res = invokeService.deleteTenant(serviceInfoDTO, TenantInvokeDTO.builder()
                    .tenantCode(serviceInfoDTO.getTenantCode())
                    .tenantName(serviceInfoDTO.getTenantName()).build());
            if (!res.isSuccess()) {
                throw new BusinessException(SecErrorCodeConstant.INIT_TENANT_ERROR);
            }
        }
    }

    @Override
    public SecRestResponse<SecPageVO<ServiceInfoPageVO>> find(ServiceInfoPageDTO serviceInfoPageDTO) {
        SecRestResponse<SecPageVO<ServiceInfoPageVO>> result = serviceInfoServiceApi.find(serviceInfoPageDTO);
        SecPageVO<ServiceInfoPageVO> data = result.getResult();
        if (CollectionUtils.isEmpty(data.getList())) {
            return result;
        }
        //查询租户信息-租户名称
        QueryWrapper<TenantPO> queryWrapper = Wrappers.query();
        List<TenantPO> tenantPOList = tenantMapper.selectList(queryWrapper);
        Map<Long, String> tenantMap = null;
        if (!CollectionUtils.isEmpty(tenantPOList)) {
            tenantMap = tenantPOList.stream().collect(Collectors.toMap(TenantPO::getTenantId, TenantPO::getTenantName, (key1, key2) -> key2));
        }

        //查询设备组信息-设备组名称 //资源组不展示
        DeviceGroupFindListDTO deviceGroupFindListDTO = new DeviceGroupFindListDTO();
        SecRestResponse<List<DeviceGroupVO>> deviceGroupListRes = deviceGroupServiceApi.list(deviceGroupFindListDTO);
        Map<Long, String> deviceGroupMap = null;
        if (!CollectionUtils.isEmpty(deviceGroupListRes.getResult())) {
            deviceGroupMap = deviceGroupListRes.getResult().stream().collect(Collectors.toMap(DeviceGroupVO::getDeviceGroupId, DeviceGroupVO::getDeviceGroupName, (key1, key2) -> key2));
        }

        //查询区域名称
        Map<Long, String> regionMap = regionService.getRegionIdNameMap();

        //查询镜像信息
        List<DockerImagePO> dockerImagePOList = dockerImageService.list();
        Map<Long, DockerImagePO> dockerImageMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dockerImagePOList)) {
            dockerImageMap = dockerImagePOList.stream().collect(Collectors.toMap(DockerImagePO::getId,image->image, (key1, key2) -> key2));
        }

        for (ServiceInfoPageVO serviceInfoPageVO : data.getList()) {
            if (tenantMap != null && !Objects.equals(serviceInfoPageVO.getTenantId(), CommonConstant.TOP_TENANT_ID)) {
                serviceInfoPageVO.setTenantName(tenantMap.get(serviceInfoPageVO.getTenantId()));
            }
            if (deviceGroupMap != null) {
                serviceInfoPageVO.setDeviceGroupName(deviceGroupMap.get(serviceInfoPageVO.getDeviceGroupId()));
            }
            if (regionMap != null) {
                serviceInfoPageVO.setRegionName(regionMap.get(serviceInfoPageVO.getRegionId()));
            }
            //资源组不展示
            if (serviceInfoPageVO.getServiceGroupType() != null && 1 == serviceInfoPageVO.getServiceGroupType()) {
                serviceInfoPageVO.setServiceGroupName(null);
            }
            //镜像信息
            DockerImagePO dockerImagePO = dockerImageMap.get(serviceInfoPageVO.getImageId() != null ? Long.valueOf(serviceInfoPageVO.getImageId()) : null);
            if (null != dockerImagePO) {
                serviceInfoPageVO.setImageName(dockerImagePO.getImageName());
                serviceInfoPageVO.setImageVersion(dockerImagePO.getImageVersion());
            }
            //部署模式
            DeployMod deployMod = DeployMod.get(serviceInfoPageVO.getDeployMod());
            if (null != deployMod) {
                serviceInfoPageVO.setDeployModStr(deployMod.getName());
            }
        }
        return result;
    }

    @Override
    public SecRestResponse<SecPageVO<ServiceInfoPageVO>> findByServiceGroup(ServiceInfoByServiceGroupPageDTO serviceInfoPageDTO) {
        SecRestResponse<SecPageVO<ServiceInfoPageVO>> result = serviceInfoServiceApi.findByServiceGroup(serviceInfoPageDTO);
        SecPageVO<ServiceInfoPageVO> data = result.getResult();
        if (CollectionUtils.isEmpty(data.getList())) {
            return result;
        }
        //查询租户信息-租户名称
        QueryWrapper<TenantPO> queryWrapper = Wrappers.query();
        List<TenantPO> tenantPOList = tenantMapper.selectList(queryWrapper);
        Map<Long, String> tenantMap = null;
        if (!CollectionUtils.isEmpty(tenantPOList)) {
            tenantMap = tenantPOList.stream().collect(Collectors.toMap(TenantPO::getTenantId, TenantPO::getTenantName, (key1, key2) -> key2));
        }

        //查询设备组信息-设备组名称 //资源组不展示
        DeviceGroupFindListDTO deviceGroupFindListDTO = new DeviceGroupFindListDTO();
        SecRestResponse<List<DeviceGroupVO>> deviceGroupListRes = deviceGroupServiceApi.list(deviceGroupFindListDTO);
        Map<Long, String> deviceGroupMap = null;
        if (!CollectionUtils.isEmpty(deviceGroupListRes.getResult())) {
            deviceGroupMap = deviceGroupListRes.getResult().stream().collect(Collectors.toMap(DeviceGroupVO::getDeviceGroupId, DeviceGroupVO::getDeviceGroupName, (key1, key2) -> key2));
        }

        //查询区域名称
        Map<Long, String> regionMap = regionService.getRegionIdNameMap();

        for (ServiceInfoPageVO serviceInfoPageVO : data.getList()) {
            if (tenantMap != null && !Objects.equals(serviceInfoPageVO.getTenantId(), CommonConstant.TOP_TENANT_ID)) {
                serviceInfoPageVO.setTenantName(tenantMap.get(serviceInfoPageVO.getTenantId()));
            }
            if (deviceGroupMap != null) {
                serviceInfoPageVO.setDeviceGroupName(deviceGroupMap.get(serviceInfoPageVO.getDeviceGroupId()));
            }
            if (regionMap != null) {
                serviceInfoPageVO.setRegionName(regionMap.get(serviceInfoPageVO.getRegionId()));
            }
            //资源组不展示
            if (serviceInfoPageVO.getServiceGroupType() != null && 1 == serviceInfoPageVO.getServiceGroupType()) {
                serviceInfoPageVO.setServiceGroupName(null);
            }
        }
        return result;
    }

    @Override
    public SecRestResponse<Object> start(Long id) {
        ServiceInfoParamDTO serviceInfoParamDTO = new ServiceInfoParamDTO();
        serviceInfoParamDTO.setId(id);
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.getServiceInfoByServiceParam(serviceInfoParamDTO);
        if (CollectionUtils.isEmpty(res.getResult())) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_NOT_EXEIST);
        }
        ServiceInfoVO serviceInfoVO = res.getResult().get(0);
        // docker模式下 先做Docker容器的如停止就启动的动作
        if (serviceInfoVO.getDeployMod()==DeployMod.DOCKER.getValue()){
            ServiceInfoDTO serviceInfoDTO = interInvokerConvert.voToDto(serviceInfoVO);
            Map<String, String> remoteHost = regionRemoteAdapterService.getRemoteHost(serviceInfoDTO);
            Docker docker = DockerUtils.https(remoteHost.get(RemoteConstant.REMOTE_IP), String.valueOf(remoteHost.get(RemoteConstant.REMOTE_PORT)), remoteService.generateHeader(serviceInfoDTO), remoteService.getSwak());
            docker.container().startIfStop(serviceInfoDTO.getContainerName());
        }
        //
        InterInvokeService service = serviceFactory.getService(serviceInfoVO.getServiceTypeId());
        //启动服务
        service.startBusiService(InvokeStartBusiDTO.builder().data(res.getResult()).build());

        //更新运行状态为启动中
        EditRunStatusParamDTO serviceStatusUpdateDTO = new EditRunStatusParamDTO();
        serviceStatusUpdateDTO.setId(id);
        serviceStatusUpdateDTO.setRunStatus(3);
        return serviceInfoServiceApi.editServiceRunStatus(serviceStatusUpdateDTO);
    }

    /**
     * 启动租户服务
     *
     * @param tenantId
     * @return
     */
    @Override
    public SecRestResponse<Object> startByTenantId(Long tenantId) {
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.findList(ServiceInfoDTO.builder().tenantId(tenantId).build());
        if (Utils.isExistVal(res)) {
            for (ServiceInfoVO infoVO : res.getResult()) {
                //文件加密、时间戳、CA、数据库加密、VPN无法进行启停，自动跳过
                if (ServiceTypeEnum.busiAndMgtService.containsKey(infoVO.getServiceTypeId())) {
                    log.info("该服务无法进行启停stop,infoVO = {}", infoVO);
                    continue;
                }
                //判断服务是否已启动 已启动不需要调用
                InterInvokeService service = serviceFactory.getService(infoVO.getServiceTypeId());
                SecRestResponse<Map<Long, Integer>> result = service.getServiceState(InvokeServiceStateDTO.builder().data(Lists.newArrayList(infoVO)).build());
                if (result.isSuccess() && result.getResult() != null && 1 == result.getResult().get(infoVO.getId())) {
                    log.info("已启动不需要调用start,infoVO = {}", infoVO);
                    continue;
                }
                // docker模式下 先做Docker容器的如停止就启动的动作
                if (infoVO.getDeployMod()==DeployMod.DOCKER.getValue()){
                    ServiceInfoDTO serviceInfoDTO = interInvokerConvert.voToDto(infoVO);
                    Map<String, String> remoteHost = regionRemoteAdapterService.getRemoteHost(serviceInfoDTO);
                    Docker docker = DockerUtils.https(remoteHost.get(RemoteConstant.REMOTE_IP), String.valueOf(remoteHost.get(RemoteConstant.REMOTE_PORT)), remoteService.generateHeader(serviceInfoDTO), remoteService.getSwak());
                    docker.container().startIfStop(serviceInfoDTO.getContainerName());
                }
                service.startBusiService(InvokeStartBusiDTO.builder().data(Lists.newArrayList(infoVO)).build());
            }
        }
        return ResultUtil.ok();
    }

    /**
     * 停止服务 by租户id
     *
     * @param id
     * @return
     */
    @Override
    public SecRestResponse<Object> stop(Long id) {
        ServiceInfoParamDTO serviceInfoParamDTO = new ServiceInfoParamDTO();
        serviceInfoParamDTO.setId(id);
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.getServiceInfoByServiceParam(serviceInfoParamDTO);
        if (CollectionUtils.isEmpty(res.getResult())) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_NOT_EXEIST);
        }
        ServiceInfoVO serviceInfoVO = res.getResult().get(0);
        InterInvokeService service = serviceFactory.getService(serviceInfoVO.getServiceTypeId());
        //停止服务
        service.stopBusiService(InvokeEndBusiDTO.builder().data(res.getResult()).build());

        //更新运行状态为停止中
        EditRunStatusParamDTO serviceStatusUpdateDTO = new EditRunStatusParamDTO();
        serviceStatusUpdateDTO.setId(id);
        serviceStatusUpdateDTO.setRunStatus(4);
        return serviceInfoServiceApi.editServiceRunStatus(serviceStatusUpdateDTO);
    }

    @Override
    public SecRestResponse<Object> restart(Long id) {
        ServiceInfoParamDTO serviceInfoParamDTO = new ServiceInfoParamDTO();
        serviceInfoParamDTO.setId(id);
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.getServiceInfoByServiceParam(serviceInfoParamDTO);
        if (CollectionUtils.isEmpty(res.getResult())) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_NOT_EXEIST);
        }
        ServiceInfoVO infoVO = res.getResult().get(0);

        //3.3.1 if(容器模式) docker exec /opt/sansec/xxx/setup/startService.sh
        //TODO 重启容器  为什么要有重启？
        //调用管控程序重启服务
        InitServiceStartDTO initServiceStartDTO = new InitServiceStartDTO();
        String initStartScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                builder().scriptCode(ScriptConstant.INIT_START_SCRIPT).serviceTypeId(infoVO.getServiceTypeId()).build()).getResult();
        initServiceStartDTO.setRunScript(getScriptPath(infoVO.getServiceTypeId(), initStartScript));
        remoteService.initStart(initServiceStartDTO, infoVO);

        //更新运行状态为重启中
        EditRunStatusParamDTO serviceStatusUpdateDTO = new EditRunStatusParamDTO();
        serviceStatusUpdateDTO.setId(id);
        serviceStatusUpdateDTO.setRunStatus(5);
        return serviceInfoServiceApi.editServiceRunStatus(serviceStatusUpdateDTO);
    }

    @Override
    public SecRestResponse<Object> upgrade(ServiceUpgradeDTO serviceUpgradeDTO) {

        CheckUpgradeAuthCodeDTO checkUpgradeAuthCodeDTO = new CheckUpgradeAuthCodeDTO();
        checkUpgradeAuthCodeDTO.setAuthCode(serviceUpgradeDTO.getAuthCode());
        checkUpgradeAuthCodeDTO.setPubKeyId(serviceUpgradeDTO.getPubKeyId());
        SecRestResponse<Object> checkAuthCodeResponse = pubKeyService.checkUpgradeAuthCode(checkUpgradeAuthCodeDTO);
        if (!checkAuthCodeResponse.isSuccess()) {
            throw new BusinessException(checkAuthCodeResponse.getCode(), checkAuthCodeResponse.getMessage());
        }
        if ("false".equals(String.valueOf(checkAuthCodeResponse.getResult()))) {
            log.error("升级密码校验错误,errorCode:{}", SecErrorCodeConstant.UPGRADE_CODE_CHECK_ERROR);
            throw new BusinessException(SecErrorCodeConstant.UPGRADE_CODE_CHECK_ERROR);
        }

        Long id = serviceUpgradeDTO.getId();
        ServiceInfoParamDTO serviceInfoParamDTO = new ServiceInfoParamDTO();
        serviceInfoParamDTO.setId(id);
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.getServiceInfoByServiceParam(serviceInfoParamDTO);
        if (CollectionUtils.isEmpty(res.getResult())) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_NOT_EXEIST);
        }
        ServiceInfoVO infoVO = res.getResult().get(0);

        //更新运行状态为升级中
        EditRunStatusParamDTO serviceStatusUpdateDTO = new EditRunStatusParamDTO();
        serviceStatusUpdateDTO.setId(id);
        serviceStatusUpdateDTO.setRunStatus(6);
        SecRestResponse<Object> response = serviceInfoServiceApi.editServiceRunStatus(serviceStatusUpdateDTO);

        //开启服务升级异步任务
        serviceUpgradeAsynTaskStart(infoVO);

        return response;
    }

    /**
     * @param infoVO
     * @description: 服务升级异步任务
     * @return: void
     */
    private void serviceUpgradeAsynTaskStart(ServiceInfoVO infoVO) {
        SysTaskDTO sysTaskDTO = new SysTaskDTO();
        sysTaskDTO.setTaskId(IdGenerator.ins().generator());
        String taskName = "serviceUpgradeAsynTask" + infoVO.getId();
        sysTaskDTO.setTaskName(taskName);
        sysTaskDTO.setTaskGroup("serviceUpgradeAsynTask");
        JSONObject jo = new JSONObject();
        jo.put(TASK_ID, sysTaskDTO.getTaskId());
        jo.put(SERVICE_ID_KEY, infoVO.getId());
        String jsonParam = "serviceInfoServiceImpl.serviceUpgradeAsynTask('" + jo.toJSONString() + "')";
        sysTaskDTO.setJsonParam(jsonParam);
        sysTaskService.add(sysTaskDTO);
    }

    @Override
    public void serviceUpgradeAsynTask(String jsonParm) {
        log.info("服务升级异步任务-开启,jsonParm = {}", jsonParm);
        SecRestResponse<ServiceInfoVO> response = serviceInfoServiceApi.findById(ServiceInfoDTO.builder().id(Long.valueOf(JSON.parseObject(jsonParm).getString(SERVICE_ID_KEY))).build());
        ServiceInfoDTO serviceInfoDTO = interInvokerConvert.voToDto(response.getResult());

        InterInvokeService invokeService = serviceFactory.getService(serviceInfoDTO.getServiceTypeId());
        ServiceAddContext serviceAddContext = new ServiceAddContext();

        try {
            log.info("服务升级异步任务-数据准备");
            initAddPrepare(serviceInfoDTO, serviceAddContext);

            log.info("服务升级异步任务-数据展示,serviceInfoDTO = {},serviceAddContext = {}", serviceInfoDTO, serviceAddContext);

            log.info("服务升级异步任务-初始化参数");
            InitServiceParamDTO initParamDTO = new InitServiceParamDTO();

            log.info("服务升级异步任务--获取设备信息");
            List<DeviceInvokeDTO> deviceList = deviceGroupService.getDeviceInvokeListByGroupId(serviceInfoDTO.getDeviceGroupId());
            if (CollectionUtils.isEmpty(deviceList)) {
                throw new BusinessException(SecErrorCodeConstant.NO_FREE_DEVICE_AVAILABLE);
            }

            try {
                log.info("服务升级参数-获取数据库信息");
                initAddInitServiceParamDb(initParamDTO, serviceInfoDTO, serviceAddContext);

                log.info("服务升级参数-获取网关信息");
                initParamDTO.setBusiGatewayIp(StringUtils.isNotBlank(serviceInfoDTO.getBusiGatewayIp()) ? serviceInfoDTO.getBusiGatewayIp() : serviceInfoDTO.getMgtGatewayIp());
                initParamDTO.setBusiGatewayPort(!ObjectUtils.isEmpty(serviceInfoDTO.getBusiGatewayPort()) ? serviceInfoDTO.getBusiGatewayPort() : serviceInfoDTO.getMgtGatewayPort());

                log.info("服务升级参数-签章操作密码机信息");
                //电子签章需要调用管控服务初始化设备 不然它的业务服务起不来 下面轮询获取状态就获取不到正常的状态
                if (ServiceTypeEnum.TSC.getId().equals(serviceInfoDTO.getServiceTypeId())) {
                    InitServiceParamDeviceDTO initServiceParamDeviceDTO = new InitServiceParamDeviceDTO();
                    //随机给一台设备使其可以启动即可 后续还会重新下发设备信息
                    initServiceParamDeviceDTO.setHsmList(Collections.singletonList(deviceList.get(0)));
                    String initJceScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                            builder().scriptCode(ScriptConstant.INIT_JCE_SCRIPT).serviceTypeId(null).build()).getResult();
                    initServiceParamDeviceDTO.setRunScript(initJceScript);
                    String jceIniScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                            builder().scriptCode(ScriptConstant.JCE_INI_SCRIPT).serviceTypeId(serviceInfoDTO.getServiceTypeId()).build()).getResult();
                    initServiceParamDeviceDTO.setIniScript(getScriptPath(serviceInfoDTO.getServiceTypeId(), jceIniScript));
                    remoteService.initJce(initServiceParamDeviceDTO, serviceInfoDTO);
                }

                //1时填protocol=33554432  2时填protocol=50331648
                String protocol = deviceList.get(0).getProtocol();
                initParamDTO.setDeviceType("33554432".equals(protocol) ? 1 : 2);

                log.info("服务升级参数-调用管控服务初始化配置接口");
                String initParamScript = dicScriptPathApi.getValueByCodeAndTypeId(DicScriptPathSeekValueDTO.
                        builder().scriptCode(ScriptConstant.INIT_PARAM_SCRIPT).serviceTypeId(serviceInfoDTO.getServiceTypeId()).build()).getResult();
                initParamDTO.setRunScript(getScriptPath(serviceInfoDTO.getServiceTypeId(), initParamScript));
                remoteService.initParam(initParamDTO, serviceInfoDTO);
            } catch (Exception e) {
                log.error("initAddInitServiceParam-服务初始化参数异常,serviceInfoDTO={}", serviceInfoDTO, e);
                throw new BusinessException(SecErrorCodeConstant.INIT_ADD_INIT_SERVICE_PARAM_ERROR, true, e.getMessage());
            }

            log.info("服务升级异步任务-初始化配置");
            initAddInitServiceConfig(serviceInfoDTO, serviceAddContext);

            log.info("服务升级异步任务-初始化启动");
            initAddInitServiceStart(serviceAddContext.getServiceInfoVO());

            log.info("服务升级异步任务-设备操作");
            try {
                //当前服务设备下发
                bindDeviceByList(serviceAddContext.getServiceInfoVO(), invokeService, deviceList);
            } catch (Exception e) {
                log.error("自动添加设备-下发设备信息异常-serviceAddDeviceOper,serviceInfoDTO = {}", serviceInfoDTO);
                throw new BusinessException(SecErrorCodeConstant.RECEIVE_DEVICE_ERROR);
            }

            //刷新菜单
            try {
                serviceMenuLoginUrlTask.getServiceLoginMenu();
            } catch (Exception e) {
                log.error("刷新菜单异常initAddAsynTask,jsonParm = {}", jsonParm);
            }
            genDatabaseUpgradeRecord(serviceInfoDTO, true, "Success");
        } catch (Exception e) {
            log.error("serviceUpgradeAsynTask-服务升级异步任务异常,serviceInfoDTO={}", serviceInfoDTO, e);
            //更新运行状态为升级失败
            EditRunStatusParamDTO serviceStatusUpdateDTO = new EditRunStatusParamDTO();
            serviceStatusUpdateDTO.setId(serviceInfoDTO.getId());
            serviceStatusUpdateDTO.setRunStatus(7);
            serviceInfoServiceApi.editServiceRunStatus(serviceStatusUpdateDTO);

            //报错异常信息入库 供页面展示
            recordPtOperationLog("服务升级", serviceInfoDTO, e.getMessage());

            genDatabaseUpgradeRecord(serviceInfoDTO, false, e.getMessage());
        }
    }

    /**
     * 生成服务升级记录
     */
    private void genDatabaseUpgradeRecord(ServiceInfoDTO serviceInfoDTO, boolean flag, String message) {
        DatabaseUpgradeRecordPO databaseUpgradeRecordPO = new DatabaseUpgradeRecordPO();
        databaseUpgradeRecordPO.setId(IdGenerator.ins().generator());
        DbInfoByGroupAndTypeVO dbVO = initAddDbCheck(serviceInfoDTO);
        databaseUpgradeRecordPO.setDatabaseMinimumUnitId(dbVO.getDatabaseMinimumUnitId());
        databaseUpgradeRecordPO.setDatabaseTypeId(dbVO.getDatabaseTypeId());
        databaseUpgradeRecordPO.setServiceTypeId(dbVO.getServiceTypeId());
        databaseUpgradeRecordPO.setRequestData(JSON.toJSONString(serviceInfoDTO));
        databaseUpgradeRecordPO.setResponseData(message);
        databaseUpgradeRecordPO.setStatus(flag ? CommonConstant.SUCCESS : CommonConstant.DB_UPGRADE_FAIL);
        databaseUpgradeRecordPO.setCreateTime(DateUtils.getCurrentLocalDateTime2String());
        databaseUpgradeRecordPO.setType(CommonConstant.UPGRADE_TYPE_SERVICE_UPGRADE);
        JSONObject jo = new JSONObject();
        jo.put("databaseUpgradeRecordPO", databaseUpgradeRecordPO);
        dbUnitServiceApi.genDatabaseUpgradeRecord(jo.toJSONString());
    }

    /**
     * 停止服务 by 租户id
     *
     * @param tenantId
     * @return
     */
    @Override
    public SecRestResponse<Object> stopByTenantId(Long tenantId) {
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.findList(ServiceInfoDTO.builder().tenantId(tenantId).build());
        if (Utils.isExistVal(res)) {
            for (ServiceInfoVO infoVO : res.getResult()) {
                //文件加密、时间戳、CA、数据库加密、VPN无法进行启停，自动跳过
                if (ServiceTypeEnum.busiAndMgtService.containsKey(infoVO.getServiceTypeId())) {
                    log.info("该服务无法进行启停stop,infoVO = {}", infoVO);
                    continue;
                }
                //判断服务是否已停止 已停止不需要调用
                InterInvokeService service = serviceFactory.getService(infoVO.getServiceTypeId());
                SecRestResponse<Map<Long, Integer>> result = service.getServiceState(InvokeServiceStateDTO.builder().data(Lists.newArrayList(infoVO)).build());
                if (result.isSuccess() && result.getResult() != null && 1 != result.getResult().get(infoVO.getId())) {
                    log.info("已停止不需要调用stop,infoVO = {}", infoVO);
                    continue;
                }

                service.stopBusiService(InvokeEndBusiDTO.builder().data(Lists.newArrayList(infoVO)).build());
            }
        }

        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Map<Long, Integer>> findServiceRunStatus(FindServiceRunStatusParamDTO findServiceRunStatusParamDTO) {
        return serviceInfoServiceApi.findServiceRunStatus(findServiceRunStatusParamDTO);
    }

    @Override
    public SecRestResponse<Map<Long, ServiceStatusVO>> findServiceRunAndOperStatus(FindServiceRunStatusParamDTO findServiceRunStatusParamDTO) {
        return serviceInfoServiceApi.findServiceRunAndOperStatus(findServiceRunStatusParamDTO);
    }

    /**
     * 接受设备信息 并修改配置信息
     *
     * @param deviceGroupId
     * @param deviceInfoDTOList
     * @return
     */
    @Override
    public SecRestResponse<Object> receiveDeviceInfo(Long deviceGroupId, List<DeviceInvokeDTO> deviceInfoDTOList) {
        if (CollectionUtils.isEmpty(deviceInfoDTOList)) {
            log.error("根据设备组id和设备信息 receiveDeviceInfo-设备组不存在设备,deviceGroupId = {}", deviceGroupId);
            return ResultUtil.ok();
        }

        //调用服务接收设备信息接口
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.findList(ServiceInfoDTO.builder().deviceGroupId(deviceGroupId).build());
        if (Utils.isExistVal(res)) {
            res.getResult().forEach(infoVO -> executor.execute(() -> {
                InterInvokeService invokeService = serviceFactory.getService(infoVO.getServiceTypeId());
                SecRestResponse<Object> result = invokeService.receiveDeviceInfo(Lists.newArrayList(infoVO), DeviceInvokeBeanDTO.builder().hsmList(deviceInfoDTOList).build());
                if (!result.isSuccess()) {
                    log.error("根据设备组id和设备信息 receiveDeviceInfo-调用异常,infoVO = {}", infoVO);
                }
            }));

        }

        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<Object> receiveDeviceInfoByServiceId(Long serviceId, List<DeviceInvokeDTO> deviceInfoDTOList) {
        ServiceInfoVO infoVO = getServiceVO(serviceId);
        //签章空设备集合也给它传，它需要置空,其他服务空就不处理了
        if (CollectionUtils.isEmpty(deviceInfoDTOList) && !ServiceTypeEnum.TSC.getId().equals(infoVO.getServiceTypeId())) {
            log.error("根据服务id和设备信息 receiveDeviceInfoByServiceId-设备组不存在设备,serviceId = {}", serviceId);
            return ResultUtil.ok();
        }

        //签章每次都置空
        if (ServiceTypeEnum.TSC.getId().equals(infoVO.getServiceTypeId())) {
            deviceInfoDTOList = new ArrayList<>();
        }

        InterInvokeService invokeService = serviceFactory.getService(infoVO.getServiceTypeId());
        SecRestResponse<Object> result = invokeService.receiveDeviceInfo(Lists.newArrayList(infoVO), DeviceInvokeBeanDTO.builder().hsmList(deviceInfoDTOList).build());
        if (!result.isSuccess()) {
            log.error("根据服务id和设备信息 receiveDeviceInfoByServiceId-调用异常,infoVO = {}", infoVO);
        }

        return ResultUtil.ok();
    }

    /**
     * 接受设备信息 并修改配置信息
     *
     * @param deviceGroupId
     * @return
     */
    @Override
    public SecRestResponse<Object> receiveDeviceInfo(Long deviceGroupId) {
        //调用服务接收设备信息接口
        SecRestResponse<List<ServiceInfoVO>> res = serviceInfoServiceApi.findList(ServiceInfoDTO.builder().deviceGroupId(deviceGroupId).build());
        if (Utils.isExistVal(res)) {
            List<DeviceInvokeDTO> list = deviceGroupService.getDeviceInvokeListByGroupId(deviceGroupId);
            if (!CollectionUtils.isEmpty(list)) {
                res.getResult().forEach(infoVO -> executor.execute(() -> {
                    InterInvokeService invokeService = serviceFactory.getService(infoVO.getServiceTypeId());
                    SecRestResponse<Object> result = invokeService.receiveDeviceInfo(Lists.newArrayList(infoVO), DeviceInvokeBeanDTO.builder().hsmList(list).build());
                    if (!result.isSuccess()) {
                        log.error("根据设备组id receiveDeviceInfo-调用异常,infoVO = {}", infoVO);
                    }
                }));
            } else {
                log.error("根据设备组id receiveDeviceInfo-设备组不存在设备,deviceGroupId = {}", deviceGroupId);
            }
        }

        return SecRestResponse.success();
    }

    @Override
    public SecRestResponse<List<ServiceInfoVO>> getServiceByFree() {
        return serviceInfoServiceApi.getServiceByFree();
    }

    @Override
    public ServiceInfoVO getServiceInfo(Long serviceId) {
        List<ServiceInfoVO> serviceInfoVOS = getServiceList(serviceId, null);
        if (CollectionUtils.isEmpty(serviceInfoVOS)) {
            return null;
        } else {
            return serviceInfoVOS.get(0);
        }
    }

    @Override
    public ServiceInfoVO getServiceInfoById(Long serviceId) {
        ServiceInfoParamDTO serviceInfoParamDTO = new ServiceInfoParamDTO();
        serviceInfoParamDTO.setId(serviceId);
        List<ServiceInfoVO> serviceInfoVOS = getServiceList(serviceInfoParamDTO);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(serviceInfoVOS)) {
            return null;
        } else {
            return serviceInfoVOS.get(0);
        }
    }

    /**
     * 不抛异常
     *
     * @param serviceId
     * @return
     */
    @Override
    public ServiceInfoVO getServiceInfoByIdCatch(Long serviceId) {
        ServiceInfoVO serviceInfoVO = null;
        try {

            serviceInfoVO = getServiceInfoById(serviceId);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return serviceInfoVO;
    }
    @Override
    public List<ServiceInfoVO> getServiceList(Long serviceId, String serviceName){
        ServiceInfoParamDTO serviceInfoParamDTO = new ServiceInfoParamDTO();

        if (serviceId != null) {
            serviceInfoParamDTO.setId(serviceId);
        }

        if (StringUtils.isNotBlank(serviceName)) {
            serviceInfoParamDTO.setServiceName(serviceName);
        }
        return getServiceList(serviceInfoParamDTO);
    }

    @Override
    public List<ServiceInfoVO> getServiceList(ServiceInfoParamDTO serviceInfoParamDTO) {
        SecRestResponse<List<ServiceInfoVO>> secRestResponse = serviceInfoServiceApi.getServiceInfoByServiceParam(serviceInfoParamDTO);

        if (secRestResponse.isSuccess()) {
            if (secRestResponse.getResult() != null) {
                return secRestResponse.getResult();
            } else {
                return new ArrayList<>();
            }
        } else {
            throw new BusinessException(secRestResponse.getCode());
        }
    }

    /**
     * 获取租户下服务列表
     *
     * @param tenantIdList
     * @return
     */
    @Override
    public List<ServiceInfoVO> getTenantServiceList(List<Long> tenantIdList) {
        List<ServiceInfoVO> serviceInfoVOList = new ArrayList<>();
        List<Long> allServiceGroupIdList=new ArrayList<>();
        for (Long tenantId : tenantIdList) {
            List<Long> serviceGroupIdList = tenantToServiceGroupService.getNotShareGroupIdByTenantId(tenantId);
            allServiceGroupIdList.addAll(serviceGroupIdList);
        }
        List<Long> finalServiceGroupIdList = allServiceGroupIdList.stream().distinct().collect(Collectors.toList());
        ServiceInfoDTO serviceInfoDTO = new ServiceInfoDTO();
        serviceInfoDTO.setServiceGroupIdList(finalServiceGroupIdList);
        SecRestResponse<List<ServiceInfoVO>> response = serviceInfoServiceApi.findList(serviceInfoDTO);

        if (response.isSuccess()) {
            if (CollectionUtils.isEmpty(response.getResult())) {
                return serviceInfoVOList;
            }
        } else {
            throw new BusinessException(response.getCode());
        }

        return response.getResult();
    }

    @Override
    public SecRestResponse<List<ServiceTenantVO>> getTenantService() {
        LoginUser loginUser = LoginUserUtil.getCurrentLoginUser();
        List<Long> tenantIdList = new ArrayList<>(2);
        tenantIdList.add(loginUser.getTenantId());
        //租户共享服务租户id为1
        tenantIdList.add(CommonConstant.TOP_TENANT_ID);

        List<ServiceTenantVO> serviceTenantVOList = new ArrayList<>();
        List<ServiceInfoVO> serviceVOList = getTenantServiceList(tenantIdList);
        Map<Long, List<ServiceInfoVO>> voMap = serviceVOList.stream().collect(Collectors.groupingBy(ServiceInfoVO::getServiceTypeId));
        voMap.forEach((key, value) -> {
            ServiceInfoVO infoVO = value.get(0);

            ServiceTypeEnum typeEnum = ServiceTypeEnum.byId(infoVO.getServiceTypeId());
            ServiceTenantVO serviceTenantVO = ServiceTenantVO.builder()
                    .serviceTypeName(typeEnum.getName())
                    .serviceName(infoVO.getServiceName())
                    .mgtGatewayIp(infoVO.getMgtGatewayIp())
                    .mgtGatewayPort(infoVO.getMgtGatewayPort())
                    .busiGatewayIp(infoVO.getBusiGatewayIp())
                    .busiGatewayPort(infoVO.getBusiGatewayPort())
                    .build();
            //若kms tcp端口不为空 获取网关tcp端口
            if (infoVO.getTcpPort() != null) {
                serviceTenantVO.setTcpGatewayPort(getGateWayTcpPort(infoVO));
            }
            //判断是否为签章服务，如果是签章，拼服务地址
            Long tscId = 10L;
            if (tscId.equals(typeEnum.getId())) {
                String serUrl = String.format("%s%s%s%s%s", "/thridsystem?tenantCode=", loginUser.getTenantCode(), "&url=/", ServiceUriEnum.getRegexPath(typeEnum.getId()), "/");
                String url = HttpUtils.httpsUrl(serviceTenantVO.getMgtGatewayIp(), serviceTenantVO.getMgtGatewayPort(), serUrl);
                serviceTenantVO.setServiceLoginUrl(url);
            }
            serviceTenantVOList.add(serviceTenantVO);
        });
        return ResultUtil.ok(serviceTenantVOList);
    }

    private int getGateWayTcpPort(ServiceInfoVO infoVO) {
        ServiceGatewayRouteDTO serviceTypeTenantParam = new ServiceGatewayRouteDTO();
        serviceTypeTenantParam.setRouteType(4);
        serviceTypeTenantParam.setBusiTypeId(infoVO.getServiceTypeId());
        serviceTypeTenantParam.setTenantId(infoVO.getTenantId());
        // 查询列表
        SecRestResponse<List<ServiceGatewayRouteVO>> secRestResponse = serviceGatewayRouteService.findList(serviceTypeTenantParam);
        if (!CollectionUtils.isEmpty(secRestResponse.getResult())) {
            return secRestResponse.getResult().get(0).getServerPort();
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecRestResponse<Object> tenantServiceEdit(ServiceEditDTO serviceEditDTO) {
        SecRestResponse<ServiceInfoVO> response = serviceInfoServiceApi.findById(ServiceInfoDTO.builder().id(serviceEditDTO.getId()).build());
        ServiceInfoVO serviceInfoVO = response.getResult();
        if (ObjectUtils.isEmpty(serviceInfoVO)) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_NOT_EXEIST);
        }

        InterInvokeService invokeService = serviceFactory.getService(serviceInfoVO.getServiceTypeId());
        serviceInfoVO.setDeviceGroupId(serviceEditDTO.getDeviceGroupId());
        //绑定设备组的话需要调用接收设备接口
        bindDeviceAndCheck(serviceInfoVO, invokeService);

        //更新服务表
        EditByTenantParamDTO editByTenantParamDTO = new EditByTenantParamDTO();
        editByTenantParamDTO.setId(serviceEditDTO.getId());
        editByTenantParamDTO.setDeviceGroupId(serviceEditDTO.getDeviceGroupId());
        serviceInfoServiceApi.editByTenant(editByTenantParamDTO);
        return ResultUtil.ok();
    }

    @Override
    public List<ServiceInfoVO> getServiceInfoByTenantId(Long tenantId) {
        ServiceListByTenantIdDTO serviceListByTenantIdDTO = new ServiceListByTenantIdDTO();
        serviceListByTenantIdDTO.setTenantId(tenantId);
        SecRestResponse<List<ServiceInfoVO>> response = serviceInfoServiceApi.getServiceByTenantId(serviceListByTenantIdDTO);
        if (response.isSuccess()) {
            return response.getResult();
        } else {
            throw new BusinessException(response.getCode());
        }
    }

    @Override
    public List<ServiceInfoVO> getServiceInfoByGroupId(Long serviceGroupId) {
        ServiceListByGroupIdDTO serviceListByGroupIdDTO = new ServiceListByGroupIdDTO();
        serviceListByGroupIdDTO.setServiceGroupId(serviceGroupId);
        SecRestResponse<List<ServiceInfoVO>> response = serviceInfoServiceApi.getServiceByGroupId(serviceListByGroupIdDTO);
        if (response.isSuccess()) {
            return response.getResult();
        } else {
            throw new BusinessException(response.getCode());
        }
    }

    @Override
    public List<ServiceInfoVO> getServiceInfoByGatewayId(Long gatewayId) {
        return serviceInfoServiceApi.findList(ServiceInfoDTO.builder().gatewayId(gatewayId).build()).getResult();
    }

    /**
     * 根据IP查询监控中的服务
     *
     * @param ip
     * @return
     */
    @Override
    public List<ServiceInfoVO> getMonitorServiceListByIp(String ip) {
        ServiceInfoIpDTO serviceInfoIpDTO = new ServiceInfoIpDTO();
        serviceInfoIpDTO.setIp(ip);

        SecRestResponse<List<ServiceInfoVO>> response = serviceInfoServiceApi.getMonitorServiceListByIp(serviceInfoIpDTO);

        if (response.isSuccess()) {
            return response.getResult();
        } else {
            throw new BusinessException(response.getCode());
        }
    }

    @Override
    public SecRestResponse<Long> getCountService(Long regionId) {
        RegionIdDTO regionIdDTO = new RegionIdDTO();
        regionIdDTO.setRegionId(regionId);
        return serviceInfoServiceApi.getCountService(regionIdDTO);
    }

    @Override
    public SecRestResponse<List<ServiceInfoVO>> getServiceInfoByServiceParam(ServiceInfoParamDTO serviceInfoParamDTO) {
        return serviceInfoServiceApi.getServiceInfoByServiceParam(serviceInfoParamDTO);
    }

    @Override
    public List<ServiceInfoVO> findList(ServiceInfoDTO serviceInfoDTO) {
        Long tenantId = serviceInfoDTO.getTenantId();
        if(tenantId != null && !tenantId.equals(CommonConstant.TOP_TENANT_ID)){
            List<Long> serviceGroupIdList = tenantToServiceGroupService.getNotShareGroupIdByTenantId(tenantId);
            serviceInfoDTO.setServiceGroupIdList(serviceGroupIdList);
            serviceInfoDTO.setTenantId(null);
        }
        SecRestResponse<List<ServiceInfoVO>> restResponse = serviceInfoServiceApi.findList(serviceInfoDTO);
        if (!restResponse.isSuccess()) {
            throw new BusinessException(restResponse.getCode());
        }
        return restResponse.getResult();
    }

    /**
     * 根据区域ID查询服务信息
     *
     * @param serviceInfoByRegionIdDTO
     * @return
     */
    @Override
    public SecRestResponse<List<ServiceInfoVO>> getServiceInfoListByRegionId(ServiceInfoByRegionIdDTO serviceInfoByRegionIdDTO) {
        return serviceInfoServiceApi.getServiceInfoListByRegionId(serviceInfoByRegionIdDTO);
    }

    @Override
    public List<Long> getServiceIdList() {
        SecRestResponse<List<Long>> restResponse = serviceInfoServiceApi.getServiceIdList();
        if (!restResponse.isSuccess()) {
            throw new BusinessException(restResponse.getCode());
        }
        return restResponse.getResult();
    }

    @Override
    public String getServiceNameById(Long serviceId) {
        ServiceInfoDTO serviceInfoDTO = new ServiceInfoDTO();
        serviceInfoDTO.setId(serviceId);
        SecRestResponse<ServiceInfoVO> restResponse = serviceInfoServiceApi.findById(serviceInfoDTO);
        if (!restResponse.isSuccess()) {
            throw new BusinessException(restResponse.getCode());
        }
        return restResponse.getResult().getServiceName();
    }

    @Override
    public Map<Long, String> getServiceIdNameMap() {
        List<ServiceInfoVO> list = findList(new ServiceInfoDTO());
        return list.stream().collect(Collectors.toMap(ServiceInfoVO::getId, ServiceInfoVO::getServiceName));
    }

    @Override
    public Boolean checkIpExist(String ip, Long regionId) {
        ServiceInfoPageDTO pageDTO = new ServiceInfoPageDTO();
        pageDTO.setIp(ip);
        pageDTO.setRegionId(regionId);

        SecRestResponse<SecPageVO<ServiceInfoPageVO>> restResponse = serviceInfoServiceApi.find(pageDTO);
        if (!restResponse.isSuccess()) {
            throw new BusinessException(restResponse.getCode());
        }
        return CollectionUtils.isNotEmpty(restResponse.getResult().getList());
    }

    @Override
    public List<String> getBusiMonitorIpList() {
        SecRestResponse<List<String>> restResponse = serviceInfoServiceApi.getBusiMonitorIpList();
        if (!restResponse.isSuccess()) {
            throw new BusinessException(restResponse.getCode());
        }
        return restResponse.getResult();
    }

    @Autowired
    ProxyRouteService proxyRouteService;

    /**
     * 添加区域管控代理
     * @param serviceInfoDTO
     */
    private void initAddRemoteProxy(ServiceInfoDTO serviceInfoDTO){
        if (regionService.checkIsRegionMode()){
            log.info("服务添加异步任务-添加区域管控代理");
            ProxyRouteDTO proxyRouteDTO = new ProxyRouteDTO();
            proxyRouteDTO.setRegionId(serviceInfoDTO.getRegionId());
            proxyRouteDTO.setProxyRouteTypeEnum(ProxyRouteTypeEnum.SERVICE_REMOTE);
            proxyRouteDTO.setIp(serviceInfoDTO.getRemoteIp());
            proxyRouteDTO.setPort(String.valueOf(serviceInfoDTO.getRemotePort()));
            if(proxyRouteService.checkUpStreamExist(proxyRouteDTO)){
                return;
            }
            proxyRouteService.addProxyRoute(proxyRouteDTO);
        }
    }

    private void handleContainerMod(ServiceInfoDTO serviceInfoDTO, ServiceAddContext serviceAddContext) {
        if (serviceInfoDTO.getDeployMod() == DeployMod.DOCKER.getValue()) {
            //检查宿主机的负载情况
            SecRestResponse<RemoteSysStatusCheckVo> sysStatusCheckResponse = remoteService.sysStatusCheck(serviceInfoDTO);
            if (null == sysStatusCheckResponse || null == sysStatusCheckResponse.getResult() || sysStatusCheckResponse.getResult().getStatus().equals(4)) {
                log.error("host is busy,{}", sysStatusCheckResponse);
                throw new BusinessException(HOST_STATUS_BUSY);
            }
            //
            String arch = sysStatusCheckResponse.getResult().getArch();
            ServiceTypeVO serviceTypeVO = serviceAddContext.getServiceTypeVO();
            String serviceTypeCode = serviceTypeVO.getServiceCode();
            DockerImageVO dockerImageVO = dockerImageService.queryLastImage(serviceTypeCode, ServiceInfoConstant.ARCH_MAP.get(arch));
            if (null == dockerImageVO) {
                log.error("image {}_{} not exist", serviceTypeCode, arch);
                throw new BusinessException(IMAGE_NOT_EXIST_ADD_SERVICE, true, serviceTypeCode, arch);
            }
            Map<String, String> remoteHost = regionRemoteAdapterService.getRemoteHost(serviceInfoDTO);
            Docker docker = DockerUtils.https(remoteHost.get(RemoteConstant.REMOTE_IP), String.valueOf(remoteHost.get(RemoteConstant.REMOTE_PORT)), remoteService.generateHeader(serviceInfoDTO), remoteService.getSwak());
            //上传镜像 如果镜像不存在 则从hdfs上传
            uploadImage(dockerImageVO, serviceInfoDTO,docker, serviceAddContext);

            //创建容器
            String containerName = "ccsp-" + serviceTypeCode + "-" + serviceInfoDTO.getId();
            //创建docker容器
            List<PortMapping> portMappings = new ArrayList<>();
            //使用map过滤重复的端口
            Map<Integer,ContainerCreateParam.HostIpPort> portMap = this.filterRepeatPort(serviceTypeVO,serviceInfoDTO);
            portMap.forEach((k, v) -> portMappings.add(new PortMapping(tcp, k, v)));
            Map<String, String> env = new HashMap<>();
            if (ServiceTypeEnum.VPN.getCode().equals(serviceTypeCode)) {
                //特殊处理VPN业务服务代理端口范围
                this.addVpnPortRange(portMappings, env, serviceInfoDTO);
            }

            //创建容器
            ContainerCreateResult containerCreateResult = docker.container().run(containerName, dockerImageVO.getImageName(), dockerImageVO.getImageVersion(), portMappings,env);
            log.info("container :{}  create result : {}", containerName, containerCreateResult);
        }
    }

    private void uploadImage(DockerImageVO dockerImageVO, ServiceInfoDTO serviceInfoDTO, Docker docker, ServiceAddContext serviceAddContext) {
        String imageFileName = null;
        String redisKey = REDIS_KEY_UPLOAD_IMAGES + serviceInfoDTO.getRemoteIp() + ":" + serviceAddContext.getServiceTypeVO().getServiceCode();
        try {
            log.info("uploadImage distributedLock start ...");
            boolean tryLock = distributedLock.tryLock(redisKey, TimeUnit.MINUTES, 1, 30);
            if (!tryLock){
                log.error("try lock failed redisKey = {}",redisKey);
                throw new BusinessException(IMAGE_IS_UPLOADING,true,serviceAddContext.getServiceTypeVO().getServiceCode(),serviceInfoDTO.getRemoteIp());
            }
            List<Image> images = docker.image().listByNameAndTag(dockerImageVO.getImageName(), dockerImageVO.getImageVersion());
            //如果镜像不存在 则从hdfs上传
            if (CollectionUtils.isNotEmpty(images)) {
                return;
            }
            FileInfoPO fileInfo = dockerImageVO.getFileInfo();
            String downloadTempPath = serviceAddContext.getConfigMap().getOrDefault(CommonConstant.DOWNLOAD_TEMP_PATH,"/opt/ccsp/temp/");
            imageFileName = downloadTempPath + serviceInfoDTO.getId() + "/" + fileInfo.getFileName();
            log.info("start download image file ---");
            fileInfoService.download(fileInfo, imageFileName);
            log.info("start upload image file to remote ---");
            remoteService.imageReceive(
                    new ImageReceiveParamDTO(new File(imageFileName)
                            , dockerImageVO.getImageName()
                            , fileInfo.getFileDigest()
                            , dockerImageVO.getImageVersion())
                    , serviceInfoDTO);
        } catch (Exception e) {
            log.info("for 10 * 30s wait image file download and upload");
            boolean imageDownAndUpResult = false;
            for (int i = 0; i < 10; i++) {
                try {
                    Thread.sleep(30000);
                } catch (InterruptedException interruptedException) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException(interruptedException);
                }
                log.info("go remote {} check image {}:{} exits", i + 1, dockerImageVO.getImageName(), dockerImageVO.getImageVersion());
                List<Image> images = docker.image().listByNameAndTag(dockerImageVO.getImageName(), dockerImageVO.getImageVersion());
                if (!CollectionUtils.isEmpty(images)) {
                    log.info("good success remote {} check image {}:{} exits", i + 1, dockerImageVO.getImageName(), dockerImageVO.getImageVersion());
                    imageDownAndUpResult = true;
                    break;
                }
            }
            if (!imageDownAndUpResult) {
                log.error("final image file download and upload error----");
                throw new BusinessException(IMAGE_FILE_DOWNLOAD_UPLOAD_ERROR);
            }
        } finally {
            //删除残留的镜像文件
            log.info("delete no use image file");
            distributedLock.unlock(redisKey);
            if (null != imageFileName) {
                File file = new File(imageFileName);
                file.delete();
            }
        }
    }

    private void addVpnPortRange(List<PortMapping> portMappings,Map<String, String> env, ServiceInfoDTO serviceInfoDTO) {
        String redisKey = REDIS_KEY_ADD_VPN_PORT_RANGE;
        try {
            log.info("addVpnPortRange distributedLock start,serviceInfoDTO={}",serviceInfoDTO);
            boolean tryLock = distributedLock.tryLock(redisKey, TimeUnit.SECONDS, 60, 60);
            if (!tryLock) {
                log.error("try lock failed redisKey = {}", redisKey);
                throw new BusinessException(ADD_VPN_PORT_RANGE_LOCK_FAILED);
            }
            log.info("addVpnPortRange start,serviceInfoDTO={}",serviceInfoDTO);
            String portRange = "PORT_RANGE";
            String mappingCode = "vpnPort";
            ServiceDockerPortMappingDTO serviceDockerPortMappingDTO = new ServiceDockerPortMappingDTO();
            serviceDockerPortMappingDTO.setMappingCode(mappingCode);
            SecRestResponse<List<ServiceGroupDockerPortMappingVO>> response = serviceDockerPortMappingApi.findByServiceGroupId(serviceDockerPortMappingDTO);
            List<ServiceGroupDockerPortMappingVO> ServiceGroupPortMappingList = response.getResult();

            ServiceInfoAddPortsDTO serviceInfoAddPortsDTO = new ServiceInfoAddPortsDTO();
            serviceInfoAddPortsDTO.setServiceInfoId(serviceInfoDTO.getId());
            List<ServiceDockerPortMappingDTO> serviceDockerPortMappingDTOList = new ArrayList<>();
            int range = 100;
            //如果未查询到vpn的映射端口，则从30000端口开始分配,分配100个
            if (CollectionUtils.isEmpty(ServiceGroupPortMappingList)) {
                int startPort = 30000;
                for (int i = 0; i < range; i++) {
                    int mappingPort = startPort + i;
                    portMappings.add(new PortMapping(tcp, mappingPort, new ContainerCreateParam.HostIpPort(serviceInfoDTO.getBusiIp(), String.valueOf(mappingPort))));
                    serviceDockerPortMappingDTOList.add(new ServiceDockerPortMappingDTO(null, null,
                            mappingCode,
                            mappingPort,
                            mappingPort,
                            null, null, null, null, null));
                }
                String start = String.valueOf(startPort);
                String end = String.valueOf(startPort + range - 1);
                env.put(portRange, start + "-" + end);
                serviceInfoAddPortsDTO.setMappingPorts(serviceDockerPortMappingDTOList);
                serviceDockerPortMappingApi.addByServiceInfoId(serviceInfoAddPortsDTO);
                return;
            }
            List<ServiceGroupDockerPortMappingVO> currentGroupPortMappingList = ServiceGroupPortMappingList
                    .stream()
                    .filter(t -> t.getServiceGroupId().equals(serviceInfoDTO.getServiceGroupId()))
                    .collect(Collectors.toList());
            //如果ServiceGroupPortMappingList是空,说明同一服务组下还没有vpn被创建,按顺序给vpn分配40000以下的端口
            if (CollectionUtils.isEmpty(currentGroupPortMappingList)) {
                if (ServiceGroupPortMappingList.get(0).getOutsidePort() >= 39999) {
                    //Vpn创建数量已达上线
                    throw new BusinessException(VPN_NUM_OVER_THE_LIMIT);
                }
                int startPort = response.getResult().get(0).getOutsidePort() + 1;
                for (int i = 0; i < range; i++) {
                    int mappingPort = startPort + i;
                    portMappings.add(new PortMapping(tcp, mappingPort, new ContainerCreateParam.HostIpPort(serviceInfoDTO.getBusiIp(), String.valueOf(mappingPort))));
                    serviceDockerPortMappingDTOList.add(new ServiceDockerPortMappingDTO(null, null,
                            mappingCode,
                            mappingPort,
                            mappingPort,
                            null, null, null, null, null));
                }
                String start = String.valueOf(startPort);
                String end = String.valueOf(startPort + range - 1);
                env.put(portRange, start + "-" + end);
            }
            //如果ServiceGroupPortMappingList不是空,说明同一服务组下已经创建过vpn,同一服务组下vpn使用相同的端口
            else {
                Set<Integer> ports = currentGroupPortMappingList.stream()
                        .map(ServiceGroupDockerPortMappingVO::getOutsidePort)
                        .collect(Collectors.toSet());

                ports.forEach(t -> {
                    portMappings.add(new PortMapping(tcp, t, new ContainerCreateParam.HostIpPort(serviceInfoDTO.getBusiIp(), String.valueOf(t))));

                    serviceDockerPortMappingDTOList.add(new ServiceDockerPortMappingDTO(null, null, mappingCode, t, t,
                            null, null, null, null, null));
                });
                String start = String.valueOf(ports.stream().min(Comparator.comparingInt(o -> o)));
                String end = String.valueOf(ports.stream().max(Comparator.comparingInt(o -> o)));
                env.put(portRange, start + "-" + end);
            }

            serviceInfoAddPortsDTO.setMappingPorts(serviceDockerPortMappingDTOList);
            serviceDockerPortMappingApi.addByServiceInfoId(serviceInfoAddPortsDTO);
        } finally {
            distributedLock.unlock(redisKey);
        }
    }

    /**
     * 过滤重复的端口
     * @param serviceTypeVO
     * @param serviceInfoDTO
     * @return
     */
    Map<Integer,ContainerCreateParam.HostIpPort> filterRepeatPort(ServiceTypeVO serviceTypeVO,ServiceInfoDTO serviceInfoDTO){
        Map<Integer,ContainerCreateParam.HostIpPort> portMap = new HashMap<>();
        //管理端口映射
        portMap.put(serviceTypeVO.getMgtPort(), new ContainerCreateParam.HostIpPort(serviceInfoDTO.getMgtIp(), String.valueOf(serviceInfoDTO.getMgtPort())));
        //业务端口映射
        portMap.put(serviceTypeVO.getBusiPort(), new ContainerCreateParam.HostIpPort(serviceInfoDTO.getBusiIp(), String.valueOf(serviceInfoDTO.getBusiPort())));
        //tcp端口映射
        if (null != serviceTypeVO.getTcpPort()) {
            portMap.put(serviceTypeVO.getTcpPort(), new ContainerCreateParam.HostIpPort(serviceInfoDTO.getBusiIp(), String.valueOf(serviceInfoDTO.getTcpPort())));
        }
        //扩展端口映射
        if (null != serviceTypeVO.getExpandPort()) {
            portMap.put(serviceTypeVO.getExpandPort(), new ContainerCreateParam.HostIpPort(serviceInfoDTO.getBusiIp(), String.valueOf(serviceInfoDTO.getExpandPort())));
        }
        //监控端口映射
        if (null != serviceTypeVO.getMonitorPort()) {
            portMap.put(serviceTypeVO.getMonitorPort(), new ContainerCreateParam.HostIpPort(serviceInfoDTO.getBusiIp(), String.valueOf(serviceInfoDTO.getMonitorPort())));
        }
        return portMap;
    }

    private void containerPrepare(ServiceAddContext serviceAddContext, ServiceInfoDTO serviceInfoDTO){
        String serviceTypeCode = serviceAddContext.getServiceTypeVO().getServiceCode();
        //调用【根据密码服务类型查询最新镜像】接口，判断是容器模式or进程模式
        DockerImageVO dockerImageVO = dockerImageService.queryLastImage(serviceTypeCode);
        if (dockerImageVO != null) {
            // 管理端口 和 业务端口不能一样 否则会导致 容器创建失败
//            if (!Objects.isNull(serviceInfoDTO.getMgtPort()) && !Objects.isNull(serviceInfoDTO.getBusiPort())
//                &&serviceInfoDTO.getMgtPort().equals(serviceInfoDTO.getBusiPort())) {
//                //
//                throw new BusinessException(SecErrorCodeConstant.SERVICE_PORT_SAME);
//            }
            //容器模式,填充容器信息
            serviceInfoDTO.setDeployMod(DeployMod.DOCKER.getValue());
            serviceInfoDTO.setImageId(String.valueOf(dockerImageVO.getId()));
            serviceInfoDTO.setContainerName("ccsp-" + serviceTypeCode + "-" + serviceInfoDTO.getId());

            ServiceTypeVO serviceTypeVO = serviceAddContext.getServiceTypeVO();
            //根据前端入参的端口信息，调用【获取可用端口】接口并修改serviceInfoDTO、serviceAddContext.serviceInfoVO中的端口信息，可用端口不可与service_docker_port_mapping表中数据冲突
            List<Integer> checkPorts = new ArrayList<>();
            if (serviceInfoDTO.getMgtPort() != null) {
                checkPorts.add(serviceInfoDTO.getMgtPort());
            }
            if (serviceInfoDTO.getBusiPort() != null) {
                checkPorts.add(serviceInfoDTO.getBusiPort());
            }
            //需要返回的可用的端口数量
            int wantCount = 2;
            //如果需要tcpport和ExpandPort、monitorPort，wantCount需要各加1
            if (null != serviceTypeVO.getTcpPort()) {
                wantCount++;
            }
            if (null != serviceTypeVO.getExpandPort()) {
                wantCount++;
            }
            if (null != serviceTypeVO.getMonitorPort()) {
                wantCount++;
            }
            //获取可用端口列表
            //availablePorts中的端口顺序是根据checkPorts的顺序返回的，filterRepeatPort方法做处理时注意顺序
            List<Integer> availablePorts = serviceInfoPortsService.getOkPortsFromRemote(serviceInfoDTO, ServiceTypeEnum.byCode(serviceAddContext.getServiceTypeVO().getServiceCode()), checkPorts, wantCount);
            if (CollectionUtils.isEmpty(availablePorts) || availablePorts.size() < wantCount) {
                log.error("container mode port check failed,param->{},result->{}", checkPorts, availablePorts);
                throw new BusinessException(CONTAINER_PORT_CHECK_FAILED);
            }
            Collections.reverse(availablePorts);
            //使用map过滤重复的端口
            Map<Integer,Integer> portMap = this.filterRepeatPort(serviceTypeVO,availablePorts,wantCount);
            //这里填充的是宿主机上的端口,容器内部的端口可从serviceType获取
            serviceInfoDTO.setMgtPort(portMap.get(serviceTypeVO.getMgtPort()));
            serviceInfoDTO.setBusiPort(portMap.get(serviceTypeVO.getBusiPort()));
            if (null != serviceTypeVO.getTcpPort()) {
                serviceInfoDTO.setTcpPort(portMap.get(serviceTypeVO.getTcpPort()));
            }
            if (null != serviceTypeVO.getExpandPort()) {
                serviceInfoDTO.setExpandPort(portMap.get(serviceTypeVO.getExpandPort()));
            }
            if (null != serviceTypeVO.getMonitorPort()) {
                serviceInfoDTO.setMonitorPort(portMap.get(serviceTypeVO.getMonitorPort()));
            }
        }
    }

    /**
     * 过滤 serviceTypeVO 重复的端口
     * @param serviceTypeVO
     * @param availablePorts
     * @param wantCount
     * @return
     */
    private Map<Integer, Integer> filterRepeatPort(ServiceTypeVO serviceTypeVO, List<Integer> availablePorts, int wantCount) {
        Map<Integer, Integer> portMap = new HashMap<>();
        //此处 端口 注意顺序！！！
        portMap.put(serviceTypeVO.getMgtPort(), availablePorts.get(--wantCount));
        portMap.put(serviceTypeVO.getBusiPort(), availablePorts.get(--wantCount));
        if (null != serviceTypeVO.getTcpPort()) {
            portMap.put(serviceTypeVO.getTcpPort(), availablePorts.get(--wantCount));
        }
        if (null != serviceTypeVO.getExpandPort()) {
            portMap.put(serviceTypeVO.getExpandPort(), availablePorts.get(--wantCount));
        }
        if (null != serviceTypeVO.getMonitorPort()) {
            portMap.put(serviceTypeVO.getMonitorPort(), availablePorts.get(--wantCount));
        }
        return portMap;
    }

    @Override
    public List<ServiceInfoVO> getListByTenantAndTypeId(Long tenantId, List<Long> busiTypeIds) {
        ServiceInfoDTO serviceInfoDTO = new ServiceInfoDTO();
        serviceInfoDTO.setTenantId(tenantId);
        serviceInfoDTO.setServiceTypeIds(busiTypeIds);
        SecRestResponse<List<ServiceInfoVO>> response = tenantToServiceGroupService.getListByTenantAndTypeId(serviceInfoDTO);

        if (response.isSuccess()) {
            return response.getResult();
        } else {
            throw new BusinessException(response.getCode());
        }
    }


    /**
     * 添加区域密码服务代理
     * @param serviceInfoDTO
     */
    private void initAddServiceProxy(ServiceInfoDTO serviceInfoDTO){
        if (regionService.checkIsRegionMode()){
            log.info("服务添加异步任务-添加区域密码服务代理");
            ProxyRouteDTO proxyRouteDTO = new ProxyRouteDTO();
            proxyRouteDTO.setRegionId(serviceInfoDTO.getRegionId());
            proxyRouteDTO.setProxyRouteTypeEnum(ProxyRouteTypeEnum.SERVICE);
            proxyRouteDTO.setIp(serviceInfoDTO.getMgtIp());
            proxyRouteDTO.setPort(String.valueOf(serviceInfoDTO.getMgtPort()));
            if(proxyRouteService.checkUpStreamExist(proxyRouteDTO)){
                throw new BusinessException(REGION_ADD_PROXY_FAILED_EXIST);
            }
            proxyRouteService.addProxyRoute(proxyRouteDTO);
        }
    }

    /**
     * 删除服务代理
     * @param serviceInfoDTO
     */
    private void deleteServiceProxy(ServiceInfoDTO serviceInfoDTO){
        if (regionService.checkIsRegionMode()){
            log.info("服务添加异步任务-删除区域密码服务代理");
            ProxyRouteDTO proxyRouteDTO = new ProxyRouteDTO();
            proxyRouteDTO.setRegionId(serviceInfoDTO.getRegionId());
            proxyRouteDTO.setProxyRouteTypeEnum(ProxyRouteTypeEnum.SERVICE);
            proxyRouteDTO.setIp(serviceInfoDTO.getMgtIp());
            proxyRouteDTO.setPort(String.valueOf(serviceInfoDTO.getMgtPort()));
            proxyRouteService.deleteProxyRoute(proxyRouteDTO);
        }
    }

    /**
     * 删除服务管控代理
     * @param serviceInfoDTO
     */
    private void deleteRemoteProxy(ServiceInfoDTO serviceInfoDTO){
        if (regionService.checkIsRegionMode()){
            log.info("服务添加异步任务-删除区域密码服务管控代理");
            ServiceInfoIpDTO serviceInfoIpDTO = new ServiceInfoIpDTO();
            serviceInfoIpDTO.setIp(serviceInfoDTO.getMgtIp());
            SecRestResponse<List<ServiceInfoVO>> secRestResponse = serviceInfoServiceApi.getServiceListByIp(serviceInfoIpDTO);
            if (CollectionUtils.isEmpty(secRestResponse.getResult())){
                //如果没有同ip的服务 则将管控代理删除
                ProxyRouteDTO proxyRouteDTO = new ProxyRouteDTO();
                proxyRouteDTO.setRegionId(serviceInfoDTO.getRegionId());
                proxyRouteDTO.setProxyRouteTypeEnum(ProxyRouteTypeEnum.SERVICE_REMOTE);
                proxyRouteDTO.setIp(serviceInfoDTO.getRemoteIp());
                proxyRouteDTO.setPort(String.valueOf(serviceInfoDTO.getRemotePort()));
                proxyRouteService.deleteProxyRoute(proxyRouteDTO);
            }
        }
    }
}