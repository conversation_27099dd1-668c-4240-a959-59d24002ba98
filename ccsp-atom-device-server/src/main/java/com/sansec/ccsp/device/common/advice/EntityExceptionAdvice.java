package com.sansec.ccsp.device.common.advice;

import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.PriorityOrdered;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Priority;

/**
 * @Describe
 * <AUTHOR>
 * @create 2023/3/29 23:29
 */
@RestControllerAdvice
@Priority(PriorityOrdered.HIGHEST_PRECEDENCE)
@Slf4j
public class EntityExceptionAdvice {
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public SecRestResponse<Object> httpMessageNotReadableHandle(HttpMessageNotReadableException e) {
        log.error("请求参数错误：", e);
        return ResultUtil.error("0A010010", "参数数据类型转换异常");
    }

    /**
     * 仅仅捕捉校验所产生的异常
     *
     * @param exception
     * @return
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public SecRestResponse<Object> methodArgumentNotValidHandler(MethodArgumentNotValidException exception) {
        log.error("请求参数错误：", exception);
        FieldError fieldError = exception.getBindingResult().getFieldError();
        return ResultUtil.error("0A010011", "参数错误：" + fieldError.getDefaultMessage());
    }
}
