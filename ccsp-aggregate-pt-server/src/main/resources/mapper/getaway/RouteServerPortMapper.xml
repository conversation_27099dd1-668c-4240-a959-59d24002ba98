<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ccsp.pt.business.servicemgt.gateway.mapper.RouteServerPortMapper">
    <sql id="Base_Column_List" >
    ID,ROUTE_ID,SERVER_PORT,APISIX_SERVER_PORT,TENANT_ID,BUSI_TYPE_ID,GROUP_ID,GROUP_TYPE,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>

    <select id="selectServerPort"  resultType="com.sansec.ccsp.pt.business.servicemgt.gateway.entity.RouteServerPortPO">
        select rp.SERVER_PORT,rp.APISIX_SERVER_PORT
        from route_server_port rp
        where
            rp.REGION_ID = #{regionId}
            AND rp.SERVER_PORT not in(
            select SERVER_PORT from service_gateway_route where REGION_ID = #{regionId}
            AND SERVER_PORT is not null
        )
        ORDER BY rp.SERVER_PORT
    </select>

    <select id="selectServerPortRegionNull"  resultType="com.sansec.ccsp.pt.business.servicemgt.gateway.entity.RouteServerPortPO">
        select rp.SERVER_PORT,rp.APISIX_SERVER_PORT
        from route_server_port rp
        where
            rp.REGION_ID is null
            AND rp.SERVER_PORT not in(
            select SERVER_PORT from service_gateway_route where REGION_ID is null
            AND SERVER_PORT is not null
        )
        ORDER BY rp.SERVER_PORT
    </select>
</mapper>