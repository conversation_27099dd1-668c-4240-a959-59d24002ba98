package com.sansec.ccsp.pt.business.device.device.service.impl;

import com.sansec.ccsp.device.deviceinfo.api.DeviceInfoServiceApi;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoStatusDTO;
import com.sansec.ccsp.device.deviceinfo.response.GatewayVO;
import com.sansec.ccsp.pt.business.common.config.service.CommonConfigService;
import com.sansec.ccsp.pt.business.device.device.service.DeviceRunStatusCronJob;
import com.sansec.ccsp.pt.business.device.deviceInfo.service.impl.GatewayConvert;
import com.sansec.ccsp.pt.business.servicemgt.gateway.response.ServiceGatewayVO;
import com.sansec.ccsp.pt.business.servicemgt.gateway.service.ServiceGatewayService;
import com.sansec.ccsp.pt.business.statistic.alarm.service.AlarmInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
@Slf4j
public class DeviceRunStatusCronJobImpl implements DeviceRunStatusCronJob {
    @Resource
    DeviceInfoServiceApi deviceInfoServiceApi;
    @Resource
    ServiceGatewayService serviceGatewayService;
    @Resource
    CommonConfigService commonConfigService;
    @Resource
    GatewayConvert gatewayConvert;
    @Resource
    private AlarmInfoService alarmInfoService;

    @Override
    public void getDeviceInfoStatus() {
        DeviceInfoStatusDTO deviceInfoStatusDTO = new DeviceInfoStatusDTO();
        Map<Long, String> aliveMonitorUrlMap = alarmInfoService.getAliveMonitorUrlMap();
        Boolean isRegionMode = commonConfigService.isRegionMode();
        //只有区域模式下需要查询可用网关
        if(isRegionMode){
            Map<Long, ServiceGatewayVO> allAliveGateWayRegion = serviceGatewayService.getAllAliveGateWayRegion();
            Map<Long, GatewayVO> gatewayVOMap = gatewayConvert.serviceGateWayToDeviceMap(allAliveGateWayRegion);
            deviceInfoStatusDTO.setProxyRouteIs(true);
            deviceInfoStatusDTO.setGatewayVOMap(gatewayVOMap);
        }else{
            deviceInfoStatusDTO.setProxyRouteIs(false);
        }
        deviceInfoStatusDTO.setMonitorUrlMap(aliveMonitorUrlMap);
        deviceInfoServiceApi.getDeviceInfoStatus(deviceInfoStatusDTO);
    }
}
