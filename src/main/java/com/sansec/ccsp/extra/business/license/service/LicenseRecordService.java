package com.sansec.ccsp.extra.business.license.service;


import com.sansec.ccsp.extra.business.license.reponse.LicenseRecordVO;
import com.sansec.ccsp.extra.business.license.request.*;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;


 /**
 * @description : 许可授权记录表;(LICENSE_RECORD)表服务接口
 * <AUTHOR> xia<PERSON>jiaw<PERSON>
 * @date : 2024-4-15
 */
public interface LicenseRecordService{
    /** 
     * 分页查询
     *
     * @param licenseRecordPageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<LicenseRecordVO>> find(LicenseRecordPageDTO licenseRecordPageDTO);
    /** 
     * 新增数据
     *
     * @param licenseRecordAddDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(LicenseRecordAddDTO licenseRecordAddDTO);

     SecRestResponse<Object> auth(LicenseAuthDTO licenseAuthDTO);

     SecRestResponse<Object> getLicenseInfo(ApplyLicenseInfoDTO applyLicenseInfoDTO);
 }