package com.sansec.ccsp.servicemgt.business.dbmgt.convert;

import com.sansec.ccsp.servicemgt.business.dbmgt.entity.DatabaseMinimumUnitPagePO;
import com.sansec.ccsp.servicemgt.dbmgt.request.DatabaseMinimumUnitDTO;
import com.sansec.ccsp.servicemgt.dbmgt.response.DatabaseMinimumUnitPageVO;
import com.sansec.ccsp.servicemgt.dbmgt.response.DatabaseMinimumUnitVO;
import com.sansec.ccsp.servicemgt.dbmgt.response.DatabaseUnitUpgradeVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import com.sansec.ccsp.servicemgt.business.dbmgt.entity.DatabaseMinimumUnitPO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.common.param.response.SecPageVO;
import java.util.List;

 /**
 * @description : 数据库最小单元;(DATABASE_MINIMUM_UNIT)实体类转换接口
 * <AUTHOR> xia<PERSON>jiaw<PERSON>
 * @date : 2023-7-7
 */
@Mapper(componentModel = "spring")
public interface DatabaseMinimumUnitConvert{
    /**
     * dtoToPo
     * @param databaseMinimumUnitDTO
     * @return
     */
    @Mappings({})
    DatabaseMinimumUnitPO dtoToPo(DatabaseMinimumUnitDTO databaseMinimumUnitDTO);
    
    /**
     * poToDto
     * @param databaseMinimumUnitPO
     * @return
     */
    DatabaseMinimumUnitDTO poToDto(DatabaseMinimumUnitPO databaseMinimumUnitPO);
    
    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<DatabaseMinimumUnitDTO> poToDto(List<DatabaseMinimumUnitPO> list);
     
    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<DatabaseMinimumUnitPageVO> pagePOToSecPageVOPage(IPage<DatabaseMinimumUnitPagePO> iPage);
    
    @InheritConfiguration(name = "convertVo")
    List<DatabaseMinimumUnitVO> convert(List<DatabaseMinimumUnitPO> list);
    
    @Mappings({})
    DatabaseMinimumUnitVO convertVo(DatabaseMinimumUnitPO request);

     List<DatabaseMinimumUnitPageVO> poToVo(List<DatabaseMinimumUnitPO> databaseMinimumUnitPOS);
     @Mappings({
             @Mapping(source = "size", target = "pageSize"),
             @Mapping(source = "current", target = "pageNum"),
             @Mapping(source = "records", target = "list")
     })
     SecPageVO<DatabaseUnitUpgradeVO> pagePOToSecPageVOPageForUpgrade(IPage<DatabaseMinimumUnitPagePO> pagePO);
}