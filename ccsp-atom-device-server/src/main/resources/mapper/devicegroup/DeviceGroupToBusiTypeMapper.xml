<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ccsp.device.business.devicegroup.mapper.DeviceGroupToBusiTypeMapper">
    <sql id="Base_Column_List" >
    ID,DEVICE_GROUP_ID,BUSI_TYPE_ID,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>

    <select id="selectByGroupIds"
            resultType="com.sansec.ccsp.device.business.devicegroup.entity.DeviceGroupToBusiTypePO"
            parameterType="java.util.List">

        SELECT
        <include refid="Base_Column_List"/>
        FROM
        device_group_to_busi_type a
        <if test="null != groupIds and groupIds.size > 0">
            WHERE DEVICE_GROUP_ID IN
            <foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY a.CREATE_TIME DESC
    </select>

    <select id="selectDeviceGroupIdNameList"
            resultType="com.sansec.ccsp.device.business.devicegroup.entity.DeviceGroupIdNamePO"
            parameterType="com.sansec.ccsp.device.devicegroup.request.DeviceGroupIdNameDTO">
        SELECT
            a.DEVICE_GROUP_ID,
            b.BUSI_TYPE_ID,
            a.DEVICE_GROUP_NAME
        FROM
          device_group AS a
            LEFT JOIN device_group_to_busi_type AS b
            ON a.DEVICE_GROUP_ID = b.DEVICE_GROUP_ID
        <where>
            <if test="dto.tenantId != null">
                a.TENANT_ID = #{dto.tenantId}
            </if>
            <if test="dto.busiTypeIdList.size!=0">
                AND b.BUSI_TYPE_ID IN
                <foreach collection="dto.busiTypeIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY a.CREATE_TIME DESC
    </select>
    <select id="selectByGroupId" resultType="com.sansec.ccsp.device.devicegroup.response.DeviceGroupIdNameVO">
        SELECT
            DEVICE_GROUP_ID,
            DEVICE_GROUP_NAME
        FROM
            device_group a
        WHERE
            DEVICE_GROUP_ID = #{groupId}
        ORDER BY a.CREATE_TIME DESC
    </select>

</mapper>