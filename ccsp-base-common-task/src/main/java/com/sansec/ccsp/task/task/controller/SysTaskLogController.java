package com.sansec.ccsp.task.task.controller;

import com.sansec.ccsp.task.task.request.SysTaskLogDTO;
import com.sansec.ccsp.task.task.request.SysTaskLogPageDTO;
import com.sansec.ccsp.task.task.request.TaskLogIdsDTO;
import com.sansec.ccsp.task.task.response.SysTaskLogVO;
import com.sansec.ccsp.task.task.service.SysTaskLogService;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> wwl
 * @Description: 异步任务执行日志表;(SYS_TASK_LOG)表控制层
 * @Date: 2023-3-10
 */
@RestController
@RequestMapping("/ccsp/v1/sysTaskLog")
@Validated
public class SysTaskLogController {
    @Resource
    private SysTaskLogService sysTaskLogService;

    /**
     * 分页查询
     *
     * @param sysTaskLogPageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    public SecRestResponse<SecPageVO<SysTaskLogVO>> find(@RequestBody SysTaskLogPageDTO sysTaskLogPageDTO) {
        return sysTaskLogService.find(sysTaskLogPageDTO);
    }

    /**
     * 新增数据
     *
     * @param sysTaskLogDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    public SecRestResponse<Object> add(@RequestBody SysTaskLogDTO sysTaskLogDTO) {
        return sysTaskLogService.add(sysTaskLogDTO);
    }

    /**
     * 更新数据
     *
     * @param sysTaskLogDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/edit")
    public SecRestResponse<Object> edit(@RequestBody SysTaskLogDTO sysTaskLogDTO) {
        return sysTaskLogService.edit(sysTaskLogDTO);
    }

    /**
     * 通过主键删除数据
     *
     * @param sysTaskLogId
     * @return 实例对象
     */
    @PostMapping("/deleteById")
    public SecRestResponse<Object> deleteById(@PathVariable Long sysTaskLogId) {
        return sysTaskLogService.deleteById(sysTaskLogId);
    }

    /**
     * 通过JobId主键删除任务执行日志记录数据
     *
     * @param taskId
     * @return 实例对象
     */
    @PostMapping("/deleteByTaskId/{taskId}")
    public SecRestResponse<Object> deleteByTaskId(@PathVariable Long taskId) {
        return sysTaskLogService.deleteByTaskId(taskId);
    }

    /**
     * 通过主键批量删除数据
     *
     * @param taskLogIdsDTO
     * @return 实例对象
     */
    @PostMapping("/deleteByIds")
    public SecRestResponse<Object> deleteByIds(@RequestBody TaskLogIdsDTO taskLogIdsDTO) {
        return sysTaskLogService.deleteByIds(taskLogIdsDTO);
    }

    /**
     * 通过主键查询日志详情
     *
     * @param taskLogId
     * @return 实例对象
     */
    @PostMapping("/findById/{taskLogId}")
    public SecRestResponse<SysTaskLogVO> findById(@PathVariable Long taskLogId) {
        return sysTaskLogService.findById(taskLogId);
    }
}