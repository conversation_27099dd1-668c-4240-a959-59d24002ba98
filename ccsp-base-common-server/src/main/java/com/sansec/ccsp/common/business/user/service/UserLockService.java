package com.sansec.ccsp.common.business.user.service;

import java.util.List;

/**
 * @Description: 用户锁定解锁操作
 * <AUTHOR>
 */
public interface UserLockService {

    /** 用户锁定
     * @param userId
     * @return
     */
    void lock(Long userId);


    /** 用户解锁
     * @param userId
     * @return
     */
    void unLock(Long userId);


    /**
     * 通过userName累加登录失败次数,并返回当前登录失败次数
     *  -1 表示失败次数过多已被锁定，失败次数清0
     *  -2 表示当前账号状态是禁用状态
     *  -4 表示当前账号状态是锁定状态
     *  0 表示为正常状态
     *  >0 目前错误次数
     * @param userId
     */
    void addLoginErrorTimes(Long userId);

    /**
     * 通过角色Id 获取人员列表
     * @param unlockTime 锁定到期时间
     *
     * @return List 用户列表
     */
    List<Long> findUnlockUserIdList(Long unlockTime);


}
