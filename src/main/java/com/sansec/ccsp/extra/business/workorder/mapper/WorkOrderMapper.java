package com.sansec.ccsp.extra.business.workorder.mapper;

import com.sansec.ccsp.extra.business.workorder.entity.OrderTypeListPO;
import com.sansec.ccsp.extra.business.workorder.entity.WorkOrderPO;
import com.sansec.ccsp.extra.business.workorder.entity.WorkOrderStatusChartPO;
import com.sansec.ccsp.extra.business.workorder.response.CommentAndAttachmentVO;
import com.sansec.ccsp.extra.business.workorder.response.WorkOrderDetailVO;
import com.sansec.db.mapper.SansecBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WorkOrderMapper extends SansecBaseMapper<WorkOrderPO> {

    List<OrderTypeListPO> findOrderTypeList();

    WorkOrderDetailVO findWorkOrderDetail(@Param("workOrderId") Long workOrderId);

    List<CommentAndAttachmentVO> findCommentAndAttachmentList(@Param("workOrderId") Long workOrderId);

    List<WorkOrderStatusChartPO> getStatusChartDataList(@Param("tenantId") Long tenantId,@Param("tenantIds") List<Long> tenantIds);
}
