package com.sansec.ccsp.device.business.monitor.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.sansec.ccsp.device.business.monitor.entity.DicSnmpPrivacyPO;
import com.sansec.ccsp.device.devicemonitor.request.DicSnmpPrivacyDTO;
import com.sansec.ccsp.device.devicemonitor.request.DicSnmpPrivacyPageDTO;
import com.sansec.ccsp.device.devicemonitor.response.DicSnmpPrivacyVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * @description : snmp加密方式字典表(1030);(DIC_SNMP_PRIVACY)表服务接口
 * <AUTHOR> xiaojiawei
 * @date : 2023-9-20
 */
public interface DicSnmpPrivacyService extends IService<DicSnmpPrivacyPO> {
    /** 
     * 分页查询
     *
     * @param dicSnmpPrivacyPageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<DicSnmpPrivacyVO>> find(DicSnmpPrivacyPageDTO dicSnmpPrivacyPageDTO);
    /** 
     * 新增数据
     *
     * @param dicSnmpPrivacyDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(DicSnmpPrivacyDTO dicSnmpPrivacyDTO);
    /** 
     * 更新数据
     *
     * @param dicSnmpPrivacyDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(DicSnmpPrivacyDTO dicSnmpPrivacyDTO);
    /** 
     * 通过主键删除数据
     *
     * @param dicSnmpPrivacyDTO
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(DicSnmpPrivacyDTO dicSnmpPrivacyDTO);

    /**
     * 获取snmp加密方式字典
     *
     * @return 实例对象
     */
    SecRestResponse<List<DicSnmpPrivacyVO>> getSnmpPrivacyList();
}