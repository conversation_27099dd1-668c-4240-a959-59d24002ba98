package com.sansec.ccsp.pt.business.servicemgt.serviceinfo.service.impl;

import com.sansec.ccsp.pt.business.servicemgt.remote.response.RemotePortsCheckVo;
import com.sansec.ccsp.pt.business.servicemgt.remote.service.RegionRemoteAdapterService;
import com.sansec.ccsp.pt.business.servicemgt.serviceinfo.service.ServiceInfoPortsService;
import com.sansec.ccsp.pt.common.enums.ServiceTypeEnum;
import com.sansec.ccsp.servicemgt.serviceinfo.api.ServiceInfoServiceApi;
import com.sansec.ccsp.servicemgt.serviceinfo.request.ServiceInfoDTO;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceInfoVO;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.redis.lock.DistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sansec.ccsp.pt.common.error.SecErrorCodeConstant.HOST_ALLOCATE_PORT_FAILED;

/**
 * <AUTHOR>
 * 处理服务端口相关内容
 */
@Slf4j
@Service
public class ServiceInfoPortsServiceImpl implements ServiceInfoPortsService {
    @Resource
    private RegionRemoteAdapterService remoteService;
    @Resource
    private ServiceInfoServiceApi serviceInfoServiceApi;
    @Resource
    private DistributedLock distributedLock;

    private final static String LOCK_KEY_PREFIX = "CHECK_PORT:";

    /**
     * 从某个管控程序中获取该管控程序所在的宿主机可用的端口列表(针对某个服务类型)
     *
     * @param serviceInfoDTO  服务信息
     * @param serviceType 服务类型
     * @param wantPorts   期望用到的端口（不一定会返回）
     * @param wantCount   期望的数量（如果满足不了，会抛出异常）
     * @return 可用端口列表
     */
    @Override
    public synchronized List<Integer> getOkPortsFromRemote(ServiceInfoDTO serviceInfoDTO, ServiceTypeEnum serviceType, List<Integer> wantPorts, Integer wantCount) {
        String redisKey = LOCK_KEY_PREFIX + serviceInfoDTO.getRemoteIp() + ":" + serviceType.getCode();
        try {
            boolean retLocked = distributedLock.tryLock(redisKey, TimeUnit.SECONDS, 10, 10);
            if (!retLocked) {
                log.error("try lock failed redisKey = {}", redisKey);
                throw new BusinessException(HOST_ALLOCATE_PORT_FAILED);
            }
            //
            List<Integer> retList = new ArrayList<>();
            // 根据remoteIp和serviceType查询数据所有用到的端口 并放入一个list中
            Set<Integer> setForDb = new HashSet<>();
            ServiceInfoDTO serviceInfoDTOParam = new ServiceInfoDTO();
            serviceInfoDTOParam.setRemoteIp(serviceInfoDTO.getRemoteIp());
            // @weic bugfix
            //serviceInfoDTOParam.setServiceTypeId(serviceType.getId());
            SecRestResponse<List<ServiceInfoVO>> serviceInfoServiceApiList = serviceInfoServiceApi.findList(serviceInfoDTOParam);
            serviceInfoServiceApiList.getResult().forEach(item -> {
                Optional.ofNullable(item.getMgtPort()).ifPresent(setForDb::add);
                Optional.ofNullable(item.getBusiPort()).ifPresent(setForDb::add);
                Optional.ofNullable(item.getRemotePort()).ifPresent(setForDb::add);
                Optional.ofNullable(item.getBusiGatewayPort()).ifPresent(setForDb::add);
                Optional.ofNullable(item.getMgtGatewayPort()).ifPresent(setForDb::add);
                Optional.ofNullable(item.getExpandPort()).ifPresent(setForDb::add);
                Optional.ofNullable(item.getTcpPort()).ifPresent(setForDb::add);
                Optional.ofNullable(item.getMonitorPort()).ifPresent(setForDb::add);
            });
            //
            Set<Integer> listForRemote = new LinkedHashSet<>();
            if (Objects.nonNull(wantPorts)) {
                wantPorts.forEach(item -> {
                    Optional.ofNullable(item).ifPresent(listForRemote::add);
                });
            }
            int size = 50 + setForDb.size();
            for (int i = 0; i < size; i++) {
                listForRemote.add(serviceType.getStartPort() + i);
            }
            //
            List<Integer> collected = listForRemote.stream().filter(item -> !setForDb.contains(item)).collect(Collectors.toList());
            SecRestResponse<RemotePortsCheckVo> remotePortsCheckVoSecRestResponse = remoteService.portsCheck(collected, wantCount, serviceInfoDTO);
            //
            if (remotePortsCheckVoSecRestResponse.getResult().getOkPorts().size() < wantCount) {
                distributedLock.unlock(redisKey);
                throw new BusinessException(HOST_ALLOCATE_PORT_FAILED);
            }
            //
            return remotePortsCheckVoSecRestResponse.getResult().getOkPorts().subList(0, wantCount);
        } finally {
            distributedLock.unlock(redisKey);
        }
    }
}
