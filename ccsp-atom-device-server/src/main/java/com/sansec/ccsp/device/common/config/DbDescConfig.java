package com.sansec.ccsp.device.common.config;

import lombok.Data;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Description: 数据源配置
 * <AUTHOR>
 * @Date: 2021/9/6 18:01
 */
@ConfigurationProperties("spring.datasource")
@Component
@Data
public class DbDescConfig {
    @Setter
    private String driver;
    @Setter
    private String url;
    @Setter
    private String userName;
    @Setter
    private String passWord;
    @Setter
    private String validationQuery;
    @Setter
    private Boolean testWhileIdle;
    @Setter
    private Boolean testOnReturn;
    @Setter
    private Boolean testOnBorrow;
    @Setter
    private Long timeBetweenEvictionRunsMillis;
    @Setter
    private Long timeBetweenConnectErrorMillis;
    @Setter
    private Long maxWait;
    @Setter
    private Integer maxActive;
    @Setter
    private Integer minIdle;
    @Setter
    private Integer initialSize;
    @Setter
    private Integer minEvictableIdleTimeMillis=300000;

    public static final String ORACLE_TYPE="oracle";
    public static final String KINGBASE_TYPE="kingbase";
    public static final String MYSQL_TYPE="mysql";
    public static final String HGDB_TYPE="highgo";
    public static final String DM_TYPE="dm";
    public static final String MARIADB_TYPE="mariadb";
    public static final String GAUSS_TYPE="gauss";
    public static final String VASTBASE_TYPE="vastbase";

    public static final String ORACLE_DRIVER_CLASS = "oracle.jdbc.OracleDriver";
    public static final String KINGBASE_DRIVER_CLASS = "com.kingbase8.Driver";
    public static final String HIGHGO_DIRVER_CLASS = "com.highgo.jdbc.Driver";
    public static final String MYSQL_DIRVER_CLASS = "com.mysql.jdbc.Driver";
    public static final String DM_DRIVER_CLASS="dm.jdbc.driver.DmDriver";
    public static final String MARIADB_DRIVER_CLASS="org.mariadb.jdbc.Driver";
    public static final String GAUSS_DRIVER_CLASS="com.huawei.opengauss.jdbc.Driver";
    public static final String VASTBASE_DRIVER_CLASS="cn.com.vastbase.Driver";

}
