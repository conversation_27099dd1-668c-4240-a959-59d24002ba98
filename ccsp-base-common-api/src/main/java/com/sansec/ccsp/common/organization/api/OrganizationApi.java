package com.sansec.ccsp.common.organization.api;

import com.sansec.ccsp.common.organization.api.fallback.OrganizationFallBack;
import com.sansec.ccsp.common.organization.request.OrganizationInfoDTO;
import com.sansec.ccsp.common.organization.request.OrganizationInfoPageDTO;
import com.sansec.ccsp.common.organization.response.OrganizationIdNameVO;
import com.sansec.ccsp.common.organization.response.OrganizationInfoPageResult;
import com.sansec.ccsp.common.organization.response.OrganizationInfoVO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@FeignClient(name = "${spring.protocol.prefix}${spring.application.common.name}" , path="/ccsp/common", fallbackFactory = OrganizationFallBack.class)
public interface OrganizationApi {

	/**
	 * 分页查询
	 *
	 * @param organizationInfoPageDTO 筛选条件
	 * @return 查询结果
	 */
	@PostMapping("/api/organization/v1/find")
	SecRestResponse<List<OrganizationInfoVO>> find(@RequestBody OrganizationInfoPageDTO organizationInfoPageDTO);

	/**
	 * 分页查询
	 *
	 * @param organizationInfoPageDTO 筛选条件
	 * @return 查询结果
	 */
	@PostMapping("/api/organization/v1/findTree")
	SecRestResponse<OrganizationInfoPageResult> findTree(@RequestBody OrganizationInfoPageDTO organizationInfoPageDTO);


	/**
	 * 新增数据
	 *
	 * @param organizationInfoDTO 实例对象
	 * @return 实例对象
	 */
	@PostMapping("/api/organization/v1/add")
	SecRestResponse<Object> add(@RequestBody OrganizationInfoDTO organizationInfoDTO);

	/**
	 * 更新数据
	 *
	 * @param organizationInfoDTO 实例对象
	 * @return 实例对象
	 */
	@PostMapping("/api/organization/v1/edit")
	SecRestResponse<Object> edit(@RequestBody OrganizationInfoDTO organizationInfoDTO);

	/**
	 * 通过主键删除数据
	 *
	 * @param organizationInfoDTO 实例对象
	 * @return 实例对象
	 */
	@PostMapping("/api/organization/v1/deleteById")
	SecRestResponse<Object> deleteById(@RequestBody OrganizationInfoDTO organizationInfoDTO);

	/**
	 * 通过主键查询
	 *
	 * @param organizationId id
	 * @return 实例对象
	 */
	@PostMapping("/api/organization/v1/selectById/{organizationId}")
	SecRestResponse<OrganizationInfoVO> selectById(@PathVariable(name = "organizationId") @NotNull Long organizationId);


	/**
	 * 通过主键查询
	 *
	 * @param organizationIds id
	 * @return 实例对象
	 */
	@PostMapping("/api/organization/v1/selectByIds")
	SecRestResponse<Map<Long,OrganizationInfoVO>> selectByIds(@RequestBody List<Long> organizationIds);

	/**
	 * 获取组织ID Code Name基本信息列表
	 *
	 * @return 实例对象
	 */
	@PostMapping("/api/organization/v1/getOrganizationIdNameList")
	SecRestResponse<List<OrganizationIdNameVO>> getOrganizationIdNameList();

	/**
	 * 加密所有组织数据
	 * @return
	 */
	@PostMapping("/api/organization/v1/encryptAllData")
	SecRestResponse<Integer> encryptAllData();

}
