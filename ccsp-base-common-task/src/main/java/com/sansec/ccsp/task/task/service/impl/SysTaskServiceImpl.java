package com.sansec.ccsp.task.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.task.common.TaskStatusEnum;
import com.sansec.ccsp.task.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.task.task.convert.SysTaskConvert;
import com.sansec.ccsp.task.task.entity.SysTaskPO;
import com.sansec.ccsp.task.task.mapper.SysTaskMapper;
import com.sansec.ccsp.task.task.request.SysTaskDTO;
import com.sansec.ccsp.task.task.request.SysTaskPageDTO;
import com.sansec.ccsp.task.task.response.SysTaskVO;
import com.sansec.ccsp.task.task.service.SysTaskService;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> wwl
 * @Description: 异步任务表;(SYS_TASK)表服务实现类
 * @Date: 2023-3-10
 */
@Service
@Slf4j
@Validated
public class SysTaskServiceImpl extends ServiceImpl<SysTaskMapper, SysTaskPO> implements SysTaskService {
    @Resource
    private SysTaskConvert sysTaskConvert;
    @Value("${spring.application.name}")
    private String serverId;

    /**
     * 分页查询
     *
     * @param sysTaskPageDTO 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<SysTaskVO>> find(@Valid @NotNull SysTaskPageDTO sysTaskPageDTO) {
        QueryWrapper<SysTaskPO> queryWrapper = Wrappers.query();
        IPage<SysTaskPO> page = new Page<>(sysTaskPageDTO.getPageNum(), sysTaskPageDTO.getPageSize());
        IPage<SysTaskPO> sysTaskPOPage = baseMapper.selectPage(page, queryWrapper);
        SecPageVO<SysTaskVO> sysTaskPageVO = sysTaskConvert.pagePOToSecPageVOPage(sysTaskPOPage);
        return ResultUtil.ok(sysTaskPageVO);
    }

    /**
     * 根据任务状态查询全部任务
     *
     * @param taskStatus
     * @return 查询结果
     */
    @Override
    public SecRestResponse<List<SysTaskVO>> findAll(@Valid @NotNull Integer taskStatus) {
        List<SysTaskPO> sysTaskPOList = baseMapper.selectList(Wrappers.<SysTaskPO>lambdaQuery().eq(SysTaskPO::getTaskStatus, taskStatus).orderByAsc(SysTaskPO::getTaskId));
        List<SysTaskVO> sysTaskVOList = sysTaskConvert.convert(sysTaskPOList);
        return ResultUtil.ok(sysTaskVOList);
    }

    /**
     * 新增数据
     *
     * @param sysTaskDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> add(@Valid @NotNull SysTaskDTO sysTaskDTO) {
        if (sysTaskDTO.getTaskId() == null || sysTaskDTO.getTaskId() == 0) {
            sysTaskDTO.setTaskId(IdGenerator.ins().generator());
        } else {
            boolean isExists = baseMapper.exists(Wrappers.<SysTaskPO>lambdaQuery().eq(SysTaskPO::getTaskId, sysTaskDTO.getTaskId()));
            if (isExists) {
                log.error("异步任务已存在add,sysTaskDTO = {}",sysTaskDTO);
                return ResultUtil.error(SecErrorCodeConstant.TASK_EXISTS);
            }
        }
        SysTaskPO sysTaskPO = sysTaskConvert.dtoToPo(sysTaskDTO);
        sysTaskPO.setServerId(serverId);
        sysTaskPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.insert(sysTaskPO);
        return ResultUtil.ok();
    }

    /**
     * 更新数据
     *
     * @param sysTaskDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(@Valid @NotNull SysTaskDTO sysTaskDTO) {
        SysTaskPO sysTaskPO = sysTaskConvert.dtoToPo(sysTaskDTO);
        sysTaskPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        int result = baseMapper.updateById(sysTaskPO);
        if (result > 0) {
            return ResultUtil.ok();
        }
        return ResultUtil.error(SecErrorCodeConstant.TASK_NOT_FOUND);
    }

    /**
     * 通过主键删除数据
     *
     * @param taskId
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> deleteById(@NotNull Long taskId) {
        baseMapper.deleteById(taskId);
        return ResultUtil.ok();
    }

    /**
     * 查询任务详情
     *
     * @param taskId
     * @return
     */
    @Override
    public SecRestResponse<SysTaskVO> findById(@NotNull Long taskId) {
        SysTaskPO sysTaskPO = baseMapper.selectById(taskId);
        if (sysTaskPO == null) {
            return ResultUtil.ok();
        }
        SysTaskVO sysTaskVO = sysTaskConvert.convertVo(sysTaskPO);
        return ResultUtil.ok(sysTaskVO);
    }
    /**
     * 更新任务状态
     *
     * @param taskId
     * @param taskStatusEnum
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> updateTaskStatusById(@Valid @NotNull Long taskId,@Valid @NotNull TaskStatusEnum taskStatusEnum) {
        SysTaskPO sysTaskPO = new SysTaskPO();
        sysTaskPO.setTaskId(taskId);
        sysTaskPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        sysTaskPO.setTaskStatus(taskStatusEnum.getTaskStatus());
        int result = baseMapper.updateById(sysTaskPO);
        if (result > 0) {
            return ResultUtil.ok();
        }
        return ResultUtil.error(SecErrorCodeConstant.TASK_NOT_FOUND);
    }
}