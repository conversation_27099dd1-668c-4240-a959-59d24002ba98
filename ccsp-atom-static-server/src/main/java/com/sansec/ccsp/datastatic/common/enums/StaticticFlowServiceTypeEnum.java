package com.sansec.ccsp.datastatic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/** 
* @Description: 服务统计模块，按照流量统计的服务记录在这里
* @Author: zhangweicheng
* @Date: 2024/3/13
*/

@Getter
@AllArgsConstructor
public enum StaticticFlowServiceTypeEnum {

//    SVS(2L, "svs", "签名验签服务", "svs", "svsweb/sansecplat"),

//    PKI(1L, "pki", "加解密服务", "pki", "pkiweb/sansecplat"),
    //DIGIST(3L, "digest", "杂凑服务", "digist", "digestweb/sansecplat"),
    //KMS(4L, "kms", "密钥管理服务", "kms", "SecKMS/sansecplat"),
    //TSA(5L, "tsa", "时间戳服务", "tsa", "tsaweb/sansecplat"),
    //SMS(6L, "sms", "协同签名服务", "sms", "smsweb/sansecplat"),
    //OTP(7L, "secauth", "动态令牌服务", "secauth", "secauthweb/sansecplat"),
    //SECSTORAGE(9L, "secstorage", "文件加密服务", "secstorage", "SecStorage/sansecplat"),
    //TSC(10L, "tsc", "电子签章服务", "sealss", "manager"),
    VPN(11L, "vpn", "SSL VPN加密通道服务", "vpn", "vpnweb/sansecplat"),
    //CA(12L, "ca", "数字证书认证服务", "ca", "caweb/sansecplat"),


    //SECDB(8L, "secdb", "数据库加密服务", "secdb", "secdbhsmweb/sansecplat")
    ;
    /**
     * id
     */
    private final Long id;

    /**
     * code
     */
    private final String code;

    /**
     * name
     */
    private final String name;

    /**
     * 路径名称配置
     */
    private final String path;

    /**
     * 统一web路径名称配置
     */
    private final String pathWeb;




    public static StaticticFlowServiceTypeEnum byId(Long id) {
        return Arrays.stream(values()).filter(item -> item.getId().equals(id)).findAny().orElse(null);
    }

    public static StaticticFlowServiceTypeEnum byCode(String serviceCode) {
        return Arrays.stream(values()).filter(item -> item.getCode().equals(serviceCode)).findAny().orElse(null);
    }


}
