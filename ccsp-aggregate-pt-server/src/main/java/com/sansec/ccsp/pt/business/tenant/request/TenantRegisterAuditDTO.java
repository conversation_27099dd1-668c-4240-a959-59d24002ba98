package com.sansec.ccsp.pt.business.tenant.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import com.sansec.ccsp.pt.common.constant.CommonConstant;
import com.sansec.ccsp.pt.common.error.SecErrorCodeConstant;
import com.sansec.common.exception.BusinessException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @Description:
 * @Author: d.s
 * @Date: 2023/2/24 15:46
 */
@Data
public class TenantRegisterAuditDTO {
    /**
     * 注册租户Id
     */
    private Long id;

    /**
     * 是否通过； 1：未审核；2；审核通过；3：审核拒绝
     */
    private Integer status;
    /**
     * 数据库id
     */
    private Long databaseId;
    /**
     * 支持业务类型
     */
    private List<Long> busiTypeList;

    /**
     * 审批意见 服务调用统计
     */
    @Size(max = 100, message = "审批意见长度限制100")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "审批意见格式错误")
    private String auditRemark;

    /**
     * 区域id
     */
    private Long regionId;

    /**
     * 组织机构ID
     */
    private Long organizationId;

    /**
     * 是否支持专享服务 1支持 0 不支持
     */
    private Integer exclusive;

    /**
     * @return void 返回类型
     * @Description: 校验服务类型ID、租户ID、租户编码不可为空
     */
    public void check(Boolean tenant_register_audit_no_busi_type) {
        // 审核id不可为空
        if (this.id == null) {
            throw new BusinessException(SecErrorCodeConstant.TENANT_REGISTER_ID_NULL);
        }
        // 审核状态不可为空
        if (this.status == null || (this.status != 2 && this.status != 3)) {
            throw new BusinessException(SecErrorCodeConstant.TENANT_REGISTER_INFO_ERROR);
        }
        // 通过审核业务不可为空
        if (!tenant_register_audit_no_busi_type && status == 2 && CollectionUtils.isEmpty(this.busiTypeList)) {
            throw new BusinessException(SecErrorCodeConstant.TENANT_REGISTER_INFO_ERROR);
        }
        // 拒绝审核时备注不可为空,不可有业务类型
        if (status == 3 && (StringUtils.isEmpty(auditRemark) || !CollectionUtils.isEmpty(this.busiTypeList))) {
            throw new BusinessException(SecErrorCodeConstant.TENANT_REGISTER_INFO_ERROR);
        }

        if( exclusive !=null  &&  exclusive != 0 && exclusive != 1 ){
            throw new BusinessException(SecErrorCodeConstant.TENANT_EXCLUSIVE_ERROR);
        }

    }
}
