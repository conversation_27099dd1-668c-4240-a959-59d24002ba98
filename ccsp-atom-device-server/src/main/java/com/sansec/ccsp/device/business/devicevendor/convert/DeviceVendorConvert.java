package com.sansec.ccsp.device.business.devicevendor.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.device.business.devicevendor.entity.DeviceVendorPO;
import com.sansec.ccsp.device.devicevendor.request.DeviceVendorDTO;
import com.sansec.ccsp.device.devicevendor.request.DeviceVendorEditDTO;
import com.sansec.ccsp.device.devicevendor.response.DeviceVendorInfoVO;
import com.sansec.ccsp.device.devicevendor.response.DeviceVendorVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

 /**
 * @Description: 厂商信息表;(DEVICE_VENDOR)实体类转换接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Mapper(componentModel = "spring")
public interface DeviceVendorConvert{
    /**
     * dtoToPo
     * @param deviceVendorDTO
     * @return
     */
    @Mappings({})
    DeviceVendorPO dtoToPo(DeviceVendorDTO deviceVendorDTO);

     DeviceVendorPO dtoToPo(DeviceVendorEditDTO deviceVendorDTO);
    
    /**
     * poToDto
     * @param deviceVendorPO
     * @return
     */
    DeviceVendorDTO poToDto(DeviceVendorPO deviceVendorPO);
    
    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<DeviceVendorDTO> poToDto(List<DeviceVendorPO> list);
     
    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<DeviceVendorVO> pagePOToSecPageVOPage(IPage<DeviceVendorPO> iPage);
    
    @InheritConfiguration(name = "convertVo")
    List<DeviceVendorVO> convert(List<DeviceVendorPO> list);
    
    @Mappings({})
    DeviceVendorVO convertVo(DeviceVendorPO request);

     @Mappings({})
     DeviceVendorInfoVO convertListVo(DeviceVendorPO request);

     @InheritConfiguration(name = "convertListVo")
     List<DeviceVendorInfoVO> convertList(List<DeviceVendorPO> list);


}