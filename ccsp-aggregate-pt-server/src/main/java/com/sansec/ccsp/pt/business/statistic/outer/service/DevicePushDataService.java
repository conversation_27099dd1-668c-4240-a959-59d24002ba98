package com.sansec.ccsp.pt.business.statistic.outer.service;

import com.sansec.ccsp.pt.business.statistic.outer.request.DevicePushDataPageDTO;
import com.sansec.ccsp.pt.business.statistic.outer.response.DevicePushDataVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

public interface DevicePushDataService {
    /**
     * 分页查询
     *
     * @param devicePushDataPageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<DevicePushDataVO>> find(DevicePushDataPageDTO devicePushDataPageDTO);

    SecRestResponse<Object> pushDeviceData();

    void deleteByTimestamp(long timestamp);
}
