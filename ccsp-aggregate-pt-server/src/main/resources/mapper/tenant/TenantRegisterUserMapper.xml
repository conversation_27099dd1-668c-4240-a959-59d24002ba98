<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ccsp.pt.business.tenant.mapper.TenantRegisterUserMapper">
    <sql id="Base_Column_List">
        ID
        ,TENANT_ID,USER_TYPE,USER_NAME,USER_CODE,AUTH_CODE,CERT,SERIAL,RANDOM,SIGNATURE,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>

    <select id="getRegisterUserSerial" resultType="string">
        SELECT reguser.SERIAL
        FROM TENANT_REGISTER_USER AS reguser
        LEFT JOIN TENANT_REGISTER AS tr
        ON reguser.TENANT_ID = tr.TENANT_ID
        WHERE
            tr.STATUS = 1
    </select>

    <insert id="batchInsertRegisterUser" parameterType="java.util.List">
        INSERT INTO TENANT_REGISTER_USER
        (
        ID,
        TENANT_ID,
        USER_TYPE,
        USER_NAME,
        USER_CODE,
        AUTH_CODE,
        CERT,
        SERIAL,
        RANDOM,
        SIGNATURE,
        REMARK,
        CREATE_BY,
        CREATE_TIME,
        UPDATE_BY,
        UPDATE_TIME
        )
        VALUES
        <foreach collection="list" item="registerUser" index="index" separator=",">
            (
            #{registerUser.id},
            #{registerUser.tenantId},
            #{registerUser.userType},
            #{registerUser.userName},
            #{registerUser.userCode},
            #{registerUser.authCode},
            #{registerUser.cert},
            #{registerUser.serial},
            #{registerUser.random},
            #{registerUser.signature},
            #{registerUser.remark},
            #{registerUser.createBy},
            #{registerUser.createTime},
            #{registerUser.updateBy},
            #{registerUser.updateTime}
            )
        </foreach>
    </insert>

</mapper>