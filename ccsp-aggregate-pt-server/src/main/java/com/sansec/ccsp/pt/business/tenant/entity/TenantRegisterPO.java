package com.sansec.ccsp.pt.business.tenant.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.ccsp.pt.common.interceptor.crypto.ITenantCryptoData;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

/**
 * <AUTHOR> http://www.chiner.pro
 * @Description: 租户注册表;
 * @Date: 2023-2-18
 */
@TableName("TENANT_REGISTER")
@Data
public class TenantRegisterPO extends BasePO implements ITenantCryptoData {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 租户标识
     */
    private String tenantCode;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 租户级别
     */
    private Integer tenantLevel;
    /**
     * 上级租户ID
     */
    private Long seniorTenantId;
    /**
     * 租户状态;1：初始化；2：运行中；3：停用;4:销毁
     */
    private Integer tenantStatus;
    /**
     * 租户资源分区ID
     */
    private Long partitionId;
    /**
     * 机构名称
     */
    private String organ;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 审批人
     */
    private Long auditBy;
    /**
     * 审批意见
     */
    private String auditRemark;
    /**
     * 是否作废;默认为0
     */
    private Integer invalidFlag;
    /**
     * 完整性
     */
    private String hmac;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
	/**
	 * 是否加密**0-待加密字段未加密(默认) 1-待加密字段已加密**
	 */
	private Integer encryptStatus;

}