package com.sansec.ccsp.datastatic.business.statistic.realtime.response;

import lombok.Data;

 /**
 * @description : 统计配置参数（330）;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2024-3-13
 */
@Data
public class StatisticConfigParamDataVO{
    /**
     * ID
     */
    private Long id;
    /**
     * 区域标识
     */
    private String regionCode;
    /**
     * 租户标识
     */
    private String tenantCode;
    /**
     * 应用标识
     */
    private String appCode;
    /**
     * 服务标识
     */
    private String serviceTypeCode;
    /**
     * 指标名称
     */
    private String targetCode;
    /**
     * 子指标名称
     */
    private String targetChildCode;
    /**
     * 倍数
     */
    private Long multipleVal;
    /**
     * 最大值
     */
    private Long maxVal;
    /**
     * 浮动值;默认为0
     */
    private Long floatVal;

}