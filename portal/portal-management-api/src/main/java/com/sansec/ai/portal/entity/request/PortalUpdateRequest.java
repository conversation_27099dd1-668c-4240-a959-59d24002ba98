package com.sansec.ai.portal.entity.request;

import com.sansec.ai.common.base.paramverify.Verify;
import lombok.Data;

/**
 * 更新门户请求
 *
 * <AUTHOR>
 * @since 2025/5/7 16:36
 */
@Data
public class PortalUpdateRequest {

	/**
	 * 门户ID
	 */
	@Verify(notNull = true)
	private Long id;
	/**
	 * 门户名称
	 */
	private String portalName;
	/**
	 * 门户标题
	 */
	private String portalTitle;
	/**
	 * 门户logo，base64
	 */
	private String portalLogo;
}
