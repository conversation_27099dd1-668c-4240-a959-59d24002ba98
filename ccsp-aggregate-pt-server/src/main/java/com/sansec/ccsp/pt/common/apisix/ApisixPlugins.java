package com.sansec.ccsp.pt.common.apisix;

import com.alibaba.fastjson.annotation.JSONField;
import com.sansec.ccsp.pt.common.apisix.log.KafkaLogger;
import lombok.Data;

/**
 * @类描述:Plusins
 * @author: <PERSON><PERSON><PERSON><PERSON>aw<PERSON>
 * @date: 2023/3/21 10:53
 */
@Data
public class ApisixPlugins {
    //路径改写
    @JSONField(name = "proxy-rewrite")
    private ProxyRewrite proxyRewrite;

    @JSONField(name = "kafka-logger")
    private KafkaLogger kafkaLogger;

}
