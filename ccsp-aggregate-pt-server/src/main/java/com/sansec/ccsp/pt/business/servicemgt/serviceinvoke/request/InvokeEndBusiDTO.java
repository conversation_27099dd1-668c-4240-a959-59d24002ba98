package com.sansec.ccsp.pt.business.servicemgt.serviceinvoke.request;

import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceInfoVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 调用外部的公共传输类
 * 停止类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvokeEndBusiDTO {


    /**
     * 数据信息
     */
    private List<ServiceInfoVO> data;

}
