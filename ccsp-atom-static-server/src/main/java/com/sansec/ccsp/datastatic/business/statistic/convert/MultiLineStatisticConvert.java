package com.sansec.ccsp.datastatic.business.statistic.convert;

import com.sansec.ccsp.datastatic.business.monitor.entity.MultiLineStatisticDO;
import com.sansec.ccsp.datastatic.business.monitor.entity.StatisticIncreCalPO;
import com.sansec.ccsp.datastatic.business.monitor.entity.StatisticIncreRecordPO;
import com.sansec.ccsp.staticapi.monitor.response.MultiLineStatisticVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MultiLineStatisticConvert {
    @Mapping(source = "serviceDataVOList", target = "serviceDataVOList")
    MultiLineStatisticVO convertVo(MultiLineStatisticDO request);

    /**
     * 实时数据转换统计数据
     *
     * @param statisticIncreRecordPO
     * @return
     */
    StatisticIncreCalPO calRecordToCal(StatisticIncreRecordPO statisticIncreRecordPO);

    /**
     * 实时数据转换统计数据
     *
     * @param statisticIncreRecordPO
     * @return
     */
    List<StatisticIncreCalPO> calRecordToCalList(List<StatisticIncreRecordPO> statisticIncreRecordPO);
}
