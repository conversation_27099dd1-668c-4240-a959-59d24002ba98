package com.sansec.ccsp.pt.business.app.tenant.controller;

import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.param.response.SecPageVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sansec.ccsp.pt.business.app.tenant.request.AppToTenantDTO;
import com.sansec.ccsp.pt.business.app.tenant.request.AppToTenantPageDTO;
import com.sansec.ccsp.pt.business.app.tenant.response.AppToTenantVO;
import com.sansec.ccsp.pt.business.app.tenant.service.AppToTenantService;
import javax.annotation.Resource;

 /**
 * @Description: 应用和租户关联表;(APP_TO_TENANT)表控制层
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@RestController
@RequestMapping("/appToTenant/v1")
@Validated
public class AppToTenantController{
    @Resource
    private AppToTenantService appToTenantService;
    
    /** 
     * 分页查询
     *
     * @param appToTenantPageDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/find")
    public SecRestResponse<SecPageVO<AppToTenantVO>> find(@RequestBody AppToTenantPageDTO appToTenantPageDTO){
        return appToTenantService.find(appToTenantPageDTO);
    }
    
    /** 
     * 新增数据
     *
     * @param appToTenantDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/add")
    public SecRestResponse<Object> add(@RequestBody AppToTenantDTO appToTenantDTO){
        return appToTenantService.add(appToTenantDTO);
    }
    
    /** 
     * 更新数据
     *
     * @param appToTenantDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/edit")
    public SecRestResponse<Object> edit(@RequestBody AppToTenantDTO appToTenantDTO){
        return appToTenantService.edit(appToTenantDTO);
    }
    
    /** 
     * 通过主键删除数据
     *
     * @param appToTenantDTO 实例对象
     * @return 实例对象
     */
    @PostMapping("/deleteById")
    public SecRestResponse<Object> deleteById(@RequestBody AppToTenantDTO appToTenantDTO){
        return appToTenantService.deleteById(appToTenantDTO);
    }
}