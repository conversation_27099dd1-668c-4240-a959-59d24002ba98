package com.sansec.ccsp.common.business.dic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.common.business.dic.convert.DicKeylenConvert;
import com.sansec.ccsp.common.business.dic.entity.DicKeylenPO;
import com.sansec.ccsp.common.business.dic.mapper.DicKeylenMapper;
import com.sansec.ccsp.common.business.dic.service.DicKeylenService;
import com.sansec.ccsp.common.dic.request.DicKeylenDTO;
import com.sansec.ccsp.common.dic.request.DicKeylenPageDTO;
import com.sansec.ccsp.common.dic.response.DicKeylenVO;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
 /**
 * @Description: 密钥长度字典表;(DIC_KEYLEN)表服务实现类
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
@Service
@Slf4j
public class DicKeylenServiceImpl extends ServiceImpl<DicKeylenMapper, DicKeylenPO> implements DicKeylenService{
    @Resource
    private DicKeylenConvert dicKeylenConvert;

    /**
     * 分页查询
     *
     * @param dicKeylenPageDTO 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<DicKeylenVO>> find(DicKeylenPageDTO dicKeylenPageDTO){
        QueryWrapper<DicKeylenPO> queryWrapper = Wrappers.query();
        IPage<DicKeylenPO> page = new Page<>(dicKeylenPageDTO.getPageNum(),dicKeylenPageDTO.getPageSize());
        IPage<DicKeylenPO> dicKeylenPOPage = baseMapper.selectPage(page, queryWrapper);
        SecPageVO<DicKeylenVO> dicKeylenPageVO = dicKeylenConvert.pagePOToSecPageVOPage(dicKeylenPOPage);
        return ResultUtil.ok(dicKeylenPageVO);
    }

    /**
     * 新增数据
     *
     * @param dicKeylenDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> add(DicKeylenDTO dicKeylenDTO){
        DicKeylenPO dicKeylenPO = dicKeylenConvert.dtoToPo(dicKeylenDTO);
        dicKeylenPO.setId(IdGenerator.ins().generator());
        dicKeylenPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.insert(dicKeylenPO);
        return ResultUtil.ok();
    }

    /**
     * 更新数据
     *
     * @param dicKeylenDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(DicKeylenDTO dicKeylenDTO){
        DicKeylenPO dicKeylenPO = dicKeylenConvert.dtoToPo(dicKeylenDTO);
        dicKeylenPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        baseMapper.updateById(dicKeylenPO);
        return ResultUtil.ok();
    }

    /**
     * 通过主键删除数据
     *
     * @param dicKeylenDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> deleteById(DicKeylenDTO dicKeylenDTO){
        baseMapper.deleteById(dicKeylenDTO.getId());
        return ResultUtil.ok();
    }
}
