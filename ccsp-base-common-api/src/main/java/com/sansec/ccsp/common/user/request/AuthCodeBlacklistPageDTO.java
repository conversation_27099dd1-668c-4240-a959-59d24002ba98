package com.sansec.ccsp.common.user.request;

import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;
import javax.validation.constraints.Size;


/**
 * <AUTHOR> x<PERSON><PERSON><PERSON>aw<PERSON>
 * @Description: 口令黑名单;
 * @Date: 2023-2-18
 */
@Data
public class AuthCodeBlacklistPageDTO extends SecPageDTO {
    /**
     * 明文口令
     */
    @Size(max = 32, message = "长度不能超过32位")
    private String authCode;
}
