package com.sansec.ccsp.pt.business.device.devicedic.controller;

import com.sansec.ccsp.device.devicedic.request.DicDeviceInteractionTypeListDTO;
import com.sansec.ccsp.device.devicedic.response.DicDeviceInteractionTypeApiVO;
import com.sansec.ccsp.pt.business.device.devicedic.service.DicDeviceInteractionTypeService;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备交互类型字典表
 */
@RestController
@RequestMapping("/dicDeviceInteractionType/v1")
@Validated
public class DicDeviceInteractionTypeController {

    @Resource
    DicDeviceInteractionTypeService service;

    /**
     * 返回返回全部有效（INVALID_FLAG=0）的设备交互类型
     * @return
     */
    @PostMapping("/findList")
    public SecRestResponse<List<DicDeviceInteractionTypeApiVO>> findDeviceInteractionTypeList(@Validated @RequestBody DicDeviceInteractionTypeListDTO deviceInteractionTypeListDTO){
        return service.findDeviceInteractionTypeList(deviceInteractionTypeListDTO);
    }
}
