package com.sansec.ccsp.pt.business.app.register.service;

import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.ccsp.pt.business.app.register.request.AppRegisterToTenantDTO;
import com.sansec.ccsp.pt.business.app.register.request.AppRegisterToTenantPageDTO;
import com.sansec.ccsp.pt.business.app.register.response.AppRegisterToTenantVO;

 /**
 * @Description: 应用申请和租户关联表;(APP_REGISTER_TO_TENANT)表服务接口
 * <AUTHOR> xiaojiawei
 * @Date: 2023-2-18
 */
public interface AppRegisterToTenantService{
    /** 
     * 分页查询
     *
     * @param appRegisterToTenantPageDTO 筛选条件
     * @return 查询结果
     */
    SecRestResponse<SecPageVO<AppRegisterToTenantVO>> find(AppRegisterToTenantPageDTO appRegisterToTenantPageDTO);
    /** 
     * 新增数据
     *
     * @param appRegisterToTenantDTO
     * @return 实例对象
     */
    SecRestResponse<Object> add(AppRegisterToTenantDTO appRegisterToTenantDTO);
    /** 
     * 更新数据
     *
     * @param appRegisterToTenantDTO
     * @return 实例对象
     */
    SecRestResponse<Object> edit(AppRegisterToTenantDTO appRegisterToTenantDTO);
    /** 
     * 通过主键删除数据
     *
     * @param appRegisterToTenantDTO
     * @return 实例对象
     */
    SecRestResponse<Object> deleteById(AppRegisterToTenantDTO appRegisterToTenantDTO);
}