package com.sansec.ccsp.servicemgt.business.dbmgt.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.servicemgt.business.dbmgt.convert.DicBusiTypeToDatabaseConvert;
import com.sansec.ccsp.servicemgt.business.dbmgt.entity.DicBusiTypeToDatabasePO;
import com.sansec.ccsp.servicemgt.business.dbmgt.mapper.DicBusiTypeToDatabaseMapper;
import com.sansec.ccsp.servicemgt.business.dbmgt.service.DatabaseTypeService;
import com.sansec.ccsp.servicemgt.business.dbmgt.service.DicBusiTypeToDatabaseService;
import com.sansec.ccsp.servicemgt.dbmgt.response.DicBusiTypeToDatabaseVO;
import com.sansec.ccsp.task.common.error.SecErrorCodeConstant;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description : 业务服务对应数据库名称;(DIC_BUSI_TYPE_TO_DATABASE)表服务实现类
 * <AUTHOR> xiaojiawei
 * @date : 2023-7-11
 */
@Service
@Slf4j
public class DicBusiTypeToDatabaseServiceImpl extends ServiceImpl<DicBusiTypeToDatabaseMapper, DicBusiTypeToDatabasePO> implements DicBusiTypeToDatabaseService {
    @Resource
    private DicBusiTypeToDatabaseConvert dicBusiTypeToDatabaseConvert;

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @Override
    public SecRestResponse<List<DicBusiTypeToDatabaseVO>> findList() {
        List<DicBusiTypeToDatabasePO> dicBusiTypeToDatabasePOS = baseMapper.selectList(null);
        return ResultUtil.ok(dicBusiTypeToDatabaseConvert.poToVoList(dicBusiTypeToDatabasePOS));
    }

    /**
     * 根据服务类型ID，获取数据库默认名称
     *
     * @param serviceTypeId
     * @return
     */
    @Override
    public String getDbNameByServiceTypeId(Long serviceTypeId) {
        QueryWrapper<DicBusiTypeToDatabasePO> queryWrapper = Wrappers.query();
        queryWrapper.lambda().eq(DicBusiTypeToDatabasePO::getServiceTypeId, serviceTypeId);
        DicBusiTypeToDatabasePO dicBusiTypeToDatabasePO = baseMapper.selectOne(queryWrapper);

        return dicBusiTypeToDatabasePO.getDatabaseUnitName();
    }

    /**
     * 获取数据实例名称Map
     *
     * @return
     */
    @Override
    public Map<Long, String> getDbNameByTypeIdMap() {
        List<DicBusiTypeToDatabasePO> dicBusiTypeToDatabasePOS = baseMapper.selectList(null);
        Map<Long, String> dbNameByTypeIdMap = dicBusiTypeToDatabasePOS.stream()
                .collect(Collectors.toMap(DicBusiTypeToDatabasePO::getServiceTypeId, DicBusiTypeToDatabasePO::getDatabaseUnitName));
        return dbNameByTypeIdMap;
    }


}