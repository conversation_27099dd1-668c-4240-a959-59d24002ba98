package com.sansec.ccsp.datastatic.business.statistic.realtime.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.datastatic.business.statistic.realtime.convert.StatisticMinuteDataConvert;
import com.sansec.ccsp.datastatic.business.statistic.realtime.entity.StatisticMinuteDataPO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.entity.StatisticMinuteDataRealPO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.mapper.StatisticMinuteDataMapper;
import com.sansec.ccsp.datastatic.business.statistic.realtime.request.StatisticGroupDataDTO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticDataVO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticFrontDataNameVO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticFrontDataVO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.service.StatisticConfigParamService;
import com.sansec.ccsp.datastatic.business.statistic.realtime.service.StatisticMinuteDataRealService;
import com.sansec.ccsp.datastatic.common.enums.ServiceTypeEnum;
import com.sansec.ccsp.datastatic.common.enums.StaticticFlowServiceTypeEnum;
import com.sansec.ccsp.datastatic.common.enums.StatisticConfigParamTargetEnum;
import com.sansec.ccsp.datastatic.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.datastatic.common.utils.DateUtil;
import com.sansec.ccsp.datastatic.common.utils.RealTimeDateUtil;
import com.sansec.ccsp.staticapi.realtime.request.StatisticAnalyzeDataQueryDTO;
import com.sansec.ccsp.staticapi.realtime.response.StatisticFindDataVO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.response.StatisticGroupDataVO;
import com.sansec.ccsp.datastatic.business.statistic.realtime.service.StatisticMinuteDataService;
import com.sansec.ccsp.staticapi.realtime.request.FindDataStatisticDTO;
import com.sansec.ccsp.staticapi.realtime.response.StatisticGateWayDataVO;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class StatisticMinuteDataServiceImpl extends ServiceImpl<StatisticMinuteDataMapper, StatisticMinuteDataPO> implements StatisticMinuteDataService {

    @Resource
    private StatisticMinuteDataConvert statisticMinuteDataConvert;

    @Resource
    private StatisticConfigParamService statisticConfigParamService;

    @Resource
    private StatisticMinuteDataRealService statisticMinuteDataRealService;

    @Override
    public List<StatisticGroupDataVO> analyzeDataByHour(StatisticGroupDataDTO dto) {
        List<StatisticGroupDataVO> statisticAnalyzeHourDataVOS = baseMapper.analyzeDataByHour(dto);
        return statisticAnalyzeHourDataVOS;
    }

    @Override
    public List<StatisticGateWayDataVO> analyzeDataKafka(StatisticGroupDataDTO dto) {
        List<StatisticGateWayDataVO> vos = baseMapper.analyzeDataKafka(dto);
        return vos;
    }

    @Override
    public void clearMinuteData(String deleteBeforeTime) {
        baseMapper.clearMinuteData(deleteBeforeTime);
    }

    @Override
    public SecPageVO<StatisticFindDataVO> findDataStatistic(FindDataStatisticDTO findDataStatisticDTO) {
        LambdaQueryWrapper<StatisticMinuteDataPO> queryWrapper = new LambdaQueryWrapper();

        //时间段
        if (StringUtils.isNotBlank(findDataStatisticDTO.getCollectTimeEq())) {
            queryWrapper.eq(StatisticMinuteDataPO::getCollectTime, findDataStatisticDTO.getCollectTimeEq());
        }else {
            queryWrapper.ge(StatisticMinuteDataPO::getCollectTime, findDataStatisticDTO.getCollectTimeGe());
            queryWrapper.le(StatisticMinuteDataPO::getCollectTime, findDataStatisticDTO.getCollectTimeLe());
        }

        //区域标识
        if (StringUtils.isNotBlank(findDataStatisticDTO.getRegionCode())) {
            queryWrapper.eq(StatisticMinuteDataPO::getRegionCode, findDataStatisticDTO.getRegionCode());
        }

        //租户标识列表
        if (Objects.nonNull(findDataStatisticDTO.getTenantCodeList()) && !findDataStatisticDTO.getTenantCodeList().isEmpty()) {
            queryWrapper.in(StatisticMinuteDataPO::getTenantCode, findDataStatisticDTO.getTenantCodeList());
        }

        //业务类型
        if (StringUtils.isNotBlank(findDataStatisticDTO.getServiceTypeCode())) {
            queryWrapper.in(StatisticMinuteDataPO::getServiceTypeCode, findDataStatisticDTO.getServiceTypeCode());
        }

        IPage<StatisticMinuteDataPO> page = new Page<>(findDataStatisticDTO.getPageNum(), findDataStatisticDTO.getPageSize());
        IPage<StatisticMinuteDataPO> minuteDataPOIPage = this.page(page, queryWrapper);

        SecPageVO<StatisticFindDataVO> statisticMinuteDataVOSecPageVO = statisticMinuteDataConvert.pagePOToSecPageFindDataVOPage(minuteDataPOIPage);

        return statisticMinuteDataVOSecPageVO;
    }

    @Override
    public SecPageVO<StatisticFindDataVO> findDataStatisticReal(FindDataStatisticDTO findDataStatisticDTO) {
        LambdaQueryWrapper<StatisticMinuteDataRealPO> queryWrapper = new LambdaQueryWrapper();

        //时间段
        if (StringUtils.isNotBlank(findDataStatisticDTO.getCollectTimeEq())) {
            queryWrapper.eq(StatisticMinuteDataRealPO::getCollectTime, findDataStatisticDTO.getCollectTimeEq());
        }else {
            queryWrapper.ge(StatisticMinuteDataRealPO::getCollectTime, findDataStatisticDTO.getCollectTimeGe());
            queryWrapper.le(StatisticMinuteDataRealPO::getCollectTime, findDataStatisticDTO.getCollectTimeLe());
        }

        //区域标识
        if (StringUtils.isNotBlank(findDataStatisticDTO.getRegionCode())) {
            queryWrapper.eq(StatisticMinuteDataRealPO::getRegionCode, findDataStatisticDTO.getRegionCode());
        }

        //租户标识列表
        if (Objects.nonNull(findDataStatisticDTO.getTenantCodeList()) && !findDataStatisticDTO.getTenantCodeList().isEmpty()) {
            queryWrapper.in(StatisticMinuteDataRealPO::getTenantCode, findDataStatisticDTO.getTenantCodeList());
        }

        //业务类型
        if (StringUtils.isNotBlank(findDataStatisticDTO.getServiceTypeCode())) {
            queryWrapper.in(StatisticMinuteDataRealPO::getServiceTypeCode, findDataStatisticDTO.getServiceTypeCode());
        }

        IPage<StatisticMinuteDataRealPO> page = new Page<>(findDataStatisticDTO.getPageNum(), findDataStatisticDTO.getPageSize());
        IPage<StatisticMinuteDataRealPO> minuteDataPOIPage = statisticMinuteDataRealService.page(page, queryWrapper);

        SecPageVO<StatisticFindDataVO> statisticMinuteDataVOSecPageVO = statisticMinuteDataConvert.pageRealPOToSecPageFindDataVOPage(minuteDataPOIPage);

        return statisticMinuteDataVOSecPageVO;
    }

    @Override
    public SecRestResponse<Object> queryAnalyzeMinuteData(StatisticAnalyzeDataQueryDTO statisticAnalyzeDataQueryDTO) {
        log.info("query real data by param:{}", statisticAnalyzeDataQueryDTO);

        //校验时间格式是否正确
        if (Objects.nonNull(statisticAnalyzeDataQueryDTO)) {
            //值不为空，转为符合条件的格式
            String beginTime = statisticAnalyzeDataQueryDTO.getBeginTime();
            String endTime = statisticAnalyzeDataQueryDTO.getEndTime();


            try {
                if (StringUtils.isNotBlank(beginTime)) {//开始时间不为空，转为小时制
                    beginTime = DateUtil.checkAndFormatDate(beginTime, "yyyy-MM-dd HH:mm");
                    statisticAnalyzeDataQueryDTO.setBeginTime(beginTime);
                }

                if (StringUtils.isNotBlank(endTime)) {//开始时间不为空，转为小时制
                    endTime = DateUtil.checkAndFormatDate(endTime, "yyyy-MM-dd HH:mm");
                    statisticAnalyzeDataQueryDTO.setEndTime(endTime);
                }

            } catch (Exception e) {
                throw new BusinessException(SecErrorCodeConstant.REAL_TIME_QUERY_DATA_BY_NOW_FORMAT_ERROR);
            }

            List<StatisticDataVO> statisticDataVOS = baseMapper.queryAnalyzeMinuteData(statisticAnalyzeDataQueryDTO);


            log.info("query real  data by param:{},result:{}", statisticAnalyzeDataQueryDTO, statisticDataVOS);

            String serviceTypeCode = statisticAnalyzeDataQueryDTO.getServiceTypeCode();

            StaticticFlowServiceTypeEnum staticticFlowServiceTypeEnum = StaticticFlowServiceTypeEnum.byCode(serviceTypeCode);

            //初始化标题
            String title = "";
            String lable = "";
            //统计单位
            String unit = "次";
            Boolean isFlowAnalyze = false;
            if (Objects.nonNull(staticticFlowServiceTypeEnum)) {//流量查询
                title = staticticFlowServiceTypeEnum.getName() + "使用情况";
                lable = "流量统计";
                isFlowAnalyze = true;
                unit="Mb";
            } else {//调用次数统计
                ServiceTypeEnum serviceTypeEnum = ServiceTypeEnum.byCode(serviceTypeCode);
                if (Objects.nonNull(serviceTypeEnum)) {
                    title = serviceTypeEnum.getName() + "使用情况";
                }
                lable = "次数统计";
            }

            //初始化时间数据，默认为0
            List<String> dates = new ArrayList<>();

            try {
                dates = RealTimeDateUtil.minuteTimesBetweenRangeDate(statisticAnalyzeDataQueryDTO.getBeginTime(), statisticAnalyzeDataQueryDTO.getEndTime());
            } catch (Exception e) {
                //
            }

            //先将数据全部设置为0
            Map<String, Long> dateAndDataMap = new LinkedHashMap<>();
            for (String date : dates) {
                dateAndDataMap.put(date, 0L);
            }

            if (CollUtil.isNotEmpty(statisticDataVOS)) {


                //存储统计参数配置指标，减少查询次数
                Map<String, Long> targetKeyMap = new HashMap<>();

                for (StatisticDataVO model : statisticDataVOS) {
                    //配置指标存储的最大倍数
                    Long multipleVal = statisticConfigParamService.queryConfigParam(targetKeyMap, model, StatisticConfigParamTargetEnum.MINUTE.getTargetCode(),StatisticConfigParamTargetEnum.MINUTE.getTargetChildCode());

                    String collectTime = model.getCollectTime();
                    Long count = null;

                    if (isFlowAnalyze) {//流量统计
                        count = model.getServiceFlowNum();
                    } else {//次数
                        count = model.getCallNum();
                    }


                    if (StringUtils.isNotBlank(collectTime) && count != null) {
                        if (dateAndDataMap.containsKey(collectTime)) {
                            dateAndDataMap.put(collectTime, count * multipleVal);
                        }
                    }
                }
            }

            //拼接前端格式
            List<String> times = new ArrayList<>();
            List<Double> datas = new ArrayList<>();

            Iterator<Map.Entry<String, Long>> iterator = dateAndDataMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Long> next = iterator.next();

                //此时间为  yyyy-MM-dd HH:mm 将key格式化为  23:00
                String key = next.getKey();

                times.add(DateUtil.formatDate(key, "yyyy-MM-dd HH:mm", "HH:mm"));

                Long val = next.getValue();
                String doubleVal = val.toString();
                if (isFlowAnalyze && val != null) {
                    doubleVal = String.format("%.2f", (val.doubleValue()*8.0) / 1024.00 / 1024.00);
                }
                datas.add(Double.valueOf(doubleVal));
            }


            //返回的结构体
            StatisticFrontDataVO statisticFrontDataVO = new StatisticFrontDataVO();
            statisticFrontDataVO.setTitle(title);
            statisticFrontDataVO.getLegend().add(lable);
            statisticFrontDataVO.setXAxis(times);
            statisticFrontDataVO.setUnit(unit);

            //数据结构体
            StatisticFrontDataNameVO statisticFrontDataNameVO = new StatisticFrontDataNameVO();
            statisticFrontDataNameVO.setName(lable);
            statisticFrontDataNameVO.setData(datas);

            //填充
            statisticFrontDataVO.getServiceDataList().add(statisticFrontDataNameVO);
            return ResultUtil.ok(statisticFrontDataVO);
        }
        return ResultUtil.ok();
    }
}
