package com.sansec.ccsp.app.info.request;

import com.sansec.ccsp.app.common.enums.EnumDelFlag;
import com.sansec.ccsp.app.common.enums.EnumSelectCondition;
import com.sansec.ccsp.app.common.wrapperutil.SelectCondition;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date: 2023/2/20 16:13
 */
@Data
public class AppInfoDeleteDTO {

    @SelectCondition(EnumSelectCondition.EQ)
    @NotNull(message = "应用ID不可为空")
    private Long appId;

    @SelectCondition(EnumSelectCondition.EQ)
    private Integer invalidFlag = EnumDelFlag.NORMAL.getKey();


}
