package com.sansec.ccsp.staticapi.realtime.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Description: 数据实时采集, 对接采集插件实体类
 * @Author: zhangweicheng
 * @Date: 2024/3/6
 */

@Data
public class CollectDataDTO {

    /**
     * 数据采集时间(时间戳格式),格式yyyy-MM-dd HH:mm:ss   1710211044L,先乘以1000  在转为date
     * ,采集工具此时间格式不方便调整，调整后会有性能方面的影响
     */
    @NotNull(message = "请输入数据采集时间")
    private Long time;


    /**
     * 采集内容contents
     */

    private CollectDataContentsDTO contents;

    /**
     * 采集tags,目前暂时用不到此数据，包含的字段信息如下：
     * app：
     * dst:
     * host.ip:
     * host.name:
     * log.file.path:
     */
    private Map<String, Object> tags;


}