package com.sansec.ccsp.datastatic.business.statistic.service;

import com.sansec.ccsp.staticapi.monitor.request.MonitorIndexPeriodDTO;
import com.sansec.ccsp.staticapi.monitor.request.StatisticIndexDTO;
import com.sansec.ccsp.staticapi.monitor.request.StatisticInsertListDTO;
import com.sansec.ccsp.staticapi.monitor.response.MultiLineStatisticVO;
import com.sansec.ccsp.staticapi.monitor.response.NumStatisticVO;
import com.sansec.ccsp.staticapi.monitor.response.PieStatisticVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * @Description: 首页统计服务
 * @Author: wangjunjie
 * @CreateTime: 2023/05/13  21:43
 */
public interface StatisticIndexService {
    /**
     * 管理首页统计数字类型指标
     */
    SecRestResponse<List<NumStatisticVO>> getStatisticNum(StatisticIndexDTO statisticIndexDTO);

    /**
     * 租户首页统计数字类型指标
     */
    SecRestResponse<List<NumStatisticVO>> getTenantStatisticNum(StatisticIndexDTO statisticIndexDTO);

    /**
     * 管理首页统计饼图类型指标
     */
    SecRestResponse<List<PieStatisticVO>> getStatisticPie(StatisticIndexDTO statisticIndexDTO);

    /**
     * 租户首页统计饼图类型指标
     */
    SecRestResponse<List<PieStatisticVO>> getTenantStatisticPie(StatisticIndexDTO statisticIndexDTO);

    /**
     * 管理首页统计密码服务
     */
    SecRestResponse<MultiLineStatisticVO> getServiceMultiLine(MonitorIndexPeriodDTO monitorIndexPeriodDTO);

    /**
     * 租户首页统计密码服务
     */
    SecRestResponse<MultiLineStatisticVO> getTenantServiceMultiLine(MonitorIndexPeriodDTO monitorIndexPeriodDTO);

    /**
     * 租户首页统计服务次数
     */
    SecRestResponse<Long> getTenantAllStatistic(StatisticIndexDTO statisticIndexDTO);

    SecRestResponse<Integer> insertStatisticIncreCalList(StatisticInsertListDTO statisticInsertListDTO);

    SecRestResponse<Integer> insertStatisticTotalCalList(StatisticInsertListDTO statisticInsertListDTO);

    SecRestResponse<Integer> insertStatisticUnincreCalList(StatisticInsertListDTO statisticInsertListDTO);
}
