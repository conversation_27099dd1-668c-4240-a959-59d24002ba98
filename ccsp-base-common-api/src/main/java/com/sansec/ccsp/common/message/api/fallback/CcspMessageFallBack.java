package com.sansec.ccsp.common.message.api.fallback;

import com.sansec.ccsp.common.message.api.CcspMessageApi;
import com.sansec.ccsp.common.message.request.*;
import com.sansec.ccsp.common.message.response.CcspMessageVO;
import com.sansec.ccsp.common.message.response.RemindMessageNumVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class CcspMessageFallBack implements FallbackFactory<CcspMessageApi> {

    @Override
    public CcspMessageApi create(Throwable throwable) {
        return new CcspMessageApi() {
            @Override
            public SecRestResponse<SecPageVO<CcspMessageVO>> find(CcspMessagePageDTO ccspMessagePageDTO) {
                log.error("分页查询异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<List<CcspMessageVO>> findMessage(CcspMessageParamDTO ccspMessageParamDTO) {
                log.error("铃铛内消息列表异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> delete(CcspMessageIdDTO ccspMessageIdDTO) {
                log.error("消息删除异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> updateMessageStatus(CcspMessageIdDTO ccspMessageIdDTO) {
                log.error("消息标记已读异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> addMessage(List<CcspMessageDTO> ccspMessageDTOList) {
                log.error("消息增加异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<RemindMessageNumVO> findRemindMessageNum() {
                log.error("获取未读消息数异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> readAllMessage() {
                log.error("已读全部消息异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<List<CcspMessageVO>> findRemindMessageByNum(RemindMessageByNumDTO remindMessageByNumDTO) {
                log.error("获取未读消息异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }

            @Override
            public SecRestResponse<Object> addAlarmMessage(List<CcspMessageDTO> ccspMessageDTOList) {
                log.error("添加告警消息异常：", throwable);
                return SecRestResponse.fallback(throwable);
            }
        };
    }
}
