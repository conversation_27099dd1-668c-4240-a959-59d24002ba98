package com.sansec.ccsp.extra.business.product.request;

import lombok.Data;

/**
 * @description : 租户的密码产品修改记录;
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2023-11-23
 */
@Data
public class TenantToProductRecordDTO{
    /**
     * 主键
     */
    private Long id;
    /**
     * 租户的密码产品ID
     */
    private Long tenantToProductId;
    /**
     * 编辑类型;1：持有数量；2：到期时间
     */
    private Integer editType;
    /**
     * 编辑前
     */
    private String editBefore;
    /**
     * 编辑后
     */
    private String editAfter;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * hmac值
     */
    private String hmac;
}