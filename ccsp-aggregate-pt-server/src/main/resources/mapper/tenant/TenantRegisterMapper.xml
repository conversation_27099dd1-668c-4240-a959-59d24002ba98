<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ccsp.pt.business.tenant.mapper.TenantRegisterMapper">
    <sql id="Base_Column_List">
        ID
        ,TENANT_ID,TENANT_CODE,TENANT_NAME,TENANT_LEVEL,SENIOR_TENANT_ID,TENANT_STATUS,PARTITION_ID,ORGAN,STATUS,AUDIT_BY,AUDIT_REMARK,INVALID_FLAG,REMARK,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME
    </sql>
    <select id="selectUserInfoByTenantId" parameterType="long"
            resultType="com.sansec.ccsp.pt.business.tenant.entity.UserInfo">
        select USER_TYPE, USER_NAME, USER_CODE
        from tenant_register_user
        where TENANT_ID = #{tenantId}
    </select>

    <select id="checkTenantRegister" resultType="com.sansec.ccsp.pt.business.tenant.entity.TenantRegisterPO">
        select
        <include refid="Base_Column_List"/>
        from tenant_register
        <where>
            (TENANT_CODE = #{tenantCode}
            or TENANT_NAME = #{tenantName})
            and STATUS != 3
            and INVALID_FLAG = 0
        </where>
    </select>

</mapper>