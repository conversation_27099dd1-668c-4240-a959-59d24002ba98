package com.sansec.ccsp.common.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户角色枚举类
 */
@Getter
@AllArgsConstructor
public enum UserRoleEnum {
    PT_OPER(1L,"平台操作员", UserTypeEnum.PT_USER),
    PT_ADMIN(2L,"平台管理员", UserTypeEnum.PT_USER),
    PT_AUDIT(3L,"平台审计员", UserTypeEnum.PT_USER),
    TENANT_OPER(4L,"租户操作员", UserTypeEnum.TENANT_USER),
    TENANT_ADMIN(5L,"租户管理员", UserTypeEnum.TENANT_USER),
    TENANT_AUDIT(6L,"租户审计员", UserTypeEnum.TENANT_USER),
    ORG_AUDIT(12L,"单位审计员", UserTypeEnum.UNIT_USER)
    ;

    private final Long roleId;

    private final String roleName;

    private final UserTypeEnum userTypeEnum;

    private static final List<Long> ptUserList  = new ArrayList<>();
    private static final List<Long> tenantUserList  = new ArrayList<>();
    private static final List<Long> orgUserList  = new ArrayList<>();

    static {
        ptUserList.add(PT_OPER.getRoleId());
        ptUserList.add(PT_ADMIN.getRoleId());
        ptUserList.add(PT_AUDIT.getRoleId());

        tenantUserList.add(TENANT_OPER.getRoleId());
        tenantUserList.add(TENANT_ADMIN.getRoleId());
        tenantUserList.add(TENANT_AUDIT.getRoleId());

        orgUserList.add(ORG_AUDIT.getRoleId());
    }

    public static boolean checkIsPtUser(Long roleId) {
        return ptUserList.contains(roleId);
    }

    public static boolean checkIsTenantUser(Long roleId) {
        return tenantUserList.contains(roleId);
    }

    public static boolean checkIsOrgUser(Long roleId) {
        return orgUserList.contains(roleId);
    }
}
