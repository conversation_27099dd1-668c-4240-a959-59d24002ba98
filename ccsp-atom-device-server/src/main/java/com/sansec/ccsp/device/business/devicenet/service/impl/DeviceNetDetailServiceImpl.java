package com.sansec.ccsp.device.business.devicenet.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.common.config.api.CommonConfigServiceApi;
import com.sansec.ccsp.device.business.deviceinfo.entity.DeviceInfoPO;
import com.sansec.ccsp.device.business.deviceinfo.handler.dic.DeviceInfoDic;
import com.sansec.ccsp.device.business.deviceinfo.mapper.DeviceInfoMapper;
import com.sansec.ccsp.device.business.devicenet.convert.DeviceNetDetailConvert;
import com.sansec.ccsp.device.business.devicenet.entity.DeviceNetDetailPO;
import com.sansec.ccsp.device.business.devicenet.mapper.DeviceNetDetailMapper;
import com.sansec.ccsp.device.business.devicenet.service.DeviceNetDetailService;
import com.sansec.ccsp.device.common.constant.CommonConstant;
import com.sansec.ccsp.device.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.device.common.utils.IPToolUtil;
import com.sansec.ccsp.device.devicenet.request.DeviceNetDetailAddDTO;
import com.sansec.ccsp.device.devicenet.request.DeviceNetDetailDTO;
import com.sansec.ccsp.device.devicenet.request.DeviceNetDetailIdsDTO;
import com.sansec.ccsp.device.devicenet.request.DeviceNetDetailPageDTO;
import com.sansec.ccsp.device.devicenet.response.DeviceNetDetailVO;
import com.sansec.ccsp.security.util.LoginUserUtil;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xiaojiawei
 * @Description: IP资源详情;(DEVICE_NET_DETAIL)表服务实现类
 * @Date: 2023-2-18
 */
@Service
@Slf4j
public class DeviceNetDetailServiceImpl extends ServiceImpl<DeviceNetDetailMapper, DeviceNetDetailPO> implements DeviceNetDetailService {
    @Resource
    private DeviceNetDetailConvert deviceNetDetailConvert;

    @Resource
    private DeviceInfoMapper deviceInfoMapper;

    @Resource
    CommonConfigServiceApi commonConfigServiceApi;


    /**
     * 分页查询
     *
     * @param deviceNetDetailPageDTO 筛选条件
     * @return 查询结果
     */
    @Override
    public SecRestResponse<SecPageVO<DeviceNetDetailVO>> find(DeviceNetDetailPageDTO deviceNetDetailPageDTO) {
        //可按业务IP、管理IP、网关进行模糊查询
        QueryWrapper<DeviceNetDetailPO> queryWrapper = Wrappers.query();
        //关联云密码机id
        queryWrapper.lambda().eq(DeviceNetDetailPO::getDeviceId,deviceNetDetailPageDTO.getDeviceId());

        if (!StringUtils.isEmpty(deviceNetDetailPageDTO.getMgtIp())) {
            //管理IP模糊查询
            queryWrapper.lambda().like(DeviceNetDetailPO::getMgtIp, deviceNetDetailPageDTO.getMgtIp());
        }
        if (!StringUtils.isEmpty(deviceNetDetailPageDTO.getBusiIp())) {
            //业务IP模糊查询
            queryWrapper.lambda().like(DeviceNetDetailPO::getBusiIp, deviceNetDetailPageDTO.getBusiIp());
        }
        if (!StringUtils.isEmpty(deviceNetDetailPageDTO.getGateway())) {
            //网关模糊查询
            queryWrapper.lambda().like(DeviceNetDetailPO::getGateway, deviceNetDetailPageDTO.getGateway());
        }
        queryWrapper.lambda().orderByAsc(DeviceNetDetailPO::getIpId).orderByAsc(DeviceNetDetailPO::getStatus);
        IPage<DeviceNetDetailPO> page = new Page<>(deviceNetDetailPageDTO.getPageNum(), deviceNetDetailPageDTO.getPageSize());
        IPage<DeviceNetDetailPO> deviceNetDetailPOPage = baseMapper.selectPage(page, queryWrapper);

        SecPageVO<DeviceNetDetailVO> deviceNetDetailPageVO = deviceNetDetailConvert.pagePOToSecPageVOPage(deviceNetDetailPOPage);
        return ResultUtil.ok(deviceNetDetailPageVO);
    }

    /**
     * 新增数据
     *
     * @param deviceNetDetailAddDTO 实例对象
     * @return 实例对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SecRestResponse<Object> add(DeviceNetDetailAddDTO deviceNetDetailAddDTO) {
        DeviceNetDetailDTO deviceNetDetailDTO = deviceNetDetailConvert.addDtoToDTO(deviceNetDetailAddDTO);
        // 填充业务起止ip
        if (StringUtils.isBlank(deviceNetDetailDTO.getStartBusiIp())
                || StringUtils.isBlank(deviceNetDetailDTO.getEndBusiIp())) {
            deviceNetDetailDTO.setStartBusiIp(deviceNetDetailDTO.getStartMgtIp());
            deviceNetDetailDTO.setEndBusiIp(deviceNetDetailDTO.getEndMgtIp());
        }

        long startMgtIpLong = IPToolUtil.ip2Long(deviceNetDetailDTO.getStartMgtIp());
        long endMgtIpLong = IPToolUtil.ip2Long(deviceNetDetailDTO.getEndMgtIp());
        long startBusiIpLong = IPToolUtil.ip2Long(deviceNetDetailDTO.getStartBusiIp());
        long endBusiIpLong = IPToolUtil.ip2Long(deviceNetDetailDTO.getEndBusiIp());
        long gatewayLong = IPToolUtil.ip2Long(deviceNetDetailDTO.getGateway());

        //IP网段中不得包含网关地址IP
        if(IPToolUtil.ipIsOneNet(deviceNetDetailDTO.getStartMgtIp(),deviceNetDetailDTO.getGateway())
                && ((startMgtIpLong - gatewayLong <= 0) && (endMgtIpLong - gatewayLong >=0)) ){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_HAS_GATEWAY_ERROR);
        }
        //IP网段中不得包含网关地址IP
        if(IPToolUtil.ipIsOneNet(deviceNetDetailDTO.getStartBusiIp(),deviceNetDetailDTO.getGateway())
                && ((startBusiIpLong - gatewayLong <= 0) && (endBusiIpLong - gatewayLong >=0)) ){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_HAS_GATEWAY_ERROR);
        }
        //管理IP和服务IP不能使用同一网段的不同IP段
        if(IPToolUtil.ipIsOneNet(deviceNetDetailDTO.getStartMgtIp(),deviceNetDetailDTO.getStartBusiIp())
                && (!deviceNetDetailDTO.getStartMgtIp().equals(deviceNetDetailDTO.getStartBusiIp())) ){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_BUSI_MGT_IPNET_ERROR);
        }
        //管理IP和服务IP不能使用同一网段的不同IP段
        if(IPToolUtil.ipIsOneNet(deviceNetDetailDTO.getEndMgtIp(),deviceNetDetailDTO.getEndBusiIp())
                && (!deviceNetDetailDTO.getEndMgtIp().equals(deviceNetDetailDTO.getEndBusiIp())) ){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_BUSI_MGT_IPNET_ERROR);
        }
        //地址信息中管理IP数和服务IP数不同
        if(!IPToolUtil.ipIsOneNet(deviceNetDetailDTO.getStartMgtIp(),deviceNetDetailDTO.getStartBusiIp())
                && ((endMgtIpLong-startMgtIpLong) != (endBusiIpLong-startBusiIpLong)) ){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_BUSI_MGT_NUM_ERROR);
        }

        boolean result = addressResourceAddCheck(deviceNetDetailDTO);

        if (!result) {
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_DETAIL_IP_ENGINE);
        }
        addResourceDetail(deviceNetDetailDTO);
        return ResultUtil.ok();
    }

    /**
     * 批量处理地址资源处理
     *
     * @param deviceNetDetailDTO 实例对象
     */
    private void addResourceDetail(DeviceNetDetailDTO deviceNetDetailDTO) {
        long startIPNum = IPToolUtil.ip2Long(deviceNetDetailDTO.getStartMgtIp());
        long endIPNum = IPToolUtil.ip2Long(deviceNetDetailDTO.getEndMgtIp());

        Long startBusinessIPNum = IPToolUtil.ip2Long(deviceNetDetailDTO.getStartBusiIp());
        while (startIPNum <= endIPNum) {
            addIp(deviceNetDetailDTO, startIPNum, startBusinessIPNum, deviceNetDetailDTO.getGateway());
            startIPNum++;
            startBusinessIPNum++;
        }
    }

    /**
     * 添加单个IP地址
     *
     * @param ipNum         ip long数值
     * @param businessIpNum 服务ip long数值
     * @param gateway       网关地址
     */
    private void addIp(DeviceNetDetailDTO deviceNetDetailDTO, Long ipNum, Long businessIpNum, String gateway) {
        String ip = IPToolUtil.long2Ip(ipNum);
        String businessIp = IPToolUtil.long2Ip(businessIpNum);

        DeviceNetDetailPO deviceNetDetailPO = deviceNetDetailConvert.dtoToPo(deviceNetDetailDTO);
        deviceNetDetailPO.setIpId(IdGenerator.ins().generator());
        deviceNetDetailPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));

        if (deviceNetDetailPO.getCreateBy() == null) {
            deviceNetDetailPO.setCreateBy(LoginUserUtil.getUserId());
        }
        deviceNetDetailPO.setMgtIp(ip);
        deviceNetDetailPO.setBusiIp(businessIp);
        deviceNetDetailPO.setGateway(gateway);
        deviceNetDetailPO.setMgtIpNum(ipNum);
        deviceNetDetailPO.setBusiIpNum(businessIpNum);
        deviceNetDetailPO.setStatus(1);
        baseMapper.insert(deviceNetDetailPO);
    }

    /**
     * 添加地址资源校验
     *
     * @param deviceNetDetailDTO 添加的网段信息
     * @return
     */
    private boolean addressResourceAddCheck(DeviceNetDetailDTO deviceNetDetailDTO) {
        boolean result;
        String startIp = deviceNetDetailDTO.getStartMgtIp();
        String endIp = deviceNetDetailDTO.getEndMgtIp();

        // 检查管理ip
        result = mgtIpAddCheck(startIp, endIp, deviceNetDetailDTO.getGateway());
        if (!result) {
            return false;
        }

        String startBusinessIp = deviceNetDetailDTO.getStartBusiIp();
        String endBusinessIp = deviceNetDetailDTO.getEndBusiIp();
        if (!startBusinessIp.equals(startIp) || !endBusinessIp.equals(endIp)) {
            result = busiIpAddCheck(startBusinessIp, endBusinessIp, deviceNetDetailDTO.getGateway());
            return result;
        }

        return true;
    }


    /**
     * 添加网关资源校验
     *
     * @param gateway 网段网关地址
     * @return
     */
    public boolean gatewayAddCheck(String gateway) {
        QueryWrapper<DeviceNetDetailPO> queryWrapper = Wrappers.query();
        //v3宿主机是否与维护地址资源ip冲突
        List<DeviceNetDetailPO> addressList = baseMapper.selectList(queryWrapper.eq("GATEWAY", gateway));
        return addressList.isEmpty();
    }

    /**
     * 添加ip资源校验
     *
     * @param startIp IP段起始IP
     * @param endIp   IP段结束I
     * @return
     */
    private Boolean mgtIpAddCheck(String startIp, String endIp, String gateway) {
        long checkIp = IPToolUtil.ip2Long(startIp);
        long checkEndIp = IPToolUtil.ip2Long(endIp);

        if (checkIp > checkEndIp) {
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_MGT_START_END_ERROR);
        }
        if(!IPToolUtil.ipIsOneNet(startIp,endIp)){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_MGT_IP_NOT_ONE_NET);
        }

        //区域模式下，统一区域内，IP不能重复
        //循环相等检查来确认数据库中是否存在被使用ip地址
        while (checkIp <= checkEndIp) {
            LambdaQueryWrapper<DeviceNetDetailPO> queryWrapper = new LambdaQueryWrapper<>();
            LambdaQueryWrapper<DeviceInfoPO> queryWrapperInfo = new LambdaQueryWrapper<>();
            String checkIpStr = IPToolUtil.long2Ip(checkIp);
            //地址资源是否与维护地址资源ip冲突
            queryWrapper.eq(DeviceNetDetailPO::getMgtIp, checkIpStr);
            queryWrapper.eq(DeviceNetDetailPO::getGateway, gateway);
            List<DeviceNetDetailPO> addressList = baseMapper.selectList(queryWrapper);

            queryWrapperInfo.eq(DeviceInfoPO::getMgtIp, checkIpStr);

            List<DeviceInfoPO> addressInfoList = deviceInfoMapper.selectList(queryWrapperInfo);
            if (!addressList.isEmpty() || !addressInfoList.isEmpty()) {
                return false;
            }
            checkIp++;
        }
        return true;
    }
//    private Boolean mgtIpAddCheck(String startIp, String endIp, String gateway,Long deviceId) {
//        long checkIp = IPToolUtil.ip2Long(startIp);
//        long checkEndIp = IPToolUtil.ip2Long(endIp);
//
//        if (checkIp > checkEndIp) {
//            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_MGT_START_END_ERROR);
//        }
//        if(!IPToolUtil.ipIsOneNet(startIp,endIp)){
//            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_MGT_IP_NOT_ONE_NET);
//        }
//
//        String region_mode = commonConfigServiceApi.getConfigValueByConfigCode(CommonConstant.REGION_MODE).getResult();
//        //获取与此宿主机同一区域的所有宿主机
//        Long regionId = null;
//        List<Long> regionDeviceId = new ArrayList<>();
//        if("1".equals(region_mode)){
//            //查询宿主机的区域id
//            LambdaQueryWrapper<DeviceInfoPO> queryWrapper=new LambdaQueryWrapper<>();
//            queryWrapper.eq(DeviceInfoPO::getDeviceId,deviceId);
//            DeviceInfoPO infoPO = deviceInfoMapper.selectOne(queryWrapper);
//            regionId=infoPO.getRegionId();
//            //查询该区域下都有哪些宿主机
//            LambdaQueryWrapper<DeviceInfoPO> regionQueryWrapper=new LambdaQueryWrapper<>();
//            regionQueryWrapper.eq(DeviceInfoPO::getRegionId,regionId);
//            regionQueryWrapper.eq(DeviceInfoPO::getFamilyType,1);
//            List<DeviceInfoPO> deviceInfoPOList = deviceInfoMapper.selectList(regionQueryWrapper);
//            regionDeviceId = deviceInfoPOList.stream().map(po -> po.getDeviceId()).collect(Collectors.toList());
//        }
//        //区域模式下，统一区域内，IP不能重复
//        //循环相等检查来确认数据库中是否存在被使用ip地址
//        while (checkIp <= checkEndIp) {
//            LambdaQueryWrapper<DeviceNetDetailPO> queryWrapper = new LambdaQueryWrapper<>();
//            LambdaQueryWrapper<DeviceInfoPO> queryWrapperInfo = new LambdaQueryWrapper<>();
//            String checkIpStr = IPToolUtil.long2Ip(checkIp);
//            //地址资源是否与维护地址资源ip冲突
//            queryWrapper.eq(DeviceNetDetailPO::getMgtIp, checkIpStr);
//            queryWrapper.eq(DeviceNetDetailPO::getGateway, gateway);
//            if("1".equals(region_mode)){
//                queryWrapper.in(DeviceNetDetailPO::getDeviceId,regionDeviceId);
//            }
//            List<DeviceNetDetailPO> addressList = baseMapper.selectList(queryWrapper);
//
//            queryWrapperInfo.eq(DeviceInfoPO::getMgtIp, checkIpStr);
//            if("1".equals(region_mode)){
//                queryWrapperInfo.eq(DeviceInfoPO::getRegionId,regionId);
//            }
//            List<DeviceInfoPO> addressInfoList = deviceInfoMapper.selectList(queryWrapperInfo);
//            if (!addressList.isEmpty() || !addressInfoList.isEmpty()) {
//                return false;
//            }
//            checkIp++;
//        }
//        return true;
//    }


    private Boolean busiIpAddCheck(String startIp, String endIp, String gateway) {
        long checkIp = IPToolUtil.ip2Long(startIp);
        long checkEndIp = IPToolUtil.ip2Long(endIp);

        if (checkIp > checkEndIp) {
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_BUSI_START_END_ERROR);
        }
        if(!IPToolUtil.ipIsOneNet(startIp,endIp)){
            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_BUSI_IP_NOT_ONE_NET);
        }
        //区域模式下，统一区域内，IP不能重复
        //循环相等检查来确认数据库中是否存在被使用ip地址
        while (checkIp <= checkEndIp) {
            LambdaQueryWrapper<DeviceNetDetailPO> queryWrapper = new LambdaQueryWrapper<>();
            LambdaQueryWrapper<DeviceInfoPO> queryWrapperInfo = new LambdaQueryWrapper<>();
            String checkIpStr = IPToolUtil.long2Ip(checkIp);
            //地址资源是否与维护地址资源ip冲突
            queryWrapper.eq(DeviceNetDetailPO::getBusiIp, checkIpStr);
            queryWrapper.eq(DeviceNetDetailPO::getGateway, gateway);
            List<DeviceNetDetailPO> addressList = baseMapper.selectList(queryWrapper);
            queryWrapperInfo.eq(DeviceInfoPO::getBusiIp, checkIpStr);
            List<DeviceInfoPO> addressInfoList = deviceInfoMapper.selectList(queryWrapperInfo);
            if (!addressList.isEmpty() || !addressInfoList.isEmpty()) {
                return false;
            }
            checkIp++;
        }
        return true;
    }
//    private Boolean busiIpAddCheck(String startIp, String endIp, String gateway,Long deviceId) {
//        long checkIp = IPToolUtil.ip2Long(startIp);
//        long checkEndIp = IPToolUtil.ip2Long(endIp);
//
//        if (checkIp > checkEndIp) {
//            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_BUSI_START_END_ERROR);
//        }
//        if(!IPToolUtil.ipIsOneNet(startIp,endIp)){
//            throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_BUSI_IP_NOT_ONE_NET);
//        }
//        String region_mode = commonConfigServiceApi.getConfigValueByConfigCode(CommonConstant.REGION_MODE).getResult();
//        //获取与此宿主机同一区域的所有宿主机
//        Long regionId = null;
//        List<Long> regionDeviceId = new ArrayList<>();
//        if("1".equals(region_mode)){
//            //查询宿主机的区域id
//            LambdaQueryWrapper<DeviceInfoPO> queryWrapper=new LambdaQueryWrapper<>();
//            queryWrapper.eq(DeviceInfoPO::getDeviceId,deviceId);
//            DeviceInfoPO infoPO = deviceInfoMapper.selectOne(queryWrapper);
//            regionId=infoPO.getRegionId();
//            //查询该区域下都有哪些宿主机
//            LambdaQueryWrapper<DeviceInfoPO> regionQueryWrapper=new LambdaQueryWrapper<>();
//            regionQueryWrapper.eq(DeviceInfoPO::getRegionId,regionId);
//            regionQueryWrapper.eq(DeviceInfoPO::getFamilyType,1);
//            List<DeviceInfoPO> deviceInfoPOList = deviceInfoMapper.selectList(regionQueryWrapper);
//            regionDeviceId = deviceInfoPOList.stream().map(po -> po.getDeviceId()).collect(Collectors.toList());
//        }
//        //区域模式下，统一区域内，IP不能重复
//        //循环相等检查来确认数据库中是否存在被使用ip地址
//        while (checkIp <= checkEndIp) {
//            LambdaQueryWrapper<DeviceNetDetailPO> queryWrapper = new LambdaQueryWrapper<>();
//            LambdaQueryWrapper<DeviceInfoPO> queryWrapperInfo = new LambdaQueryWrapper<>();
//            String checkIpStr = IPToolUtil.long2Ip(checkIp);
//            //地址资源是否与维护地址资源ip冲突
//            queryWrapper.eq(DeviceNetDetailPO::getBusiIp, checkIpStr);
//            queryWrapper.eq(DeviceNetDetailPO::getGateway, gateway);
//            if("1".equals(region_mode)){
//                queryWrapper.in(DeviceNetDetailPO::getDeviceId,regionDeviceId);
//            }
//            List<DeviceNetDetailPO> addressList = baseMapper.selectList(queryWrapper);
//
//            queryWrapperInfo.eq(DeviceInfoPO::getBusiIp, checkIpStr);
//            if("1".equals(region_mode)){
//                queryWrapperInfo.eq(DeviceInfoPO::getRegionId,regionId);
//            }
//            List<DeviceInfoPO> addressInfoList = deviceInfoMapper.selectList(queryWrapperInfo);
//            if (!addressList.isEmpty() || !addressInfoList.isEmpty()) {
//                return false;
//            }
//            checkIp++;
//        }
//        return true;
//    }


    /**
     * 更新数据
     *
     * @param deviceNetDetailDTO 实例对象
     * @return 实例对象
     */
    @Override
    public SecRestResponse<Object> edit(DeviceNetDetailDTO deviceNetDetailDTO) {
        DeviceNetDetailPO deviceNetDetailPO = deviceNetDetailConvert.dtoToPo(deviceNetDetailDTO);
        deviceNetDetailPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        Long updateBy = LoginUserUtil.getUserId();
        deviceNetDetailPO.setUpdateBy(updateBy);
        baseMapper.updateById(deviceNetDetailPO);
        return ResultUtil.ok();
    }

    /**
     * 通过主键删除数据
     *
     * @param deviceNetDetailDTO 实例对象
     * @return 实例对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SecRestResponse<Object> deleteById(DeviceNetDetailIdsDTO deviceNetDetailDTO) {
        List<Long> ipIdList = deviceNetDetailDTO.getIpIdList();
        if (ipIdList.size() != 0) {
            ipIdList.forEach(ipId -> {
                DeviceNetDetailPO deviceNetDetailPO = baseMapper.selectById(ipId);
                if (deviceNetDetailPO == null) {
                    throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_DETAIL_NOT_EXIST);
                }
                if (deviceNetDetailPO.getStatus() == 2 || deviceNetDetailPO.getStatus() == 3) {
                    throw new BusinessException(SecErrorCodeConstant.DEVICE_NET_DETAIL_NOT_DELETE);
                } else {
                    baseMapper.deleteById(ipId);
                }
            });
        }
        return ResultUtil.ok();
    }

    @Override
    public List<DeviceNetDetailPO> availableIpList(Long deviceId, Integer number, String gateway, List<String> notAvailableIpList) {
        if (number == null || number <= 0 || StringUtils.isBlank(gateway)) {
            throw new BusinessException("输入参数异常");
        }
        log.info("获取可用IP地址列表，输入参数，数量{},网关{}", number, gateway);
        List<DeviceNetDetailPO> list = baseMapper.selectList(new LambdaQueryWrapper<DeviceNetDetailPO>()
                .eq(DeviceNetDetailPO::getDeviceId,deviceId)
                .eq(DeviceNetDetailPO::getGateway, gateway)
                .eq(DeviceNetDetailPO::getStatus, DeviceInfoDic.NET_NOT_USE)
                .notIn(DeviceNetDetailPO::getBusiIp, notAvailableIpList)

        );
        if (list.size() < number) {
            throw new BusinessException(SecErrorCodeConstant.AVAILABLE_IP_NOT_ENOUGH);
        }
        List<DeviceNetDetailPO> result = new ArrayList<>(number);
        for (int i = 0; i < number; i++) {
            result.add(list.get(i));
        }
        return result;
    }

    @Override
    public List<DeviceNetDetailPO> selectByIds(List<Long> ids) {
        return baseMapper.selectList(new LambdaQueryWrapper<DeviceNetDetailPO>().in(DeviceNetDetailPO::getIpId,ids));
    }

    @Override
    public boolean ipExistsByDeviceAndGateway(Long deviceId, String gateway) {
        QueryWrapper<DeviceNetDetailPO> queryWrapper = Wrappers.query();

        queryWrapper.lambda().eq(DeviceNetDetailPO::getDeviceId,deviceId).
                eq(DeviceNetDetailPO::getGateway,gateway);

        return baseMapper.selectCount(queryWrapper) > 0;
    }

}