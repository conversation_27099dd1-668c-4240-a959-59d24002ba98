package com.sansec.ccsp.extra.business.product.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.extra.business.product.entity.ProductAttachmentPO;
import com.sansec.ccsp.extra.business.product.request.ProductAttachmentDTO;
import com.sansec.ccsp.extra.business.product.response.ProductAttachmentVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

 /**
 * @description : 密码产品附件表;(PRODUCT_ATTACHMENT)实体类转换接口
 * <AUTHOR> x<PERSON><PERSON>jiaw<PERSON>
 * @date : 2023-11-22
 */
@Mapper(componentModel = "spring")
public interface ProductAttachmentConvert{
    /**
     * dtoToPo
     * @param productAttachmentDTO
     * @return
     */
    @Mappings({})
    ProductAttachmentPO dtoToPo(ProductAttachmentDTO productAttachmentDTO);
    
    /**
     * poToDto
     * @param productAttachmentPO
     * @return
     */
    ProductAttachmentDTO poToDto(ProductAttachmentPO productAttachmentPO);
    
    /**
     * poToDto-list
     * @param list
     * @return
     */
    List<ProductAttachmentDTO> poToDto(List<ProductAttachmentPO> list);

     @Mapping(source = "size", target = "pageSize")
     @Mapping(source = "current", target = "pageNum")
     @Mapping(source = "records", target = "list")
    SecPageVO<ProductAttachmentVO> pagePOToSecPageVOPage(IPage<ProductAttachmentPO> iPage);
    
    @InheritConfiguration(name = "convertVo")
    List<ProductAttachmentVO> convert(List<ProductAttachmentPO> list);
    
    @Mappings({})
    ProductAttachmentVO convertVo(ProductAttachmentPO request);
}