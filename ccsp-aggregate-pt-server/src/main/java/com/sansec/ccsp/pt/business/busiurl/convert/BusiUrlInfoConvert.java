package com.sansec.ccsp.pt.business.busiurl.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.pt.business.busiurl.entity.BusiUrlInfoPO;
import com.sansec.ccsp.pt.business.busiurl.entity.DicBusiUrlTypePO;
import com.sansec.ccsp.pt.business.busiurl.request.*;
import com.sansec.ccsp.pt.business.busiurl.response.BusiUrlInfoPageVO;
import com.sansec.ccsp.pt.business.busiurl.response.BusiUrlInfoVO;
import com.sansec.ccsp.pt.business.busiurl.response.DicBusiUrlTypeVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @Description: 业务地址类型转换
 * @CreateTime: 2023-05-09
 * @Author: wangjunjie
 */
@Mapper(componentModel = "spring")
public interface BusiUrlInfoConvert {

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<BusiUrlInfoPageVO> infoPageToSecPage(IPage<BusiUrlInfoPO> busiUrlInfoPOPage);

    @Mappings({})
    BusiUrlInfoPO addDtoToInfoPO(AddBusiUrlDTO addBusiUrlDTO);

    @Mappings({})
    BusiUrlInfoPO editDtoToInfoPO(EditBusiUrlDTO editBusiUrlDTO);

    @Mappings({})
    List<BusiUrlInfoVO> poListToVoList(List<BusiUrlInfoPO> busiUrlInfoPOList);

    @Mappings({})
    List<DicBusiUrlTypeVO> urlTypePoListToVoList(List<DicBusiUrlTypePO> dicBusiUrlPOList);
}