package com.sansec.ccsp.pt.business.sim.request;

import com.sansec.ccsp.common.pattern.CommonPattern;
import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class SimKeyCertPageDTO extends SecPageDTO {

    /**
     * 手机号 密文
     */
    private String phone;

    /**
     * 会话公钥id
     */
//    @NotNull(message = "sessionKeyId不可为空")
    private String sessionKeyId;
}

