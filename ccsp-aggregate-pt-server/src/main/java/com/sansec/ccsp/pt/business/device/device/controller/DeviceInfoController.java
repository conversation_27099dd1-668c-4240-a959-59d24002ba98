package com.sansec.ccsp.pt.business.device.device.controller;

import com.sansec.ccsp.device.deviceinfo.request.DeviceIdDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdsDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoPageDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.FreeDeviceByTenantDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.DeviceInfoListDTO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoInGroupVO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoVO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceStatusVO;
import com.sansec.ccsp.device.devicetype.request.DeviceTypeIdsDTO;
import com.sansec.ccsp.pt.business.device.device.service.DeviceInfoService;
import com.sansec.ccsp.security.annotation.HasAnyPermissions;
import com.sansec.ccsp.security.annotation.IgnoreToken;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> ds
 * @Description: 设备组平台管理
 * @Date: 2023-3-3
 */
@RestController
@RequestMapping("/device/deviceinfo/v1")
@Validated
public class DeviceInfoController {

    @Resource
    private DeviceInfoService deviceInfoService;

    /**
     * @param deviceInfoPageDTO 筛选条件
     * @return SecPageVO
     * @Description: 分页查询
     */
    @PostMapping("/find")
    @HasAnyPermissions("device:deviceinfo:list")
    public SecRestResponse<SecPageVO<DeviceInfoVO>> find(@Validated @RequestBody DeviceInfoPageDTO deviceInfoPageDTO) {
        return deviceInfoService.find(deviceInfoPageDTO);
    }

    /**
     * @param deviceInfoListDTO 筛选条件
     * @return List
     * @Description: 列表查询
     */
    @PostMapping("/list")
    public SecRestResponse<List<DeviceInfoVO>> list(@Validated @RequestBody DeviceInfoListDTO deviceInfoListDTO) {
        return ResultUtil.ok(deviceInfoService.list(deviceInfoListDTO));
    }

    @PostMapping("/getDeviceInfoByFree")
    public SecRestResponse<List<DeviceInfoInGroupVO>> getDeviceInfoByFree(@Validated @RequestBody FreeDeviceByTenantDTO freeDeviceByTenantDTO) {
        return ResultUtil.ok(deviceInfoService.getFreeDeviceByTenant(freeDeviceByTenantDTO));
    }

    /**
     * 获取设备类型下的空闲设备
     *
     * @param deviceTypeIdsDTO 支持多种类型，字符串格式逗号隔开
     * @return
     */
    @PostMapping("/getFreeDeviceInfoByTypes")
    public SecRestResponse<List<DeviceInfoInGroupVO>> getFreeDeviceInfoByTypes(@Validated @RequestBody DeviceTypeIdsDTO deviceTypeIdsDTO) {
        return ResultUtil.ok(deviceInfoService.getFreeDeviceInfoByTypes(deviceTypeIdsDTO));
    }

    /**
     * @param deviceIdDTO 实例对象
     * @return 实例对象
     * @Description: 通过主键获取数据
     */
    @PostMapping("/info")
    public SecRestResponse<DeviceInfoVO> info(@RequestBody @Validated DeviceIdDTO deviceIdDTO) {
        return ResultUtil.ok(deviceInfoService.info(deviceIdDTO));
    }

    /**
     * @param deviceIdDTO 实例对象
     * @return 实例对象
     * @Description: 通过主键删除数据
     */
    @PostMapping("/delete")
    public SecRestResponse<Object> delete(@RequestBody @Validated DeviceIdDTO deviceIdDTO) {
        return deviceInfoService.delete(deviceIdDTO);
    }

    /**
     * @param deviceIdsDTO 设备Id列表
     * @return 实例对象
     * @Description: 获取传入的设备列表的状态
     */
    @PostMapping("/getDeviceStatusList")
    @IgnoreToken
    public SecRestResponse<List<DeviceStatusVO>> getDeviceStatusList(@RequestBody @Validated DeviceIdsDTO deviceIdsDTO) {
        return deviceInfoService.getDeviceStatusList(deviceIdsDTO);
    }

}