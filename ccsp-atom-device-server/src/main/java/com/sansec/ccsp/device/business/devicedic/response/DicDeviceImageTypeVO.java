package com.sansec.ccsp.device.business.devicedic.response;

import lombok.Data;

 /**
 * @Description: 虚拟机镜像类型字典表;
 * <AUTHOR> x<PERSON><PERSON>jiaw<PERSON>
 * @Date: 2023-2-18
 */
@Data
public class DicDeviceImageTypeVO{
    /**
     * 镜像类型ID
     */
    private Long id;
    /**
     * 镜像名称
     */
    private String imageName;
    /**
     * 镜像值
     */
    private String imageValue;
    /**
     * 是否作废;默认为0
     */
    private Integer invalidFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

}