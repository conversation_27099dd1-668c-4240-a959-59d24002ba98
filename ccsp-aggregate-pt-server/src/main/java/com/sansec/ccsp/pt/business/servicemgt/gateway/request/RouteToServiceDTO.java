package com.sansec.ccsp.pt.business.servicemgt.gateway.request;

import lombok.Data;

import java.util.List;

/**
 * @Description: 路由与服务或设备关联表;
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>aw<PERSON>
 * @Date: 2023-2-24
 */
@Data
public class RouteToServiceDTO{
    /**
     * 主键
     */
    private Long id;
    /**
     * 路由表主键
     */
    private Long routeId;
    /**
     * 服务主键/设备主键
     */
    private Long serviceId;
    /**
     * 1服务 2设备
     */
    private Integer serviceType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 服务ID集合
     */
    private List<Long> serviceIdList;
}