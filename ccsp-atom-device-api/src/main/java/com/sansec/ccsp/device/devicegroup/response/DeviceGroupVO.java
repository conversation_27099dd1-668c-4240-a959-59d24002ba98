package com.sansec.ccsp.device.devicegroup.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description : 设备组;
 * @date : 2023-2-18
 */
@Data
public class DeviceGroupVO {
    /**
     * 设备组ID
     */
    private Long deviceGroupId;
    /**
     * 设备组名称
     */
    private String deviceGroupName;
    /**
     * 设备组类型
     */
    private Integer deviceGroupType;
    /**
     * 区域id
     */
    private Long regionId;
    /**
     * 区域名称
     */
    private String regionName;
    /**
     * 所属租户ID
     */
    private Long tenantId;
    /**
     * 所属租户名称
     */
    private String tenantName;
    /**
     * 业务类型列表
     */
    private List<BusiTypeVO> busiTypeList;
    /**
     * 设备组设备数量
     */
    private Integer deviceNum;
    /**
     * 是否支持业务调用;0：不支持；1：支持
     */
    private Integer isRest;
    /**
     * 网络协议;1：TCP；2：HTTPS
     */
    private Integer netPro;
    /**
     * 设备类型ID
     */
    private Long deviceTypeId;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;
    /**
     * 是否支持共享
     */
    private Integer isShare;
    /**
     * 是否作废
     */
    private Integer invalidFlag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 网关对外业务地址
     */
    private String busiUrl;

    public boolean checkIsShare(){
        if(isShare != null && isShare==1){
            return true;
        }
        return false;
    }
    public boolean checkIsRest(){
        if(isRest != null && isRest==1){
            return true;
        }
        return false;
    }
}