spring:
  datasource:
    druid:
      driver-class-name: com.kingbase8.Driver
      url: ******************************************************************
      username: G1BB34LtW1dHITqnaBlL9A==
      password: xVVPkDeghinz8HulYbhy1g==
      #password: swxa1234.
      #检查池中的连接是否仍可用的 SQL 语句,drui会连接到数据库执行该SQL, 如果正常返回，则表示连接可用，否则表示连接不可用
      validationQuery: SELECT 1 FROM DUAL
      #缓存通过以下两个方法发起的SQL:
      #public PreparedStatement prepareStatement(String sql)
      #public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency)
      poolPreparedStatements: true
      #每个连接最多缓存多少个SQL
      maxPoolPreparedStatementPerConnectionSize: 20
      #这里配置的是插件,常用的插件有:
      #监控统计: filter:stat
      #日志监控: filter:log4j 或者 slf4j
      #防御SQL注入: filter:wall
#      filters: stat,wall
mybatis:
  databaseType: kingbasees
