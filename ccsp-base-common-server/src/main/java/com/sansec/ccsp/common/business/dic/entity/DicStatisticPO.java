package com.sansec.ccsp.common.business.dic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

 /**
 * @description : 统计指标字典表;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-5-13
 */
@TableName("DIC_STATISTIC")
@Data
public class DicStatisticPO extends BasePO{
    /**
     * ID
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 服务简称
     */
    private String serviceCode;
    /**
     * 统计类型 1增量 2非增量数量计算类 3非增量非计算类
     */
    private Integer statisticType;
     /**
      * 统计指标展示名称
      */
     private String statisticShowName;
    /**
     * 统计指标名称
     */
    private String statisticName;
    /**
     * 是否启用 1启用 0停用
     */
    private Integer isAvailable;
    /**
     * 是否同服务类型全服务调用 1是 2否
     */
    private Integer isInvokeMultiService;
    /**
     * OID
     */
    private String oid;
    /**
     * 单位
     */
    private String unit;

}