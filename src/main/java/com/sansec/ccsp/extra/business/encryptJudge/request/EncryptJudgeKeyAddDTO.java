package com.sansec.ccsp.extra.business.encryptJudge.request;

import com.sansec.ccsp.extra.business.constant.CommonPattern;
import lombok.Data;

import javax.validation.constraints.*;

@Data
public class EncryptJudgeKeyAddDTO {
    /**
     * 业务账号id
     */
    @NotNull(message = "业务账号id不可为空")
    Long appId;

    /**
     * 密钥类型  1内部密钥  2外部密钥
     */
    @NotNull(message = "密钥类型不可为空")
    @Min(value = 1, message = "密钥类型的值只能是1或2")
    @Max(value = 2, message = "密钥类型的值只能是1或2")
    Integer type;

    /**
     * 密钥名称
     */
    @NotBlank(message = "密钥名称不可为空")
    @Max(value = 50, message = "密钥名称长度最大为50")
    @Pattern(regexp = CommonPattern.COMMON_CODE, message = "密钥名称必须以大小写字母或者数字开头并只能包含大小写字母、数字、特殊字符-_")
    String keyName;

    /**
     * 密钥算法
     */
    @NotBlank(message = "密钥算法不可为空")
    String alg;

    /**
     * 密钥长度
     */
    @NotNull(message = "密钥长度不可为空")
    Integer keyLength;

    /**
     * 接收方区域id
     */
    Long regionIdReceiver;

    /**
     * 接收方租户id
     */
    Long tenantIdReceiver;

    /**
     * 接收方业务账号id
     */
    Long appIdReceiver;

    /**
     * 备注
     */
    @Size(max = 100, message = "备注长度最大为100")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "备注请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
    String remark;
}
