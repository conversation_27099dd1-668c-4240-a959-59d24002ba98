package com.sansec.ccsp.common.config.api;


import com.sansec.ccsp.common.config.api.fallback.UserManagerConfigServiceFallBack;
import com.sansec.ccsp.common.config.request.EditConfigDTO;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@FeignClient(name = "${spring.protocol.prefix}${spring.application.common.name}" , path="/ccsp/common", fallbackFactory = UserManagerConfigServiceFallBack.class)
@Validated
public interface UserManagerConfigServiceApi {


    @PostMapping("/api/config/v1/getUserManagerConfigList")
    SecRestResponse<Object> getUserManagerConfigList();
    /**
     * 配置登陆失败次数
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/loginFailuresAllowedTimes")
    SecRestResponse<Object> updateLoginFailuresAllowedTimes(@RequestBody @Validated EditConfigDTO editConfigDTO);
    /**
     * 配置登录失败锁定时长
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/loginErrorLockTime")
    SecRestResponse<Object> updateLoginErrorLockTime(@Validated @RequestBody EditConfigDTO editConfigDTO);

    /**
     * 配置口令有效期
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/authCodeExpireDate")
    SecRestResponse<Object> updateAuthCodeExpiredate(@Validated @RequestBody EditConfigDTO editConfigDTO);
    /**
     * 配置口令有效期告警
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/authCodeExpirationReminder")
    SecRestResponse<Object> updateAuthCodeExpirationReminder(@Validated @RequestBody EditConfigDTO editConfigDTO);
    /**
     * 配置多长时间未登录禁用账户
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/unloggedTime")
    SecRestResponse<Object> updateUnloggedTime(@Validated @RequestBody EditConfigDTO editConfigDTO);
    /**
     * 配置历史口令限制
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/authCodeHistoryLimit")
    SecRestResponse<Object> updateAuthCodeHistoryLimit(@Validated @RequestBody EditConfigDTO editConfigDTO);
    /**
     * 配置默认口令
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/defaultAuthCode")
    SecRestResponse<Object> updateDefaultAuthCode(@Validated @RequestBody EditConfigDTO editConfigDTO);
    /**
     * 配置口令登录方式
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/openAuthCodeLogin")
    SecRestResponse<Object> updateAuthCodeLogin(@Validated @RequestBody EditConfigDTO editConfigDTO);

    /**
     * 配置SIM盾登录方式
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/openSimShieldLogin")
    SecRestResponse<Object> updateSimShieldLogin(@Validated @RequestBody EditConfigDTO editConfigDTO);

    /**
     * 配置SIMKEY登录方式
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/openSimKeyLogin")
    SecRestResponse<Object> updateSimKeyLogin(@Validated @RequestBody EditConfigDTO editConfigDTO);



    /**
     * 配置ukey登录方式
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/openUKeyLogin")
    SecRestResponse<Object> updateUKeyLogin(@Validated @RequestBody EditConfigDTO editConfigDTO);
    /**
     * 查询登录方式
     * @return
     */
    @PostMapping("/api/config/v1/getLoginType")
    SecRestResponse<Object> getLoginType();

    /**
     * 查询登录方式
     * @return
     */
    @PostMapping("/api/config/v1/getLoginTypeByTenantId/{id}")
    SecRestResponse<Object> getLoginTypeByTenantId(@NotNull @PathVariable(value = "id") Long tenantId);

    /**
     * 是否强制修改默认口令
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/forceUpdatePassword")
    SecRestResponse<Object> forceUpdatePassword(@Validated @RequestBody EditConfigDTO editConfigDTO);

    /**
     * 密码服务平台SIM登录回调地址
     * @param editConfigDTO
     * @return
     */
    @PostMapping("/api/config/v1/ccspSimCallBackUrl")
    SecRestResponse<Object> ccspSimCallBackUrl(@Validated @RequestBody EditConfigDTO editConfigDTO);


}

