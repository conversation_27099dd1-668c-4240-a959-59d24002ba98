spring.datasource.driver=vastbase
spring.datasource.url=*************************************************************
spring.datasource.userName=cij12HnDlBS+LSux23RUCw==
spring.datasource.passWord=A/YC4vF0axVbZJsskjNGhQ==
mybatis.databaseType=postgresql

spring.datasource.initialSize = 30
spring.datasource.maxActive = 500
spring.datasource.minIdle = 30
spring.datasource.maxWait = 20000
spring.datasource.timeBetweenConnectErrorMillis = 90000
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT 1
spring.datasource.testOnBorrow = false
spring.datasource.testOnReturn = false
spring.datasource.testWhileIdle = true
# 连接池中连接最小空闲时间（毫秒）
spring.datasource.minEvictableIdleTimeMillis = 300000

