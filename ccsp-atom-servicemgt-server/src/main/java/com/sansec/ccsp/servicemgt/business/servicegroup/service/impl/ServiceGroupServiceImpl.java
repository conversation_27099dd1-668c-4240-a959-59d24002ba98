package com.sansec.ccsp.servicemgt.business.servicegroup.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ccsp.common.config.api.CommonConfigServiceApi;
import com.sansec.ccsp.common.dic.response.DicBusiTypeVO;
import com.sansec.ccsp.common.dic.response.DicShareGroupTypeVO;
import com.sansec.ccsp.security.util.LoginUserUtil;
import com.sansec.ccsp.servicemgt.business.common.service.CommonConfigService;
import com.sansec.ccsp.servicemgt.business.common.service.DicShareGroupTypeService;
import com.sansec.ccsp.servicemgt.business.dbmgt.entity.DatabaseUnitToServiceGroupPO;
import com.sansec.ccsp.servicemgt.business.dbmgt.service.DatabaseInfoService;
import com.sansec.ccsp.servicemgt.business.dbmgt.service.DatabaseMinimumUnitService;
import com.sansec.ccsp.servicemgt.business.dbmgt.service.DatabaseUnitToServiceGroupService;
import com.sansec.ccsp.servicemgt.business.dbmgt.service.DicBusiTypeToDatabaseService;
import com.sansec.ccsp.servicemgt.business.service.BusiTypeService;
import com.sansec.ccsp.servicemgt.business.servicegroup.convert.ServiceGroupConvert;
import com.sansec.ccsp.servicemgt.business.servicegroup.entity.ServiceGroupPO;
import com.sansec.ccsp.servicemgt.business.servicegroup.entity.ServiceGroupToBusiTypePO;
import com.sansec.ccsp.servicemgt.business.servicegroup.entity.ServiceGroupToGroupPO;
import com.sansec.ccsp.servicemgt.business.servicegroup.mapper.ServiceGroupMapper;
import com.sansec.ccsp.servicemgt.business.servicegroup.mapper.ServiceGroupToBusiTypeMapper;
import com.sansec.ccsp.servicemgt.business.servicegroup.service.ServiceGroupService;
import com.sansec.ccsp.servicemgt.business.servicegroup.service.ServiceGroupToBusiTypeService;
import com.sansec.ccsp.servicemgt.business.servicegroup.service.ServiceGroupToGroupService;
import com.sansec.ccsp.servicemgt.business.serviceinfo.entity.ServiceInfoPO;
import com.sansec.ccsp.servicemgt.business.serviceinfo.mapper.ServiceInfoMapper;
import com.sansec.ccsp.servicemgt.business.serviceinfo.service.ServiceInfoService;
import com.sansec.ccsp.servicemgt.business.servicetype.entity.ServiceTypeToBusiTypePO;
import com.sansec.ccsp.servicemgt.business.servicetype.mapper.ServiceTypeToBusiTypeMapper;
import com.sansec.ccsp.servicemgt.common.constant.CommonConstant;
import com.sansec.ccsp.servicemgt.common.error.SecErrorCodeConstant;
import com.sansec.ccsp.servicemgt.dbmgt.request.DatabaseMinimumUnitDeleteDTO;
import com.sansec.ccsp.servicemgt.dbmgt.request.DatabaseUnitToServiceGroupDTO;
import com.sansec.ccsp.servicemgt.dbmgt.response.DatabaseInfoVO;
import com.sansec.ccsp.servicemgt.dbmgt.response.DatabaseMinimumUnitVO;
import com.sansec.ccsp.servicemgt.dbmgt.response.DatabaseUnitToServiceGroupVO;
import com.sansec.ccsp.servicemgt.servicegroup.request.*;
import com.sansec.ccsp.servicemgt.servicegroup.response.*;
import com.sansec.ccsp.servicemgt.serviceinfo.request.*;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceGroupWithInfoVO;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceInfoInGroupVO;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceInfoVO;
import com.sansec.ccsp.servicemgt.serviceinfo.response.ServiceListByTenantIdPageVO;
import com.sansec.ccsp.task.task.request.SysTaskDTO;
import com.sansec.ccsp.task.task.service.SysTaskService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.DateUtils;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> xiaojiawei
 * @Description: 服务组;(SERVICE_GROUP)表服务实现类
 * @Date: 2023-2-19
 */
@Service
@Slf4j
public class ServiceGroupServiceImpl extends ServiceImpl<ServiceGroupMapper, ServiceGroupPO>
        implements ServiceGroupService {
    @Resource
    private ServiceGroupConvert serviceGroupConvert;
    @Resource
    private ServiceInfoMapper serviceInfoMapper;
    @Resource
    private ServiceInfoService serviceInfoService;
    @Resource
    private ServiceGroupToBusiTypeMapper serviceGroupToBusiTypeMapper;
    @Resource
    private ServiceTypeToBusiTypeMapper serviceTypeToBusiTypeMapper;
    @Resource
    private ServiceGroupToBusiTypeService serviceGroupToBusiTypeService;
    @Resource
    private BusiTypeService busiTypeService;
    @Resource
    private CommonConfigService commonConfigService;
    @Resource
    private DatabaseInfoService databaseInfoService;
    @Resource
    private SysTaskService sysTaskService;
    @Resource
    private DatabaseUnitToServiceGroupService databaseUnitToServiceGroupService;
    @Resource
    private DicBusiTypeToDatabaseService dicBusiTypeToDatabaseService;
    @Resource
    CommonConfigServiceApi configServiceApi;
    @Resource
    DatabaseMinimumUnitService databaseMinimumUnitService;
    @Resource
    private ServiceGroupToGroupService serviceGroupToGroupService;
    @Resource
    private DicShareGroupTypeService dicShareGroupTypeService;

    /**
     * @param serviceGroupPageDTO 筛选条件
     * @return 查询结果
     * @Description: 分页查询
     */
    @Override
    public SecRestResponse<SecPageVO<ServiceGroupVO>> find(ServiceGroupPageDTO serviceGroupPageDTO) {
        int pageNum = serviceGroupPageDTO.getPageNum();
        int pageSize = serviceGroupPageDTO.getPageSize();
        Long busiType = serviceGroupPageDTO.getBusiType();

        if (busiType != null) {
            //校验业务类型是否合法
            List<Long> busiTypeIdList = new ArrayList<>();
            busiTypeIdList.add(busiType);
            checkBusiType(busiTypeIdList);
        }

        IPage<ServiceGroupPO> page = new Page<>(pageNum, pageSize);
        IPage<ServiceGroupPO> pageList = baseMapper.findPageList(page, serviceGroupPageDTO);

        SecPageVO<ServiceGroupVO> serviceGroupVOSecPageVO = serviceGroupConvert.pagePOToSecPageVOPage(pageList);

        List<ServiceGroupVO> serviceGroupVOList = serviceGroupVOSecPageVO.getList();
        // 调用接口补充业务id列表调用接口补充业务id列表
        if (!CollectionUtils.isEmpty(serviceGroupVOList)) {
            List<ServiceGroupVO> tmpList = addServiceNum(serviceGroupVOList);
            serviceGroupVOList = addBusiTypeList(tmpList, null);
            this.addDatabaseId(serviceGroupVOList);
            serviceGroupVOSecPageVO.setList(serviceGroupVOList);
        }
        return ResultUtil.ok(serviceGroupVOSecPageVO);
    }

    /**
     * 设置服务组的数据库id
     *
     * @param serviceGroupVOList
     */
    private void addDatabaseId(List<ServiceGroupVO> serviceGroupVOList) {
        List<Long> serviceGroupIdList = serviceGroupVOList.stream().map(ServiceGroupVO::getServiceGroupId).distinct().collect(toList());
        List<DatabaseUnitToServiceGroupPO> databaseUnitToServiceGroupPOList = databaseUnitToServiceGroupService.findByServiceGroupIdList(serviceGroupIdList);
        Map<Long, Long> dataMap = databaseUnitToServiceGroupPOList.stream().collect(Collectors.toMap(DatabaseUnitToServiceGroupPO::getServiceGroupId,
                DatabaseUnitToServiceGroupPO::getDatabaseId, (key1, key2) -> key2));
        serviceGroupVOList.stream().forEach(vo -> {
            vo.setDatabaseId(dataMap.get(vo.getServiceGroupId()));
        });
    }

    @Override
    public SecRestResponse<List<ServiceGroupVO>> findServiceGroupList(ServiceGroupListDTO serviceGroupListDTO) {
        List<Long> busiTypeIdList = serviceGroupListDTO.getBusiTypeList();

        if (busiTypeIdList != null && !busiTypeIdList.isEmpty()) {
            //校验业务类型是否合法
            checkBusiType(busiTypeIdList);
        }

        List<ServiceGroupPO> serviceGroupPOS = baseMapper.findList(serviceGroupListDTO);
        List<ServiceGroupVO> serviceGroupVOS = serviceGroupConvert.convert(serviceGroupPOS);

        // 调用接口补充业务id列表
        if (serviceGroupVOS != null && !serviceGroupVOS.isEmpty()) {
            List<ServiceGroupVO> tmpList = addServiceNum(serviceGroupVOS);
            serviceGroupVOS = addBusiTypeList(tmpList, busiTypeIdList);
        }

        return ResultUtil.ok(serviceGroupVOS);
    }

    @Override
    public SecRestResponse<List<ServiceGroupIdNameVO>> getServiceGroupIdNameList(ServiceGroupIdNameDTO serviceGroupIdNameDTO) {
        return ResultUtil.ok(getServiceIdNames(serviceGroupIdNameDTO, false, true));
    }

    @Override
    public SecRestResponse<List<ServiceGroupIdNameVO>> getAllServiceGroupIdName(ServiceGroupIdNameDTO serviceGroupIdNameDTO) {
        return ResultUtil.ok(getServiceIdNames(serviceGroupIdNameDTO, true, true));
    }

    @Override
    public SecRestResponse<List<ServiceGroupIdNameVO>> getAllBusiTypeServiceGroup(ServiceGroupIdNameDTO serviceGroupIdNameDTO) {
        return ResultUtil.ok(getServiceIdNames(serviceGroupIdNameDTO, true, false));
    }

    /**
     * 区别：是否包含默认组
     */
    private List<ServiceGroupIdNameVO> getServiceIdNames(ServiceGroupIdNameDTO serviceGroupIdNameDTO, boolean isAll, boolean addBusiType) {
        List<Long> busiTypeIdList = serviceGroupIdNameDTO.getBusiTypeIdList();
        //校验业务类型是否合法
        if (CollectionUtils.isNotEmpty(busiTypeIdList)) {
            checkBusiType(busiTypeIdList);
        }

        List<ServiceGroupPO> serviceGroupPOS;
        if (isAll) {
            serviceGroupPOS = baseMapper.getAllServiceGroupIdNameList(serviceGroupIdNameDTO);
        } else {
            serviceGroupPOS = baseMapper.getServiceGroupIdNameList(serviceGroupIdNameDTO);
        }

        ServiceGroupAndBusiTypeIdDTO dto=new ServiceGroupAndBusiTypeIdDTO();
        dto.setServiceTypeIds(serviceGroupIdNameDTO.getBusiTypeIdList());
        dto.setServiceGroupIdList(serviceGroupIdNameDTO.getServiceGroupIdList());
        SecRestResponse<List<ServiceInfoVO>> response = serviceInfoService.getListByGroupAndTypeId(dto);

        List<Long> groupIDSet = new ArrayList<>();
        if (response.isSuccess()) {
            List<ServiceInfoVO> serviceInfoVOList = response.getResult();
            if (CollectionUtils.isNotEmpty(serviceInfoVOList)) {
                groupIDSet = serviceInfoVOList.stream().map(ServiceInfoVO::getServiceGroupId).collect(Collectors.toList());
            }
        } else {
            throw new BusinessException(response.getCode());
        }

        List<ServiceGroupPO> serviceGroupPOList = new ArrayList<>();
        for (ServiceGroupPO serviceGroupPO : serviceGroupPOS) {
            if (groupIDSet.contains(serviceGroupPO.getServiceGroupId())) {
                serviceGroupPOList.add(serviceGroupPO);
            }
        }

        List<ServiceGroupVO> serviceGroupVOList = serviceGroupConvert.convert(serviceGroupPOList);
        // 调用接口补充业务id列表
        if (addBusiType && serviceGroupPOList != null && !serviceGroupPOList.isEmpty()) {
            serviceGroupVOList = addBusiTypeList(serviceGroupVOList, busiTypeIdList);
        }
        List<ServiceGroupIdNameVO> serviceGroupIdNameVOS = serviceGroupConvert.VoToIDNameVo(serviceGroupVOList);
        return serviceGroupIdNameVOS;
    }

    @Override
    public SecRestResponse<ServiceGroupVO> info(ServiceGroupInfoDTO ServiceGroupDTO) {
        Long serviceGroupId = ServiceGroupDTO.getServiceGroupId();
        //判断对象和权限
        ServiceGroupPO serviceGroupPO = checkServiceGroup(serviceGroupId);
        ServiceGroupVO serviceGroupVO = serviceGroupConvert.convertVo(serviceGroupPO);
        List<ServiceGroupVO> serviceGroupVOS = new ArrayList<>();
        serviceGroupVOS.add(serviceGroupVO);
        addBusiTypeList(serviceGroupVOS, null);
        addServiceNum(serviceGroupVOS);
        return ResultUtil.ok(serviceGroupVOS.get(0));
    }

    /**
     * @param serviceGroupDTO 实例对象
     * @return 实例对象
     * @Description: 新增数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SecRestResponse<Long> add(ServiceGroupAddDTO serviceGroupDTO) {
        //判断服务组名称同租户下不可重复
        List<ServiceGroupPO> serviceGroupPOS = baseMapper.selectByGroupName(serviceGroupDTO.getServiceGroupName(), null);
        if (!serviceGroupPOS.isEmpty()) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_NAME_DUPLICATE);
        }
        //判断服务组code同租户下不可重复
        List<ServiceGroupPO> serviceGroupPOList = baseMapper.selectByGroupCode(serviceGroupDTO.getServiceGroupCode(), null);
        if (!serviceGroupPOList.isEmpty()) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_CODE_DUPLICATE);
        }

        //创建服务组对象
        ServiceGroupPO serviceGroupPO = serviceGroupConvert.dtoToPoAdd(serviceGroupDTO);
        serviceGroupPO.setServiceGroupId(IdGenerator.ins().generator());
        serviceGroupPO.setCreateBy(LoginUserUtil.getUserId());
        serviceGroupPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        serviceGroupPO.setRegionId(serviceGroupDTO.getRegionId());

//        //获取配置项
//        //服务组是否单业务类型
//        String serviceGroupOnlyBusi = commonConfigService.getConfigValueByConfigCodeString(CommonConstant.SERVICE_GROUP_ONLY_BUSI);
//        //服务组是否数据隔离
//        String serviceGroupDataIsolation = commonConfigService.getConfigValueByConfigCodeString(CommonConstant.SERVICE_GROUP_DATA_LSOLATION);

        //获取所有业务类型
        List<DicBusiTypeVO> busiTypeList = busiTypeService.getBusiTypeList();
        //服务支持的业务类型列表
        List<DicBusiTypeVO> busiTypeVOList = new ArrayList<>();

        //数据库ID不可为空
        if (serviceGroupDTO.getDatabaseId() == null) {
            throw new BusinessException(SecErrorCodeConstant.DB_INFO_NOT_NULL);
        }
        //获取数据库服务对象
        DatabaseInfoVO databaseInfoVO = databaseInfoService.findById(serviceGroupDTO.getDatabaseId());

        if (serviceGroupDTO.getIsShare() == 1) {//共享服务组单业务类型
            //校验并获取对应业务类型对象
            busiTypeVOList.add(checkBusiType(serviceGroupDTO.getBusiTypeId(), busiTypeList));
        } else { //专享组是全部业务类型
            busiTypeVOList = busiTypeList;
        }

        //开启异步任务，创建数据库实例，并执行初始化脚本
        createDataUnitAll(serviceGroupPO.getServiceGroupId(), databaseInfoVO, serviceGroupDTO, busiTypeVOList);

        //设置服务组状态为创建中
        serviceGroupPO.setServiceGroupStatus(CommonConstant.SERVICE_GROUP_CREATE);

        //判断服务组是否单业务类型,需创建服务组和业务类型的关联数据
        if (serviceGroupDTO.getIsShare() == 1) {
            ServiceGroupToBusiTypeDTO serviceGroupToBusiTypeDTO = new ServiceGroupToBusiTypeDTO();
            serviceGroupToBusiTypeDTO.setServiceGroupId(serviceGroupPO.getServiceGroupId());
            serviceGroupToBusiTypeDTO.setBusiTypeId(serviceGroupDTO.getBusiTypeId());
            serviceGroupToBusiTypeService.batchAdd(Arrays.asList(serviceGroupToBusiTypeDTO));

            /**
             * 判断业务类型是否支持创建共享服务组
             */
            if (serviceGroupDTO.getKmsServiceGroupId() != null) {
                ServiceGroupToGroupAddDTO serviceGroupToGroupAddDTO = new ServiceGroupToGroupAddDTO();
                serviceGroupToGroupAddDTO.setServiceGroupId(serviceGroupPO.getServiceGroupId());
                serviceGroupToGroupAddDTO.setKmsGroupId(serviceGroupDTO.getKmsServiceGroupId());
                serviceGroupToGroupService.addKmsGroup(serviceGroupToGroupAddDTO);
            }
        }

        Integer num = baseMapper.insert(serviceGroupPO);
        if (num == null || num != 1) {
            throw new BusinessException(SecErrorCodeConstant.DB_ERROR);
        }
        return ResultUtil.ok(serviceGroupPO.getServiceGroupId());
    }

    /**
     * 校验业务类型
     *
     * @param busiTypeId
     * @param busiTypeList
     * @return
     */
    private DicBusiTypeVO checkBusiType(Long busiTypeId, List<DicBusiTypeVO> busiTypeList) {
        Map<Long, DicBusiTypeVO> dicBusiTypeVOMap = busiTypeList.stream().collect(Collectors.toMap(DicBusiTypeVO::getId, item -> item));
        //校验业务类型不可为空、业务对象不可为空
        if (busiTypeId == null) {
            throw new BusinessException(SecErrorCodeConstant.BUSI_TYPE_NOT_NULL);
        }
        if (!dicBusiTypeVOMap.containsKey(busiTypeId)) {
            throw new BusinessException(SecErrorCodeConstant.BUSI_TYPE_NOT_EXIST);
        }

        return dicBusiTypeVOMap.get(busiTypeId);
    }


    /**
     * 开启异步任务，创建数据库实例，并执行初始化脚本
     *
     * @param databaseInfoVO  数据库服务
     * @param serviceGroupDTO 服务组
     * @param busiTypeVOList  业务类型列表
     */
    // private void createDataUnit(Long serviceGroupId, DatabaseInfoVO databaseInfoVO, ServiceGroupAddDTO serviceGroupDTO, List<DicBusiTypeVO> busiTypeVOList) {
    //     Map<Long, String> dbNameByTypeIdMap = dicBusiTypeToDatabaseService.getDbNameByTypeIdMap();
    //
    //     busiTypeVOList.forEach(busiType -> {
    //         SysTaskDTO sysTaskDTO = new SysTaskDTO();
    //         sysTaskDTO.setTaskId(IdGenerator.ins().generator());
    //         String taskName = "createDataUnit" + serviceGroupDTO.getServiceGroupCode() + busiType.getId() + serviceGroupId;
    //         sysTaskDTO.setTaskName(taskName);
    //         sysTaskDTO.setTaskGroup(taskName);
    //         JSONObject jo = new JSONObject();
    //         jo.put("taskId", sysTaskDTO.getTaskId());
    //         //todo  部分参数待定
    //
    //         //服务类型ID
    //         jo.put("serviceTypeId", busiType.getId());
    //         //服务组ID
    //         jo.put("serviceGroupId", serviceGroupId);
    //         //服务组code
    //         jo.put("serviceGroupCode", serviceGroupDTO.getServiceGroupCode());
    //         //pt管控程序IP
    //         jo.put("remoteServiceHost", serviceGroupDTO.getRemoteServiceHost());
    //         //pt管控程序端口
    //         jo.put("remoteServicePort", serviceGroupDTO.getRemoteServicePort());
    //         //数据库服务ID
    //         jo.put("dataBaseId", databaseInfoVO.getId());
    //         //数据库实例名称
    //         String dbName = dbNameByTypeIdMap.get(busiType.getId()) + "_" + serviceGroupId;
    //         jo.put("dbName", dbName);
    //
    //         String jsonParam = "serviceGroupTaskServiceImpl.createDataUnit(\"" + jo.toJSONString() + "\")";
    //         sysTaskDTO.setJsonParam(jsonParam);
    //         sysTaskService.add(sysTaskDTO);
    //     });
    // }
    /**
     * 开启异步任务，创建所有服务数据库实例，并执行初始化脚本
     *
     * @param databaseInfoVO  数据库服务
     * @param serviceGroupDTO 服务组
     * @param busiTypeVOList  业务类型列表
     */
    private void createDataUnitAll(Long serviceGroupId, DatabaseInfoVO databaseInfoVO,
                                   ServiceGroupAddDTO serviceGroupDTO, List<DicBusiTypeVO> busiTypeVOList) {
        SysTaskDTO sysTaskDTO = new SysTaskDTO();
        sysTaskDTO.setTaskId(IdGenerator.ins().generator());
        String taskName = "createDataUnitAll" + serviceGroupDTO.getServiceGroupCode() + serviceGroupId;
        sysTaskDTO.setTaskName(taskName);
        sysTaskDTO.setTaskGroup(taskName);
        JSONObject jo = new JSONObject();
        jo.put("taskId", sysTaskDTO.getTaskId());
        //服务组ID
        jo.put("serviceGroupId", serviceGroupId);
        //服务组code
        jo.put("serviceGroupCode", serviceGroupDTO.getServiceGroupCode());
        //pt管控程序IP
        jo.put("remoteServiceHost", serviceGroupDTO.getRemoteServiceHost());
        //pt管控程序端口
        jo.put("remoteServicePort", serviceGroupDTO.getRemoteServicePort());
        //数据库服务ID
        jo.put("dataBaseId", databaseInfoVO.getId());
        jo.put("busiTypeVOList", busiTypeVOList);
        String jsonParam = "serviceGroupTaskServiceImpl.createDataUnitAll(\"" + jo.toJSONString() + "\")";
        sysTaskDTO.setJsonParam(jsonParam);
        sysTaskService.add(sysTaskDTO);
    }

    /**
     * @param serviceGroupDTO
     * @return SecRestResponse<Object> 返回类型
     * @Description: 单租户模式，初始化创建顶级租户默认服务组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecRestResponse<ServiceGroupVO> singleTenantInitServiceGroup(ServiceGroupAddDTO serviceGroupDTO) {
        ServiceGroupPO serviceGroupPO = serviceGroupConvert.dtoToPoAdd(serviceGroupDTO);
        serviceGroupPO.setServiceGroupId(1L);
        serviceGroupPO.setCreateBy(LoginUserUtil.getUserId());
        serviceGroupPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        Integer num = baseMapper.insert(serviceGroupPO);
        if (num == null || num != 1) {
            throw new BusinessException(SecErrorCodeConstant.DB_ERROR);
        }
        ServiceGroupVO serviceGroupVO = serviceGroupConvert.convertVo(serviceGroupPO);

//        //获取所有业务类型
//        List<DicBusiTypeVO> dicBusiTypeVOList = busiTypeService.getBusiTypeList();
//
//        //默认组绑定所有业务类型
//        List<ServiceGroupToBusiTypeDTO> serviceGroupToBusiTypeDTOList = new ArrayList<>();
//        dicBusiTypeVOList.forEach(item -> {
//            ServiceGroupToBusiTypeDTO serviceGroupToBusiTypeDTO = new ServiceGroupToBusiTypeDTO();
//            serviceGroupToBusiTypeDTO.setServiceGroupId(serviceGroupPO.getServiceGroupId());
//            serviceGroupToBusiTypeDTO.setBusiTypeId(item.getId());
//            serviceGroupToBusiTypeDTOList.add(serviceGroupToBusiTypeDTO);
//        });
//        serviceGroupToBusiTypeService.batchAdd(serviceGroupToBusiTypeDTOList);

        return ResultUtil.ok(serviceGroupVO);
    }

    /**
     * @param serviceGroupDTO 实例对象
     * @return 实例对象
     * @Description: 更新数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SecRestResponse<Long> edit(ServiceGroupEditDTO serviceGroupDTO) {
        Long serviceGroupId = serviceGroupDTO.getServiceGroupId();
        //判断对象
        ServiceGroupPO serviceGroupPO = checkServiceGroup(serviceGroupId);

        //判断是否重名
        if (StringUtils.isNotBlank(serviceGroupDTO.getServiceGroupName())
                && !serviceGroupPO.getServiceGroupName().equals(serviceGroupDTO.getServiceGroupName())) {

            List<ServiceGroupPO> serviceGroupPOS = this.baseMapper.selectByGroupName(serviceGroupDTO.getServiceGroupName(), null);
            if (!serviceGroupPOS.isEmpty()) {
                throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_NAME_DUPLICATE);
            }
        }

        serviceGroupPO = serviceGroupConvert.dtoToPoEdit(serviceGroupDTO);
        serviceGroupPO.setUpdateBy(LoginUserUtil.getUserId());
        serviceGroupPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        Integer num = baseMapper.updateById(serviceGroupPO);
        if (num == null || num != 1) {
            throw new BusinessException(SecErrorCodeConstant.DB_ERROR);
        }
        return ResultUtil.ok(serviceGroupPO.getServiceGroupId());
    }

    /**
     * @param serviceGroupDTO 实例对象
     * @return 实例对象
     * @Description: 通过主键删除数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecRestResponse<Object> deleteById(ServiceGroupDeleteDTO serviceGroupDTO) {
        log.info("ServiceGroupServiceImpl.deleteById() , serviceGroupDTO= {}", serviceGroupDTO);
        Long serviceGroupId = serviceGroupDTO.getServiceGroupId();
        //判断对象和权限
        ServiceGroupPO serviceGroupPO = checkServiceGroup(serviceGroupId);

        log.info("ServiceGroupServiceImpl.deleteById() , 删除数据库实例= {}", serviceGroupPO);
        this.deleteDatabaseInfo(serviceGroupPO);

        QueryWrapper<ServiceGroupToBusiTypePO> busiTypeWrapper = new QueryWrapper<>();
        busiTypeWrapper.lambda().eq(ServiceGroupToBusiTypePO::getServiceGroupId, serviceGroupId);

        //共享服务组，如果使用KMS服务，需同步删除服务组和KMS服务组的关联关系
        if (serviceGroupPO.getIsShare() == 1) {
            List<ServiceGroupToBusiTypePO> busiTypePOList = serviceGroupToBusiTypeMapper.selectList(busiTypeWrapper);
            if (CollectionUtils.isNotEmpty(busiTypePOList)) {
                DicShareGroupTypeVO dicShareGroupTypeVO = dicShareGroupTypeService.getShareGroupType(busiTypePOList.get(0).getBusiTypeId());
                if (dicShareGroupTypeVO != null && dicShareGroupTypeVO.getIsNeedKms() == 1) {
                    ServiceGroupToGroupGetDTO serviceGroupToGroupGetDTO = new ServiceGroupToGroupGetDTO();
                    serviceGroupToGroupGetDTO.setServiceGroupId(serviceGroupId);
                    serviceGroupToGroupService.deleteToKmsGroup(serviceGroupToGroupGetDTO);
                }
            }
        }

        serviceGroupToBusiTypeMapper.delete(busiTypeWrapper);
        int num = baseMapper.deleteById(serviceGroupDTO.getServiceGroupId());
        if ( num != 1 ) {
            throw new BusinessException(SecErrorCodeConstant.DB_ERROR);
        }
        return ResultUtil.ok();
    }

    /**
     * @param dto
     * @return 实例对象
     * @Description: 租户删除时，调用该方法，删除租户下所有服务组相关数据
     */
    @Override
    public SecRestResponse<Integer> deleteListByTenantId(DeleteByTenantIdDTO dto) {
        log.info("ServiceGroupServiceImpl.deleteListByTenantId() , DeleteByTenantIdDTO= {}", dto);
        List<Long> serviceGroupIds = dto.getServiceGroupIdList();
        if (CollectionUtils.isEmpty(serviceGroupIds)) {
            return ResultUtil.ok();
        }

        //查询服务组关联数据库实例IDS
        List<Long> databaseUnitIds = databaseUnitToServiceGroupService.getDataMiniUtilIdsByGroupIds(serviceGroupIds);
        log.info("ServiceGroupServiceImpl.deleteListByTenantId() , databaseUnitIds= {}", databaseUnitIds);
        //删除服务组关联数据库数据列表
        Integer deleteDataUnitToServiceGroupNum = databaseUnitToServiceGroupService.deleteByServiceGroupIds(serviceGroupIds);
        log.info("ServiceGroupServiceImpl.deleteListByTenantId() , deleteDataUnitToServiceGroupNum= {}", deleteDataUnitToServiceGroupNum);
        //删除数据库实例
        Integer deleteDataUnitNum = databaseMinimumUnitService.deleteDataBaseUnitList(databaseUnitIds);
        log.info("ServiceGroupServiceImpl.deleteListByTenantId() , deleteDataUnitNum= {}", deleteDataUnitNum);

        //删除服务组对应业务类型数据
        QueryWrapper<ServiceGroupToBusiTypePO> busiTypeWrapper = new QueryWrapper<>();
        busiTypeWrapper.lambda().in(ServiceGroupToBusiTypePO::getServiceGroupId, serviceGroupIds);
        Integer deleteBusiTypeNum = serviceGroupToBusiTypeMapper.delete(busiTypeWrapper);
        log.info("ServiceGroupServiceImpl.deleteListByTenantId() , deleteBusiTypeNum= {}", deleteBusiTypeNum);

        //删除服务组
        Integer num = baseMapper.deleteBatchIds(serviceGroupIds);
        log.info("ServiceGroupServiceImpl.deleteListByTenantId() , deleteServiceGroup= {}", num);
        if (num == null) {
            throw new BusinessException(SecErrorCodeConstant.DB_ERROR);
        }

        return ResultUtil.ok(num);
    }

    /**
     * 数据库数据清除
     * 服务组隔离时删除DatabaseMinimumUnitPO
     * 删除DatabaseUnitToServiceGroupPO
     *
     * @param serviceGroupPO
     */
    private void deleteDatabaseInfo(ServiceGroupPO serviceGroupPO) {
        String isolation = commonConfigService.getConfigValueByConfigCodeString(CommonConstant.SERVICE_GROUP_DATA_LSOLATION);
        List<DatabaseUnitToServiceGroupPO> databaseUnitToServiceGroupPOS = databaseUnitToServiceGroupService.findByServiceGroupIdList(Arrays.asList(serviceGroupPO.getServiceGroupId()));
        databaseUnitToServiceGroupPOS.forEach(databaseUnitToServiceGroupPO -> {
            DatabaseUnitToServiceGroupDTO databaseUnitToServiceGroupDTO = new DatabaseUnitToServiceGroupDTO();
            databaseUnitToServiceGroupDTO.setId(databaseUnitToServiceGroupPO.getId());
            databaseUnitToServiceGroupService.deleteById(databaseUnitToServiceGroupDTO);
            if (Boolean.TRUE.toString().equalsIgnoreCase(isolation) || serviceGroupPO.getServiceGroupType().equals(CommonConstant.ROOT_GROUP_TYPE)) {
                DatabaseMinimumUnitDeleteDTO dto = new DatabaseMinimumUnitDeleteDTO();
                dto.setId(databaseUnitToServiceGroupPO.getDatabaseUnitId());
                databaseMinimumUnitService.deleteById(dto);
            }
        });
    }

    @Override
    public SecRestResponse<SecPageVO<ServiceListByTenantIdPageVO>> getServiceListByTenantId(ServiceListByTenantIdPageDTO servicePageDTO) {
        // 设备组为普通分组
        IPage<ServiceListByTenantIdPageVO> ipage = new Page(servicePageDTO.getPageNum(), servicePageDTO.getPageSize());
        IPage<ServiceListByTenantIdPageVO> servicePagePO = serviceInfoMapper.getServicesInfoByTenantId(ipage, servicePageDTO);
        if (servicePagePO == null) {
            throw new RuntimeException(SecErrorCodeConstant.SERVICE_IN_GROUP_ERROR);
        }
        SecPageVO<ServiceListByTenantIdPageVO> secPageVO = serviceGroupConvert.listPageToSecPageVOPage(servicePagePO);
        return ResultUtil.ok(secPageVO);
    }

    @Override
    public Long addService(ServiceInGroupDTO serviceInGroupDTO) {
        //校验服务组对象、服务对象是否存在，服务组中的租户ID是否为当前登录的租户
        Long serviceId = serviceInGroupDTO.getServiceId();
        Long serviceGroupId = serviceInGroupDTO.getServiceGroupId();

        ServiceGroupPO serviceGroupPO = baseMapper.selectById(serviceGroupId);
        ServiceInfoPO serviceInfoPO = serviceInfoMapper.selectById(serviceId);
        checkObject(serviceGroupPO, serviceInfoPO);

        serviceInGroupDTO.setServiceGroupType(serviceGroupPO.getServiceGroupType());

        if (serviceGroupId.equals(serviceInfoPO.getServiceGroupId())) {
            //租户ID为空或为1，设备对象所属设备组必须为空
            log.error("服务已被当前服务组绑定");
            throw new BusinessException(SecErrorCodeConstant.SERVICE_HAS_BOND_CUR_GROUP);
        }

        Long groupTenantId = serviceGroupPO.getTenantId();
        Long rootGroupId = null;
        if (!Objects.equals(1, serviceGroupPO.getServiceGroupType())) {
            // 根据租户ID获取租户ID下类型为1的服务组ID
            List<ServiceGroupPO> rootServiceGroupPOS = baseMapper.selectResourcesServiceGroupByTenantId(groupTenantId);
            if (rootServiceGroupPOS == null || rootServiceGroupPOS.size() != 1) {
                throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_TENANT_ERROR);
            }

            rootGroupId = rootServiceGroupPOS.get(0).getServiceGroupId();
            Long serviceGroupId1 = serviceInfoPO.getServiceGroupId();
            if (serviceGroupId1 != null && !serviceGroupId1.equals(rootGroupId)) {
                throw new BusinessException(SecErrorCodeConstant.SERVICE_IN_GROUP_THIS_TENANT_TYPE1);
            }
        } else {
            if (serviceInfoPO.getServiceGroupId() != null) {
                //租户ID为空或为1，服务对象所属服务组必须为空
                log.error("服务已被其他服务组绑定");
                throw new BusinessException(SecErrorCodeConstant.SERVICE_IN_GROUP_ALREADY_BOND);
            }
        }

        //服务组是否数据隔离
        String serviceGroupDataIsolation = commonConfigService.getConfigValueByConfigCodeString(CommonConstant.SERVICE_GROUP_DATA_LSOLATION);
        //修改服务对象下的服务组ID为传入的ID
        serviceInfoPO.setServiceGroupId(serviceGroupId);
        serviceInfoPO.setTenantId(serviceGroupPO.getTenantId());
        serviceInfoPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        serviceInfoPO.setUpdateBy(LoginUserUtil.getUserId());

        if (CommonConstant.TRUE.equals(serviceGroupDataIsolation)) {
            serviceInfoPO.setOperStatus(2);
        } else {
            //获取服务支持的业务类型，和服务组业务类型对比，修改服务组组的业务类型为两者的并集
            addGroupBusType(serviceInGroupDTO);

            if (rootGroupId != null) {
                ServiceInGroupDTO rootServiceInGroupDTO = new ServiceInGroupDTO();
                rootServiceInGroupDTO.setServiceGroupId(rootGroupId);
                rootServiceInGroupDTO.setServiceId(serviceId);
                dellGroupBusType(rootServiceInGroupDTO);
            }
        }

        serviceInfoMapper.updateById(serviceInfoPO);
        return serviceId;
    }

    /**
     * 修改服务组绑定服务状态
     *
     * @param serviceInGroupDTO
     * @return
     */
    @Override
    public SecRestResponse<Object> setAddServiceStatus(ServiceInGroupDTO serviceInGroupDTO) {
        log.info("setAddServiceStatus: serviceGroupId:{};serviceId();isSuccess:{}", serviceInGroupDTO.getServiceGroupId(),
                serviceInGroupDTO.getServiceId(), serviceInGroupDTO.getIsSuccess());
        ServiceGroupPO serviceGroupPO = baseMapper.selectById(serviceInGroupDTO.getServiceGroupId());
        if (serviceInGroupDTO.getIsSuccess() == 1) {
            //获取服务支持的业务类型，和服务组业务类型对比，修改服务组组的业务类型为两者的并集
            addGroupBusType(serviceInGroupDTO);
            Long groupTenantId = serviceGroupPO.getTenantId();
            Long rootGroupId = null;
            if (!Objects.equals(1, serviceGroupPO.getServiceGroupType())) {
                // 根据租户ID获取租户ID下类型为1的服务组ID
                List<ServiceGroupPO> rootServiceGroupPOS = baseMapper.selectResourcesServiceGroupByTenantId(groupTenantId);
                if (rootServiceGroupPOS == null || rootServiceGroupPOS.size() != 1) {
                    throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_TENANT_ERROR);
                }

                rootGroupId = rootServiceGroupPOS.get(0).getServiceGroupId();
            }

            if (rootGroupId != null) {
                ServiceInGroupDTO rootServiceInGroupDTO = new ServiceInGroupDTO();
                rootServiceInGroupDTO.setServiceGroupId(rootGroupId);
                rootServiceInGroupDTO.setServiceId(serviceInGroupDTO.getServiceId());
                dellGroupBusType(rootServiceInGroupDTO);
            }
        }

        setGroupServiceOperStatus(serviceInGroupDTO);

        return ResultUtil.ok();
    }

    /**
     * 修改服务组中迁移服务的状态
     *
     * @param serviceInGroupDTO
     */
    @Override
    public void setGroupServiceOperStatus(ServiceInGroupDTO serviceInGroupDTO) {
        log.info("setAddServiceStatus: serviceGroupId:{};serviceId{};isSuccess:{}", serviceInGroupDTO.getServiceGroupId(),
                serviceInGroupDTO.getServiceId(), serviceInGroupDTO.getIsSuccess());

        ServiceInfoPO serviceInfoPO = serviceInfoMapper.selectById(serviceInGroupDTO.getServiceId());
        if (serviceInGroupDTO.getIsSuccess() == 1) {
            serviceInfoPO.setOperStatus(1);
        } else {
            serviceInfoPO.setOperStatus(3);
        }
        serviceInfoPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
        serviceInfoMapper.updateById(serviceInfoPO);
    }

    @Override
    public SecRestResponse<List<ServiceGroupStatusVO>> getStatusByServiceGroupIds(ServiceGroupIdsDTO serviceGroupIdsDTO) {
        List<ServiceGroupPO> serviceGroupPOS = baseMapper.selectBatchIds(serviceGroupIdsDTO.getServiceGroupIds());
        if (CollectionUtils.isEmpty(serviceGroupPOS)) {
            return null;
        }
        return ResultUtil.ok(serviceGroupConvert.poToServiceGroupStatusVOList(serviceGroupPOS));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> addServiceList(ServiceListInGroupDTO serviceListInGroupDTO) {
        Long serviceGroupId = serviceListInGroupDTO.getServiceGroupId();

        List<Long> serviceIdList = serviceListInGroupDTO.getServiceIdList();
        List<Long> serviceIds = new ArrayList<>();
        serviceIdList.forEach(id -> {
            ServiceInGroupDTO serviceInGroupDTO = new ServiceInGroupDTO();
            serviceInGroupDTO.setServiceId(id);
            serviceInGroupDTO.setServiceGroupId(serviceGroupId);
            Long deviceId = addService(serviceInGroupDTO);
            serviceIds.add(deviceId);
        });
        return serviceIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SecRestResponse<Object> deleteService(ServiceInGroupDTO serviceInGroupDTO) {
        //校验服务组对象、服务对象是否存在，服务组中的租户ID是否为当前登录的租户
        Long serviceId = serviceInGroupDTO.getServiceId();
        Long serviceGroupId = serviceInGroupDTO.getServiceGroupId();

        ServiceGroupPO serviceGroupPO = baseMapper.selectById(serviceGroupId);
        ServiceInfoPO serviceInfoPO = serviceInfoMapper.selectById(serviceId);
        checkObject(serviceGroupPO, serviceInfoPO);

        //根据服务ID获取服务对象
        if (serviceInfoPO.getServiceGroupId() == null || !Objects.equals(serviceInfoPO.getServiceGroupId(), serviceGroupId)) {
            //判断服务是否已分配服务组，服务所属服务组的ID是否为要解绑的服务组
            log.error("需要解绑的服务不在该服务组下");
            throw new BusinessException(SecErrorCodeConstant.SERVICE_IN_GROUP_DEVICE_GROUP_NOT_SAME);
        }

        serviceInfoMapper.updateServiceGroup(serviceInGroupDTO.getServiceId());

        if (serviceGroupPO.getIsShare() == 0) {
            //独享服务组查找服务组下的所有服务，取出每个服务支持的业务类型，并取并集，将结果重新写入到服务组的业务类型表中
            dellGroupBusType(serviceInGroupDTO);
        }
        return ResultUtil.ok();
    }

    @Override
    public SecRestResponse<SecPageVO<ServiceInfoInGroupVO>> getServiceByGroupIdPage(GetServicesByGroupIdDTO getServicesByGroupIdDTO) {
        Long serviceGroupId = getServicesByGroupIdDTO.getServiceGroupId();
        ServiceGroupPO serviceGroupPO = baseMapper.selectById(getServicesByGroupIdDTO.getServiceGroupId());
        if (serviceGroupPO == null) {
            log.error("需要查询的服务组不存在");
            throw new BusinessException(SecErrorCodeConstant.SERVICE_IN_GROUP_GROUP_NOT_EXIST);
        }
        IPage<ServiceInfoInGroupVO> page = new Page<>(getServicesByGroupIdDTO.getPageNum(), getServicesByGroupIdDTO.getPageSize());
        IPage<ServiceInfoInGroupVO> pageList = serviceInfoMapper.getServiceInfoByGroupIdPage(page, serviceGroupId);
        SecPageVO<ServiceInfoInGroupVO> secPageVO = serviceGroupConvert.infoPageToSecPage(pageList);
        return ResultUtil.ok(secPageVO);
    }

    @Override
    public SecRestResponse<List<ServiceInfoVO>> getServiceByGroupId(ServiceGroupInfoDTO serviceGroupInfoDTO) {
        Long serviceGroupId = serviceGroupInfoDTO.getServiceGroupId();
        ServiceGroupPO serviceGroupPO = baseMapper.selectById(serviceGroupId);
        if (serviceGroupPO == null) {
            log.error("需要查询的服务组不存在");
            throw new BusinessException(SecErrorCodeConstant.SERVICE_IN_GROUP_GROUP_NOT_EXIST);
        }
        List<ServiceInfoInGroupVO> serviceInfoList = serviceInfoMapper.getServiceInfoByGroupId(serviceGroupInfoDTO);

        List<ServiceInfoVO> serviceInfoVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(serviceInfoList)) {
            serviceInfoVOS = serviceGroupConvert.serviceInGroupConvert(serviceInfoList);
        }


        return ResultUtil.ok(serviceInfoVOS);
    }

    @Override
    public SecRestResponse<List<GetBusiTypeListVO>> getBusiTypeList(GetBusiTypeListDTO getBusiTypeListDTO) {
        List<GetBusiTypeListVO> list = null;
        Long tenantId = getBusiTypeListDTO.getTenantId();
        //校验
        //(1)如果服务组ID不为空,服务组对象不为空
        //(2)如果租户ID为空，服务组ID不可为空
        //(3)如果租户ID不为空，服务组ID不起作用
        checkBusiTypeListDTO(getBusiTypeListDTO, tenantId);
        //逻辑处理

        if (tenantId != null && tenantId != 1) {
            // 根据租户id查询的方法是先查询出来关联的服务组id，再根据服务组id查询
            List<Long> serviceGroupIdList = getBusiTypeListDTO.getServiceGroupIdList();
            list = getBusiAndServiceListByGroupIds(serviceGroupIdList);
        } else {
            //根据服务组ID，查询服务组中包含服务信息和所属服务类型支持的业务类型
            list = getBusiAndServiceListByGroupId(getBusiTypeListDTO.getServiceGroupId());
        }
        return ResultUtil.ok(list);
    }

    @Override
    public SecRestResponse<ServiceCountVO> getServiceNumByTenantId(ServicesCountDTO serviceCountDTO) {
        Long tenantId = serviceCountDTO.getTenantId();

        //获取租户下的所有设备组
        QueryWrapper<ServiceGroupPO> queryWrapper = Wrappers.query();
        // 如果租户ID不为空，则查询租户下的服务数量
        if (tenantId != null) {
            queryWrapper.lambda().eq(ServiceGroupPO::getTenantId, tenantId);
        }
        // 如果租户为空，则查询平台所有的服务数量
        List<ServiceGroupPO> groupList = baseMapper.selectList(queryWrapper);

        List<ServiceGroupVO> serviceGroupVOS = serviceGroupConvert.convert(groupList);

        addServiceNum(serviceGroupVOS);

        final int[] freeDevicesNum = {0};
        final int[] allDevicesNum = {0};
        serviceGroupVOS.forEach(item -> {
            allDevicesNum[0] += item.getServiceNum();
            if (item.getServiceGroupType() == 1) {
                //查询资源组下的设备数量
                freeDevicesNum[0] = item.getServiceNum();
            }
        });

        ServiceCountVO vo = new ServiceCountVO();
        vo.setAllSeviceNum(allDevicesNum[0]);
        vo.setFreeSeviceNum(freeDevicesNum[0]);
        return ResultUtil.ok(vo);
    }

    @Override
    public ServiceGroupDTO findById(ServiceGroupInfoDTO serviceGroupInfoDTO) {
        return serviceGroupConvert.poToDto(baseMapper.selectById(serviceGroupInfoDTO.getServiceGroupId()));
    }

    /**
     * 查询租户的默认服务组
     *
     * @param findDefaultServiceGroupDTO
     * @return
     */
    @Override
    public SecRestResponse<List<ServiceGroupInDefaultVO>> findDefaultServiceGroup(FindDefaultServiceGroupDTO findDefaultServiceGroupDTO) {
        List<ServiceGroupPO> defaultByTentantId = baseMapper.getDefaultByTentantId(findDefaultServiceGroupDTO.getTenantId());
        List<ServiceGroupInDefaultVO> serviceGroupVo = serviceGroupConvert.poToDefaultVo(defaultByTentantId);
        return ResultUtil.ok(serviceGroupVo);
    }

    /**
     * 修改服务组状态
     *
     * @param serviceGroupId
     * @param serviceGroupStatus
     */
    @Override
    public void setServiceGroupStatus(Long serviceGroupId, Integer serviceGroupStatus) {
        ServiceGroupPO serviceGroupPO = new ServiceGroupPO();
        serviceGroupPO.setServiceGroupId(serviceGroupId);
        serviceGroupPO.setServiceGroupStatus(serviceGroupStatus);
        baseMapper.updateById(serviceGroupPO);
    }

    public void checkObject(ServiceGroupPO serviceGroupPO, ServiceInfoPO serviceInfoPO) {
        //查询条件：根据服务组ID获取服务组对象
        //获取服务对象
        if (serviceGroupPO == null) {
            //(1)服务组对象不为空
            log.error("服务组对象为空,不能绑定服务");
            throw new BusinessException(SecErrorCodeConstant.SERVICE_IN_GROUP_GROUP_OBJ_NOT_EXIST);
        }

        if (serviceInfoPO == null) {
            //(2)服务对象不为空
            log.error("服务对象为空,不能绑定服务");
            throw new BusinessException(SecErrorCodeConstant.SERVICE_IN_GROUP_SERVICE_OBJ_NOT_EXIST);
        }
    }

    @Override
    public void addGroupBusType(ServiceInGroupDTO serviceInGroupDTO) {
        ServiceGroupPO serviceGroupPO = baseMapper.selectById(serviceInGroupDTO.getServiceGroupId());
        if(serviceGroupPO.checkIsShare()){
            //共享服务组单业务类型不需要操作，直接返回
            return;
        }
        //根据服务ID获取服务对象
        ServiceInfoPO serviceInfo = serviceInfoMapper.selectById(serviceInGroupDTO.getServiceId());
        //根据服务组ID获取服务组支持的业务类型
        QueryWrapper<ServiceGroupToBusiTypePO> queryWrapper_group_type = Wrappers.query();
        queryWrapper_group_type.lambda().eq(ServiceGroupToBusiTypePO::getServiceGroupId, serviceInGroupDTO.getServiceGroupId());
        List<ServiceGroupToBusiTypePO> group_type_list = serviceGroupToBusiTypeMapper.selectList(queryWrapper_group_type);

        //根据服务类型ID取出服务支持的业务类型
        QueryWrapper<ServiceTypeToBusiTypePO> queryWrapper_service_type = Wrappers.query();
        queryWrapper_service_type.lambda().eq(ServiceTypeToBusiTypePO::getServiceTypeId, serviceInfo.getServiceTypeId());
        List<ServiceTypeToBusiTypePO> service_type_list = serviceTypeToBusiTypeMapper.selectList(queryWrapper_service_type);

        for (ServiceTypeToBusiTypePO po : service_type_list) {
            //服务支持的类型如果服务组中没有，就在服务组中添加该类型
            long TypeId = po.getBusiTypeId();
            ServiceGroupToBusiTypePO serviceGroupToBusiTypePO = new ServiceGroupToBusiTypePO();
            serviceGroupToBusiTypePO.setId(IdGenerator.ins().generator());
            serviceGroupToBusiTypePO.setServiceGroupId(serviceInGroupDTO.getServiceGroupId());
            serviceGroupToBusiTypePO.setBusiTypeId(TypeId);
            serviceGroupToBusiTypePO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
            serviceGroupToBusiTypePO.setUpdateBy(LoginUserUtil.getUserId());
            boolean exist = false;
            if (CollectionUtils.isNotEmpty(group_type_list)) {
                for (ServiceGroupToBusiTypePO groupPo : group_type_list) {
                    if (TypeId == groupPo.getBusiTypeId()) {
                        exist = true;
                        break;
                    }
                }
            }
            if (!exist) {
                serviceGroupToBusiTypeMapper.insert(serviceGroupToBusiTypePO);
            }
        }
    }

    @Override
    public void dellGroupBusType(ServiceInGroupDTO serviceInGroupDTO) {
        ServiceGroupPO serviceGroupPO = baseMapper.selectById(serviceInGroupDTO.getServiceGroupId());
        //（1）查找服务组下的所有服务
        QueryWrapper<ServiceInfoPO> queryWrapper_ServiceInfo_byGid = Wrappers.query();
        queryWrapper_ServiceInfo_byGid.lambda().eq(ServiceInfoPO::getServiceGroupId, serviceInGroupDTO.getServiceGroupId());
        List<ServiceInfoPO> serviceInfoList = serviceInfoMapper.selectList(queryWrapper_ServiceInfo_byGid);
        //（2）查找每个服务支持的业务类型
        ArrayList<Long> busids = new ArrayList<>();
        if (!serviceInfoList.isEmpty()) {
            for (ServiceInfoPO serviceInfoPO : serviceInfoList) {
                QueryWrapper<ServiceTypeToBusiTypePO> queryWrapper_service_type = Wrappers.query();
                queryWrapper_service_type.lambda().eq(ServiceTypeToBusiTypePO::getBusiTypeId, serviceInfoPO.getServiceTypeId());
                List<ServiceTypeToBusiTypePO> service_type_list = serviceTypeToBusiTypeMapper.selectList(queryWrapper_service_type);
                //取所有服务支持的业务类型取并集
                if (!service_type_list.isEmpty()) {
                    for (ServiceTypeToBusiTypePO serviceBusitypePO : service_type_list) {
                        if (!busids.contains(serviceBusitypePO.getBusiTypeId())) {
                            busids.add(serviceBusitypePO.getBusiTypeId());
                        }
                    }
                }
            }
        }
        //将取到的业务类型并集写入到服务组支持的类型中
        insertGroupBusType(busids, serviceInGroupDTO.getServiceGroupId());
    }

    public void insertGroupBusType(ArrayList<Long> busids, Long groupId) {
        QueryWrapper<ServiceGroupToBusiTypePO> queryWrapper_group_type = Wrappers.query();
        queryWrapper_group_type.lambda().eq(ServiceGroupToBusiTypePO::getServiceGroupId, groupId);
        //删除服务组原来支持的业务类型
        serviceGroupToBusiTypeMapper.delete(queryWrapper_group_type);
        if (!busids.isEmpty()) {
            for (Long busid : busids) {
                //将取到的业务类型并集写入到服务组支持的类型中
                ServiceGroupToBusiTypePO serviceGroupToBusiTypePO = new ServiceGroupToBusiTypePO();
                serviceGroupToBusiTypePO.setId(IdGenerator.ins().generator());
                serviceGroupToBusiTypePO.setServiceGroupId(groupId);
                serviceGroupToBusiTypePO.setBusiTypeId(busid);
                serviceGroupToBusiTypePO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
                serviceGroupToBusiTypePO.setCreateBy(LoginUserUtil.getUserId());
                serviceGroupToBusiTypeMapper.insert(serviceGroupToBusiTypePO);
            }
        }
    }

    public void checkBusiTypeListDTO(GetBusiTypeListDTO getBusiTypeListDTO, Long tenantId) {
        //如果服务组ID不可空,服务组对象不为空
        //如果租户ID为空，服务组ID不可为空
        //如果租户ID不为空，服务组ID不起作用
        if (tenantId == null || tenantId == 1) {
            if (getBusiTypeListDTO.getServiceGroupId() != null) {
                //查询服务组对象是否存在
                QueryWrapper<ServiceGroupPO> queryWrapper = Wrappers.query();
                queryWrapper.lambda().eq(ServiceGroupPO::getServiceGroupId, getBusiTypeListDTO.getServiceGroupId());
                if (!baseMapper.exists(queryWrapper)) {
                    log.error("未传入租户的情况下，服务组对象不存在");
                    throw new BusinessException(SecErrorCodeConstant.SERVICE_IN_GROUP_GROUP_OBJ_NOT_EXIST_WITH_NOTENANT);
                }
            } else {
                log.error("未传入租户的情况下，服务组ID不可为空");
                throw new BusinessException(SecErrorCodeConstant.SERVICE_IN_GROUP_GROUP_ID_NOT_EXIST_WITH_NOTENANT);
            }
        }
    }

    @Override
    public List<GetBusiTypeListVO> getBusiAndServiceListByTenantId(Long tenantId) {
        //(1)调用基础接口中的查询业务类型接口，获取所有业务类型
        Map<Long, String> map = busiTypeService.getBusiTypeMap();
        List<GetBusiTypeListVO> list = new ArrayList<>();
        ArrayList<Long> sercices_tenant = getAllServiceIdsBytenantId(tenantId);
        // (2)根据租户ID，查询租户下所有服务组包含的服务信息和服务支持的业务类型
        for (Map.Entry<Long, String> entry : map.entrySet()) {
            Long busiTypeId = entry.getKey();
            String BusiTypeName = entry.getValue();
            List<Long> serviceIdList = new ArrayList<>();
            GetBusiTypeListVO getBusiTypeListVO = new GetBusiTypeListVO();
            getBusiTypeListVO.setBusiTypeId(busiTypeId);
            getBusiTypeListVO.setBusiTypeName(BusiTypeName);
            // (3)获取支持该类型的所有服务ID
            ArrayList<Long> services = getAllDeviceIdsByBusiTypeId(busiTypeId);
            if (!sercices_tenant.isEmpty()) {
                for (Long serviceId : sercices_tenant) {
                    if (services.contains(serviceId)) {
                        serviceIdList.add(serviceId);
                    }
                }
            }
            getBusiTypeListVO.setServiceList(serviceIdList);
            list.add(getBusiTypeListVO);
        }
        return list;
    }

    @Override
    public List<GetBusiTypeListVO> getBusiAndServiceListByGroupIds(List<Long> serviceGroupIdList) {
        //(1)调用基础接口中的查询业务类型接口，获取所有业务类型
        Map<Long, String> map = busiTypeService.getBusiTypeMap();
        List<GetBusiTypeListVO> list = new ArrayList<>();
        ArrayList<Long> sercices_tenant = getAllServiceIdsByGroupIds(serviceGroupIdList);
        // (2)根据租户ID，查询租户下所有服务组包含的服务信息和服务支持的业务类型
        for (Map.Entry<Long, String> entry : map.entrySet()) {
            Long busiTypeId = entry.getKey();
            String BusiTypeName = entry.getValue();
            List<Long> serviceIdList = new ArrayList<>();
            GetBusiTypeListVO getBusiTypeListVO = new GetBusiTypeListVO();
            getBusiTypeListVO.setBusiTypeId(busiTypeId);
            getBusiTypeListVO.setBusiTypeName(BusiTypeName);
            // (3)获取支持该类型的所有服务ID
            ArrayList<Long> services = getAllDeviceIdsByBusiTypeId(busiTypeId);
            if (!sercices_tenant.isEmpty()) {
                for (Long serviceId : sercices_tenant) {
                    if (services.contains(serviceId)) {
                        serviceIdList.add(serviceId);
                    }
                }
            }
            getBusiTypeListVO.setServiceList(serviceIdList);
            list.add(getBusiTypeListVO);
        }
        return list;
    }

    @Override
    public List<GetBusiTypeListVO> getBusiAndServiceListByGroupId(Long groupId) {
        // (1)调用基础接口中的查询业务类型接口，获取所有业务类型
        Map<Long, String> map = busiTypeService.getBusiTypeMap();
        List<GetBusiTypeListVO> list = new ArrayList<>();
        // (2)根据服务组ID，查询服务组中包含服务信息和所属服务类型支持的业务类型
        QueryWrapper<ServiceInfoPO> queryWrapper = Wrappers.query();
        queryWrapper.lambda().eq(ServiceInfoPO::getServiceGroupId, groupId);
        List<ServiceInfoPO> serviceInfoPOS = serviceInfoMapper.selectList(queryWrapper);

        for (Map.Entry<Long, String> entry : map.entrySet()) {
            Long busiTypeId = entry.getKey();
            String BusiTypeName = entry.getValue();
            //遍历业务类型
            GetBusiTypeListVO getBusiTypeListVO = new GetBusiTypeListVO();
            getBusiTypeListVO.setBusiTypeId(busiTypeId);
            getBusiTypeListVO.setBusiTypeName(BusiTypeName);
            //获取支持该类型的所有服务ID
            ArrayList<Long> services = getAllDeviceIdsByBusiTypeId(busiTypeId);
            List<Long> serviceIdList = new ArrayList<>();
            //遍历服务组内的所有服务，如果服务ID存在上述的list中，就把此服务放在该类型下
            if (!serviceInfoPOS.isEmpty()) {
                for (ServiceInfoPO serviceInGroup : serviceInfoPOS) {
                    if (services.contains(serviceInGroup.getId())) {
                        serviceIdList.add(serviceInGroup.getId());
                    }
                }
                getBusiTypeListVO.setServiceList(serviceIdList);
                list.add(getBusiTypeListVO);
            }

        }
        return list;
    }


    public ArrayList<Long> getAllDeviceIdsByBusiTypeId(Long busiTypeId) {
        ArrayList<Long> services = new ArrayList<>();
        //根据业务类型ID获取服务类型ID
        QueryWrapper<ServiceTypeToBusiTypePO> queryWrapper_DeviceBusitype = Wrappers.query();
        queryWrapper_DeviceBusitype.lambda().eq(ServiceTypeToBusiTypePO::getBusiTypeId, busiTypeId)
                .orderByDesc(ServiceTypeToBusiTypePO::getCreateTime);
        List<ServiceTypeToBusiTypePO> deviceBusitypeLsit = serviceTypeToBusiTypeMapper.selectList(queryWrapper_DeviceBusitype);
        if (!deviceBusitypeLsit.isEmpty()) {
            for (ServiceTypeToBusiTypePO serviceTypeToBusiTypePO : deviceBusitypeLsit) {
                Long serviceTypeId = serviceTypeToBusiTypePO.getServiceTypeId();
                //根据服务类型ID获取该类型下的所有服务信息
                QueryWrapper<ServiceInfoPO> queryWrapper_Device = Wrappers.query();
                queryWrapper_Device.lambda().eq(ServiceInfoPO::getServiceTypeId, serviceTypeId)
                        .orderByDesc(ServiceInfoPO::getCreateTime);
                List<ServiceInfoPO> serviceInfoList = serviceInfoMapper.selectList(queryWrapper_Device);
                //将所有的服务id加入到list中
                if (!serviceInfoList.isEmpty()) {
                    for (ServiceInfoPO service : serviceInfoList) {
                        services.add(service.getId());
                    }
                }
            }
        }
        return services;
    }

    public ArrayList<Long> getAllServiceIdsBytenantId(Long tenantId) {
        ArrayList<Long> services = new ArrayList<>();
        //根据租户ID获取所有服务组
        QueryWrapper<ServiceGroupPO> groupQueryWrapper = Wrappers.query();
        groupQueryWrapper.lambda().eq(ServiceGroupPO::getTenantId, tenantId);
        List<ServiceGroupPO> groupList = baseMapper.selectList(groupQueryWrapper);
        if (!groupList.isEmpty()) {
            for (ServiceGroupPO group : groupList) {
                //根据服务组ID获取服务组下的所服务
                QueryWrapper<ServiceInfoPO> servicesQueryWrapper = Wrappers.query();
                servicesQueryWrapper.lambda().eq(ServiceInfoPO::getServiceGroupId, group.getServiceGroupId());
                List<ServiceInfoPO> deviceInfoList = serviceInfoMapper.selectList(servicesQueryWrapper);
                if (!deviceInfoList.isEmpty()) {
                    for (ServiceInfoPO service : deviceInfoList) {
                        services.add(service.getId());
                    }
                }
            }
        }
        return services;
    }

    public ArrayList<Long> getAllServiceIdsByGroupIds(List<Long> serviceGroupIdList) {
        ArrayList<Long> services = new ArrayList<>();
        if (!serviceGroupIdList.isEmpty()) {
            for (Long serviceGroupId : serviceGroupIdList) {
                //根据服务组ID获取服务组下的所服务
                QueryWrapper<ServiceInfoPO> servicesQueryWrapper = Wrappers.query();
                servicesQueryWrapper.lambda().eq(ServiceInfoPO::getServiceGroupId, serviceGroupId);
                List<ServiceInfoPO> deviceInfoList = serviceInfoMapper.selectList(servicesQueryWrapper);
                if (!deviceInfoList.isEmpty()) {
                    for (ServiceInfoPO service : deviceInfoList) {
                        services.add(service.getId());
                    }
                }
            }
        }
        return services;
    }

    /**
     * @param serviceGroupId
     * @return
     * @Description: 校验设备组信息
     */
    private ServiceGroupPO checkServiceGroup(Long serviceGroupId) {
        ServiceGroupPO serviceGroupPO = baseMapper.selectById(serviceGroupId);
        if (serviceGroupPO == null) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_NOT_FIND);
        }
        return serviceGroupPO;
    }

    /**
     * @param busiTypeIdList
     * @Description: 校验业务类型是否合法
     */
    private void checkBusiType(List<Long> busiTypeIdList) {
        // 定义的业务类型列表
        List<DicBusiTypeVO> dicBusiTypeVOList = busiTypeService.getBusiTypeList();
        if (dicBusiTypeVOList != null && dicBusiTypeVOList.isEmpty()) {
            throw new BusinessException(SecErrorCodeConstant.BUSI_TYPE_API_ERROR);
        }
        // 验证业务类型参数是否合法
        List<Long> busiTypeIds = new ArrayList<>();
        if (dicBusiTypeVOList != null) {
            dicBusiTypeVOList.forEach(item -> {
                busiTypeIds.add(item.getId());
            });
        }

        for (Long busiTypeId : busiTypeIdList) {
            if (!busiTypeIds.contains(busiTypeId)) {
                throw new BusinessException(SecErrorCodeConstant.BUSI_TYPE_NOT_EXIST);
            }
        }
    }

    /**
     * @param groupList
     * @return
     * @Description: 根据服务组ID，获取服务组支持的业务类型对象和服务数量
     */
    private List<ServiceGroupVO> addBusiTypeList(List<ServiceGroupVO> groupList, List<Long> busiTypeIdList) {
        if (groupList == null) {
            return new ArrayList<>();
        }

        // 先获取所有查询到的服务组id列表
        List<Long> groupIds = groupList.stream().map(ServiceGroupVO::getServiceGroupId).collect(toList());

        // 根据id列表查询所有对应的服务组与业务类型关联列表
        List<ServiceGroupToBusiTypePO> serviceGroupToBusiTypePOS = serviceGroupToBusiTypeMapper.selectByGroupIds(groupIds);

        // 根据服务组id进行分组，服务组id -> 业务idList
        Map<Long, List<Long>> serviceGroupIdToBusiIdMap = serviceGroupToBusiTypePOS.stream().collect(Collectors.groupingBy(
                ServiceGroupToBusiTypePO::getServiceGroupId, Collectors.mapping(ServiceGroupToBusiTypePO::getBusiTypeId, toList())));

        // 定义的所有业务类型Map表
        List<DicBusiTypeVO> dicBusiTypeVOList = busiTypeService.getBusiTypeList();
        Map<Long, String> busiTypeMap = dicBusiTypeVOList.stream().collect(Collectors.toMap(DicBusiTypeVO::getId,
                DicBusiTypeVO::getBusiTypeName));

        // 收集满足所有业务类型的服务组
        List<ServiceGroupVO> serviceGroupVOList = new ArrayList<>();
        groupList.forEach(item -> {
            Long groupId = item.getServiceGroupId();
            List<Long> busiIdList = serviceGroupIdToBusiIdMap.get(groupId);

            // 满足所有业务类型
            if (CollectionUtils.isEmpty(busiIdList)) {
                if (CollectionUtils.isEmpty(busiTypeIdList)) {
                    serviceGroupVOList.add(item);
                }
            } else if (CollectionUtils.isEmpty(busiTypeIdList) || busiIdList.containsAll(busiTypeIdList)) {
                // 业务类型列表
                List<BusiTypeVO> busiTypeVOList = new ArrayList<>();
                // 拼接业务类型对象
                busiIdList.forEach(id -> {
                    BusiTypeVO busiTypeVO = new BusiTypeVO();
                    busiTypeVO.setBusiTypeId(id);
                    busiTypeVO.setBusiTypeName(busiTypeMap.get(id));
                    busiTypeVOList.add(busiTypeVO);
                });
                item.setBusiTypeList(busiTypeVOList);
                // 当前服务组加入服务组列表
                serviceGroupVOList.add(item);
            }
        });

        return serviceGroupVOList;
    }


    /**
     * @param groupList
     * @return
     * @Description: 设置服务组数量
     */
    private List<ServiceGroupVO> addServiceNum(List<ServiceGroupVO> groupList) {
        List<Long> groupIds = groupList.stream().map(ServiceGroupVO::getServiceGroupId).collect(toList());
        if (CollectionUtils.isEmpty(groupIds)) {
            return groupList;
        }
        QueryWrapper<ServiceInfoPO> queryWrap = Wrappers.query();
        queryWrap.lambda().select(ServiceInfoPO::getId, ServiceInfoPO::getServiceGroupId)
                .in(ServiceInfoPO::getServiceGroupId, groupIds);
        List<ServiceInfoPO> serviceInfoPOS = serviceInfoMapper.selectList(queryWrap);
        Map<Long, List<Long>> serviceNumByGroupIdMap = serviceInfoPOS.stream().collect(Collectors.groupingBy(
                ServiceInfoPO::getServiceGroupId, Collectors.mapping(ServiceInfoPO::getId, toList())));

        groupList.forEach(item -> {
            // 服务数量
            Long groupId = item.getServiceGroupId();
            item.setServiceNum(serviceNumByGroupIdMap.containsKey(groupId) ? serviceNumByGroupIdMap.get(groupId).size() : 0);
        });
        return groupList;
    }

//    /**
//     * 获取服务组是否单业务
//     *
//     * @return
//     */
//    private boolean getServiceGroupOnlyBusi() {
//        SecRestResponse<String> serviceGroupOnlyBusiConfig = configServiceApi.getConfigValueByConfigCode(CommonConstant.SERVICE_GROUP_ONLY_BUSI);
//        if (!serviceGroupOnlyBusiConfig.isSuccess()) {
//            throw new BusinessException(serviceGroupOnlyBusiConfig.getCode(), serviceGroupOnlyBusiConfig.getMessage());
//        }
//        if ("true".equals(serviceGroupOnlyBusiConfig.getResult())) {
//            //服务组单业务类型
//            return true;
//        }
//        return false;
//    }

    @Override
    public List<ServiceGroupVO> getServiceGroupByTenantId(Long tenantId) {
        LambdaQueryWrapper<ServiceGroupPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ServiceGroupPO::getTenantId, tenantId)
                .eq(ServiceGroupPO::getInvalidFlag, 0);
        List<ServiceGroupPO> serviceGroupPOS = baseMapper.selectList(queryWrapper);
        List<ServiceGroupVO> serviceGroupVOList = serviceGroupConvert.convert(serviceGroupPOS);
        return serviceGroupVOList;
    }

    /**
     * 根据服务组标识查询服务组对象
     *
     * @param serviceGroupCode
     * @return
     */
    @Override
    public ServiceGroupVO getServiceGroupByCode(String serviceGroupCode) {
        log.info("serviceGroup:getServiceGroupByCode: code={}", serviceGroupCode);
        LambdaQueryWrapper<ServiceGroupPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ServiceGroupPO::getServiceGroupCode, serviceGroupCode);
        ServiceGroupPO serviceGroupPO = baseMapper.selectOne(queryWrapper);

        if (serviceGroupPO == null) {
            log.error("serviceGroup:getServiceGroupByCode: error={}", "服务组不存在");
            throw new BusinessException(SecErrorCodeConstant.SERVICE_IN_GROUP_GROUP_NOT_EXIST);
        }
        return serviceGroupConvert.convertVo(serviceGroupPO);
    }

    /**
     * 根据区域ID查询服务组对象
     *
     * @param serviceGroupByRegionIdDTO
     * @return
     */
    @Override
    public List<ServiceGroupVO> getServiceGroupListByRegionId(ServiceGroupByRegionIdDTO serviceGroupByRegionIdDTO) {
        QueryWrapper<ServiceGroupPO> queryWrap = Wrappers.query();
        queryWrap.lambda().eq(ServiceGroupPO::getRegionId,serviceGroupByRegionIdDTO.getRegionId());
        List<ServiceGroupPO> serviceGroupPOList = baseMapper.selectList(queryWrap);
        List<ServiceGroupVO> serviceGroupVOList = serviceGroupConvert.convert(serviceGroupPOList);
        return serviceGroupVOList;
    }

    @Override
    public String getKmsServiceGroupCode(String serviceGroupCode) {
        ServiceGroupToGroupPO serviceGroupToGroupPO = baseMapper.getKmsServiceGroupCode(serviceGroupCode);
        if (serviceGroupToGroupPO == null) {
            log.error("serviceGroup:getKmsServiceGroupCode,serviceGroupCode= {}, error={}", serviceGroupCode,"根据pki和svs的服务组标识查询远程kms的服务组标识不存在");
            throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_TO_GROUP_NOT_EXIST);
        }

        ServiceGroupPO serviceGroupPO = baseMapper.selectById(serviceGroupToGroupPO.getKmsGroupId());
        if (serviceGroupPO == null) {
            throw new BusinessException(SecErrorCodeConstant.SERVICE_GROUP_NOT_FIND);
        }

        return serviceGroupPO.getServiceGroupCode();
    }

    @Override
    public List<ServiceGroupVO> getAll() {
        LambdaQueryWrapper<ServiceGroupPO> queryWrapper = new LambdaQueryWrapper<>();
        List<ServiceGroupPO> serviceGroupPOS = baseMapper.selectList(queryWrapper);
        return serviceGroupConvert.convert(serviceGroupPOS);
    }

    @Override
    public List<ServiceGroupVO> getServiceGroupWithGroupIds(List<Long> serviceGroupIdList) {
        if( serviceGroupIdList.isEmpty() ){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ServiceGroupPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ServiceGroupPO::getServiceGroupId,serviceGroupIdList);
        List<ServiceGroupPO> serviceGroupPOS = baseMapper.selectList(queryWrapper);
        return serviceGroupConvert.convert(serviceGroupPOS);
    }
}