package com.sansec.ccsp.device.business.deviceinfo.service;

import com.sansec.ccsp.device.deviceinfo.request.DeviceIdDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdPageDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceIdsDTO;
import com.sansec.ccsp.device.deviceinfo.request.DeviceInfoPageDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.VsmHostDeviceInfoAddDTO;
import com.sansec.ccsp.device.deviceinfo.request.vsmhost.VsmHostDeviceInfoEditDTO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceInfoVO;
import com.sansec.ccsp.device.deviceinfo.response.DeviceStatusVO;
import com.sansec.ccsp.device.deviceinfo.response.vsmhost.VsmHostDeviceInfoVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/2/23 18:01
 * @Description: 宿主机对外接口
 */
public interface VsmHostDeviceInfoService {
    /**
     * @param deviceInfoPageDTO
     * @return
     * @Description: 分页查询
     */
    SecRestResponse<SecPageVO<DeviceInfoVO>> find(DeviceInfoPageDTO deviceInfoPageDTO);

    /**
     * 根据宿主机id查询虚拟机信息
     * @param deviceIdDTO
     * @return
     */
    SecRestResponse<SecPageVO<DeviceInfoVO>> findVsmByHostId(DeviceIdPageDTO deviceIdDTO);

    /**
     * @param vsmHostDeviceInfoAddDTO
     * @return
     * @Description: 添加宿主机
     */
    SecRestResponse<Long> add(VsmHostDeviceInfoAddDTO vsmHostDeviceInfoAddDTO);

    /**
     * @param deviceIdDTO
     * @return
     * @Description: 删除宿主机
     */
    SecRestResponse<Long> delete(DeviceIdDTO deviceIdDTO);

    /**
     * @param deviceIdsDTO
     * @return
     * @Description: 获取宿主机设备状态
     */
    SecRestResponse<List<DeviceStatusVO>> status(DeviceIdsDTO deviceIdsDTO);

    /**
     * @param deviceIdDTO
     * @return
     * @Description: 获取宿主机设备内部详细信息
     */
    SecRestResponse<VsmHostDeviceInfoVO> hccsinfo(DeviceIdDTO deviceIdDTO);

    /**
     * @param deviceIdDTO
     * @return
     * @Description: 获取宿主机基本信息
     */
    SecRestResponse<DeviceInfoVO> info(DeviceIdDTO deviceIdDTO);

    /**
     * @param vsmHostDeviceInfoEditDTO
     * @return
     * @Description: 编辑宿主机信息
     */
    SecRestResponse<Long> edit(VsmHostDeviceInfoEditDTO vsmHostDeviceInfoEditDTO);

    /**
     * @param deviceId
     * @return
     * @Description: 是否支持0088
     */
    boolean support0088ByVsmHostId(Long deviceId);
}
