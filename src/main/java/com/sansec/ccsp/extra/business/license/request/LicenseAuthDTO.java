package com.sansec.ccsp.extra.business.license.request;

import com.sansec.ccsp.extra.business.constant.CommonPattern;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> wwl
 * @description : 许可证申请表;
 * @date : 2023-5-8
 */
@Data
public class LicenseAuthDTO {

    /**
     * 申请许可内容
     */
    @NotBlank(message = "许可授权内容不可为空")
    private String licenseApplyContent;
    /**
     * 授权时间，永久授权为null
     */
    private String authTime;
    /**
     * 授权单位,永久授权为null
     */
    private String unit;
    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不可为空")
    @Size(max = 50, message = "客户名称长度限制50")
    private String customerName;
    /**
     * 备注
     */
    @Size(max = 100, message = "备注长度限制100")
    @Pattern(regexp = CommonPattern.COMMON_DESC, message = "备注请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符")
    private String remark;

}