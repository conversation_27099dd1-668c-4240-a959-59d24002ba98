package com.sansec.ccsp.pt.business.common.dic.controller;

import com.sansec.ccsp.common.dic.response.DicBusiTypeVO;
import com.sansec.ccsp.common.dic.response.DicSysDataVO;
import com.sansec.ccsp.pt.business.common.dic.service.impl.DicServiceServiceImpl;
import com.sansec.common.param.response.SecRestResponse;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Description: 字典服务
 * @CreateTime: 2023-05-05
 * @Author: wangjunjie
 */
@RequestMapping("/common/api/dic/v1")
@RestController
public class DicController {

    @Resource
    private DicServiceServiceImpl dicServiceService;

    /**
     * @return
     * @Description: 业务类型列表
     */
    @PostMapping(value = "/getBusiTypeList")
    public SecRestResponse<List<DicBusiTypeVO>> getBusiTypeList() {
        return dicServiceService.getBusiTypeList();
    }

    /**
     * @param dicType
     * @return
     * @Description: 获取系统字典
     */
    @PostMapping(value = "/getSysDictByType/{dicType}")
    public SecRestResponse<List<DicSysDataVO>> getSysDictByType(@PathVariable(value = "dicType") @NotEmpty String dicType) {
        return dicServiceService.getSysDictByType(dicType);
    }
}
