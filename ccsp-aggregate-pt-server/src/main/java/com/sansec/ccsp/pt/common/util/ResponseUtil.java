package com.sansec.ccsp.pt.common.util;

import com.sansec.ccsp.pt.common.error.SecErrorCodeConstant;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;

/**
 * @<PERSON> l<PERSON>
 * @Date 2024/3/21 16:08
 */
public class ResponseUtil {

    private ResponseUtil() {}

    public static <E> E getResult(SecRestResponse<E> response) {
        checkResponse(response);
        return response.getResult();
    }

    public static void checkResponse(SecRestResponse<?> response) {
        if(response == null || !response.isSuccess()) {
            throw new BusinessException(SecErrorCodeConstant.RPC_INVOKE_ERROR);
        }
    }


}
