package com.sansec.ccsp.pt.business.sim.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ccsp.pt.business.sim.entity.SimFlowDataPO;
import com.sansec.ccsp.pt.business.sim.response.SimFlowDataVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * @Description: 流水日志转换
 * @CreateTime: 2023-09-13
 * @Author: wangjunjie
 */
@Mapper(componentModel = "spring")
public interface SimFlowDataConvert {

    SimFlowDataVO poToVo(SimFlowDataPO simFlowDataPO);

    @Mappings({
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "current", target = "pageNum"),
            @Mapping(source = "records", target = "list")
    })
    SecPageVO<SimFlowDataVO> pagePoToSecPageVo(IPage<SimFlowDataPO> simFlowDataPOIPage);
}
