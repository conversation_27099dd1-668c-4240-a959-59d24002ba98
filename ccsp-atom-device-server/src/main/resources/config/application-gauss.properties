spring.datasource.driver=gauss
spring.datasource.url=************************************,119.13.125.147:5432/mfpt?currentSchema=ccsp_device
spring.datasource.userName=+N1qXbD7srO4+WWi5A2Nyg==
spring.datasource.passWord=A/YC4vF0axVbZJsskjNGhQ==
mybatis.databaseType=postgresql

spring.datasource.initialSize = 30
spring.datasource.maxActive = 500
spring.datasource.minIdle = 30
spring.datasource.maxWait = 20000
spring.datasource.timeBetweenConnectErrorMillis = 90000
spring.datasource.timeBetweenEvictionRunsMillis = 60000
spring.datasource.validationQuery = SELECT 1
spring.datasource.testOnBorrow = false
spring.datasource.testOnReturn = false
spring.datasource.testWhileIdle = true
# 连接池中连接最小空闲时间（毫秒）
spring.datasource.minEvictableIdleTimeMillis = 300000

